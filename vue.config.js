const path = require('path')
const fs = require('fs')
const dayjs = require('dayjs')
// 引入压缩插件
const ZipPlugin = require('zip-webpack-plugin')
const preload = require('@vue/preload-webpack-plugin')

// 格式化名字 fileNameZip
// function getFileNameZip() {
//   return `dist${dayjs().format('YYYY年MM月DD日HH时mm分ss秒')}.zip`
// }
const fileName = 'ljdw-saas-front-admin.zip'

function resolve(dir) {
  return path.join(__dirname, dir)
}

const isProduction = process.env.NODE_ENV === 'production'
process.env.VUE_APP_BUILD_DATE = new Date().toString()

module.exports = {
  publicPath: './',
  productionSourceMap: !isProduction,
  // 开发环境配置tree shaking;生产环境 自带，不用配置
  devServer: {
    host: '0.0.0.0',
    port: 8088, // 先启动之前的登陆以后，关闭之前的，再启动就可以获取相应的cookie，保持端口一致，不需要获取cookie和localStorage调试，可以修改端口
    proxy: {
      '/api': {
        target: process.env.VUE_APP_APP_HOST,
        // target: 'http://preview.gpsnow.net/',
        changeOrigin: true, // 是否跨域
        // logLevel: 'debug', // 查看真实地址
        pathRewrite: {
          '^/api': '/'
        }
      },
      '/v2': {
        // target: 'https://saas.whatsgps.com/saas-pre/web/api',
        // target: 'http://**************:9080/gpstrack/dev/web',
        target: 'http://**************:9080/gpstrack/test/web',
        // target: 'https://www.whatsgps.com/web/api',
        // target: 'https://saas.gpsnow.net/saas/web/api',
        // target: 'http://**************:9080/gpstrack/dev/web',
        // target: 'http://**************:28081',
        // target: 'https://www.gpsnow.net/saas-pre/web/api',
        changeOrigin: true,
        pathRewrite: {
          '^/v2': '/'
        }
      },
      '/static-path': {
        target: 'http://**************:9080/gpstrack/dev/index',
        changeOrigin: true,
        pathRewrite: {
          '^/static-path': '/'
        }
      }
    },
    client: {
      overlay: false // 编译错误时，取消全屏覆盖（建议关掉）
    },
    headers: {
      'access-control-allow-origin': '*'
    }
  },
  chainWebpack: config => {
    config
      .plugin('preload')
      .use(preload)
      .tap(() => [
        {
          rel: 'preload',
          fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
          include: 'initial'
        }
      ])
    config.plugins.delete('prefetch-index')
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
    if (isProduction) {
      config.optimization.minimizer('terser').tap(args => {
        Object.assign(args[0].terserOptions.compress, {
          drop_console: true
        })
        return args
      })
    }
  },
  configureWebpack: config => {
    if (isProduction) {
      config.plugins.push(
        new ZipPlugin({
          path: '.', //路径名
          filename: fileName //打包名
        })
      )
    }
    config.devtool = !isProduction ? 'source-map' : undefined
    config.resolve.alias = {
      '@': resolve('src'),
      '@utils': resolve('./node_modules/@swd/utils/lib'),
      vue$: 'vue/dist/vue.esm.js' // 解决 element-ui table为空问题，需引入完整版
    }
    /**
     * 解决 element-ui 升级 dart-sass 之后icon乱码问题
     * sass 升级 1.39.0
     * 转换unicode编码
     * @type {string}
     */
    const sassLoader = require.resolve('sass-loader')
    config.module.rules
      .filter(rule => {
        return rule.test.toString().indexOf('scss') !== -1
      })
      .forEach(rule => {
        rule.oneOf.forEach(oneOfRule => {
          const sassLoaderIndex = oneOfRule.use.findIndex(item => item.loader === sassLoader)
          oneOfRule.use.splice(sassLoaderIndex, 0, { loader: require.resolve('css-unicode-loader') })
        })
      })
  },
  css: {
    // 向预处理器 Loader 传递选项
    loaderOptions: {
      scss: {
        // 全局变量
        prependData: `
              @import "~@/styles/mixin.scss";
              @import "~@/styles/variables.scss";
            `
      },
      sass: {
        sassOptions: {
          outputStyle: 'expanded'
        }
      }
    }
  },
  transpileDependencies: ['vue-echarts', 'resize-detector'],
  lintOnSave: !isProduction
}
