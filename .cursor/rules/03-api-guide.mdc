---
description: 
globs: 
alwaysApply: true
---
# API使用指南

本项目的API接口定义位于 `src/api` 目录，按功能模块拆分成不同文件。

## API目录结构

- **src/api/v2/**: API接口目录
  - 按功能模块划分的各个API文件（user.js, device.js, system.js等）
  - **v2/**: API的新版本接口

## HTTP请求配置

项目使用Axios作为HTTP客户端，通过 **@src/api/v2/request/index.js** 实现请求封装、拦截器配置和全局错误处理。

## API使用范例

```javascript
// 导入API
import { getUserInfo, updateUserInfo } from '@/api/user'

// 使用API
async function fetchUserData() {
  try {
    const { data } = await getUserInfo(userId)
    // 处理数据...
  } catch (error) {
    // 错误处理...
  }
}

// 发送数据
async function submitUserData(userData) {
  try {
    await updateUserInfo(userData)
    // 成功处理...
  } catch (error) {
    // 错误处理...
  }
}
```

## 新增API

创建新的API请遵循以下规范：

1. 按功能模块归类，创建或使用对应的API文件
2. 使用ES6模块语法导出API函数
3. 函数命名应明确表示操作类型（get, create, update, delete等）
5. 接口方法命名以下划线开头，以区分普通函数方法，如：_getUserInfo()
6. 添加必要的注释说明API用途和参数

## 最佳实现
新增的API接口方法命名以下划线开头，以区分普通函数方法，如：_getUserInfo()

