---
description: 
globs: 
alwaysApply: true
---
# 国际化(i18n)指南

本项目使用vue-i18n进行国际化处理，配置文件位于 `src/i18n` 目录。

## 国际化目录结构

- **[src/i18n/index.js](mdc:src/i18n/index.js)**: 国际化配置主文件
- **src/i18n/langs/**: 多语言翻译文件目录
  - **zh.js**: 中文翻译
  - **en.js**: 英文翻译
  - 其他语言...

## 使用示例

```javascript
// 在模板中使用
<template>
  <div>
    <p>{{ $t('common.welcome') }}</p>
    <p>{{ $t('login.title') }}</p>
    <p>{{ $t('user.info', { name: username }) }}</p>
  </div>
</template>

// 在JS中使用
import { i18n } from '@/i18n'

const welcomeText = i18n.t('common.welcome')
const formattedText = i18n.t('message.hello', { name: 'World' })
```

## 路由中的国际化

路由配置中使用 `meta.trans` 属性定义国际化键名：

```javascript
{
  path: '/dashboard',
  component: Layout,
  redirect: '/dashboard/index',
  children: [
    {
      path: 'index',
      name: 'Dashboard',
      component: () => import('@/views/Dashboard/index'),
      meta: { 
        title: '首页',
        icon: 'dashboard',
        trans: 'dashboard'  // 国际化翻译键名
      }
    }
  ]
}
```

## 国际化开发规范

1. 所有面向用户的文本应使用国际化键名
2. 翻译键按功能模块和页面组织
3. 常用文本提取到common命名空间
4. 遵循项目已有的键名命名约定
5. 带变量的文本使用参数占位符

