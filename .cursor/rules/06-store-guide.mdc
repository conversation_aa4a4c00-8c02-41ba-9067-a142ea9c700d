---
description: 
globs: 
alwaysApply: true
---
# 状态管理指南

本项目使用Vuex进行状态管理，统一存放在 `src/store` 目录下。

## 状态管理目录结构

- **[src/store/index.js](mdc:src/store/index.js)**: Vuex store主文件
- **src/store/modules/**: Vuex模块化store
- **src/store/getters.js**: 全局getters

## Vuex模块

项目按功能模块拆分Vuex store，主要包括：

- **app**: 应用全局状态
- **user**: 用户信息和认证状态
- **permission**: 权限和路由状态
- **settings**: 用户设置和偏好
- **tagsView**: 标签导航状态

## 状态管理使用示例

```javascript
// Vuex使用示例
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  computed: {
    // 映射状态
    ...mapState({
      token: state => state.user.token,
      roles: state => state.user.roles
    }),
    // 映射getters
    ...mapGetters([
      'username',
      'avatar',
      'permission_routes'
    ])
  },
  methods: {
    // 映射actions
    ...mapActions('user', [
      'login',
      'logout',
      'getUserInfo'
    ]),
    
    async handleLogin() {
      try {
        await this.login(this.loginForm)
        this.$router.push('/')
      } catch (error) {
        // 错误处理
      }
    }
  }
}

```

## 状态管理开发规范

1. 按功能模块拆分store
2. 遵循Vuex的actions、mutations、state、getters组织结构
3. 异步操作放在actions中处理
4. 状态修改必须通过mutations
5. 使用常量作为mutation类型
6. 复杂状态应考虑规范化（避免嵌套）
