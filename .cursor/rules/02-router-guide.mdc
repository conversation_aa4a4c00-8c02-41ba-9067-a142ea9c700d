---
description: 
globs: 
alwaysApply: true
---
# 路由系统指南

本项目使用Vue Router管理路由，并采用模块化方式组织路由配置。

## 路由文件结构

- **[src/router/index.js](mdc:src/router/index.js)**: 路由主配置文件，导入和合并各模块路由
- **[src/router/whiteList.js](mdc:src/router/whiteList.js)**: 白名单路由（无需权限可访问）
- **src/router/modules/**: 按功能模块拆分的路由配置

## 路由类型

路由分为三种类型：
1. **constantRoutes**: 基础路由，如登录、404等无需权限的路由
2. **asyncRoutes**: 动态路由，根据用户权限动态加载
3. **whiteListRoutes**: 白名单路由，无需权限检查

## 路由配置示例

```javascript
// 路由配置示例
{
  path: '/dashboard',
  component: Layout,
  redirect: '/dashboard/index',
  children: [
    {
      path: 'index',
      name: 'Dashboard',
      component: () => import('@/views/Dashboard/index'),
      meta: { 
        title: '首页',  // 页面标题
        icon: 'dashboard',  // 图标
        affix: true,  // 是否固定在标签栏
        trans: 'dashboard'  // 国际化翻译键名
      }
    }
  ]
}
```

## 权限控制

路由权限控制在 **[src/permission.js](mdc:src/permission.js)** 中实现，通过路由守卫控制页面访问权限。

