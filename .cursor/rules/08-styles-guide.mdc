---
description: 
globs: 
alwaysApply: true
---
# 样式指南

本项目使用SCSS进行样式管理，样式文件位于 `src/styles` 目录下。

## 样式目录结构

- **[src/styles/index.scss](mdc:src/styles/index.scss)**: 主样式文件，导入所有其他样式
- **[src/styles/variables.scss](mdc:src/styles/variables.scss)**: 全局变量定义
- **[src/styles/mixin.scss](mdc:src/styles/mixin.scss)**: 混合(mixin)定义
- **[src/styles/transition.scss](mdc:src/styles/transition.scss)**: 过渡动画样式
- **[src/styles/element-ui.scss](mdc:src/styles/element-ui.scss)**: Element UI样式覆盖

## 使用示例

 全局变量和混合(minxin)已在webpack css预处理器中注入，可以直接在代码中使用，不要再次引入这些变量冗余

```scss
// 在Vue组件中使用
<style lang="scss" scoped>
    //
.container {
  background-color: $ContainerBg;
  
  .header {
    @include clearfix;
    height: 50px;
    line-height: 50px;
    color: $primary;
  }
  
  .content {
    @include scrollBar;
    min-height: 300px;
  }
}
</style>
```

## CSS类命名规范

项目使用BEM命名规范（Block__Element--Modifier）：
```scss
.block {
  /* 块样式 */
  
  &__element {
    /* 元素样式 */
    
    &--modifier {
      /* 修饰符样式 */
    }
  }
}

// 例如
.form {
  &__item {
    margin-bottom: 20px;
    
    &--required {
      .label::before {
        content: '*';
        color: red;
      }
    }
  }
}
```

## 样式开发规范

1. 优先使用项目定义的变量而非硬编码的值
2. 使用SCSS嵌套减少选择器复杂度
3. 组件样式使用scoped特性隔离
4. 全局样式谨慎添加，避免样式冲突
5. 媒体查询使用变量定义断点
6. 使用mixins封装复用的样式片段

