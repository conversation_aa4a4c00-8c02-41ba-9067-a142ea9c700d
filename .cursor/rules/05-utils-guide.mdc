---
description: 
globs: 
alwaysApply: true
---
# 工具函数指南

本项目的工具函数位于 `src/utils` 目录，按功能类型拆分成不同文件。

## 工具函数目录结构

- **[src/utils/index.js](mdc:src/utils/index.js)**: 通用工具函数集合
- **[src/utils/http.js](mdc:src/utils/http.js)**: HTTP请求封装
- **[src/utils/auth.js](mdc:src/utils/auth.js)**: 认证相关工具
- **[src/utils/date.js](mdc:src/utils/date.js)**: 日期处理工具
- **[src/utils/common.js](mdc:src/utils/common.js)**: 通用工具函数
- **[src/utils/validate.js](mdc:src/utils/validate.js)**: 数据验证工具
- **[src/utils/event-bus.js](mdc:src/utils/event-bus.js)**: 事件总线
- **[src/utils/mapTool.js](mdc:src/utils/mapTool.js)**: 地图相关工具

## 工具函数使用示例

```javascript
// 导入工具函数
import { UTCToLocalTime } from '@/utils/date'
import { getToken } from '@/utils/auth'

// 使用日期格式化工具
const UTCToLocalTime = formatDate('2025-04-25')

// 获取认证令牌
const token = getToken()


```

## 工具函数开发规范

1. 按功能类型归类到对应文件中
2. 函数命名应明确表示功能
3. 为复杂函数添加JSDoc注释
4. 纯函数优先，避免副作用
5. 考虑边界情况和错误处理
6. 避免过度依赖全局状态
7. 时间格式相关操作使用dayjs来完成

