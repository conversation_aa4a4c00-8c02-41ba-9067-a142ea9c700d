---
description: 
globs: 
alwaysApply: true
---
# 组件使用指南

本项目组件分为业务组件和通用组件，统一存放在 `src/components` 目录下。

## 组件目录结构

- **src/components/**: 所有组件目录
  - **Common/**: 通用功能组件
  - **SvgIcon/**: SVG图标组件
  - **Video/**: 视频相关组件
  - **Device/**: 设备相关组件
  - **Upload/**: 上传相关组件
  - **Dialogs/**: 对话框组件
  - **Maptalks/**: 地图相关组件
  - **v2/**: 新版组件
  - 其他按功能和类型分类的组件

## UI框架

项目基于Element UI，主要配置文件：
- **[src/plugins/element.js](mdc:src/plugins/element.js)**: Element UI配置
- **[src/base-ui/](mdc:src/base-ui)**: 自定义UI组件

## 组件开发规范

1. **组件命名**:
   - 使用PascalCase（大驼峰）命名组件文件和组件名
   - 通用组件加前缀（如Base、App、V等）

2. **组件结构**:
   - 单个组件文件不要过大，保持在300行以内
   - 复杂组件拆分为子组件
   - 推荐使用SFC（单文件组件）格式

3. **Props定义**:
   - 明确定义prop类型和默认值
   - 添加必要的props验证
   - 使用kebab-case命名传入的props

4. **组件复用**:
   - 优先使用现有组件
   - 将可复用逻辑提取到mixins或hooks中
   - 组件间通信优先使用props和事件
