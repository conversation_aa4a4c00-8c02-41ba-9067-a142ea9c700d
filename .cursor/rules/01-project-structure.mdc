---
description: 
globs: 
alwaysApply: true
---
# 项目结构概览

本项目是一个基于Vue 2.7的SaaS前端Web应用，使用Vue CLI构建。

## 核心目录结构

- **src/**: 源代码目录
  - **api/**: 后端API接口定义，按功能模块划分
  - **assets/**: 静态资源（图片、字体等）
  - **components/**: 可复用组件，按功能和类型分类
  - **layout/**: 布局组件
  - **router/**: 路由配置，使用模块化管理
  - **store/**: 状态管理（Vuex和Pinia）
  - **styles/**: 全局样式文件
  - **utils/**: 工具函数库
  - **views/**: 页面视图组件
  - **i18n/**: 国际化配置
  - **icons/**: SVG图标
  - **plugins/**: 插件配置
  - **directive/**: 自定义指令
  - **mixins/**: 混入

## 核心文件

- **[src/main.js](mdc:src/main.js)**: 应用入口文件
- **[src/App.vue](mdc:src/App.vue)**: 根组件
- **[src/permission.js](mdc:src/permission.js)**: 权限控制配置
- **[src/settings.js](mdc:src/settings.js)**: 应用全局设置

## 构建配置

- **[vue.config.js](mdc:vue.config.js)**: Vue CLI配置文件
- **[babel.config.js](mdc:babel.config.js)**: Babel配置
- **[postcss.config.js](mdc:postcss.config.js)**: PostCSS配置
- **[.eslintrc.js](mdc:.eslintrc.js)**: ESLint配置
- **[.prettierrc.json](mdc:.prettierrc.json)**: Prettier配置

