/*!
 * maptalks.markercluster v0.8.3
 * LICENSE : MIT
 * (c) 2016-2019 maptalks.org
 */
/*!
 * requires maptalks@>=0.26.3 
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("maptalks")):"function"==typeof define&&define.amd?define(["exports","maptalks"],e):e(t.maptalks=t.maptalks||{},t.maptalks)}(this,function(t,m){"use strict";function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function e(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):function(t,e){for(var r=Object.getOwnPropertyNames(e),o=0;o<r.length;o++){var i=r[o],n=Object.getOwnPropertyDescriptor(e,i);n&&n.configurable&&void 0===t[i]&&Object.defineProperty(t,i,n)}}(t,e))}var r=function(i){function s(){return o(this,s),n(this,i.apply(this,arguments))}return e(s,i),s.fromJSON=function(t){if(!t||"ClusterLayer"!==t.type)return null;for(var e=new s(t.id,t.options),r=t.geometries,o=[],i=0;i<r.length;i++){var n=m.Geometry.fromJSON(r[i]);n&&o.push(n)}return e.addGeometry(o),e},s.prototype.addMarker=function(t){return this.addGeometry(t)},s.prototype.addGeometry=function(t){for(var e=0,r=t.length;e<=r;e++)if(!t[e]instanceof m.Marker)throw new Error("Only a point(Marker) can be added into a ClusterLayer");return i.prototype.addGeometry.apply(this,arguments)},s.prototype.onConfig=function(t){if(i.prototype.onConfig.call(this,t),t.maxClusterRadius||t.symbol||t.drawClusterText||t.textSymbol||t.maxClusterZoom){var e=this._getRenderer();e&&e.render()}return this},s.prototype.identify=function(t,e){var r=this.getMap(),o=this.options.maxClusterZoom;return o&&r&&r.getZoom()>o?i.prototype.identify.call(this,t,e):this._getRenderer()?this._getRenderer().identify(t,e):null},s.prototype.toJSON=function(){var t=i.prototype.toJSON.call(this);return t.type="ClusterLayer",t},s.prototype.getClusters=function(){var t=this._getRenderer();return t&&t._currentClusters||[]},s}(m.VectorLayer);r.mergeOptions({maxClusterRadius:160,textSumProperty:null,symbol:null,drawClusterText:!0,textSymbol:null,animation:!0,animationDuration:450,maxClusterZoom:null,noClusterWithOneMarker:!0,forceRenderOnZooming:!0}),r.registerJSONType("ClusterLayer");var i={textFaceName:'"microsoft yahei"',textSize:16,textDx:0,textDy:0},s={markerType:"ellipse",markerFill:{property:"count",type:"interval",stops:[[0,"rgb(135, 196, 240)"],[9,"#1bbc9b"],[99,"rgb(216, 115, 149)"]]},markerFillOpacity:.7,markerLineOpacity:1,markerLineWidth:3,markerLineColor:"#fff",markerWidth:{property:"count",type:"interval",stops:[[0,40],[9,60],[99,80]]},markerHeight:{property:"count",type:"interval",stops:[[0,40],[9,60],[99,80]]}};r.registerRenderer("canvas",function(u){function r(t){o(this,r);var e=n(this,u.call(this,t));return e._animated=!0,e._refreshStyle(),e._clusterNeedRedraw=!0,e}return e(r,u),r.prototype.checkResources=function(){var t=this.layer.options.symbol||s,e=u.prototype.checkResources.apply(this,arguments);if(t!==this._symbolResourceChecked){var r=m.Util.getExternalResources(t,!0);r&&e.push.apply(e,r),this._symbolResourceChecked=t}return e},r.prototype.draw=function(){this.canvas||this.prepareCanvas();var t=this.getMap().getZoom(),e=this.layer.options.maxClusterZoom;if(e&&e<t)return delete this._currentClusters,this._markersToDraw=this.layer._geoList,void u.prototype.draw.apply(this,arguments);this._clusterNeedRedraw&&(this._clearDataCache(),this._computeGrid(),this._clusterNeedRedraw=!1);var r=this._clusterCache[t]?this._clusterCache[t].clusters:null,o=this._getClustersToDraw(r);o.zoom=t,this._drawLayer(o)},r.prototype._getClustersToDraw=function(t){this._markersToDraw=[];var e=this.getMap(),r=m.StringUtil.getFont(this._textSymbol),o=m.StringUtil.stringLength("9",r).toPoint(),i=e.getContainerExtent(),n=[],s=void 0,a=void 0,l=void 0,p=void 0,u=void 0;for(var h in t)if(this._currentGrid=t[h],1===t[h].count&&this.layer.options.noClusterWithOneMarker){var c=t[h].children[0];c._cluster=t[h],this._markersToDraw.push(c)}else if(p=(l=this._getSprite()).canvas.width,u=l.canvas.height,s=e._prjToContainerPoint(t[h].center),a=new m.PointExtent(s.sub(p,u),s.add(p,u)),i.intersects(a)){if(!t[h].textSize){var y=this._getClusterText(t[h]);t[h].textSize=new m.Point(o.x*y.length,o.y)._multi(.5)}n.push(t[h])}return n},r.prototype.drawOnInteracting=function(){this._currentClusters&&this._drawClusters(this._currentClusters,1),u.prototype.drawOnInteracting.apply(this,arguments)},r.prototype.forEachGeo=function(e,r){this._markersToDraw&&this._markersToDraw.forEach(function(t){r?e.call(r,t):e(t)})},r.prototype.onGeometryShow=function(){this._clusterNeedRedraw=!0,u.prototype.onGeometryShow.apply(this,arguments)},r.prototype.onGeometryHide=function(){this._clusterNeedRedraw=!0,u.prototype.onGeometryHide.apply(this,arguments)},r.prototype.onGeometryAdd=function(){this._clusterNeedRedraw=!0,u.prototype.onGeometryAdd.apply(this,arguments)},r.prototype.onGeometryRemove=function(){this._clusterNeedRedraw=!0,u.prototype.onGeometryRemove.apply(this,arguments)},r.prototype.onGeometryPositionChange=function(){this._clusterNeedRedraw=!0,u.prototype.onGeometryPositionChange.apply(this,arguments)},r.prototype.onRemove=function(){this._clearDataCache()},r.prototype.identify=function(t,e){var r=this.getMap(),o=this.layer.options.maxClusterZoom;if(o&&r.getZoom()>o)return u.prototype.identify.call(this,t,e);if(this._currentClusters){for(var i=r.coordinateToContainerPoint(t),n=this._currentGrid,s=0;s<this._currentClusters.length;s++){var a=this._currentClusters[s],l=r._prjToContainerPoint(a.center);this._currentGrid=a;var p=this._getSprite().canvas.width;if(i.distanceTo(l)<=p)return{center:r.getProjection().unproject(a.center.copy()),children:a.children.slice(0)}}this._currentGrid=n}return this._markersToDraw?this.layer._hitGeos(this._markersToDraw,t,e):null},r.prototype.onSymbolChanged=function(){this._refreshStyle(),this._computeGrid(),this._stopAnim(),this.setToRedraw()},r.prototype._refreshStyle=function(){var t=this,e=this.layer.options.symbol||s,r=this.layer.options.textSymbol||i,o=function(){return[t.getMap().getZoom(),t._currentGrid]};this._symbol=m.MapboxUtil.loadFunctionTypes(e,o),this._textSymbol=m.MapboxUtil.loadFunctionTypes(r,o)},r.prototype._drawLayer=function(e){var r=this,o=this._currentClusters||e;this._currentClusters=e,delete this._clusterMaskExtent;var t=this.layer;if(t.options.animation&&this._animated&&this._inout){var i=[0,1];"in"===this._inout&&(i=[1,0]),this._player=m.animation.Animation.animate({d:i},{speed:t.options.animationDuration,easing:"inAndOut"},function(t){"finished"===t.state.playState?(r._animated=!1,r._drawClusters(e,1),r._drawMarkers(),r.completeRender()):("in"===r._inout?r._drawClustersFrame(e,o,t.styles.d):r._drawClustersFrame(o,e,t.styles.d),r.setCanvasUpdated())}).play()}else this._animated=!1,this._drawClusters(e,1),this._drawMarkers(),this.completeRender()},r.prototype._drawMarkers=function(){u.prototype.drawGeos.call(this,this._clusterMaskExtent)},r.prototype._drawClustersFrame=function(t,e,s){var a=this;this._clusterMaskExtent=this.prepareCanvas();var l=this.getMap(),r={};if(t&&t.forEach(function(t){var e=l._prjToContainerPoint(t.center);r[t.key]||(r[t.key]=1,a._drawCluster(e,t,1-s))}),0!==s&&e){var p=t.zoom,u=l._getResolution(p)*this.layer.options.maxClusterRadius,h=this._markerExtent.getMin();e.forEach(function(t){var e=l._prjToContainerPoint(t.center),r=t.center,o=Math.floor((r.x-h.x)/u)+"_"+Math.floor((r.y-h.y)/u),i=a._clusterCache[p].clusterMap[o];if(i){var n=l._prjToContainerPoint(i.center);e=n.add(e.sub(n)._multi(s))}a._drawCluster(e,t,.5<s?1:s)})}},r.prototype._drawClusters=function(t,r){var o=this;if(t){this._clusterMaskExtent=this.prepareCanvas();var i=this.getMap();t.forEach(function(t){var e=i._prjToContainerPoint(t.center);o._drawCluster(e,t,.5<r?1:r)})}},r.prototype._drawCluster=function(t,e,r){this._currentGrid=e;var o=this.context,i=this._getSprite(),n=o.globalAlpha;if(n*r!=0){if(o.globalAlpha=n*r,i){var s=t.add(i.offset)._sub(i.canvas.width/2,i.canvas.height/2);o.drawImage(i.canvas,s.x,s.y)}if(this.layer.options.drawClusterText&&e.textSize){m.Canvas.prepareCanvasFont(o,this._textSymbol),o.textBaseline="middle";var a=this._textSymbol.textDx||0,l=this._textSymbol.textDy||0,p=this._getClusterText(e);m.Canvas.fillText(o,p,t.sub(e.textSize.x,0)._add(a,l))}o.globalAlpha=n}},r.prototype._getClusterText=function(t){return(this.layer.options.textSumProperty?t.textSumProperty:t.count)+""},r.prototype._getSprite=function(){this._spriteCache||(this._spriteCache={});var t=m.Util.getSymbolStamp(this._symbol);return this._spriteCache[t]||(this._spriteCache[t]=new m.Marker([0,0],{symbol:this._symbol})._getSprite(this.resources,this.getMap().CanvasClass)),this._spriteCache[t]},r.prototype._initGridSystem=function(){var e=[],r=void 0,o=void 0;this.layer.forEach(function(t){t.isVisible()&&(o=t._getPrjCoordinates(),r=r?r._combine(t._getPrjExtent()):t._getPrjExtent(),e.push({x:o.x,y:o.y,id:t._getInternalId(),geometry:t}))}),this._markerExtent=r,this._markerPoints=e},r.prototype._computeGrid=function(){var t=this.getMap(),e=t.getZoom();this._markerExtent||this._initGridSystem(),this._clusterCache||(this._clusterCache={});var r=t._getResolution(t.getMinZoom())>t._getResolution(t.getMaxZoom())?e-1:e+1;this._clusterCache[r]&&this._clusterCache[r].length===this.layer.getCount()&&(this._clusterCache[e]=this._clusterCache[r]),this._clusterCache[e]||(this._clusterCache[e]=this._computeZoomGrid(e))},r.prototype._computeZoomGrid=function(t){if(!this._markerExtent)return null;var e=this.getMap(),r=e._getResolution(t)*this.layer.options.maxClusterRadius,o=e._getResolution(t-1)?e._getResolution(t-1)*this.layer.options.maxClusterRadius:null,i=this._clusterCache[t-1];!i&&t-1>=e.getMinZoom()&&(this._clusterCache[t-1]=i=this._computeZoomGrid(t-1));for(var n=this._markerPoints,s=this.layer.options.textSumProperty,a={},l=this._markerExtent.getMin(),p=void 0,u=void 0,h=0,c=n.length;h<c;h++){var y=n[h].geometry,d=0;s&&y.getProperties()&&y.getProperties()[s]&&(d=y.getProperties()[s]),a[p=Math.floor((n[h].x-l.x)/r)+"_"+Math.floor((n[h].y-l.y)/r)]?(a[p].sum._add(new m.Coordinate(n[h].x,n[h].y)),a[p].count++,a[p].center=a[p].sum.multi(1/a[p].count),a[p].children.push(y),a[p].textSumProperty+=d):(a[p]={sum:new m.Coordinate(n[h].x,n[h].y),center:new m.Coordinate(n[h].x,n[h].y),count:1,textSumProperty:d,children:[y],key:p+""},o&&i&&(u=Math.floor((n[h].x-l.x)/o)+"_"+Math.floor((n[h].y-l.y)/o),a[p].parent=i.clusterMap[u]))}return this._mergeClusters(a,r/2)},r.prototype._mergeClusters=function(t,e){var r={};for(var o in t)r[o]=t[o];var i={},n={},s=void 0,a=void 0;for(var l in t)if(!n[(s=t[l]).key])for(var p=s.key.split("_"),u=+p[0],h=+p[1],c=-1;c<=1;c++)for(var y=-1;y<=1;y++){if(0!==c||0!==y)(a=t[u+c+"_"+(h+y)])&&this._distanceTo(s.center,a.center)<=e&&(i[s.key]||(i[s.key]=[]),i[s.key].push(a),n[a.key]=1)}for(var d in i){var m=t[d];if(m){for(var _=i[d],f=0;f<_.length;f++)t[_[f].key]&&(m.sum._add(_[f].sum),m.count+=_[f].count,m.textSumProperty+=_[f].textSumProperty,m.children=m.children.concat(_[f].children),r[_[f].key]=m,delete t[_[f].key]);m.center=m.sum.multi(1/m.count)}}return{clusters:t,clusterMap:r}},r.prototype._distanceTo=function(t,e){var r=t.x-e.x,o=t.y-e.y;return Math.sqrt(r*r+o*o)},r.prototype._stopAnim=function(){this._player&&"finished"!==this._player.playState&&this._player.finish()},r.prototype.onZoomStart=function(t){this._stopAnim(),u.prototype.onZoomStart.call(this,t)},r.prototype.onZoomEnd=function(t){!this.layer.isEmpty()&&this.layer.isVisible()&&(this._inout=t.from>t.to?"in":"out",this._animated=!0,this._computeGrid()),u.prototype.onZoomEnd.apply(this,arguments)},r.prototype._clearDataCache=function(){this._stopAnim(),delete this._markerExtent,delete this._markerPoints,delete this._clusterCache,delete this._zoomInClusters},r}(m.renderer.VectorLayerCanvasRenderer)),t.ClusterLayer=r,Object.defineProperty(t,"__esModule",{value:!0}),"undefined"!=typeof console&&console.log("maptalks.markercluster v0.8.3, requires maptalks@>=0.26.3.")});