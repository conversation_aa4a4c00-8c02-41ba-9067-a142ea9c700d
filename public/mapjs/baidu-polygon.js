/* eslint-disable */
function CreateChinaMapLine() {
  var pts = [];
  try {
    pt1 = new BMap.Point(124.326919, 39.841287);
    pts.push(pt1);
    pt2 = new BMap.Point(124.630475, 40.230192);
    pts.push(pt2);
    pt3 = new BMap.Point(124.980023, 40.420289);
    pts.push(pt3);
    pt4 = new BMap.Point(125.053612, 40.458947);
    pts.push(pt4);
    pt5 = new BMap.Point(125.028316, 40.520401);
    pts.push(pt5);
    pt6 = new BMap.Point(125.331871, 40.63438);
    pts.push(pt6);
    pt7 = new BMap.Point(125.456053, 40.61861);
    pts.push(pt7);
    pt8 = new BMap.Point(126.042467, 40.901897);
    pts.push(pt8);
    pt9 = new BMap.Point(126.888743, 41.73411);
    pts.push(pt9);
    pt10 = new BMap.Point(127.367072, 41.437131);

    pts.push(pt10);
    pt11 = new BMap.Point(128.148958, 41.347076);
    pts.push(pt11);
    pt12 = new BMap.Point(128.323732, 41.568524);
    pts.push(pt12);
    pt13 = new BMap.Point(128.112163, 41.961089);
    pts.push(pt13);
    pt14 = new BMap.Point(128.995233, 41.99541);
    pts.push(pt14);
    pt15 = new BMap.Point(129.418371, 42.378521);
    pts.push(pt15);
    pt16 = new BMap.Point(129.841509, 42.41943);
    pts.push(pt16);
    pt17 = new BMap.Point(130.080674, 42.90824);
    pts.push(pt17);
    pt18 = new BMap.Point(130.65099, 42.364879);
    pts.push(pt18);
    pt19 = new BMap.Point(130.503812, 42.637157);
    pts.push(pt19);
    pt20 = new BMap.Point(131.147717, 42.867653);
    pts.push(pt20);

    pt21 = new BMap.Point(131.313293, 43.366337);
    pts.push(pt21);
    pt22 = new BMap.Point(131.386882, 43.967324);
    pts.push(pt22);
    pt23 = new BMap.Point(131.202909, 44.798523);
    pts.push(pt23);
    pt24 = new BMap.Point(131.938801, 45.190155);
    pts.push(pt24);
    pt25 = new BMap.Point(133.024241, 44.916296);
    pts.push(pt25);
    pt26 = new BMap.Point(134.385641, 47.183241);
    pts.push(pt26);
    pt27 = new BMap.Point(134.845574, 47.670159);
    pts.push(pt27);
    pt28 = new BMap.Point(134.73519, 48.066299);
    pts.push(pt28);
    pt29 = new BMap.Point(135.250314, 48.434915);
    pts.push(pt29);
    pt30 = new BMap.Point(133.079433, 48.115602);
    pts.push(pt30);

    pt31 = new BMap.Point(132.453925, 47.76948);
    pts.push(pt31);
    pt32 = new BMap.Point(131.221306, 47.707427);
    pts.push(pt32);
    pt33 = new BMap.Point(130.85336, 48.078629);
    pts.push(pt33);
    pt34 = new BMap.Point(130.908552, 48.825163);
    pts.push(pt34);
    pt35 = new BMap.Point(128.093766, 49.632427);
    pts.push(pt35);
    pt36 = new BMap.Point(125.830898, 53.063643);
    pts.push(pt36);
    pt37 = new BMap.Point(123.420852, 53.60326);
    pts.push(pt37);
    pt38 = new BMap.Point(120.661258, 53.240599);
    pts.push(pt38);
    pt39 = new BMap.Point(119.906969, 52.584436);
    pts.push(pt39);
    pt40 = new BMap.Point(120.698052, 52.494695);
    pts.push(pt40);

    pt41 = new BMap.Point(120.734847, 52.05456);
    pts.push(pt41);
    pt42 = new BMap.Point(119.023898, 50.296998);
    pts.push(pt42);
    pt43 = new BMap.Point(119.263063, 50.143545);
    pts.push(pt43);
    pt44 = new BMap.Point(117.956855, 49.560675);
    pts.push(pt44);
    pt45 = new BMap.Point(116.889812, 49.858941);
    pts.push(pt45);
    pt46 = new BMap.Point(115.47322, 48.078629);
    pts.push(pt46);
    pt47 = new BMap.Point(115.988344, 47.607987);
    pts.push(pt47);
    pt48 = new BMap.Point(116.797825, 47.76948);
    pts.push(pt48);
    pt49 = new BMap.Point(117.349744, 47.520821);
    pts.push(pt49);
    pt50 = new BMap.Point(117.901663, 47.905734);
    pts.push(pt50);

    pt51 = new BMap.Point(118.508774, 47.893363);
    pts.push(pt51);
    pt52 = new BMap.Point(118.545569, 47.86861);
    pts.push(pt52);
    pt53 = new BMap.Point(119.888571, 46.843488);
    pts.push(pt53);
    pt54 = new BMap.Point(119.796585, 46.590415);
    pts.push(pt54);
    pt55 = new BMap.Point(119.005501, 46.717101);
    pts.push(pt55);
    pt56 = new BMap.Point(117.791279, 46.526961);
    pts.push(pt56);
    pt57 = new BMap.Point(117.404936, 46.310656);
    pts.push(pt57);
    pt58 = new BMap.Point(116.797825, 46.323404);
    pts.push(pt58);
    pt59 = new BMap.Point(116.282701, 45.798295);
    pts.push(pt59);
    pt60 = new BMap.Point(114.700534, 45.384961);
    pts.push(pt60);

    pt61 = new BMap.Point(113.670285, 44.759212);
    pts.push(pt61);
    pt62 = new BMap.Point(112.161707, 45.04687);
    pts.push(pt62);
    pt63 = new BMap.Point(111.444212, 44.298587);
    pts.push(pt63);
    pt64 = new BMap.Point(111.959336, 43.767669);
    pts.push(pt64);
    pt65 = new BMap.Point(110.082812, 42.569199);
    pts.push(pt65);
    pt66 = new BMap.Point(107.543985, 42.39216);
    pts.push(pt66);
    pt67 = new BMap.Point(105.115542, 41.609961);
    pts.push(pt67);
    pt68 = new BMap.Point(100.405834, 42.596391);
    pts.push(pt68);
    pt69 = new BMap.Point(96.560799, 42.772848);
    pts.push(pt69);
    pt70 = new BMap.Point(91.188788, 45.203163);
    pts.push(pt70);

    pt71 = new BMap.Point(89.992964, 47.893363);
    pts.push(pt71);
    pt72 = new BMap.Point(87.969261, 49.115857);
    pts.push(pt72);
    pt73 = new BMap.Point(86.847026, 49.055436);
    pts.push(pt73);
    pt74 = new BMap.Point(85.724791, 48.201769);
    pts.push(pt74);
    pt75 = new BMap.Point(85.301653, 47.05766);
    pts.push(pt75);
    pt76 = new BMap.Point(83.167567, 47.23339);
    pts.push(pt76);
    pt77 = new BMap.Point(82.284497, 45.52739);
    pts.push(pt77);
    pt78 = new BMap.Point(82.652442, 45.177144);
    pts.push(pt78);
    pt79 = new BMap.Point(81.769372, 45.346053);
    pts.push(pt79);
    pt80 = new BMap.Point(79.911245, 44.890145);
    pts.push(pt80);

    pt81 = new BMap.Point(80.702329, 43.20505);
    pts.push(pt81);
    pt82 = new BMap.Point(80.003232, 42.077702);
    pts.push(pt82);
    pt83 = new BMap.Point(77.096459, 41.05527);
    pts.push(pt83);
    pt84 = new BMap.Point(76.158197, 40.369298);
    pts.push(pt84);
    pt85 = new BMap.Point(75.753456, 40.594072);
    pts.push(pt85);
    pt86 = new BMap.Point(74.925578, 40.48178);
    pts.push(pt86);
    pt87 = new BMap.Point(73.803343, 39.605094);
    pts.push(pt87);
    pt88 = new BMap.Point(73.637767, 39.291284);
    pts.push(pt88);
    pt89 = new BMap.Point(73.913726, 38.457149);
    pts.push(pt89);
    pt90 = new BMap.Point(74.612824, 38.500536);
    pts.push(pt90);

    pt91 = new BMap.Point(74.999167, 37.31988);
    pts.push(pt91);
    pt92 = new BMap.Point(77.924337, 35.325072);
    pts.push(pt92);
    pt93 = new BMap.Point(78.273886, 34.598061);
    pts.push(pt93);
    pt94 = new BMap.Point(79.101764, 34.293224);
    pts.push(pt94);
    pt95 = new BMap.Point(78.770613, 33.987267);
    pts.push(pt95);
    pt96 = new BMap.Point(79.230545, 32.472038);
    pts.push(pt96);
    pt97 = new BMap.Point(78.880997, 32.59672);
    pts.push(pt97);
    pt98 = new BMap.Point(78.421064, 32.425237);
    pts.push(pt98);
    pt99 = new BMap.Point(78.844202, 31.18404);
    pts.push(pt99);
    pt100 = new BMap.Point(81.180659, 29.990271);
    pts.push(pt100);

    pt101 = new BMap.Point(81.71418, 30.34198);
    pts.push(pt101);
    pt102 = new BMap.Point(86.092737, 27.935542);
    pts.push(pt102);
    pt103 = new BMap.Point(88.760345, 27.951881);
    pts.push(pt103);
    pt104 = new BMap.Point(88.99951, 27.197728);
    pts.push(pt104);
    pt105 = new BMap.Point(89.845786, 28.098817);
    pts.push(pt105);
    pt106 = new BMap.Point(91.538337, 27.772017);
    pts.push(pt106);
    pt107 = new BMap.Point(92.108653, 26.769165);
    pts.push(pt107);
    pt108 = new BMap.Point(93.893191, 26.851709);
    pts.push(pt108);
    pt109 = new BMap.Point(95.916893, 28.115131);
    pts.push(pt109);
    pt110 = new BMap.Point(97.223102, 27.690161);
    pts.push(pt110);

    pt111 = new BMap.Point(97.609445, 28.375809);
    pts.push(pt111);
    pt112 = new BMap.Point(98.363734, 27.427807);
    pts.push(pt112);
    pt113 = new BMap.Point(98.73168, 26.653501);
    pts.push(pt113);
    pt114 = new BMap.Point(97.646239, 24.702995);
    pts.push(pt114);
    pt115 = new BMap.Point(97.627842, 23.842845);
    pts.push(pt115);
    pt116 = new BMap.Point(98.658091, 23.944344);
    pts.push(pt116);
    pt117 = new BMap.Point(98.970845, 23.045009);
    pts.push(pt117);
    pt118 = new BMap.Point(99.449174, 22.89168);
    pts.push(pt118);
    pt119 = new BMap.Point(99.21001, 21.96805);
    pts.push(pt119);
    pt120 = new BMap.Point(99.982696, 21.933723);
    pts.push(pt120);

    pt121 = new BMap.Point(100.166669, 21.366121);
    pts.push(pt121);
    pt122 = new BMap.Point(101.086534, 21.589991);
    pts.push(pt122);
    pt123 = new BMap.Point(101.730439, 21.055576);
    pts.push(pt123);
    pt124 = new BMap.Point(101.969604, 21.31441);
    pts.push(pt124);
    pt125 = new BMap.Point(101.804028, 22.190972);
    pts.push(pt125);
    pt126 = new BMap.Point(102.53992, 22.53323);
    pts.push(pt126);
    pt127 = new BMap.Point(103.956512, 22.447745);
    pts.push(pt127);
    pt128 = new BMap.Point(105.465091, 23.130116);
    pts.push(pt128);
    pt129 = new BMap.Point(107.819945, 21.435041);
    pts.push(pt129);
    pt130 = new BMap.Point(108.408658, 20.588528);
    pts.push(pt130);

    pt131 = new BMap.Point(108.243082, 17.791978); //海南岛
    pts.push(pt131);
    pt132 = new BMap.Point(110.101209, 14.556493); //海南岛-正北
    pts.push(pt132);
    pt133 = new BMap.Point(110.897395, 18.719791); //海南岛-东北
    pts.push(pt133);
    pt134 = new BMap.Point(111.797497, 20.469913); //海南岛-琼州海峡
    pts.push(pt134);
    pt135 = new BMap.Point(113.68667, 22.14331); //澳门
    pts.push(pt135);
    pt136 = new BMap.Point(113.81254, 22.382193); //零丁洋
    pts.push(pt136);
    pt137 = new BMap.Point(113.987961, 22.510662); //深圳湾
    pts.push(pt137);
    pt138 = new BMap.Point(114.240205, 22.542043); //中英街
    pts.push(pt138);
    pt139 = new BMap.Point(115.480586, 22.454159); //汕尾海
    pts.push(pt139);
    pt140 = new BMap.Point(117.683662, 23.032239); //汕头海
    pts.push(pt140);

    pt141 = new BMap.Point(118.565951, 23.267375); //台湾边界2
    pts.push(pt141);
    pt142 = new BMap.Point(120.47927, 25.658451); //台湾边界3
    pts.push(pt142);
    pt143 = new BMap.Point(124.230333, 28.457139); //台州海域
    pts.push(pt143);
    pt144 = new BMap.Point(124.855841, 32.721228);
    pts.push(pt144);
    pt145 = new BMap.Point(124.230333, 36.56674);
    pts.push(pt145);
    pt146 = new BMap.Point(124.487895, 39.59086);
    pts.push(pt146);
  } catch (error) {
    console.log(error);
  }

  return pts;
}

//此js用于辅助百度地图操作，主要判断坐标位置是否在某个圆、矩形之内还是之外

var BMapLib = (window.BMapLib = BMapLib || {});
(function () {
  var a = 6370996.81;
  var b = (BMapLib.GeoUtils = function () {});
  b.isPointInRect = function (f, g) {
    if (!(f instanceof BMap.Point) || !(g instanceof BMap.Bounds)) {
      return false;
    }
    var e = g.getSouthWest();
    var h = g.getNorthEast();
    return f.lng >= e.lng && f.lng <= h.lng && f.lat >= e.lat && f.lat <= h.lat;
  };
  b.isPointInCircle = function (e, h) {
    if (!(e instanceof BMap.Point) || !(h instanceof BMap.Circle)) {
      return false;
    }
    var i = h.getCenter();
    var g = h.getRadius();
    var f = b.getDistance(e, i);
    if (f <= g) {
      return true;
    } else {
      return false;
    }
  };
  b.isPointOnPolyline = function (f, h) {
    if (!(f instanceof BMap.Point) || !(h instanceof BMap.Polyline)) {
      return false;
    }
    var e = h.getBounds();
    if (!this.isPointInRect(f, e)) {
      return false;
    }
    var m = h.getPath();
    for (var k = 0; k < m.length - 1; k++) {
      var l = m[k];
      var j = m[k + 1];
      if (
        f.lng >= Math.min(l.lng, j.lng) &&
        f.lng <= Math.max(l.lng, j.lng) &&
        f.lat >= Math.min(l.lat, j.lat) &&
        f.lat <= Math.max(l.lat, j.lat)
      ) {
        var g =
          (l.lng - f.lng) * (j.lat - f.lat) - (j.lng - f.lng) * (l.lat - f.lat);
        if (g < 2e-10 && g > -2e-10) {
          return true;
        }
      }
    }
    return false;
  };
  b.isPointInPolygon = function (o, l) {
    if (!(o instanceof BMap.Point) || !(l instanceof BMap.Polygon)) {
      return false;
    }
    var k = l.getBounds();
    if (!this.isPointInRect(o, k)) {
      return false;
    }
    var t = l.getPath();
    var h = t.length;
    var n = true;
    var j = 0;
    var g = 2e-10;
    var s, q;
    var e = o;
    s = t[0];
    for (var f = 1; f <= h; ++f) {
      if (e.equals(s)) {
        return n;
      }
      q = t[f % h];
      if (e.lat < Math.min(s.lat, q.lat) || e.lat > Math.max(s.lat, q.lat)) {
        s = q;
        continue;
      }
      if (e.lat > Math.min(s.lat, q.lat) && e.lat < Math.max(s.lat, q.lat)) {
        if (e.lng <= Math.max(s.lng, q.lng)) {
          if (s.lat == q.lat && e.lng >= Math.min(s.lng, q.lng)) {
            return n;
          }
          if (s.lng == q.lng) {
            if (s.lng == e.lng) {
              return n;
            } else {
              ++j;
            }
          } else {
            var r =
              ((e.lat - s.lat) * (q.lng - s.lng)) / (q.lat - s.lat) + s.lng;
            if (Math.abs(e.lng - r) < g) {
              return n;
            }
            if (e.lng < r) {
              ++j;
            }
          }
        }
      } else {
        if (e.lat == q.lat && e.lng <= q.lng) {
          var m = t[(f + 1) % h];
          if (
            e.lat >= Math.min(s.lat, m.lat) &&
            e.lat <= Math.max(s.lat, m.lat)
          ) {
            ++j;
          } else {
            j += 2;
          }
        }
      }
      s = q;
    }
    if (j % 2 == 0) {
      return false;
    } else {
      return true;
    }
  };
  b.degreeToRad = function (e) {
    return (Math.PI * e) / 180;
  };
  b.radToDegree = function (e) {
    return (180 * e) / Math.PI;
  };
  function d(g, f, e) {
    if (f != null) {
      g = Math.max(g, f);
    }
    if (e != null) {
      g = Math.min(g, e);
    }
    return g;
  }
  function c(g, f, e) {
    while (g > e) {
      g -= e - f;
    }
    while (g < f) {
      g += e - f;
    }
    return g;
  }
  b.getDistance = function (j, h) {
    if (!(j instanceof BMap.Point) || !(h instanceof BMap.Point)) {
      return 0;
    }
    j.lng = c(j.lng, -180, 180);
    j.lat = d(j.lat, -74, 74);
    h.lng = c(h.lng, -180, 180);
    h.lat = d(h.lat, -74, 74);
    var f, e, i, g;
    f = b.degreeToRad(j.lng);
    i = b.degreeToRad(j.lat);
    e = b.degreeToRad(h.lng);
    g = b.degreeToRad(h.lat);
    return (
      a *
      Math.acos(
        Math.sin(i) * Math.sin(g) + Math.cos(i) * Math.cos(g) * Math.cos(e - f)
      )
    );
  };
  b.getPolylineDistance = function (f) {
    if (f instanceof BMap.Polyline || f instanceof Array) {
      var l;
      if (f instanceof BMap.Polyline) {
        l = f.getPath();
      } else {
        l = f;
      }
      if (l.length < 2) {
        return 0;
      }
      var j = 0;
      for (var h = 0; h < l.length - 1; h++) {
        var k = l[h];
        var g = l[h + 1];
        var e = b.getDistance(k, g);
        j += e;
      }
      return j;
    } else {
      return 0;
    }
  };
  b.getPolygonArea = function (t) {
    if (!(t instanceof BMap.Polygon) && !(t instanceof Array)) {
      return 0;
    }
    var R;
    if (t instanceof BMap.Polygon) {
      R = t.getPath();
    } else {
      R = t;
    }
    if (R.length < 3) {
      return 0;
    }
    var w = 0;
    var D = 0;
    var C = 0;
    var L = 0;
    var J = 0;
    var F = 0;
    var E = 0;
    var S = 0;
    var H = 0;
    var p = 0;
    var T = 0;
    var I = 0;
    var q = 0;
    var e = 0;
    var M = 0;
    var v = 0;
    var K = 0;
    var N = 0;
    var s = 0;
    var O = 0;
    var l = 0;
    var g = 0;
    var z = 0;
    var Q = 0;
    var G = 0;
    var j = 0;
    var A = 0;
    var o = 0;
    var m = 0;
    var y = 0;
    var x = 0;
    var h = 0;
    var k = 0;
    var f = 0;
    var n = a;
    var B = R.length;
    for (var P = 0; P < B; P++) {
      if (P == 0) {
        D = (R[B - 1].lng * Math.PI) / 180;
        C = (R[B - 1].lat * Math.PI) / 180;
        L = (R[0].lng * Math.PI) / 180;
        J = (R[0].lat * Math.PI) / 180;
        F = (R[1].lng * Math.PI) / 180;
        E = (R[1].lat * Math.PI) / 180;
      } else {
        if (P == B - 1) {
          D = (R[B - 2].lng * Math.PI) / 180;
          C = (R[B - 2].lat * Math.PI) / 180;
          L = (R[B - 1].lng * Math.PI) / 180;
          J = (R[B - 1].lat * Math.PI) / 180;
          F = (R[0].lng * Math.PI) / 180;
          E = (R[0].lat * Math.PI) / 180;
        } else {
          D = (R[P - 1].lng * Math.PI) / 180;
          C = (R[P - 1].lat * Math.PI) / 180;
          L = (R[P].lng * Math.PI) / 180;
          J = (R[P].lat * Math.PI) / 180;
          F = (R[P + 1].lng * Math.PI) / 180;
          E = (R[P + 1].lat * Math.PI) / 180;
        }
      }
      S = Math.cos(J) * Math.cos(L);
      H = Math.cos(J) * Math.sin(L);
      p = Math.sin(J);
      T = Math.cos(C) * Math.cos(D);
      I = Math.cos(C) * Math.sin(D);
      q = Math.sin(C);
      e = Math.cos(E) * Math.cos(F);
      M = Math.cos(E) * Math.sin(F);
      v = Math.sin(E);
      K = (S * S + H * H + p * p) / (S * T + H * I + p * q);
      N = (S * S + H * H + p * p) / (S * e + H * M + p * v);
      s = K * T - S;
      O = K * I - H;
      l = K * q - p;
      g = N * e - S;
      z = N * M - H;
      Q = N * v - p;
      m =
        (g * s + z * O + Q * l) /
        (Math.sqrt(g * g + z * z + Q * Q) * Math.sqrt(s * s + O * O + l * l));
      m = Math.acos(m);
      G = z * l - Q * O;
      j = 0 - (g * l - Q * s);
      A = g * O - z * s;
      if (S != 0) {
        o = G / S;
      } else {
        if (H != 0) {
          o = j / H;
        } else {
          o = A / p;
        }
      }
      if (o > 0) {
        y += m;
        k++;
      } else {
        x += m;
        h++;
      }
    }
    var u, r;
    u = y + (2 * Math.PI * h - x);
    r = 2 * Math.PI * k - y + x;
    if (y > x) {
      if (u - (B - 2) * Math.PI < 1) {
        f = u;
      } else {
        f = r;
      }
    } else {
      if (r - (B - 2) * Math.PI < 1) {
        f = r;
      } else {
        f = u;
      }
    }
    w = (f - (B - 2) * Math.PI) * n * n;
    return w;
  };
})();
//判断点在多边形内还是外
function ptInPolygon(lng, lat) {
  try {
    var pts = CreateChinaMapLine();
    var ply = new BMap.Polygon(pts);

    var pt = new BMap.Point(lng, lat);

    var result = BMapLib.GeoUtils.isPointInPolygon(pt, ply);

    if (result == true) {
      return "cn";
    } else {
      return "not cn";
    }
  } catch (error) {
    console.log(error)
    return 'not cn'
  }
}
