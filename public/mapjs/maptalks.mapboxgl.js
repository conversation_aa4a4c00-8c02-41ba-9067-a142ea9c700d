/* eslint-disable */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("maptalks"),require("mapbox-gl")):"function"==typeof define&&define.amd?define(["exports","maptalks","mapbox-gl"],e):e(t.maptalks=t.maptalks||{},t.maptalks,t.mapboxgl)}(this,function(t,o,r){"use strict";function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):function(t,e){for(var n=Object.getOwnPropertyNames(e),i=0;i<n.length;i++){var o=n[i],r=Object.getOwnPropertyDescriptor(e,o);r&&r.configurable&&void 0===t[o]&&Object.defineProperty(t,o,r)}}(t,e))}r=r&&r.hasOwnProperty("default")?r.default:r;var i,a=(n(s,i=o.Layer),s.fromJSON=function(t){return t&&"MapboxglLayer"===t.type?new s(t.id,t.options):null},s.prototype.getGlMap=function(){var t=this._getRenderer();return t?t.glmap:null},s.prototype.toJSON=function(){return{type:this.getJSONType(),id:this.getId(),options:this.config()}},s);function s(){return e(this,s),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,i.apply(this,arguments))}function p(t){e(this,p),this.layer=t}a.mergeOptions({renderer:"dom",container:"back",glOptions:{style:"mapbox://styles/mapbox/streets-v9"}}),a.registerJSONType("MapboxglLayer"),a.registerRenderer("dom",(p.prototype.getMap=function(){return this.layer?this.layer.getMap():null},p.prototype.show=function(){this._container&&(this.render(),this._show())},p.prototype.hide=function(){this._container&&(this._hide(),this.clear())},p.prototype.remove=function(){delete this.layer,this.glmap&&this.glmap.remove(),this._container&&o.DomUtil.removeDomNode(this._container),delete this._container,delete this.glmap},p.prototype.clear=function(){this._container&&(this._container.innerHTML="")},p.prototype.setZIndex=function(t){this._zIndex=t,this._container&&(this._container.style.zIndex=t)},p.prototype.needToRedraw=function(){var t=this.getMap(),e=t._getRenderer();return t.isInteracting()||e&&e.isViewChanged()},p.prototype.render=function(){var t=this;if(this._container||this._createLayerContainer(),!this.glmap){var e=this.getMap(),n=e.getCenter(),i=o.Util.extend({},this.layer.options.glOptions,{container:this._container,center:new r.LngLat(n.x,n.y),zoom:c(e.getResolution())});this.glmap=new r.Map(i),this.glmap.on("load",function(){t.layer.fire("layerload")})}this._syncMap()},p.prototype.drawOnInteracting=function(){var t=this.getMap();this.glmap&&t&&this._syncMap()},p.prototype.getEvents=function(){return{resize:this.onResize}},p.prototype.onResize=function(){this._resize()},p.prototype._createLayerContainer=function(){var t=this._container=o.DomUtil.createEl("div","maptalks-mapboxgllayer");t.style.cssText="position:absolute;",this._resize(),this._zIndex&&(t.style.zIndex=this._zIndex),("front"===this.layer.options.container?this.getMap()._panels.frontStatic:this.getMap()._panels.backStatic).appendChild(t)},p.prototype._resize=function(){var t=this._container;if(t){var e=this.getMap().getSize();t.style.width=e.width+"px",t.style.height=e.height+"px",this.glmap&&this.glmap.resize()}},p.prototype._show=function(){this._container.style.display=""},p.prototype._hide=function(){this._container.style.display="none"},p.prototype._syncMap=function(){var t=this.getMap();if(this.glmap&&t){var e=t.getCenter(),n={center:new r.LngLat(e.x,e.y),zoom:c(t.getResolution()),bearing:t.getBearing(),pitch:t.getPitch()};this.glmap.jumpTo(n)}},p));var l=12756274*Math.PI/(256*Math.pow(2,20));function c(t){return 19-Math.log(t/l)/Math.LN2}t.MapboxglLayer=a,Object.defineProperty(t,"__esModule",{value:!0})});