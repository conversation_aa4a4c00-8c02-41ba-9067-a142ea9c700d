var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
var lg = {
  //意大利语
  //common
  user_guide: 'Guida utente',
  remoteSwitch: "Commutatore remoto",
  pageTitle: "WhatsGPS",
  description:
    "Posizionamento immediatamente ("+site+") per l'ultima scienza e la tecnologia-based, basandosi su dati di grandi dimensioni distribuiti di elaborazione, nuvola intelligente per fornire agli utenti servizi basati sulla posizione, è un fornitore leader globale di piattaforma di servizi di localizzazione.",
  pageLang: "Italiano",
  inputCountTips: "Si prega di inserire un account / IMEI",
  inputPasswordTips: "Per favore inserisci la tua password",
  appDownload: "Download del client",
  siteName: "Posizione ora",
  rememberPassword: "Ricorda la password",
  forgetPassword: 'Ha dimenticato la password',
  noToken: "Si prega di passare il token",
  loginFirst: "Effettua prima il login",
  move: "Sportivo",
  stop: "Statica",
  query: "Inchiesta",
  imeiQuery: "IMEI",
  delete: "Cancellare",
  update: "Modifica",
  cancel: "Annullato",
  soft: "Numero di serie",
  more: "Più",
  useful:'utile',
  useless:'inutile',
  replyFeedback:'Feedback su "$"',
  edit: "Modifica",
  add: "Aggiunta",
  addTo: "Aggiungere",
  addDevice: "Aggiungi dispositivo",
  machineName: "Nome del dispositivo",
  searchDevice: "Cerca attrezzature",
  date: "Data",
  LatestUpdate: "Segnale",
  engine: "ACC",
  locTime: "Tempo di posizionamento",
  locType: "Tipo di targeting",
  startLoc: "Posizione di partenza",
  endLoc: "Posizione finale",
  address: "Indirizzo",
  noAddressTips: "Impossibile ottenere informazioni sull'indirizzo",
  lonlat: "Latitudine e longitudine",
  carNO: "Numero di targa",
  imei: "IMEI",
  IMEI: "IMEI",
  simNO: "Numero della carta SIM",
  activeTime: "Tempo di attivazione",
  expireTime: "Scadenza",
  acceptSubordinateAlarm: "Accetta l'allarme subordinato",
  acceptAlarmTips1: "Dopo il check",
  acceptAlarmTips2:
    "Riceverai le informazioni di allarme del dispositivo da tutti i clienti subordinati",
  speed: "Velocità",
  y: "Y",
  M: "M",
  d: "D",
  h: "H",
  min: "MIN",
  s: "S",
  _year: "Y",
  _month: "M",
  _day: "D",
  _hour: "H",
  _minute: "M",
  _second: "S",
  confirm: "determinare",
  yes: "si tratta di",
  car: "veicolo",
  not: "no",
  m: "metro",
  account: "Numero di conto",
  psw: "password",
  save: "Salva",
  operator: "operativo",
  queryNoData: "Nessun dato è stato interrogato",
  name: "nome",
  type: "modello",
  open: "aperto",
  close: "vicino",
  send: "inviare",
  alarm: "allarme",
  alarmSetting: "Impostazione di allarme",
  look: "vista",
  tailAfter: "pista",
  history: "riproduzione",
  dir: "corso",
  locStatus: "Stato di posizionamento",
  machineTypeText: "modello",
  carUser: "Proprietari",
  machine: "attrezzatura",
  unknowMachineType: "Modello sconosciuto",
  noCommandRecord: "Il dispositivo non ha istruzioni",
  type1: "tipo",
  role: "tipo",
  roles: "tipo",
  timeType: "Tipo di tempo",
  moveSpeed: "Velocità di marcia",
  signal: "segnale",
  loc: "localizzare",
  wiretype: "tipo",
  wire: "Wired",
  wireless: "senza fili",
  expire: "maturità",
  hour: "ora",
  hourTo: "Ora a",
  remark: "osservazione",
  remarkInfo: "Osservazioni Informazioni",
  noPriviledges: "Questo account non ha diritti di operazione",
  commandNoOpen:
    "La funzione di comando del dispositivo corrente non è stata ancora utilizzata.",
  choseDelelePhone:
    "Si prega di selezionare il numero che si desidera eliminare per primo.",
  streetView: "Vista strada",
  wrongFormat: "Errore di formato di input",
  inputFiexd: "Si prega di inserire un numero fisso",
  serialNumberStart:
    "Inserisci il numero iniziale del numero di serie che desideri connettere",
  serialNumberEnd:
    "Si prega di inserire il numero finale per essere consecutivi",
  clickSearchFirst: "Clicca prima sul numero di dispositivo di ricerca!",
  isDeleteDevice:
    "Non può essere ripristinato dopo che il dispositivo è stato eliminato. È stato eliminato?",
  //Suggerimento per il codice di errore della piattaforma
  errorTips: "L'operazione non è riuscita con il codice di errore",
  error10003: 'Password errata',
  error90010: "Il dispositivo è offline e l'invio del comando personalizzato non è riuscito!",
  error70003: 'Il valore del telecomando non può essere vuoto',
  error70006: 'Non supporta o non ha il diritto di emettere le istruzioni',
  error20001: "L'ID veicolo non può essere vuoto",
  error20012: 'Il veicolo non è attivato',
  error10012: "Vecchio errore della password",
  error10017: "Cancellare fallito, per favore cancella prima il sub-utente!",
  error10023: "Elimina non riuscito, l'utente ha un dispositivo",
  error20008: "Aggiungi fallito, il numero IMEI esiste già",
  error20006: "Inserisci il numero del dispositivo a 15 cifre",
  error10019: "Errore di formato del telefono",
  error10024: "Non ripetere le vendite",
  error120003: "Il link di condivisione è scaduto",
  error10025:
    "Le informazioni sul dispositivo modificate non possono essere vuote",
  error2010: "Si prega di caricare un file",
  error20002: "Il numero IMEI non esiste",
  error10081: "Numero insufficiente di carte di rinnovo",
  error10082: 'Non è necessario ricaricare il dispositivo per tutta la vita',
  error3000: "Il ruolo è stato assegnato all'account di sistema e non può essere eliminato",
  error103: "L'account è stato disabilitato, contatta il tuo fornitore di servizi",
  error124: 'Non può operare su se stesso',
  // Atterraggio login.js
  logining: "Accedi ...",
  login: "Entra",
  userEmpty: "Il nome utente non può essere vuoto",
  pswEmpty: "La password non può essere vuota",
  prompt: "Consigli",
  accountOrPswError: "Account o password errati",
  UserNameAlreadyExist: "L'account di accesso esiste già",
  noQualified: "Nessuna informazione ammissibile",
  //main.js
  systemName: "Posizionare immediatamente il sistema di monitoraggio ",
  navTitle_user: ["Monitore", "Rapporti", "Dispositivo"],
  navTitle_dealer: [
    "Il mio account",
    "Il mio cliente ",
    "Monitore",
    "Ancora",
    "Più operazioni",
  ],
  exitStytem: "smettere",
  user: "utente",
  UserCenter: "utente",
  alarmInfo: "allarme",
  confirmExit: "Sei sicuro di uscire dal sistema?",
  errorMsg: "Motivo dell'errore: ",
  logintimeout: "Accesso scaduto, accedi nuovamente!",
  clearAlarm: "rimuovere",
  clear: "chiaro",
  searchbtn: "Cerca utente",
  print: "stampa",
  export: "esportazione",
  // Sezione di feedback
  feedback: "feedback",
  feedback_sublime: "presentare",
  alerttitle: "Il titolo non può essere vuoto!",
  alertcontent: "Il feedback non può essere vuoto!",
  submitfail: "Invio non riuscito!",
  saveSuccess: "Salvato con successo!",
  submitsuccess:
    "Presentato con successo! Elaboreremo il tuo feedback il prima possibile ~",
  adviceTitle: "titolo",
  adviceTitle_p: "Domanda e opinione",
  adviceContent: "Domande e opinioni",
  adviceContent_p:
    "Descrivi brevemente le domande e i commenti che desideri dare un feedback e continueremo a migliorare per te.",
  contact: "Informazioni di contatto",
  contact_p: "Compila il tuo telefono o e-mail",
  //monitor.js

  myMachine: "attrezzatura",
  all: "completo",
  online: "online",
  offline: "offline",
  unUse: "non usato",
  group: "pacchetto",
  moveGruop: "Sposta a",
  arrearage: "mora",
  noStatus: "apolide",
  inputMachineName: "Dispositivo di input",
  defaultGroup: "Difetto",
  offlineLessOneDay: "Offline <1 giorno",
  demoUserForbid:
    "L'esperienza degli utenti non può utilizzare questa funzione",
  shareTrack: "quota",
  shareName: "Condividi nome",
  liveShare: "Condivisione della traccia in tempo reale",
  expiration: "Tempo effettivo",
  getShareLink: "Genera link di condivisione",
  copy: "copia",
  copySuccess: "Copia riuscita!",
  enlarge: "Ingrandisci",
  shareExpired: "Il link di condivisione è scaduto",
  LinkFailure: "Condividi collegamento aperto fallito",
  inputShareName: "Si prega di inserire un nome di condivisione",
  inputValid: "Si prega di inserire l'ora valida corretta",
  //statistics.js
  runOverview: "Panoramica delle operazioni",
  runSta: "Statistiche operative",
  mileageSta: "Statistiche di distanza in miglia",
  tripSta: "Statistiche di viaggio",
  overSpeedDetail: "Lista di eccesso di velocità",
  stopDetail: "Lista permanente",
  alarmSta: "Statistiche di allarme",
  alarmOverview: "Panoramica degli allarmi",
  alarmDetail: "Lista degli allarmi",
  shortcutQuery: "Query veloce",
  today: "oggi",
  yesterday: "ieri",
  lastWeek: "La scorsa settimana",
  thisWeek: "Questa settimana",
  thisMonth: "Questo mese",
  lastMonth: "Il mese scorso",
  mileageNum: "Chilometraggio (km)",
  overSpeedNum: "Eccesso di velocità (km / h)",
  overSpeed: "Overspeed",
  stopTimes: "Stay (volte)",
  searchMachine: "attrezzatura",
  speedNum: "Velocità (km / h)",
  querying: "essere interrogato",
  stopTime: "Tempo di permanenza",
  HisToryStopTime: "rimanere",
  clickLookLoc: "Clicca per vedere l'indirizzo",
  lookLoc: "Visualizza posizione",
  noData: "Nessun dato",
  alarmTime: "Ora di sveglia",
  vibrationLevel: "Livello di vibrazione",
  vibrationWay: "Modalità di allarme",
  acc: "ACC",
  accStatistics: "Statistiche ACC",
  accType: ["Tutti gli stati, ACC fire, ACC flameout"],
  accstatus: ["Apri, chiudi"],
  openAccQuery: "Query ACC",
  runtime: "Tempo di esecuzione",
  //Monitoraggio della modifica della pagina
  run: "Viaggio",
  speed: "Velocità",
  //Dispositivo
  machineManage: "Dispositivo",
  deviceTable: "Il mio obiettivo",
  status: "Stato",
  havaExpired: "Scaduto",
  expiredIn60: "Scade tra 60 giorni",
  expiredIn7: "Scade tra 7 giorni",
  normal: "Normale",
  allMachine: "Tutta l'attrezzatura",
  allMachine1: "Tutta l'attrezzatura",
  expiredIn7Machine: "Scade tra 7 giorni",
  expiredIn60Machine: "Scade tra 60 giorni",
  havaExpiredMachine: "Dispositivo scaduto",

  //history.js
  replay: "Broadcast",
  replaytitle: "Riproduzione",
  choseDate: "Scegli il tempo",
  from: "Da",
  to: "A",
  startTime: "Ora di inizio",
  endTime: "Ora di fine",
  pause: "Pausa",
  slow: "Lento",
  mid: "In",
  fast: "Veloce",
  startTimeMsg: "Non hai scelto un'ora di inizio",
  endTimeMsg: "Non hai scelto un'ora di fine",
  smallEnd:
    "L'ora di fine che hai inserito è inferiore all'orario di inizio, seleziona nuovamente!",
  bigInterval: "L'intervallo di tempo inserito non può superare 31 giorni!",
  trackisempty: "La traiettoria è vuota durante questo periodo di tempo,",
  longitude: "Longitudine",
  latitude: "Latitudine",
  direction: "Direzione",
  stopMark: "Rimanere segno",
  setStopTimes: [
    {
      text: "1 minuto",
      value: "1",
    },
    {
      text: "2 minuti",
      value: "2",
    },
    {
      text: "3 minuti",
      value: "3",
    },
    {
      text: "5 minuti",
      value: "5",
    },
    {
      text: "10 minuti",
      value: "10",
    },
    {
      text: "15 minuti",
      value: "15",
    },
    {
      text: "20 minuti",
      value: "20",
    },
    {
      text: "30 minuti",
      value: "30",
    },
    {
      text: "45 minuti",
      value: "45",
    },
    {
      text: "1 ora",
      value: "60",
    },
    {
      text: "6 ore",
      value: "360",
    },
    {
      text: "12 ore",
      value: "720",
    },
  ],
  filterDrift: "Deriva del filtro",
  userType: [
    "Amministratore",
    "rivenditore",
    "utente",
    "logistica",
    "noleggio",
    "utente del veicolo",
    "controllo dei rischi",
    "professionale",
  ],
  userTypeArr: [
    "Amministratore, rivenditore, utente, logistica, leasing, utente del veicolo, controllo del rischio , professionale",
  ],
  machineType: {
    '0':'Tipo sconosciuto',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95':'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: [
    "allarme ignoto ",
    " allarme a vibrazione ",
    " allarme off ",
    " allarme batteria bassa ",
    " aiutando SOS ",
    " allarme di velocità ",
    " un allarme recinto ",
    " allarme spostamento ",
    " allarme batteria bassa straniera",
    "la polizia regionale ",
    " allarme smontare ",
    "l'allarme sensibile alla luce ",
    " allarme induzione magnetica ",
    " allarme manomissione ",
    " allarme Bluetooth ",
    "il segnale per mascherare l'allarme ",
    " pseudo allarme stazioni di base ",
    " nominato allarme bar ",
    "allarme nominato bar",
    "un allarme di recinto ",
    " allarme porta aperta ",
    " stanchezza del guidatore ",
    " in due punti carica ",
    " i due punti carica ",
    " due punti di ricarica soggiorno ",
    " offline terminale ",
    " allarme nominato bar ",
    " un allarme di recinto ",
    " allarme bar nominato ",
    " un allarme di recinto ",
    " allarme olio ",
    "ACC ON",
    "ACC OFF",
    "Allarme di collisione",
  ],
  alarmTypeNew:  {
    '40': "Allarme di alta temperatura",
    '45': "Allarme di bassa temperatura",
    '50': "Allarme di sovratensione",
    '55': "Allarme di sottotensione",
    '60': 'Allarme parcheggio'
  },
  alarmNotificationType: [
    { type: "Allarme vibrazione", value: 1 },
    { type: "Allarme di interruzione di corrente", value: 2 },
    { type: "Allarme batteria scarica", value: 3 },
    { type: "SOS per aiuto", value: 4 },
    { type: "Allarme di velocità", value: 5 },
    // {type:'Allarme recinzione',value:6},
    { type: "Allarme di spostamento", value: 7 },
    { type: "allarme batteria bassa straniera", value: 8 },
    { type: "Allarme fuori area", value: 9 },
    { type: "Smontare l'allarme", value: 10 },
    { type: "Allarme luminoso", value: 11 },

    { type: "Allarme di manomissione", value: 13 },

    { type: "Allarme schermatura del segnale", value: 15 },
    { type: "Allarme stazione base pseudo", value: 16 },
    // {type:'Allarme recinzione entrata (decisione piattaforma)',value:17},
    // {type:'Allarme recinzione entrata (decisione terminale)',value:18},
    // {type:'Allarme recinzione',value:19},

    { type: "Fatica alla guida", value: 21 },
    { type: "Inserisci due punti", value: 22 },
    { type: "Due punti", value: 23 },
    { type: "Due punti lunghi", value: 24 },
    { type: "Terminale offline", value: 25 },
    // {type:'Allarme recinzione entrata (controllo meteo)',value:26},
    // {type:'Allarme recinzione (controllo meteo)',value:27}
    { type: "Allarme recinto di entrata", value: 26 },
    { type: "Allarme recinzione", value: 27 },
    { type: " allarme olio ", value: 30 },
    { type: "ACC ON", value: 31 },
    { type: "ACC OFF", value: 32 },
    { type: "Allarme di collisione", value: 33 },
  ],
  alarmTypeText: "Tipo di allarme",
  alarmNotification: "Spingere le impostazioni",
  pointType: [
    "Non mirati ",
    " GPS ",
    " Beidou ",
    " stazione base posizione ",
    " il posizionamento WIFI ",
  ],

  cardType: [
    "tipo sconosciuto ",
    "punto di anno d'importazione",
    " il punto di importazione per tutta la vita ",
    " nella scheda ",
    " carta per tutta la vita ",
  ],
  // Sud-est e nord-ovest
  directarray: ["Est", "Sud", "Ovest", "Nord"],
  // Campo di direzione
  directionarray: [
    "Nord",
    "nord-est",
    "est",
    "sud-est",
    "sud",
    " Sud-Ovest ",
    " verso ovest ",
    " Nord-Ovest ",
  ],
  // Metodo di posizionamento
  pointedarray: [
    "Non posizionato",
    "GPS",
    "stazione base",
    "posizionamento della stazione base",
    "posizionamento WIFI",
  ],

  //mapcollegato
  ruler: "Che vanno",
  distance: "Informazioni sul traffico",
  baidumap: "Mappa di Baidu",
  map: "mappa",
  satellite: "satellitare",
  ThreeDimensional: "3D",
  baidusatellite: "Baidu satellitare",
  googlemap: "Google Maps",
  googlesatellite: "Google satellite",
  fullscreen: "Schermo intero",
  noBaidumapStreetView:
    "当Posizione attuale sulla mappa di Baidu senza vista sulla strada",
  noGooglemapStreetView: "Posizione attuale su Google Maps senza vista strada",
  exitStreetView: "Esci dalla vista stradale",
  draw: "disegnare",
  finish: "completo",
  unknown: "sconosciuto",
  realTimeTailAfter: "Tracciamento in tempo reale",
  trackReply: "Traccia la riproduzione",
  afterRefresh: "Dopo aver rinfrescato",
  rightClickEnd: "Estremità destra, raggio",
  rightClickEndGoogle: "Estremità destra - raggio",

  //treecollegato
  currentUserMachineCount: "Numero corrente di dispositivi utente",
  childUserMachineCount: "Contiene il numero totale di dispositivi sub-utente",

  //Relativo alla finestra

  electronicFence: "Recinzione elettronica",
  drawTrack: "Traccia traccia",
  showOrHide: "Mostra / Nascondi",
  showDeviceName: "Nome del dispositivo",
  circleCustom: "Round personalizzato",
  circle200m: "Round 200 metri",
  polygonCustom: "Personalizzazione del poligono",
  drawPolygon: "Disegnare un poligono",
  drawCircle: "Disegna un cerchio",
  radiusMin100:
    "Il raggio minimo del recinto disegnato da r è 20 metri. Si prega di ridisegnare. Raggio di recinzione attuale:",
  showAllFences: "Mostra tutte le recinzioni",
  lookEF: "Visualizza recinto",
  noEF: "Non sono stati trovati recinti elettronici!",
  hideEF: "Recinzione nascosta",
  blockUpEF: "Disattiva la recinzione",
  deleteEF: "Elimina recinto",
  isStartUsing: "Se abilitare",
  startUsing: "permettere",
  stopUsing: "disabilitare",
  nowEFrange: "Campo di recinzione attuale",
  enableSucess: "Abilitato con successo",
  unableSucess: "Disabilitato con successo",
  sureDeleteMorgage: "Assicurati di eliminare il secondo punto",
  enterMorgageName: "Si prega di inserire il nome del secondo post",
  openMorgagelongStayAlarm: "Aprire il secondo allarme di lunga durata",
  openMorgageinOutAlarm: "Apri l'allarme in entrata e in uscita",
  setEFSuccess:
    "Impostare correttamente la recinzione e abilitare il raggio di recinzione",
  setElectronicFence: "Impostare la recinzione elettronica",
  drawFence: "Disegnare una recinzione elettronica",
  drawMorgagePoint: "Disegna due punti",
  customFence: "Recinto personalizzato",
  enterFenceTips: "Inserire l'allarme",
  leaveFenceTips: "Lascia l'allarme",
  inputFenceName: "Si prega di inserire il nome della recinzione",
  relation: "collegato",
  relationDevice: "Dispositivo associato",
  unRelation: "Non associato",
  hadRelation: "connesso",
  quickRelation: "Associazione con un clic",
  cancelRelation: "Unlinked",
  relationSuccess: "Associazione di successo",
  cancelRelationSuccess: "Disassociare con successo",
  relationFail: "Fallimento dell'associazione",
  deviceList: "Elenco dei dispositivi",
  isDeleteFence: "Se cancellare la recinzione",
  choseRelationDeviceFirst:
    "Si prega di selezionare il dispositivo che si desidera associare prima!",
  choseCancelRelationDeviceFirst:
    "Si prega di selezionare il dispositivo che si desidera scollegare prima!",
  selectOneTips: "Si prega di selezionare almeno un metodo di avviso",
  radius: "raggio",
  //Imposta la seconda pagina
  setMortgagePoint: "Imposta due punti",

  circleMortage: "Round due punti",
  polygonMorgage: "Poligonale due punti",
  morgageSet: "Sono state impostate due scommesse",
  operatePrompt: "Operazione richiesta",
  startDrawing: "Clicca per iniziare a disegnare",
  drawingtip1:
    "Fare clic con il tasto sinistro del mouse per iniziare a disegnare, fare doppio clic per terminare il disegno",
  drawingtip2:
    "Fare clic con il tasto sinistro del mouse e trascinare per avviare il disegno",

  /************************************************/
  endTrace: "La riproduzione della traccia è finita ",
  travelMileage: "chilometraggio ",
  /************************************************/
  myAccount: "Il mio account",
  serviceProvide: "Fornitore di servizi",
  completeInfo:
    "Si prega di completare le seguenti informazioni, come persona di contatto, numero di telefono.",
  clientName: "Nome del cliente",
  loginAccount: "Account di accesso",
  linkMan: "contatto",
  linkPhone: "telefono",
  clientNameEmpty: "Il nome del cliente non può essere vuoto!",
  updateSuccess: "Aggiornato con successo!",
  /************************************************/
  oldPsw: "Vecchia password",
  newPsw: "Nuova password",
  confirmPsw: "Conferma della password",
  pswNoSame: "Immissione password incoerente",
  pswUpdateSuccess: "La password è stata modificata con successo!",
  email: "cassetta della posta",
  oldPwdWarn: "Si prega di inserire la vecchia password",
  newPwdWarn: "Si prega di inserire una nuova password",
  pwdConfirmWarn: "Si prega di confermare la nuova password",
  /************************************************/
  //Gruppo popup personalizzato
  resetPswFailure: "Impossibile reimpostare la password",
  notification: "sollecito",
  isResetPsw_a: "Vuoi‘",
  isResetPsw_b: "’Reimpostazione della password?",
  pwsResetSuccess_a: "ha‘",
  pwsResetSuccess_b: "’Password Reset 123456",
  /************************************************/
  machineSearch: "Ricerca di",
  search: "ricerca",
  clientRelation: "customer Relationship",
  machineDetail: "dettaglio",
  machineDetail2: "dettagli dei dispositivi",
  machineCtrl: "istruzione",
  transfer: "metastasi",
  belongCustom: "I vostri clienti",
  addImeiFirst: "Si prega di aggiungere il numero di IMEI!",
  addUserFirst: "Si prega di aggiungere i clienti!",
  transferSuccess: "Trasferire il successo!",
  multiAdd: "Bulk add",
  multiImport: "Aggiunta in batch",
  multiRenew: "Rinnovo in lotti",
  //批量修改设备begin
  editDevice:'Modifica il modello del dispositivo',
  deviceAfter: 'Modello del dispositivo (dopo la modifica)',
  editDeviceTips:'Conferma che il dispositivo da modificare è lo stesso modello e non è attivo!',
  pleaseChoseDevice: 'Seleziona prima il dispositivo da modificare!',
  editResult:'Modifica risultato',
  successCount:'Dispositivo modificato con successo:',
  failCount:'Dispositivi guasti:',
  //批量修改设备end
  multiDelete: "Batch elimina",
  canNotAddImei: "IMEI non esiste, non può essere aggiunto alla lista",
  importTime: "tempo Import",
  loginName: "accesso",
  platformDue: "Piattaforma",
  machinePhone: "SIM card",
  userDue: "Utente",
  overSpeedAlarm: "icona di eccesso di velocità",
  changeIcon: "icona",
  dealerNote: "osservazioni Dealer",
  noUserDue: "Inserisci il tuo tempo di scadenza utente",
  phoneLengththan3: "3 deve essere maggiore della lunghezza del telefono",
  serialNumberInput: "Anche inserimento numero",

  /************************************************/
  sending: "Invio di comandi ..... attendere prego ....",
  sendFailure: "Impossibile inviare!",
  ctrlName: " Nome dell'istruzione",
  interval: "intervallo",
  intervalError: "intervallo malformati",
  currectInterval: "Si prega di inserire l'intervallo di tempo corretto!",
  intervalLimit: "Impostare gamma intervallo 10-720, unità (minuti)",
  intervalLimit2: "Impostare Scala intervallo 10-5400, Unit (seconda)",
  intervalLimit3: "Impostare l'intervallo gamma 5-1440 unità (minuti)",
  intervalLimit4: "3-999 impostare l'intervallo di tempo, l'unità (s)",
  intervalLimit5: "Impostare Scala intervallo 10-10800, Unit (seconda)",
  intervalLimit1:
    "1-999 impostare l'unità di intervallo di tempo (min) 000, che rappresenta la modalità di ritorno, la temporizzazione di chiusura",
  intervalLimit6: "Impostare Scala intervallo 1-65535, l'unità (s)",
  intervalLimit7: "1-999999 gamma impostazione dell'intervallo, l'unità (s)",
  intervalLimit8: "Set intervallo di tempo rappresentato chiuso 0-255,0",
  intervalLimit9: "Impostare Scala intervallo 3-10800, l'unità (s)",
  intervalLimit10:
    "  Impostare l'intervallo di tempo dell'intervallo 3-86400, in secondi",
  intervalLimit11:
    "  Impostare l'intervallo di tempo dell'intervallo 180-86400, in secondi",
  intervalLimit22:
    "  Impostare l'intervallo di tempo dell'intervallo 60-86400, in secondi",
  intervalLimit24:
    "  Impostare l'intervallo di tempo dell'intervallo 10-86400, in secondi",
  intervalLimit25: "Impostare l'intervallo gamma 5-43200 unità (minuti)",
  intervalLimit12: "Impostare Scala intervallo 10-60, l'unità (s)",
  intervalLimit13:
    "Imposta l'intervallo di tempo 1-24 (significa 1-24 ore) o 101-107 (significa 1-7 giorni)",
  intervalLimit14:
    "Imposta intervallo di tempo 10-3600, unità (secondo); impostazione predefinita: 10 s",
  intervalLimit15:
    "Imposta l'intervallo di tempo dell'intervallo 180-86400, unità (secondo); impostazione predefinita: 3600s",
  intervalLimit16:
    "Imposta l'intervallo di tempo 1-72, unità (ora); impostazione predefinita: 24 ore",
  intervalLimit17: "Gamma di temperature di impostazione -127-127, unità (° C)",
  intervalLimit18: "Imposta intervallo di tempo 5-18000, unità (secondo)",
  intervalLimit19: "Imposta intervallo di tempo 10-300, unità (secondo)",
  intervalLimit20: "Imposta intervallo di tempo 5-399, unità (secondi)",
  intervalLimit21: "Imposta intervallo di tempo 5-300, unità (secondi)",
  noInterval: "Si prega di inserire l'intervallo!",
  intervalTips:
    "Disattivare la modalità di monitoraggio, impostare la sveglia in tempo",
  phoneMonitorTips:
    "Dopo il rilascio del dispositivo d'istruzione comporrà automaticamente il numero di richiamata, al fine di raggiungere l'ascoltatore.",
  time1: "tempo 1",
  time2: "Time 2",
  time3: "Time 3",
  time4: "Time 4",
  time5: "tempo 5",
  intervalNum: "Intervallo (minuti)",
  sun: "domenica",
  mon: "lunedi",
  tue: "martedì",
  wed: "mercoledì",
  thu: "giovedi",
  fri: "venerdì",
  sat: "sabato",
  awakenTime: "Sveglia",
  centerPhone: "Numero del centro",
  inputCenterPhone: "Si prega di inserire il numero del centro!",
  phone1: "Numero uno",
  phone2: "Numero due",
  phone3: "Numero tre",
  phone4: "numero quattro",
  phone5: "numero cinque",
  inputPhone: "Si prega di inserire il numero",
  offlineCtrl:
    "comando collegato è stato registrato, il dispositivo linea on-automaticamente istruzione al dispositivo sotto",
  terNotSupport: "Il terminale non supporta",
  terReplyFail: "Terminal non ha risposto",
  machineInfo: "Informazioni sul dispositivo",

  /************************************************/
  alarmTypeScreen: "Tipo di allarme Screening",
  allRead: "tutto leggere",
  read: "leggere",
  noAlarmInfo: "Non ci sono amici per cancellare le informazioni di allarme",
  alarmTip: "Suggerimento: deselezionare per filtrare il tipo di allarme",

  /************************************************/
  updatePsw: "password",
  resetPsw: "password reset",

  /************************************************/
  multiSell: "Vendite di massa",
  sell: "Vendite",
  sellSuccess: "successo di vendite!",
  modifySuccess: "Modificato con successo",
  modifyFail: "La modifica non è riuscita",
  multiTransfer: "Trasferimento batch",
  multiUserExpires: "Modificare un utente scade",
  batchModifying: "Batch Edit",
  userTransfer: "metastasi",
  machineRemark: "osservazione",
  sendCtrl: "Invia istruzioni",
  ctrl: "istruzione",
  ctrlLog: "Comando",
  ctrlLogTips: "Comando",
  s06Ctrls: [
    "alimentazione a distanza l'olio ",
    " olio di recupero remoto e di energia elettrica, ",
    " interrogazione individuare ",
  ],
  ctrlType: "Nome dell'istruzione",
  resInfo: "Messaggio di risposta",
  resTime: "Tempo di risposta",
  ctrlSendTime: "Invio del tempo",
  // csvimportazione upload di file
  choseCsv: "Si prega di selezionare il file CSV",
  choseFile: "Seleziona file",
  submit: "presentare",
  targeDevice: "dispositivo di destinazione",
  csvTips_1: "1, il file di Excel come formato csv",
  csvTips_2: "2, il file CSV nel sistema.",
  importExplain: "Import Descrizione:",
  fileDemo: "Esempi Formato file",

  // nuovo
  sendType: "Invia tipo",
  onlineCtrl: "Istruzioni online",
  offCtrl: "Istruzioni offline",
  resStatus: [
    "Non inviati, è scaduto, è stato rilasciato, è riuscito, fallito, nessuna risposta",
  ],
  /************************************************/
  addSubordinateClient: "Nuovo utente subordinato",
  noSubordinateClient: "Nessun utente subordinate",
  superiorCustomerEmpty: "Si prega di selezionare un cliente superiore",
  noCustomerName: "Inserire un nome cliente",
  noLoginAccount: "Inserisci il tuo account di accesso",
  noPsw: "Per favore inserisci la tua password",
  noConfirmPsw: "Si prega di inserire il codice di conferma",
  pswNotAtypism:
    "Immettere la password e confermare password non corrispondono!",
  addSuccess: "Aggiunto con successo",
  superiorCustomer: "Cliente superiore",
  addVerticalImei:
    "Si prega di inserire il numero IME da una colonna verticale",
  noImei: "IMEI non esistono, non possono essere aggiunti alla lista",
  addImei_curr: "Si prega di inserire il numero IMEI, corrente",
  no: "più",
  aRowAImei: "Una linea di ingresso IMEI",

  /*
   * dealer  inizio traduzione dell'interfaccia
   *
   * */
  //main.js
  imeiOrUserEmpty:
    "numero del dispositivo (IMEI) / nome del cliente non può essere vuoto!",
  accountEmpty: "Account non può essere vuoto!",
  queryNoUser: "Non ci sono richieste per l'utente",
  queryNoIMEI: "Il numero IMEI non viene interrogato",
  imeiOrClientOrAccount:
    "numero del dispositivo (IMEI) / nome del cliente / account",
  dueSoon: "che scade",
  recentlyOffline: "recentemente collegato",
  choseSellDeviceFirst:
    "Si prega di selezionare il dispositivo che si desidera vendere!",
  choseDeviceFirst:
    "Si prega di selezionare il dispositivo che si desidera trasferire!",
  choseDeviceExpiresFirst:
    "Si prega di selezionare il dispositivo che si desidera modificare!",
  choseRenewDeviceFirst:
    "Si prega di selezionare il dispositivo che si desidera rinnovare!",
  choseDeleteDeviceFirst:
    "Si prega di selezionare il dispositivo da eliminare!",
  choseClientFirst: "Si prega di selezionare un client per il trasferimento!",

  //myClient.js
  clientList: "Elenco clienti",
  accountInfo: "Informazioni account",
  machineCount: "Numero di dispositivi",
  stock: "acquisto",
  inventory: "stock",
  subordinateClient: "utente subordinato",
  datum: "dati",
  monitor: "monitore",
  dueMachineInfo: "informazioni sul dispositivo Scaduto",
  haveExpired: "scaduto",
  timeRange: [
    "7 giorni",
    "30 giorni",
    "60 giorni",
    "7-30 giorni",
    "30-60 giorni",
  ],
  offlineMachineInfo: "Offline Informazioni sul dispositivo",
  timeRange1: [
    "Più di 1 ora",
    "1 giorno",
    "7 giorni",
    "30 giorni",
    "60 giorni",
    "60 giorni",
    "1 h -1 giorni",
    "1-7 giorni",
    "7-30 giorni",
    "30-60 giorni",
  ],
  offlineTime: "linea lunga",
  includeSubordinateClient: "Gli utenti sono più bassa",

  stopMachineInfo: "informazioni sul dispositivo statico",
  stopTime1: "ancora lunga",
  unUseMachineInfo: "L'informazione non è abilitato dispositivo",
  unUseMachineCount: "Il numero di dispositivi non è abilitato",

  sellTime: "tempo di vendite",
  detail: "dettagliato",
  manageDevice: "dettagliato",
  details: "Dettagli",
  deleteSuccess: "Cancellato con successo!",
  deleteFail: "Elimina fallito!",
  renewalLink: "rinnovi dei collegamenti",
  deleteGroupTips: "Elimina gruppo",
  addGroup: "Aggiungere Group",
  jurisdictionRange: "Competenza: per modificare la funzione",
  machineSellTransfer: "trasferimento attrezzature di vendita",
  monitorMachineGroup: "gruppo di apparecchi di monitoraggio",
  jurisdictionArr: [
    "la gestione dei clienti, gestione dei messaggi, impostare le recinzioni, le informazioni di allarme, la gestione conto virtuale, ha emesso una direttiva",
  ],
  confrimDelSim: "Determinato a rimuovere il numero della carta SIM:",

  // Menu di scelta
  sellDevice: "attrezzature di vendita",
  addClient: "Nuovo cliente",
  deleteClient: "Eliminare clienti",
  resetPassword: "password reset",
  transferClient: "Trasferimento del cliente",
  ifDeleteClient: "cancellare",

  //myAccount
  myWorkPlace: "tavolo",
  availablePoints: "punti disponibili",
  yearCard: "Carta dell'anno",
  lifetimeOfCard: "Carta per tutta la vita",
  oneyear: "Un anno",
  lifetime: "per tutta la vita",
  oneyearPoint: "Importa punto di un anno",
  commonImportPoint: "Punto di importazione ordinario",
  lifetimeImportPoint: "Punto di importazione a vita",
  myServiceProvide: "Fornitore di servizi",
  moreOperator: "Altre azioni",
  dueMachine: "apparecchiature di scadenza",
  offlineMachine: "dispositivo collegato",
  quickSell: "vendite rapide",
  sellTo: "venduto a",
  machineBelong: "Attrezzatura appartenente al",
  reset: "reset",
  targetCustomer: "Cliente target",
  common_lifetimeImport:
    "punto Normale introduzione (0), per tutta la vita il punto introduzione (0)",
  cardType1: "Tipo di carta",
  credit: "punti di ricarica",
  generateImportPoint: "Generazione punto di introduzione",
  generateImportPointSuc: "punto generatore Importa successo!",
  generateImportPointFail: "Importazione punto generatore di guasto!",
  year_lifeTimeCard: "In Card (0), carta per tutta la vita (0)",
  generateRenewPoint: "Point genera rinnovi",
  transferTo: "Trasferimento a",
  transferPoint: "punti di trasferimento",
  transferRenewPoint: "Trasferimento punto di rinnovo",
  pointHistoryRecord: "punti Record",
  newGeneration: "Nuova generazione",
  operatorType: "Tipo di azione",
  consume: "consumo",
  give: "a",
  income: "reddito",
  pay: "spesa",
  imeiErr:
    "query attrezzature, numero IMEI devono essere almeno dopo sei o più digitale!",
  accountFirstPage: "account Home",

  /*
   * dealer  traduzione dell'interfaccia End
   *
   * */
  // sezione di controllo del vento di traduzione 1.4.8 pagina
  finrisk: "il controllo dei rischi finanziari",
  attention: "attenzione",
  cancelattention: "Smetti",
  poweroff: "Spegni",
  inout: "Fuori dei due carica",
  inoutEF: "Fuori dal recinto",
  longstay: "soggiorno di due carica",
  secsetting: "Due set point carica",
  EFsetting: "set recinzione",
  polygonFence: "Poligono recinto elettronico",
  cycleFence: "Rotonda geo-fence",
  haveBeenSetFence: "recinto elettronico Set",
  haveBeenSetPoint: "Sono state impostate due scommesse",
  drawingFailed: "Draw fallisce, si prega di ri-draw",
  inoutdot: "Dei due punti di ricarica",
  eleStatistics: "potenza statistica",
  noData: "Nessun dato",
  // Carica sottolineare due Form
  accountbe: "Il tuo account",
  SMtype: "Due tipi di carica puntiforme",
  SMname: "Nome punto Due carica",
  time: "tempo",
  position: "posizione",
  lastele: "La carica residua",
  statisticTime: "statistiche in tempo",
  searchalarmType: [
    "Tutto off-line, il potere, fuori dalla recinzione, fuori delle due cariche, due cariche soggiorno",
  ],
  remarks: [
    "Due cariche puntiformi, società di sicurezza, punto di smontare, mercato di seconda mano",
  ],
  focusOnly: "Unica preoccupazione,",
  // [?]descrizione
  interpretSignal:
    "comunicazione piattaforme di telefonia mobile e l'ultima volta: Signal",
  interpretPosition:
    "Posizionamento: apparecchiature di posizionamento via satellite l'ultima volta",
  interpretAll:
    "dispositivo di posizionamento in linea non è stazionario, ma ancora comunicare con la piattaforma",

  autoRecord: "Registrazione automatica",
  /******************************************************Impostazione del comando di avvio**********************************8*/
  setCtrl: {
    text: "Impostazione delle istruzioni",
    value: "",
  },
  moreCtrl: {
    text: 'Altre istruzioni',
    value: ''
  },
  sc_openTraceModel: {
    text: "Attivare la modalità di tracciamento",
    value: "0",
  },
  sc_closeTraceModel: {
    text: "Chiudi modalità di tracciamento",
    value: "1",
  },
  sc_setSleepTime: {
    text: "Imposta il tempo di sonno",
    value: "2",
  },
  sc_setAwakenTime: {
    text: "Imposta il tempo di attivazione",
    value: "3",
  },
  sc_setDismantleAlarm: {
    text: "Set Tamper Allarme",
    value: "4",
  },
  sc_setSMSC: {
    text: "Aumentare il numero del centro",
    value: "5",
  },
  sc_delSMSC: {
    text: "Elimina il numero del centro",
    value: "6",
  },
  sc_setSOS: {
    text: "Aggiungere SOS",
    value: "7",
  },
  sc_delSOS: {
    text: "Eliminare SOS",
    value: "8",
  },
  sc_restartTheInstruction: {
    text: "istruzioni restart",
    value: "9",
  },
  sc_uploadTime: {
    text: "Imposta intervallo di caricamento",
    value: "10",
  },
  /*Impostazione dell'ora sveglia
    impostazioni di tempo di ritorno a tempo
     impostazioni di allarme manomissione
    Modalità Settimana On Off*/
  sc_setAlarmClock: {
    text: "Impostare il tempo di sveglia",
    value: "11",
  },
  sc_setTimingRebackTime: {
    text: "Impostare il tempo di ritorno del timer",
    value: "12",
  },
  sc_openWeekMode: {
    text: "modalità di apertura Settimana",
    value: "13",
  },
  sc_closeWeekMode: {
    text: "Disattiva la modalità settimana",
    value: "14",
  },
  sc_powerSaverMode: {
    text: "Imposta la modalità di ritorno della tempistica",
    value: "15",
  },
  sc_carCatchingMode: {
    text: "Impostare la modalità inseguimento in auto",
    value: "16",
  },
  sc_closeDismantlingAlarm: {
    text: "Chiudere impostazioni di allarme manomissione",
    value: "17",
  },
  sc_openDismantlingAlarm: {
    text: "Aprire le impostazioni di allarme manomissione",
    value: "18",
  },
  sc_VibrationAlarm: {
    text: "impostare l'allarme di vibrazione",
    value: "19",
  },
  sc_timeZone: {
    text: "Impostazione del fuso orario",
    value: "20",
  },
  sc_phoneMonitor: {
    text: "Monitor del telefono",
    value: "21",
  },
  sc_stopCarSetting: {
    text: "Monitoraggio telefonico",
    value: "22",
  },
  sc_bindAlarmNumber: {
    text: "Impostazione di parcheggio",
    value: "23",
  },
  sc_bindPowerAlarm: {
    text: "Numero di allarme vincolante",
    value: "24",
  },
  sc_fatigueDrivingSetting: {
    text: "Impostazione della guida a fatica",
    value: "25",
  },
  sc_peripheralSetting: {
    text: "Impostazioni periferiche",
    value: "26",
  },
  sc_SMSAlarmSetting: {
    text: "Imposta sveglia SMS",
    value: "27",
  },
  sc_autoRecordSetting: {
    text: "Impostazioni di registrazione automatica",
    value: "28",
  },
  sc_monitorCallback: {
    text: "Monitorare il callback",
    value: "29",
  },
  sc_recordCtrl: {
    text: "Istruzioni di registrazione",
    value: "30",
  },
  sc_unbindAlarmNumber: {
    text: "numero di allarme unbind",
    value: "31",
  },
  sc_alarmSensitivitySetting: {
    text: "'Impostazione della sensibilità dell'allarme delle vibrazioni'",
    value: "32",
  },
  sc_alarmSMSsettings: {
    text: "Impostazioni SMS allarme a vibrazione",
    value: "33",
  },
  sc_alarmCallSettings: {
    text: "impostazioni del telefono allarme a vibrazione",
    value: "34",
  },
  sc_openFailureAlarmSetting: {
    text: "Disattivare l'allarme",
    value: "35",
  },
  sc_restoreFactory: {
    text: "Ripristino di fabbrica",
    value: "36",
  },
  sc_openVibrationAlarm: {
    text: "Vibrazione",
    value: "37",
  },
  sc_closeVibrationAlarm: {
    text: "Fuori allarme a vibrazione",
    value: "38",
  },
  sc_closeFailureAlarmSetting: {
    text: "Spegnere l'allarme",
    value: "39",
  },
  sc_feulAlarm: {
    text: "mpostazioni di allarme olio",
    value: "40",
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: "istruzioni vernacolare",
    value: "41",
  },
  sc_sleepMode: {
    text: "Chiudere impostazioni di allarme manomissione",
    value: "42",
  },
  sc_alarmMode: {
    text: "Aprire le impostazioni di allarme manomissione",
    value: "43",
  },
  sc_weekMode: {
    text: "Modello settimanale",
    value: "44",
  },
  sc_monitorNumberSetting: {
    text: "Impostazioni del numero di monitoraggio",
    value: "45",
  },
  sc_singlePositionSetting: {
    text: "Modalità di posizionamento unico",
    value: "46",
  },
  sc_timingworkSetting: {
    text: "Modalità di orario del lavoro",
    value: "47",
  },
  sc_openLightAlarm: {
    text: "Apri allarme sensore di luce",
    value: "48",
  },
  sc_closeLightAlarm: {
    text: "Chiudi allarme luce sensore",
    value: "49",
  },
  sc_workModeSetting: {
    text: "Impostazione della modalità di lavoro",
    value: "50",
  },
  sc_timingOnAndOffMachine: {
    text: "Impostazione dell'interruttore di temporizzazione",
    value: "51",
  },
  sc_setRealTimeTrackMode: {
    text: "Imposta la modalità di inseguimento in tempo reale",
    value: "52",
  },
  sc_setClockMode: {
    text: "Imposta la modalità sveglia",
    value: "53",
  },
  sc_openTemperatureAlarm: {
    text: "Attiva l'allarme di temperatura",
    value: "54",
  },
  sc_closeTemperatureAlarm: {
    text: "Disattiva l'allarme di temperatura",
    value: "55",
  },
  sc_timingPostbackSetting: {
    text: "Impostazioni di postback dei tempi",
    value: "56",
  },
  sc_remoteBoot: {
    text: "Avvio remoto",
    value: "57",
  },
  sc_smartTrack: {
    text: "Tracciamento intelligente",
    value: "58",
  },
  sc_cancelSmartTrack: {
    text: "Annulla il monitoraggio intelligente",
    value: "59",
  },
  sc_cancelAlarm: {
    text: "Annulla allarme",
    value: "60",
  },
  sc_smartPowerSavingMode: {
    text: "Imposta la modalità di risparmio energetico intelligente",
    value: "61",
  },
  sc_monitorSetting: {
    text: "Tenere sotto controllo",
    value: '62'
  },
  // 指令重构新增翻译
  sc_timedReturnMode: {
    text: 'Modalità di ritorno temporizzato',
    value: '100'
  },
  sc_operatingMode: {
      text: 'Modalità operativa',
      value: '101'
  },
  sc_realTimeMode : {
      text: 'Modalità di posizionamento in tempo reale',
      value: '102'
  },
  sc_alarmMode : {
      text: 'Modalità sveglia',
      value: '103'
  },
  sc_weekMode : {
      text: 'Modalità settimana',
      value: '104'
  },
  sc_antidemolitionAlarm : {
      text: 'Allarme anti-demolizione',
      value: '105'
  },
  sc_vibrationAlarm : {
      text: 'Allarme a vibrazione',
      value: '106'
  },
  sc_monitoringNumber : {
      text: 'Monitoraggio della gestione dei numeri',
      value: '107'
  },
  sc_queryMonitoring : {
      text: 'Query numero di monitoraggio',
      value: '108'
  },
  sc_electricityControl : {
      text: 'Controllo del petrolio e dell"elettricità',
      value: '109'
  },
  sc_SOSnumber : {
      text: 'Gestione dei numeri SOS',
      value: '110'
  },
  sc_SleepCommand : {
    text: 'Comando del sonno',
    value: '201'
  },
  sc_RadiusCommand : {
      text: 'Raggio di spostamento',
      value: '202'
  },
  sc_punchTimeMode:{
      text:'打卡模式',
      value:'203'  
  },
  sc_intervelMode:{
      text:'时间段模式',
      value:'204'  
  },
  sc_activeGPS:{
      text:'激活GPS',
      value:'205'  
  },
  sc_lowPowerAlert: {
      text: 'Promemoria batteria scarica',
      value: '206'
  },
  sc_SOSAlert: {
      text: 'SOS报警',
      value: '207'
  },
  mc_cuscom : {
    text: 'Istruzioni personalizzate',
    value: '1'
  },
  NormalTrack: 'Modalità di tracciamento normale',
  listeningToNumber:'Sei sicuro di voler controllare il numero di monitoraggio di?',
  versionNumber:'Sei sicuro di voler controllare il numero di versione di?',
  longitudeAndLatitudeInformation:'Sei sicuro di voler controllare le informazioni di latitudine e longitudine di?',
  equipmentStatus:'Sei sicuro di voler controllare lo stato di?',
  public_parameter:'Sei sicuro di voler controllare i parametri di？',
  GPRS_parameter:'Sei sicuro di voler controllare i parametri GPRS di？',
  deviceName: 'Sei sicuro di voler ruotare il dispositivo？',
  SMS_alert:'Sei sicuro di voler controllare l"allarme di promemoria SMS di',
  theBindingNumber:' Sei sicuro di voler controllare il numero di rilegatura di ',
  intervalTimeRange:'Modalità di ritorno temporizzato',
  pleaseChoose:'Modalità operativa',
  RealTimeCarChase: 'Modalità di posizionamento in tempo reale',
  inputPhoneNumber: "Inserisci il numero di telefono",
  inputCorPhoneNumber: "Inserisci il numero di telefono corretto",
  autoCallPhone: "Suggerimento: dopo che il comando è stato eseguito con successo, il terminale comporrà automaticamente il numero impostato",
  limitTheNumberOfCellPhoneNumbers1:'Questo comando supporta fino a 5 numeri di cellulare',
  limitTheNumberOfCellPhoneNumbers2:'Questo comando supporta fino a 3 numeri di cellulare',
  equipmentTorestart:'Sei sicuro di voler riavviare questo dispositivo?',
  remindTheWay:'Modo di ricordare',
  alarmWakeUpTime:'Ora della sveglia',
  alarmWakeUpTime1:'Ora sveglia 1',
  alarmWakeUpTime2:'Ora sveglia 2',
  alarmWakeUpTime3:'Ora sveglia 3',
  alarmWakeUpTime4:'Ora sveglia 4',
  sensitivityLevel:'Selezionare il livello di sensibilità',
  parking_time:'Tempo di parcheggio',
  selectWorkingMode:'Selezionare la modalità di lavoro',
  Alarm_value:'Valore di allarme',
  Buffer_value:'Valore tampone',
  gqg_disconnect:'disconnettersi',
  gqg_turnOn:'Accendi',
  Return_interval:'Intervallo di ritorno',
  gq_startTime:'Ora di inizio',
  gq_restingTime:'Tempo di riposo',
  gq_Eastern:'Fuso orario orientale',
  gq_Western:'Fuso orario occidentale',

  gq_driver:'Allarme di fatica alla guida',
  gq_deviceName:'Sei sicuro di voler eseguire il rollio di questo dispositivo?',
  gq_noteAlarm:'Sei sicuro di voler controllare l"allarme promemoria SMS?',
  gq_restoreOriginal:'Sei sicuro di voler ripristinare questa apparecchiatura alla fabbrica originale?',
  gq_normalMode:'Modalità normale',
  gq_IntelligentsleepMode:'Modalità di sospensione intelligente',
  gq_DeepsleepMode:'Modalità Deep sleep',
  gq_RemotebootMode:'Modalità di avvio remoto',
  gq_IntelligentsleepModeTips:'Sei sicuro di voler impostare la modalità di sospensione intelligente',
  gq_DeepsleepModeTips:'Sei sicuro di voler impostare la modalità di sospensione profonda',
  gq_RemotebootModeTips:'Sei sicuro di voler impostare la modalità di avvio remoto',
  gq_normalModeTips:'Sei sicuro di voler impostare la modalità normale',
  gq_sleepModeTips:'Sei sicuro di voler impostare questo dispositivo in modalità sospensione?',
  gq_Locatethereturnmode:'Modalità di ritorno al posizionamento',
  gq_regularWorkingHours:'Periodo di lavoro a tempo',
  gq_AlarmType:{
      text: 'Tipo di allarme',
      value: '111'
  },
  IssuedbyThePrompt:'Il comando è stato emesso, attendere che il dispositivo risponda',
  platformToinform:'Notifica della piattaforma',
    gq_shortNote:'Notifica SMS', 
  /************istruzioni vernacolare**********************/
  closeDismantlingAlarm: "Chiudere impostazioni di allarme manomissione",
  openDismantlingAlarm: "Aprire le impostazioni di allarme manomissione",
  closeTimingRebackMode:
    "Disattiva l'impostazione della modalità di ritorno a tempo",
  minute: "minuto",
  timingrebackModeSetting: "Rientro modalità di impostazione del timer:",
  setWakeupTime: "Imposta il tempo di attivazione",
  weekModeSetting: "Modalità settimane stabilito:",
  closeWeekMode: "impostazione della modalità Chiudi settimana",
  setRealtimeTrackMode: "modalità di inseguimento in tempo reale Set",
  fortification: "fortificazione",
  disarming: "disarmare",
  settimingrebackmodeinterval:
    "Rientro modalità di impostazione del timer a intervalli:",
  oilCutCommand: "Fuori direttiva benzina ed energia elettrica",
  restoreOilCommand: "Ripristinare direttiva petrolio ed elettricità",
  turnNnTheVehiclesPower: "Abilita la potenza del veicolo",
  turnOffTehVehiclesPower: "Spegnere il veicolo",
  implementBrakes: "Freno di esecuzione",
  dissolveBrakes: "Rilascia i freni",
  openVoiceMonitorSlarm: "Abilita la voce di allarme",
  closeVoiceMonitorAlarm: "Disattiva la sveglia",
  openCarSearchingMode: "Attiva la modalità di ricerca auto",
  closeCarSearchingMode: "Disattiva la modalità di ricerca auto",
  unrecognizedCommand: "Non riconosce il comando",
  commandSendSuccess:
    "Congratulazioni, il dispositivo esegue il comando ha esito positivo!",
  /********************************************Set si conclude istruzione**************************************************/

  /********************************************comandi di query iniziano**************************************************/
  queryCtrl: {
    text: "Istruzioni di query",
    value: "",
  },
  /*impostazioni dei parametri query*/
  qc_softwareVersion: {
    text: "versione del software di query",
    value: "1",
  },
  qc_latlngInfo: {
    text: "latitudine e la longitudine di query",
    value: "2",
  },
  qc_locationHref: {
    text: "Configurazione dei parametri di query",
    value: "3",
  },
  qc_status: {
    text: "Stato della query",
    value: "4",
  },
  qc_gprs_param: {
    text: "Parametri query GPRS",
    value: "5",
  },
  qc_name_param: {
    text: "detto",
    value: "6",
  },
  qc_SMSReminderAlarm_param: {
    text: "Query SMS promemoria allarme",
    value: "7",
  },
  qc_bindNumber_param: {
    text: "Query numero vincolante",
    value: "8",
  },

  /********************************************estremità istruzione Query**************************************************/

  /*******************************************comando di avvio di controllo***************************************************/

  controlCtrl: {
    text: "Istruzioni di controllo",
    value: "",
  },
  cc_offOilElectric: {
    text: "Olio spento",
    value: "1",
  },
  cc_recoveryOilElectricity: {
    text: "Recupero di petrolio e gas",
    value: "2",
  },
  cc_factorySettings: {
    text: "Ripristino di fabbrica",
    value: "4",
  },
  cc_fortify: {
    text: "fortificazione",
    value: "75",
  },
  cc_disarming: {
    text: "disarmare",
    value: "76",
  },
  cc_brokenOil: {
    text: "Istruzioni per il taglio dell'olio",
    value: "7",
  },
  cc_RecoveryOil: {
    text: "Circuito dell'olio di recupero",
    value: "8",
  },

  /*******************************************estremità istruzione di controllo***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: "chilometro",
  mileage: "chilometraggio",
  importMachine: "attrezzature Importazione",
  transferImportPoint: "Trasferimento punto di importazione",
  machineType1: "Modello dispositivo",
  confirmIMEI:
    "Assicurarsi che il numero IMEI e il modello prima del funzionamento",
  renew: "rinnovi",
  deductPointNum: "punti di penalizzazione",
  renewSuccess: "Rinnovi successo!",
  wireType: [
    {
      text: "Wired",
      value: false,
    },
    {
      text: "senza fili",
      value: true,
    },
  ],
  vibrationWays: [
    {
      text: "piattaforma",
      value: 0,
    },
    {
      text: "Piattaforma + SMS",
      value: 1,
    },
    {
      text: "Piattaforma + SMS + Telefono",
      value: 2,
    },
  ],
  addMachineType: [
    {
      text: "S06",
      value: "3",
    },
    // SO6 bambino --- iniziare -----
    {
      text: "GT06",
      value: "8",
    },
    {
      text: "S08V",
      value: "9",
    },
    {
      text: "S01",
      value: "10",
    },
    {
      text: "S01T",
      value: "11",
    },
    {
      text: "S116",
      value: "12",
    },
    {
      text: "S119",
      value: "13",
    },
    {
      text: "TR06",
      value: "14",
    },
    {
      text: "GT06N",
      value: "15",
    },
    {
      text: "S101",
      value: "16",
    },
    {
      text: "S101T",
      value: "17",
    },
    {
      text: "S06U",
      value: "18",
    },
    {
      text: "S112U",
      value: "19",
    },
    {
      text: "S112B",
      value: "20",
    },
    // SO6 bambino ---- fine ------
    {
      text: "S15",
      value: "1",
    },
    {
      text: "S05",
      value: "2",
    },
    {
      text: "SW06",
      value: "4",
    },
    {
      text: "S001",
      value: "5",
    },
    {
      text: "S08",
      value: "6",
    },
    {
      text: "S09",
      value: "7",
    },
  ],

  /*
   * 2018-02-02
   * */
  maploadfail:
    "La mappa attuale non riesce a caricare, se passare ad un'altra mappa?",

  /*
    2018-03-06istruzioni nuovo controllo
    * */
  cc_openPower: {
    text: "Potenza Aperto veicolo",
    value: "7",
  },
  cc_closePower: {
    text: "Spegnere il veicolo",
    value: "8",
  },
  cc_openBrake: {
    text: "freni Aperte veicoli,",
    value: "9",
  },
  cc_closeBrake: {
    text: "Ruotare i freni del veicolo",
    value: "10",
  },
  cc_openAlmrmvoice: {
    text: "allarme auto Aperto",
    value: "11",
  },
  cc_closeAlmrmvoice: {
    text: "Chiudi allarme per veicoli",
    value: "12",
  },
  /*2018-03-06istruzioni nuovo controllo
   * */
  cc_openFindCar: {
    text: "Aprire veicolo la ricerca di auto",
    value: "13",
  },
  cc_closeFindCar: {
    text: "Chiudere i veicoli la ricerca di auto",
    value: "14",
  },

  /*2018-03-19
   * */
  EF: "recinto",

  /*
    2018-03-29，campo Estensione
    * */
  exData: ["Potenza","tensione", "temperatura", "olio", "resistenza"],

  /*
    2018-04-10
    * */
  notSta: "Base posizione della stazione, non incluso nelle statistiche",

  // statistiche Oil
  fuelSetting: "set olio",
  mianFuelTank: "Il serbatoio principale",
  auxiliaryTank: "serbatoio di goccia",
  maximum: "massimo",
  minimum: "Valore minimo",
  FullTankFuel: "pieno di carburante",
  fuelMinValue: 'Il volume del carburante non può essere inferiore a 10 litri',
  standardSetting: "Impostazione standard",
  emptyBoxMax: "Svuotare il valore massimo",
  fullBoxMax: "massima del serbatoio' completa,",
  fuelStatistics: "statistiche Oil",
  settingSuccess: "Installazione riuscita",
  settingFail: "Installazione fallita",
  pleaseInput: "Si prega di inserire",
  fuelTimes: "Il numero di rifornimento",
  fuelTotal: "Andiamo totale",
  refuelingTime: 'Tempo di rifornimento',
  fuelDate: "il tempo di rifornimento di carburante",
  fuel: "Quantità di olio",
  fuelChange: "cambio olio",
  feulTable: "tavolo analisi dell'olio",
  addFullFilter: "Si prega di compilare Filtri complemento",
  enterIntNum: "Si prega di inserire un numero intero positivo",
  // statistiche di temperatura
  tempSta: "statistiche di temperatura",
  tempTable: "tabella di analisi della temperatura",
  industrySta: "statistiche del settore",
  temperature: "temperatura",
  temperature1: "temperatura1",
  temperature2: "temperatura2",
  temperature3: "temperatura3",
  tempRange: "Intervallo di temperatura",
  tempSetting:'Impostazione della temperatura',
  tempSensor:'Sensore di temperatura',
  tempAlert:'Dopo che il sensore di temperatura è spento, non riceverà i dati di temperatura!',
  phoneNumber: "Telefono cellulare",
  sosAlarm: "Allarme SOS",
  undervoltageAlarm: "Allarme di sottotensione",
  overvoltageAlarm: "Allarme di sovratensione",
  OilChangeAlarm: "Allarme cambio quantità olio",
  accDetection: "Rilevamento ACC",
  PositiveAndNegativeDetection: "Rilevazione positiva e negativa",
  alermValue: "Valore di allarme",
  bufferValue: "Valore del buffer",
  timeZoneDifference: "Differenza di fuso orario",
  meridianEast: "Est meridiano",
  meridianWest: "Meridian West",
  max12hour: "La differenza di input non può essere superiore a 12 ore",

  trackDownload: "Traccia il download",
  download: "Scarica",
  multiReset: "Ripristino totale",
  resetSuccess: "Ripristina con successo",
  multiResetTips:
    "Suggerimento: i dati di test come il tempo di attivazione del dispositivo e la traccia verranno cancellati dopo il ripristino e lo stato del dispositivo verrà ripristinato su online o offline.",
  point: "punto",
  myplace: "Il mio posto",
  addPoint: "Aggiungi punto",
  error10018: "Numero insufficiente di punti di importazione",
  error110:"L'oggetto non esiste",
  error109:"Limite massimo superato",
  error20013:"Il tipo di dispositivo non esiste",
  error90001:"Il numero di serie del tipo di dispositivo non può essere vuoto",
  error20003:"Imei non può essere vuoto",
  inputName: "Per favore, inserisci un nome",
  virtualAccount: "Account virtuale",
  createTime: "Tempo di creazione",
  permission: "competenza",
  permissionRange: "Portata dell'autorità",
  canChange: "Funzione modificabile",
  fotbidPassword: "Cambia password",
  virtualAccountTipsText:
    "Quando si crea un account virtuale, si tratta di un account alias dell'account del rivenditore attualmente registrato e può impostare le autorizzazioni per l'account virtuale.",
  noOperationPermission: "L'account virtuale non ha diritti di operazione",
  number: "numero",
  rangeSetting: "Impostazione della gamma",
  setting: "impostare",
  // 1.6.1
  duration: "Durata",
  voltageSta: "Statistiche di tensione",
  voltageAnalysis: "Analisi di tensione",
  voltageEchart: "Tabella di analisi della tensione",
  platformAlarm: "Allarme piattaforma",
  platformAlarm1: "telefono",
  platformAndPhone: 'telefono+allarme di piattaforma',
  smsAndplatformAlarm: "SMS + allarme di piattaforma",
  smsAndplatformAlarm1: "SMS",
  smsAndplatformAlarmandPhone: "Allarme piattaforma + SMS + telefono",
  smsAndplatformAlarmandPhone1: "SMS + telefono",
  more_speed: "velocità",
  attribute: "proprietà",
  profession: "specialità",
  locationPoint: "Punto di ancoraggio",
  openPlatform: "Piattaforma aperta",
  experience: "Voglio sperimentare",
  onlyViewMonitor: "Posso solo vedere il monitoraggio",

  // 1.6.3
  inputAccountOrUserName:
    "Si prega di inserire un numero di conto o nome utente",
  noDeviceTips:
    "Non ha trovato le informazioni relative all'attrezzatura, controlla il punto cliente",
  noUserTips:
    "Non ha trovato informazioni utili per l'utente, controlla i punti dell'attrezzatura",
  clickHere: "qui",
  // 1.6.4
  pointIntervalSelect: "Track point interval",
  payment: "pagamento",
  pleaceClick: "Per favore clicca",
  paymentSaveTips:
    "Consiglio di sicurezza: confermare con il fornitore di servizi la validità del collegamento",
  //1.6.4 aggiunto
  fuelAlarmValue: "Valore di allarme quantità olio",
  fuelConsumption: "Consumo di carburante",
  client: "cliente",
  create: "nuovo",
  importPoint: "Punto di importazione",
  general: "ordinario",
  lifelong: "per tutta la vita",
  renewalCard: "Carta rinnovo",
  settingFuelFirst:
    "Si prega di impostare il valore di allarme quantità olio prima di inviare il comando!",
  overSpeedSetting: "Impostazione della velocità",
  kmPerHour: "km/h",
  times: "secondario",
  total: "totale",
  primary: "primario",
  minor: "vice",
  unActiveTips: "Il dispositivo non è attivato e non può essere utilizzato.",
  arrearsTips:
    "Il dispositivo è in ritardo e non può utilizzare questa funzione",
  //1.6.5
  loading: "Caricamento dati in corso ...",
  expirationReminder: "Promemoria di scadenza",
  projectName: "Nome del progetto",
  expireDate: "Scadenza",
  changePwdTips:
    "La tua password è troppo semplice e c'è un rischio per la sicurezza. Modifica la password immediatamente.",
  pwdCheckTips1: "I suggerimenti sono 6-20 lettere, numeri o simboli",
  pwdCheckTips2: "La password che hai inserito è troppo debole.",
  pwdCheckTips3: "La tua password può essere più complicata.",
  pwdCheckTips4: "La tua password è sicura",
  pwdLevel1: "debole",
  pwdLevel2: "in",
  pwdLevel3: "forte",
  comfirmChangePwd: "Determina la password da cambiare",
  notSetYet: "Non ancora impostato",
  // 1.6.6
  liter: "litro",
  arrearageDayTips: "Dovuto in giorni",
  todayExpire: "A causa oggi",
  forgotPwd: "Hai dimenticato la password?",
  forgotPwdTips:
    "Si prega di contattare il venditore per cambiare la password.",
  //1.6.7
  commonProblem: "Domande frequenti",
  instructions: "Istruzioni per l'uso",
  webInstructions: "Istruzioni operative WEB",
  appInstructions: "Istruzioni operative APP",
  acceptAlarmNtification: "Ricevi una notifica di allarme",
  alarmPeriod: "Periodo di allarme",
  whiteDay: "giorno",
  blackNight: "Notte oscura",
  allDay: "Tutto il giorno",
  alarmEmail: "Posta di allarme",
  muchEmailTips:
    "È possibile inserire più caselle di posta, separate dal simbolo ';'.",
  newsCenter: "Centro messaggi",
  allNews: "Tutte le notizie",
  unReadNews: "Messaggio non letto",
  readNews: "Leggi il messaggio",
  allTypeNews: "Leggi il messaggio",
  alarmInformation: "Informazioni sull'allarme",
  titleContent: "Contenuto del titolo",
  markRead: " Contrassegnato come letto",
  allRead: " Tutti letti",
  allDelete: "Elimina tutto",
  selectFirst: "Seleziona prima di procedere!",
  updateFail: "Aggiornamento fallito!",
  ifAllReadTips: "Sono tutti pronti a leggere?",
  ifAllDeleteTips: "Sono tutti cancellati?",
  stationInfo: "Informazioni sulla stazione",
  phone: " Telefono cellulare",
  //1.6.72
  plsSelectTime: "Per favore, scegli il tempo!",
  customerNotFound: "Il cliente non è stato trovato.",
  Postalcode: "Posizione",
  accWarning: "Allarme ACC",
  canInputMultiPhone:
    "È possibile inserire più numeri di cellulare, si prega di utilizzare; separato",
  noLocationInfo: "Il dispositivo non ha ancora informazioni sulla posizione.",
  //1.6.9
  fenceName: "nome recinto",
  fenceManage: "Fence Management",
  circular: "cerchio",
  polygon: "poligono",
  allFence: "Tutte le recinzioni",
  shape: "forma",
  stationNews: "Messaggio del sito",
  phonePlaceholder: 'Puoi inserire più numeri, separati da ","',
  addressPlaceholder:
    "'Inserire l'indirizzo e il codice postale in ordine, separati da ','",
  isUnbind: "Vuoi scollegare",
  alarmCar: "Veicolo di allarme",
  alarmAddress: "posizione dell'allarme",
  chooseAtLeastOneTime: "Seleziona almeno una volta",
  alarmMessage: "Non ci sono dettagli di queste informazioni di allarme",
  navigatorBack: "return to superior",
  timeOverMessage:
    "Il tempo di scadenza dell'utente non può essere superiore al tempo di scadenza della piattaforma",
  //1.7.0
  userTypeStr: "Tipo di utente",
  newAdd: "Nuovo",
  findAll: "Totale",
  findStr: "Dati corrispondenti",
  customColumn: "Personalizza",
  updatePswErr: "Password aggiornata non riuscita",
  professionalUser: "Utente professionale o no?",
  confirmStr: "Confermare",
  inputTargetCustomer: "Input Target Customer",
  superiorUser: "Utente superiore",
  speedReport: "Rapporto di velocità",
  createAccount: "Crea un account",
  push: "Spingere",
  searchCreateStr:
    "Gestirà un account e trasferirà il dispositivo su questo account.",
  allowIMEI: "Consenti accesso IMEI",
  defaultPswTip: "La password predefinita è le ultime 6 cifre dell'IMEI",
  createAccountTip: "Account creato e dispositivo trasferito correttamente",
  showAll: "Mostra tutto",
  bingmap: "Mappa di Bing",
  areaZoom: "Zoom",
  areaZoomReduction: "Ripristina zoom",
  reduction: "Riduzione",
  saveImg: "Salva come immagine",
  fleetFence: "Fleet Fence",
  alarmToSub: "Notifica di allarme",
  bikeFence: "Bike Fence",
  delGroupTip: "Impossibile eliminare. Eliminare prima il punto di interesse!",
  isExporting: "Esportazione...",
  addressResolution: "Risoluzione degli indirizzi...",
  simNOTip: "Il numero della carta SIM può essere solo un numero",
  unArrowServiceTip:
    "Il tempo di scadenza dell'utente per i seguenti dispositivi è superiore al tempo di scadenza della piattaforma, selezionare nuovamente. Il numero del dispositivo è:",
  platformAlarmandPhone: "Piattaforma di allarme + tel",
  openLightAlarm: "Apri allarme sensore di luce",
  closeLightAlarm: "Chiudi allarme luce sensore",
  ACCAlarm: "Allarme ACC",
  translateError:
    "Trasferimento non riuscito, l'utente di destinazione non è autorizzato",
  distanceTip: "Fai clic su OK, fai doppio clic per terminare",
  workMode: "Modalità di lavoro",
  workModeType:
    "0: modalità normale; 1 modalità di sospensione intelligente; 2 Modalità Deep Sleep",
  clickToStreetMap: "Clicca per aprire la mappa della vista stradale",
  current: "corrente",
  remarkTip:
    "Nota: non vendere contemporaneamente carte di diversi tipi di pacchi",
  searchRes: "Risultato della ricerca",
  updateIcon: "Cambia icona",
  youHaveALarmInfo: "Hai un messaggio di avviso",
  moveInterval: "Intervallo di allenamento",
  staticInterval: "Intervallo di inattività",
  notSupportTraffic: "Coming soon.",
  ignite: "Acc ON",
  flameout: "Acc OFF",
  generateRenewalPointSuc: "Genera punti di rinnovo correttamente",
  noGPSsignal: "Non posizionato",
  imeiErr2: "Inserisci almeno le ultime 6 cifre del numero imei",
  searchCreateStr2:
    "Ciò creerà un account e trasferirà questo dispositivo al nome dell'account",
  addUser: "Aggiungi utente",
  alarmTemperature: "Valore di temperatura di allarme",
  highTemperatureAlarm: "Allarme di alta temperatura",
  lowTemperatureAlarm: "Allarme di bassa temperatura",
  temperatureTip: "Si prega di inserire un valore di temperatura！",
  locMode: "Modalità di posizionamento",
  imeiInput: "Inserisci il numero IMEI",
  noResult: "Nessun risultato corrispondente",
  noAddressKey:
    "Temporaneamente incapace di ottenere informazioni sull'indirizzo",
  deviceGroup: "Raggruppamento",
  shareManage: "Condividere",
  lastPosition: "Ultima posizione",
  defaultGroup: "Predefinito",
  tankShape: "Forma del serbatoio",
  standard: "Standard",
  oval: "Ovale",
  irregular: "Irregolare",
  //1.8.4
  inputAddressOrLoc: "Inserisci indirizzo / latitudine e longitudine",
  inputGroupName: "Inserisci il nome del gruppo",
  lock: "Locking",
  shareHistory: "Condividere",
  tomorrow: "Domani",
  threeDay: "Tre giorni",
  shareSuccess: "Genera link di condivisione correttamente",
  effective: "Efficace",
  lapse: "Periodo",
  copyShareLink: "Copia il link di condivisione",
  openStr: "Aperto",
  closeStr: 'OFF',
  linkError: "Questo link di condivisione è scaduto",
  inputUserName: "Inserisci un nome cliente",
  barCodeStatistics: "Statistiche sui codici a barre",
  barCode: "Codice a barre",
  sweepCodeTime: "Tempo di scansione",
  workModeType2:
    "1 modalità di sospensione intelligente；2 Modalità di sospensione profonda；3 Modalità accensione / spegnimento remoto",
  remoteSwitchMode: "Modalità interruttore remoto",
  saleTime: "Data di vendita",
  onlineTime: "Ora di pranzo",
  dayMileage: "Chilometraggio oggi",
  imeiNum: "IMEI",
  overSpeedValue: "Soglia di velocità eccessiva",
  shareNoOpen: "Il link di condivisione non è abilitato",
  addTo2: "Aggiungi",
  Overdue: "scaduto",
  openInterface: "Apri interfaccia",
  privacyPolicy: 'Privacy Policy',
  serviceTerm: "Termini di servizio",
  importError:
    "Impossibile aggiungere, il numero del dispositivo (IMEI) deve essere un numero di 15 cifre",
  importResult: "Importa risultati",
  totalNum: "totale",
  successInfo: "successo",
  errorInfo: "Fail",
  repeatImei: "ripetizione IMEI",
  includeAccount: "account secondario",
  formatError: "malformato",
  importErrorInfo: "Inserisci il numero IMEI di 15 cifre",
  totalMileage: "Chilometraggio totale",
  totalOverSpeed: "Total overspeed (times)",
  totalStop: "Total stop (times)",
  totalOil: "Total oil",
  timeChoose: "Selezione dell'ora",
  intervalloTime: "Interval time",
  default: "Default",
  idleSpeedStatics: "Statistiche velocità minima",
  offlineStatistics: 'Statistiche offline',
  idleSpeed: "Velocità minima",
  idleSpeedTimeTip1: "Il tempo di inattività non può essere vuoto",
  idleSpeedTimeTip2:
    "Il tempo di inattività deve essere un numero intero positivo",
  averageSpeed: "Velocità media",
  averageOil: "Consumo medio di carburante",
  oilImgTitle: "Grafico di analisi dell'olio",
  oilChangeDetail: "Dettagli cambio carburante",
  machineNameError:
    "Il nome del dispositivo non può contenere simboli speciali (/ ')",
  remarkError: "Le informazioni di commento non possono superare le 50 parole",
  defineColumnTip: "Controlla fino a 12 articoli",
  pswCheckTip:
    "Il suggerimento è una combinazione di 6-20 cifre, lettere e simboli",
  chooseGroup: "Seleziona un gruppo",
  chooseAgain:
    "Puoi interrogare i dati solo negli ultimi sei mesi, seleziona di nuovo！",
  noDataTip: "Nessun dato！",
  noMachineNameError: "Seleziona un dispositivo！",
  loginAccountError: "L'account di accesso non può contenere 15 cifre！",
  includeExpire: "Che scade",
  groupNameTip: "Il nome del gruppo non può essere vuoto！",
  outageTips: "Sei certo taglio di petrolio al largo?",
  powerSupplyTips: "Sei sicuro di ripristinare l'olio?",
  centerPhoneTips: "Per favore inserisci il numero",
  centerPhoneLenTips: "Inserisci 8-20 cifre",
  passworldillegal: "Sono presenti caratteri illegali",
  // 2.0.0 POI，权限版本
  singleAdd:'Aggiunta singola',
  batchImport:'Importazione batch',
  name:'Nome',
  icon:'Icona',
  defaultGroup:'Gruppo predefinito',
  remark:'Remark',
  uploadFile:'Carica il file',
  exampleDownload:'Esempio di download',
  uploadFiles:'Carica il file',
  poiTips1:"È possibile importare POI caricando il file Excel con le informazioni correlate. Si prega di seguire il formato dell'esempio per preparare il file",
  poiTips2:'Name: richiesto, non più di 32 caratteri',
  poiTips3:'Icona: richiesta, immettere 1,2,3,4',
  poiTips4:'Latitude: obbligatorio',
  poiTips5:'Longitudine: obbligatoria',
  poiTips6:'Nome gruppo: facoltativo, non più di 32 caratteri. Se il nome del gruppo non è inserito, il punto PDI appartiene al gruppo predefinito. Se il nome del gruppo compilato è coerente con il nome del gruppo creato, il punto POI appartiene al gruppo creato. Il nome del gruppo non è stato creato, il sistema aggiungerà il gruppo',
  poiTips7:'Note: facoltativo, non più di 50 caratteri',
  // 权限相关
  roleLimit: 'Autorizzazioni ruolo',
  operateLog: 'Registro delle operazioni',
  sysAccountManage: 'Account di autorità',
  rolen: 'Ruoli',
  rolename: 'Nome ruolo',
  addRole: 'Nuovo ruolo',
  editRole: 'Modifica ruolo',
  deleteRole: 'Elimina ruolo',
  delRoleTip: 'Sei sicuro di voler eliminare questo ruolo?',
  delAccountTip: 'Sei sicuro di voler eliminare questo account?',
  limitconfig: 'Profilo dei diritti',
  newAccountTip1: "L'account di autorità è simile al vecchio account virtuale ed è l'account secondario dell'amministratore. Gli amministratori possono creare account privilegiati e assegnare ruoli diversi agli account privilegiati in modo che account diversi possano vedere contenuti e operazioni diversi sulla piattaforma.",
  newAccountTip2: "Processo di creazione di un account di autorizzazione:",
  newAccountTip31: '1. Nella pagina di gestione dei ruoli,',
  newAccountTip32: 'Nuovo ruolo',
  newAccountTip33: 'E configurare le autorizzazioni per il ruolo;',
  newAccountTip4: "2. Nella pagina di gestione dell'account di autorità, creare un nuovo account di autorità e assegnare i ruoli all'account.",
  newRoleTip1: 'Gli amministratori possono creare ruoli e configurare autorizzazioni operative diverse per ruoli diversi per soddisfare le esigenze aziendali in scenari diversi.',
  newRoleTip2: "Ad esempio, configurare se un ruolo finanziario ha l'autorizzazione per individuare e monitorare, se ha l'autorizzazione per aggiungere clienti, se ha l'autorizzazione per modificare le informazioni sul dispositivo, ecc.",
  "refuelrate": "Tasso di rifornimento",
  "refuellimit": "Quando l'aumento di olio al minuto è maggiore di xxxxL e minore di xxxxL, è considerato rifornimento.",
  "refueltip": "La velocità massima di rifornimento non deve essere inferiore alla velocità minima di rifornimento!",
  viewLimitConf: 'Visualizza le impostazioni di autorizzazione',
  viewLimit: 'Visualizza autorizzazioni',
  newSysAcc: 'Nuovo account di sistema',
  editSysAcc: 'Modifica account di autorizzazione',
  virtualAcc: 'Account virtuale',
  oriVirtualAcc: 'Conto virtuale originale',
  virtualTip: "Il modulo dell'account virtuale è stato aggiornato a un modulo dell'account di sistema, creare un nuovo account di sistema",
  operaTime: 'Tempo di operatività',
  ipaddr: 'indirizzo IP',
  businessType: 'tipo di affari',
  params: 'Richiedi parametro',
  operateType: 'Tipo di operazione',
  uAcc: 'account utente',
  uName: 'nome utente',
  uType: 'tipologia di utente',
  logDetail: 'Dettagli del registro',
  delAccount: "Eliminare l'account",
  modifyTime: 'Modifica ora',
  unbindlimit: 'Non è possibile creare un recinto elettronico con un clic senza le autorizzazioni del dispositivo associate!',
  setSmsTip: 'Se si imposta la notifica SMS, è necessario prima attivare la notifica della piattaforma; se la consegna della notifica della piattaforma ha esito positivo, la notifica SMS non va a buon fine, è necessario riattivare la notifica SMS',
  cusSetComTip: 'Dichiarazione di non responsabilità: il rischio portato dalle istruzioni personalizzate non ha nulla a che fare con la piattaforma',
  cusSetComPas: "Immettere la password dell'account di accesso corrente",
  cusSetComDes1: 'Le istruzioni personalizzate supportano solo le istruzioni in linea.',
  cusSetComDes2: "Se il dispositivo non risponde entro due minuti dall'invio del comando, il processo viene terminato e lo stato del comando viene giudicato come nessuna risposta.",
  cueSetComoffline: "Il dispositivo non risponde e l'invio del comando personalizzato non è riuscito!",
  fbType: 'Tipo di feedback',
  fbType1: 'Consultivo',
  fbType2: 'Malfunzionamento',
  fbType3: "l'esperienza utente",
  fbType4: 'Suggerimenti per nuove funzionalità',
  fbType5: 'altro',
  upload: 'Caricare',
  uploadImg: 'carica immagine',
  uploadType: 'Carica file di tipo .jpg .png .jpeg .gif',
  uploadSize: 'Il file di caricamento non può essere più grande di 3M',
  fbManager: 'Gestione del feedback',
  blManager: 'Gestione degli annunci',
  fbUploadTip: 'Seleziona il tipo di feedback',
  menuPlatform: "Notizie dalla piattaforma",
  menuFeedback: "Risposta",
  menuBulletin: "Annuncio della piattaforma",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "Comportamento di guida",
  BtyjdfghtwsrgGHFEEGRDAF: "Rapida accelerazione",
  BtyuwyfgrWERERRTHDAsdDF: "Decelerazione rapida",
  Be2562h253grgsHHJDbRDAF: "Svolta brusca",
  celTemperature:'Celsius Temperature.'
};
// 权限tree
lg.limits = {
  "ACC_statistics": "Statistiche ACC",
  "Account_Home": "account Home",
  "Add": "Nuovo",
  "Add_POI": "Aggiungi PDI",
  "Add_customer": "Nuovo cliente",
  "Add_device_group": "Aggiungi gruppo di dispositivi",
  "Add_fence": "Aggiungi recinzione",
  "Add_sharing_track": "Aggiungi traccia di condivisione",
  "Add_system_account": "Nuovo account di autorizzazione",
  "Alarm_details": "Lista degli allarmi",
  "Alarm_message": "Messaggio di allarme",
  "Alarm_overview": "Panoramica degli allarmi",
  "Alarm_statistics": "Statistiche di allarme",
  "All_news": "Tutte le notizie",
  "Associated_equipment": "Dispositivo associato",
  "Available_points": "punti disponibili",
  "Barcode_statistics": "Statistiche sui codici a barre",
  "Batch_Import": "Importazione batch",
  "Batch_renewal": "Rinnovo in lotti",
  "Batch_reset": "Ripristino totale",
  "Bulk_sales": "Vendite di massa",
  "Call_the_police": "allarme",
  "Customer_details": "Dettagli cliente",
  "Customer_transfer": "Trasferimento del cliente",
  "Delete_POI": "Elimina PDI",
  "Delete_account": "Eliminare l'account",
  "Delete_customer": "Eliminare clienti",
  "Delete_device": "Elimina dispositivo",
  "Delete_device_group": "Elimina gruppo di dispositivi",
  "Delete_fence": "Elimina recinto",
  "Delete_role": "Elimina ruolo",
  "Device_List": "Elenco dei dispositivi",
  "Device_grouping": "Raggruppamento",
  "Device_transfer": "Trasferimento del dispositivo",
  "Due_reminder": "Promemoria di scadenza",
  "Edit_details": "Modifica i dettagli",
  "Equipment_management": "Dispositivo",
  "My_clinet": "Dispositivo",
  "Fence": "recinto",
  "Fence_management": "Fence Management",
  "Generate": "Creare",
  "Generate_lead-in_points": "Generazione punto di introduzione",
  "Generate_renewal_points": "Point genera rinnovi",
  "Have_read": "rimuovere",
  "Idle_speed_statistics": "Statistiche velocità minima",
  "Import": "Importare",
  "Import_Device": "attrezzature Importazione",
  "Industry_Statistics": "statistiche del settore",
  "Location_monitoring": "Monitore",
  "Log_management": "Gestione dei registri",
  "Mark_read": "Contrassegnato come letto",
  "Menu_management": "Gestione dei menu",
  "Message_Center": "Centro messaggi",
  "Mileage_statistics": "Statistiche di distanza in miglia",
  "Modify_POI": "Modifica POI",
  "Modify_device_details": "Modifica i dettagli del dispositivo",
  "Modify_device_group": "Modifica gruppo di dispositivi",
  "Modify_role": "Modifica ruolo",
  "Modify_sharing_track": "Modifica traccia di condivisione",
  "Modify_user_expiration": "Modificare un utente scade",
  "More": "Più",
  "My_client": "Il mio cliente",
  "New_role": "Nuovo ruolo",
  "New_users": "Aggiungi utente",
  "Oil_statistics": "statistiche Oil",
  "POI_management": "Gestione dei POI",
  "Points_record": "punti Record",
  "Push": "Spingere",
  "Quick_sale": "vendite rapide",
  "Renew": "rinnovi",
  "Replay": "Riproduzione",
  "Role_management": "Gestione dei ruoli",
  "Run_overview": "Panoramica delle operazioni",
  "Running_statistics": "Statistiche operative",
  "Sales_equipment": "attrezzature di vendita",
  "Set_expiration_reminder": "Imposta il promemoria di scadenza",
  "Share_track": "Condividere",
  "Sharing_management": "Condividere",
  "Speeding_detailed_list": "Lista di eccesso di velocità",
  "Statistical_report": "Rapporti",
  "Stay_detailed_list": "Lista permanente",
  "System_account_management": "Account di autorità",
  "Temperature_statistics": "statistiche di temperatura",
  "Transfer": "metastasi",
  "Transfer_group": "Gruppo di trasferimento",
  "Transfer_point": "Trasferimento punto di importazione",
  "Transfer_renewal_point": "Trasferimento punto di rinnovo",
  "Trip_statistics": "Statistiche di viaggio",
  "Unlink": "Scollega",
  "View": "vista",
  "View_POI": "Visualizza POI",
  "View_device_group": "Visualizza gruppo di dispositivi",
  "View_fence": "Visualizza recinto",
  "View_role": "Visualizza ruolo",
  "View_sharing_track": "Visualizza la traccia di condivisione",
  "Virtual_account": "Account virtuale",
  "Voltage_analysis": "Analisi di tensione",
  "Voltage_statistics": "Statistiche di tensione",
  "batch_deletion": "Batch elimina",
  "change_Password": "Cambia password",
  "delete": "Cancellare",
  "edit": "Modifica",
  "instruction": "istruzione",
  "modify": "Modifica",
  "monitor": "monitore",
  "my_account": "Il mio account",
  "reset_Password": "password reset",
  "share_it": "quota",
  "sub_user": "utente subordinato",
  "track": "pista",
  "Custom_Order": "Istruzioni personalizzate",
  "GeoKey_Manager": "Gestione GeoKey",
  "GeoKey_Update": "modificare",
  "GeoKey_Delete": "Elimina",
  "GeoKey_Add": "Aggiungere a",
  "GeoKey_View": "Visualizza",
  "feedback_manager": "Gestione del feedback",
  "feedback_list": "Visualizza",
  "feedback_handle": "Elaborazione del feedback",
  "proclamat_manager": "Gestione degli annunci",
  "proclamat_manager_list": "Visualizza l'annuncio",
  "proclamat_manager_update": "Annuncio di modifica",
  "proclamat_manager_delete": "Elimina annuncio",
  "proclamat_manager_save": "Nuovo annuncio",
  "device_update_batch_model": "Modifica in batch il modello del dispositivo",
}
// Il contenuto del documento problematico
lg.questionDocumentArr = [
  [
    "Q: Dopo aver installato il dispositivo di cablaggio, la spia è spenta e non è in linea. ",
    "A:",
  ],
  [
    "Q: Dispositivi cablati o dispositivi di localizzazione wireless in tempo reale, chiamate telefoniche o dispositivo di stato di avvio in background di IoT offline ",
    "A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1.A: 1. Invia un messaggio di testo per riavviare, osserva alcuni minuti per vedere se è online. In genere, invia RESET # per favore contatta il rivenditore per determinare.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.La connessione di rete è instabile. Spostare l'automobile in un'area con un buon segnale.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Dopo che i passaggi precedenti non Q: Il dispositivo è offline in lotti all'inizio e alla fine del mese.,'A: Si prega di verificare se la carta è in arretrato.Se è arretrato, si prega di ricaricare in tempo e riprendere ad usarlo.verificare se la scheda è anormale.",
  ],
  [
    "Q: Il dispositivo è offline in lotti all'inizio e alla fine del mese.",
    "A:",
  ],
  [
    "Q: L'auto sta guidando, la posizione GPS online non è aggiornata.",
    "答：<br/>&nbsp;&nbsp;&nbsp;&nbsp;A: Dispositivo 1. cablaggio può inviare messaggi di testo di stato # visualizzare il segnale di ricezione satellitare di stato, vedi GPS: ricerca satellitare che è stato alla ricerca del segnale satellitare, questa situazione ha bisogno di controllare la posizione di montaggio, se v'è in conformità con le istruzioni necessarie per l'installazione. A faccia in su, non c'è una copertura metallica sulla parte superiore.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. SMS STATUS #, lo stato di ritorno è di GPS: Dopo OFF, quindi si prega di inviare FACTORY #, ricevere una risposta OK, osservare cinque minuti per vedere se una posizione di aggiornamento<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. In base ai due metodi precedenti, il guasto non può essere eliminato. Si prega di contattare il venditore per la riparazione.",
  ],
  [
    "Q: Perché la piattaforma di ricarica si carica da molto tempo e continua a mostrare che non è piena?",
    "A: La piattaforma di visualizzazione di potenza è di fare un dispositivo di analisi di dati sulla base delle informazioni retroazionato per determinare la corrente di carica di attrezzature, in alcuni casi particolari ci sarà potenza errore di visualizzazione Soluzione:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1, I dati di apparecchiature di alimentazione e dati posizione del dispositivo viene caricato insieme, se la carica per molto tempo, non si è verificato sostituzione della batteria, si prega di: ① portare il vostro dispositivo mobile 100-300 metri in posizione, in modo che le informazioni sulla posizione del dispositivo aggiornato per l'elettricità I dati e i dati di localizzazione possono essere ricondotti insieme alla piattaforma per essere un aggiornamento del display di alimentazione.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, l'indicatore di alimentazione viene determinato se un cambiamento a piena potenza, (ad esempio a S15) secondo le seguenti fasi: ① carica 8-10 ore, poi il verde giallo indicatore di alimentazione, scollegare il cavo di carico, è inserita la linea di carico, L'indicatore di alimentazione diventa giallo verde in 15 minuti per essere completamente carico, fare riferimento al manuale per altri modelli.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, è anche carica elettrica pienamente soddisfatto per lungo tempo, questa situazione può essere inferiore alla tensione di carica della spina 1A, per favore tensione 5V, 1A testa di carico per caricare 8-10 ore",
  ],
  [
    "Q: Il comando di interruzione dell'alimentazione GPS è stato emesso correttamente. Perché l'automobile non è ancora rotta?",
    "A: dopo che il comando power-off è stato emesso correttamente, l'apparecchiatura deve essere eseguita nelle seguenti condizioni:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Assicurarsi che il cablaggio dell'apparecchiatura sia corretto e seguire lo schema elettrico del manuale.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, Il dispositivo sta funzionando, guida, o in uno stato, posizionamento, non fuori-linea stazionaria, e la velocità del veicolo di non più di 20 chilometri, se il veicolo è spento, o il veicolo non è posizionato più di 20 chilometri all'ora, cioè, Il petrolio al largo della direttiva sull'energia elettrica emesso con successo, chiedere il terminale non viene eseguita: i primi tre anni di prodotti wireless installati, il dispositivo di visualizzazione non è posizionato o on-line",
  ],
  [
    "Q: Tre anni di prodotti wireless vengono installati per la prima volta, il dispositivo di visualizzazione non è posizionato o online ",
    "<br/>&nbsp;&nbsp;&nbsp;&nbsp;A: 1. Accendere l'interruttore per osservare se la luce dell'indicatore lampeggia. Gli indicatori giallo e verde lampeggiano contemporaneamente al normale, lampeggia lentamente nel segnale di ricerca e il dispositivo non è acceso. (Lo stato dei diversi modelli sarà diverso. Si prega di fare riferimento al manuale per altri modelli)<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, La spia non lampeggia sulla linea, se il segnale viene confrontato Se la differenza è attiva, si prega di accendere il segnale in una buona area. L'area del segnale buona non è online, può essere disattivata 1 minuto, reinstallare la scheda e avviare il test.",
  ],
  [
    "Q: Il prodotto via cavo viene installato per la prima volta e il dispositivo di visualizzazione non è posizionato..",
    "A: 1. Osservare se l'indicatore di stato GPS del terminale è normale, Controllare lo stato dell'indicatore in base alle istruzioni dei diversi modelli.。<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Se la spia è spenta, il dispositivo non è in grado di essere alimentato normalmente.<br/>&nbsp;&nbsp;&nbsp;&nbsp;L'indicatore 3, (giallo verde) della scheda non è acceso, si spegne e si reinstalla la scheda, quindi si accende per vedere che la luce normale è normale.<br/>&nbsp;&nbsp;&nbsp;&nbsp;4. Determinare se il numero della carta SIM nel dispositivo non è in ritardo e se la funzione di accesso a Internet GPRS è normale.<br/>&nbsp;&nbsp;&nbsp;&nbsp;5. Non c'è nessuna rete GSM nel luogo in cui si trova l'apparecchiatura, come la stanza inferiore, il tunnel, ecc., Dove il segnale è debole, si prega di aprire L'auto è testata in un buon posto coperto da GPRS.<br/>&nbsp;&nbsp;&nbsp;&nbsp;6, La posizione del posizionatore non deve essere troppo chiusa, non avere oggetti metallici, il più lontano possibile nella posizione di installazione dell'auto. Altrimenti influisce sulla ricezione del segnale.<br/>&nbsp;&nbsp;&nbsp;&nbsp;7, Avvio normale, interruzione nel segnale, area non in linea, è possibile riemettere il comando di linea per verificare se l'interfaccia IP e la rete di collegamento con scheda sono normali.",
  ],
  [
    "Q: Dopo aver installato le luci dei dispositivi cablaggio non sono luminose, la soluzione non in linea, dopo aver spento la macchina, penna e misuratore di potenza universale per misurare se la tensione di linea inseguitore GPS per auto collegato coincida con la gamma di tensione è tipicamente 9-36V.<br/>&nbsp;&nbsp;&nbsp;&nbsp;Precauzioni per il cablaggio:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Il personale addetto all'installazione e al cablaggio deve avere una conoscenza della linea della vettura e avere determinate capacità pratiche per evitare danni alla vostra auto causati da un cablaggio scorretto.",
  ],
  [
    "Dispositivo cablato o dispositivo di localizzazione wireless in tempo reale, il telefono è connesso o lo sfondo IoT è acceso e il dispositivo è offline.",
    "<br/>&nbsp;&nbsp;&nbsp;&nbsp;A: 1. Invia un messaggio di testo per riavviare, osserva alcuni minuti per vedere se è online. In genere, invia RESET # per favore contatta il rivenditore per determinare.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. La connessione di rete è instabile. Spostare l'automobile in un'area con un buon segnale.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Dopo i passaggi precedenti, non è stato possibile andare online. È necessario contattare l'operatore mobile per verificare se la scheda è anormale.",
  ],
  [
    "Q: L'attrezzatura è offline offline all'inizio e alla fine del mese ",
    " Soluzione: si prega di verificare se la carta è Gli arretrati sono in calo, se è arretrato, ricaricarlo in tempo e riprendere a usarlo.",
  ],
  [
    "Q: La corsa auto, posizione GPS non è aggiornamento on-line, ",
    "Soluzione: 1 dispositivo di connessione in grado di inviare messaggi di testo di stato # visualizzare il segnale di ricezione satellitare di stato, vedi GPS: ricerca satellitare che è stato alla ricerca del segnale satellitare, questa situazione ha bisogno di controllare Luogo di installazione, se è installato secondo le istruzioni. A faccia in su, non c'è una copertura metallica sulla parte superiore.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. SMS STATUS #, lo stato di ritorno è di GPS: Dopo OFF, quindi si prega di inviare FACTORY #, ricevere una risposta OK, osservare cinque minuti per vedere gli aggiornamenti di posizione.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. In base ai due metodi precedenti, il guasto non può essere eliminato. Si prega di contattare il venditore per la riparazione.",
  ],
  [
    "Q: Perché la piattaforma di ricarica si carica da molto tempo e continua a mostrare che non è piena? visualizzazione piattaforma potenza è di fare un dispositivo di analisi di dati sulla base delle informazioni retroazionato per determinare la corrente di carica di attrezzature, in alcuni casi particolari ci sarà errore di visualizzazione di potenza.",
    "Soluzione: 1, i dati di apparecchiature di potenza ei dati relativi all'ubicazione dispositivo è caricato insieme, se la carica per molto tempo, non si è verificato sostituzione della batteria, si prega di: ① portare il vostro dispositivo mobile 100-300 metri in posizione, cerchiamo di aggiornare il dispositivo informazioni sulla posizione, Crea i suoi dati e la posizione della batteria I dati possono essere rinviati alla piattaforma insieme per essere un aggiornamento del display di potenza;<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, l'indicatore di alimentazione viene determinato se un cambiamento a piena potenza, (ad esempio a S15) secondo le seguenti fasi: ① carica 8-10 ore, poi il verde giallo indicatore di alimentazione, scollegare il cavo di carico, è inserita la linea di carico, entro 15 minuti la spia di alimentazione diventa giallo e poi il verde è il pieno potere; altri modelli, vedere le istruzioni.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, è anche carica elettrica pienamente soddisfatto per lungo tempo, questa situazione può essere inferiore alla tensione di carica della spina 1A, per favore tensione 5V, ricarica testa 1A ricarica 8-10 ore.",
  ],
];
lg.webOptDoc =
  "<h1>一、新增用户帐号步骤</h1>" +
  "<ol>" +
  "<li>" +
  "1. Nel browser, inserisci la nostra piattaforma https://www.gpsnow.net, inserisci l'account del rivenditore per accedere all'interfaccia di accesso della piattaforma." +
  "</li>" +
  "<li>" +
  "2. Dopo aver effettuato l'accesso correttamente, verrà visualizzata la home page della piattaforma Fare clic su 'Il mio client' nella barra di navigazione in alto (come mostrato di seguito).”。" +
  "</li>" +
  "<li>" +
  "3. Seleziona un cliente nell'elenco di tutti i clienti, quindi seleziona Aggiungi per visualizzare la nuova interfaccia utente subordinata (come mostrato di seguito)." +
  '<img src="../../lib/document/cn/img/1.png"/>' +
  "</li>" +
  "<li>" +
  "4. Assegnare il tipo di utente in base alle esigenze dell'utente (controllare l'uso dell'autorizzazione professionale per visualizzare ulteriori informazioni per il dispositivo di progetto sensibile alla temperatura dell'olio S208)." +
  "</li>" +
  "<li>" +
  "5. Il nome del cliente può essere incoerente con l'account di accesso o lo stesso." +
  "</li>" +
  "<li>" +
  "6. Dopo aver inserito il punto informativo, il display è completato con successo." +
  "</li>" +
  "<li>" +
  "7. Dopo aver effettuato l'accesso con successo, vedrai la home page della piattaforma, fai clic sulla barra di navigazione in alto (come mostrato di seguito)Il mio cliente”" +
  "</li>" +
  "<li>" +
  "8. Dopo aver effettuato l'accesso con successo, vedrai la home page della piattaforma, fai clic sulla barra di navigazione in alto (come mostrato di seguito)Il mio cliente”" +
  "</li>" +
  "</ol>" +
  "<h1>二、Fase dell'attrezzatura di vendita</h1>" +
  "<ol>" +
  "<li>" +
  "1. Inserisci l'account utente subordinato nella casella di ricerca (come mostrato di seguito)" +
  "</li>" +
  "<li>" +
  "2. Seleziona l'account a sinistra e appare il pulsante destro del mouse." +
  '<img src="../../lib/document/cn/img/2.png"/>' +
  "</li>" +
  "<li>" +
  "3. Immettere un numero IMEI singolo o batch e immettere il numero IMEI batch." +
  '<img src="../../lib/document/cn/img/3.png"/>' +
  "</li>" +
  "<li>" +
  "4. Immettere il punto per confermare, quindi fare clic su Invia, il sistema richiede che la vendita venga completata correttamente." +
  "</li>" +
  "</ol>" +
  "<h1>三、Invia un nuovo account all'utente per accedere.Il browser apre </h1>" +
  "<ol>" +
  "<li>" +
  "1. https://www.gpsnow.net per accedere all'interfaccia di accesso, inserire la password dell'account (come mostrato di seguito)" +
  "</li>" +
  "<li>" +
  "2. Guida i clienti a utilizzare il codice QR di seguito, l'app Android o IOS della prossima settimana." +
  "</li>" +
  "<li>" +
  "3. Creare l'account e la password nel primo passaggio e inviarlo all'utente subordinato per accedere." +
  "</li>" +
  '<img src="../../lib/document/cn/img/4.png"/>' +
  '<img src="../../lib/document/cn/img/5.png"/>' +
  "<li>" +
  "Visualizzazione della pagina di monitoraggio della pagina Web dopo l'accesso" +
  "</li>" +
  "<li>" +
  "Visualizzazione della pagina di monitoraggio della pagina Web dopo l'accessoL'utente monitora qui il dispositivo, controlla le statistiche del dispositivo e le informazioni di allarme, controlla i dettagli del dispositivo e modifica i propri dati e la password di accesso." +
  "</li>" +
  "</ol>";
lg.appOptDoc = "Resta sintonizzato ...";
// Guida ai parametri di query
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Password del terminale di query</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push(
  "<td>Interrogare il numero della carta SIM incorporata nel terminale</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Controlla il numero di cellulare del proprietario</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push("<td>Query valore limite velocità allarme velocità eccessiva</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Interroga la frequenza dei rapporti dopo l'avvio del monitoraggio, in secondi</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push(
  "<td>Chiedi se abilitare il tracciamento, 1 è abilitare il tracciamento, 0 è disattivare il tracciamento</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push(
  "<td>Interrogare l'intervallo di giudizio dell'allarme di spostamento non valido, l'unità è il metro</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>Chiedere se attivare l'allarme SMS vibrazione, 1 è abilitato per disattivare 0"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>La sensibilità alla vibrazione delle query 0 ~ 15, 0 è la sensibilità massima, troppo alta può essere falsa positiva, 15 è la sensibilità più bassa</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>Chiedere se attivare l'allarme del telefono a vibrazione, 1 è abilitato per disattivare 0"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Funzione di filtro delle query GPS drift è acceso, 1 è spento è 0, se l'allarme è attivata la vibrazione non si verifica entro 5 minuti, poi si ferma, tutto escursione GPS del filtro</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push(
  "<td Funzione sleep Query è acceso, uno 0 è spento, la vibrazione è attivata se l'allarme non si verifica entro 30 minuti, in letargo, scissione a catena GPS per risparmiare energia</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Chiedere se disattivare la funzione di allarme di interruzione di corrente, 1 è quello di attivare 0 per spegnere</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">GPS:</td>');
html.push(
  "<td>Interrogare il numero e l'intensità del satellite ricevuti dal GPS, ad esempio: 2300 1223 3431. . . Un totale di 12 a quattro cifre, 2300 indica che la potenza del segnale ricevuto di un numero del satellite 23 00,1223 indica che l'intensità del segnale ricevuto del satellite numerata 12 a 23</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VBAT:</td>');
html.push(
  "<td>Interrogare la tensione della batteria, la tensione di interfaccia di carico, corrente di carica, ad esempio: VBAT = 3713300: 4960750: 303.500 indica la tensione della batteria 3713300uV, cioè 3.71v, la tensione di carica di 4.96V, corrente di carica 303mA</td>"
);
html.push("</tr>");
html.push("</table>");
lg.queryparamhelp = html.join("");

// Aiuto con l'impostazione dei parametri
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push(
  "<td>Impostare la password del terminale, la password può essere solo di 6 cifre</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>Impostare il numero della carta SIM nel terminale</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Imposta il numero di cellulare del proprietario</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push(
  "<td>Impostare il valore limite di velocità di allarme velocità eccessiva, l'intervallo dovrebbe essere compreso tra 0 ~ 300</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Imposta la frequenza dei rapporti dopo l'attivazione del monitoraggio, in secondi.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push(
  "<td>Attivare o disattivare il monitoraggio, la traccia 1 è acceso, 0 è fuori pista</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push(
  "<td>Impostare il campo di giudizio degli allarmi di spostamento non valido, l'unità è il metro</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>Impostare se abilitare l'allarme SMS vibrazione, 1 è abilitato per disattivare 0"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Impostare la sensibilità alla vibrazione 0 ~ 15, 0 è la sensibilità massima, troppo alta può essere falsa positiva, 15 è la sensibilità più bassa</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>Impostare se abilitare l'allarme vibrazione del telefono, 1 è attivato 0 è spento"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>设置是否开启GPS过滤漂移功能,1为开启0为关闭,如果开启则防盗器在5分钟内没有发生振动,则进入静止状态,过滤所有GPS漂移点</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Filtro impostare se la funzione deriva GPS, uno 0 è disattivata, la vibrazione viene attivata se l'allarme non avviene entro 5 minuti, poi viene fermato, tutti escursione GPS del filtro</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Impostare se abilitare la funzione di sospensione, 1 è abilitato per disattivare 0, se è attivato</td>"
);
html.push("</tr>");
html.push("</table>");
lg.setparamhelp = html.join("");

//Bulk add
html = [];
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox" ' +
    'style="z-index: 999;position:absolute;left:215px;top:88px;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Aggiungi:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkAdds_treeDiv" +
    "," +
    "bulkAdds_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Piattaforma:</td>'
);
html.push("<td>");
html.push(
  '<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Modello di equipaggiamento:</td>'
);
html.push("<td>");
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    "</div>" +
    '<div id="deviceList"></div>' +
    "</div>" +
    "</span>"
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Aggiungi dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push(
  '<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>'
);
lg.bulkAdds = html.join("");

//Rinnovo in lotti
html = [];
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Aggiungi dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="re_addNumBox">corrente：<span id="account_re_addNum">0</span>'
);
html.push("</span>");
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<tr>");
html.push(
  '<td style="text-align:right;"><span style="color:red">*</span>Tipo di carta</td>'
);
html.push("<td>");
html.push('<input  type="radio" name="red_cardType"');
html.push(
  'class="easyui-validatebox"  value="3" checked><label>Annuale</label></input>'
);
html.push(
  '<input  type="radio" name="red_cardType" style="margin-left:15px;" '
);
html.push(
  'class="easyui-validatebox" value="4"><label>Per tutta la vita</label></input>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td style="text-align: right">Punti di penalizzazione</td>');
html.push("<td>");
html.push(
  '<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Utente:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Osservazione</td>'
);
html.push("<td>");
html.push(
  '<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkRenew = html.join("");

// //Vendite di massa，myAccount
html = [];
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel treePulldownBox"  style="z-index: 999;position:absolute;left:202px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Venduto a:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkSales_treeDiv" +
    "," +
    "bulkSales_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push(
  '<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Utente:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Aggiungi dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="bs_addNumBox">corrente：<span id="account_bs_addNum">0</span>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="bs_reset" class="swd-gray-btn" title="' +
    lg.reset +
    '"  style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");

lg.bulkSales = html.join("");

//Trasferimento in gruppo，弹出框
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:127px;top:172px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Cliente target:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Aggiungi dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >Bulk add</a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Metastasi</a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)">Annullato</a>'
);

lg.bulkTransfer = html.join("");

//Trasferimento batch 2,myClient
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:117px;top:84px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Cliente target:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Aggiungi dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bt_addMachines" style="cursor:pointer" title="' +
    lg.addTo +
    '" src="../../images/main/myAccount/add3.png" />'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);

lg.bulkTransfer2 = html.join("");

//Trasferisci gli utenti in blocco
html = [];
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:151px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Cliente target:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">'
);
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("<td></td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");

lg.bulkTransferUser = html.join("");
window.lg = lg