var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
var lg = {
  //西班牙语
  //common common
  user_guide: 'Guía del usuario',
  remoteSwitch: "Interruptor remoto",
  pageTitle:
    "WhatsGPS Global Tracking System|Vehicle GPS Tracker|3G Tracker|mini 4G Tracker|GPSNow|Car Locator",
  description:
    site+" is dedicated to providing users with intelligent cloud location services. It is the world's leading location service platform.",
  pageLang: "Español",
  inputCountTips: "Numero de cuenta/IMEI",
  inputPasswordTips: "Contraseña",
  appDownload: "Descarga de APP",
  rememberPassword: "Recuerda contraseña",
  forgetPassword: 'Has olvidado tu contraseña',
  siteName: "WhatsGPS",
  noToken: " Por favor envia token",
  loginFirst: "Registrese primero",
  move: "Movimiento",
  stop: "Estático",
  query: "Comprobar",
  imeiQuery: "IMEI",
  delete: "Borrar",
  update: "actualizar",
  cancel: "Cancelar",
  soft: "No.",
  more: "Mas",
  useful:'servicial',
  useless:'inútil',
  about:'About',
  replyFeedback:'Comentarios sobre "$"',
  edit: "Editar",
  add: "Añadir",
  addTo: "Añadir",
  addDevice: "Add device",
  machineName: "Nombre del dispositivo",
  searchDevice: "Dispositivo",
  date: "Fecha Hora",
  LatestUpdate: "actualizar",
  engine: "ACC",
  locTime: "GPS tiempo",
  locType: "Tipo de ubicación",
  startLoc: "Inicio Ubicacion",
  endLoc: "Final Ubicacion",
  address: "Habla a",
  noAddressTips: "No se puede obtener información de la dirección",
  lonlat: "Latitud y longitud ",
  carNO: "Número de placa",
  imei: "IMEI",
  IMEI: "IMEI",
  simNO: "Tarjeta SIM",
  activeTime: "El tiempo de activado",
  expireTime: "Expiró el tiempo",
  acceptSubordinateAlarm: "Accept Subordinate Alarm",
  acceptAlarmTips1: "After the check,",
  acceptAlarmTips2:
    "You will receive the device alarm information of all subordinate customers",
  speed: "Velocidad",
  y: "A?os",
  M: "Meses",
  d: "Dias",
  h: "Horas",
  min: "Minutos",
  s: "Segundos",
  _year: "y",
  _month: "m",
  _day: "d",
  _hour: "h",
  _minute: "m",
  _second: "s",
  confirm: "Confirmar",
  yes: "Sí",
  car: "Auto",
  not: "No",
  m: "Metros",
  account: "Cuenta",
  psw: "La contraseña",
  save: "Guardar",
  operator: "Operar",
  queryNoData: "No hay datos",
  name: "Nombre",
  type: "Modelo",
  open: "Abrir",
  close: "Cerca",
  send: "Enviar",
  alarm: "Alarma",
  alarmSetting: "Ajustes de alarma",
  look: "Vista",
  tailAfter: "Seguimiento",
  history: "Reproducción",
  dir: "Dirección",
  locStatus: "Estado de ubicacion",
  machineTypeText: "Modelo",
  carUser: "Auto usuario",
  machine: "Equipo",
  unknowMachineType: "Equipo desconocido",
  noCommandRecord: "Las órdenes no sirven para este equipo",
  type1: "Tipo",
  role: "Papel",
  roles: "Papel",
  timeType: "Tiempo de tipo",
  moveSpeed: "Velocidad de movimiento",
  signal: "La señal",
  loc: "Posición",
  wiretype: "Tipo",
  wire: "Por",
  wireless: "Inalámbrico",
  expire: "Caducado",
  hour: "Horas",
  hourTo: "Hora",
  remark: "Observación",
  remarkInfo: "Observación",
  noPriviledges: "La cuenta no tiene operación privilegios",
  commandNoOpen:
    "El actual dispositivo de mando aún no está abierto para el uso",
  choseDelelePhone: "Por favor, seleccione el número de borrar primero",
  streetView: "Vista de la calle",
  wrongFormat: "Error de formato de entrada",
  inputFiexd: "Entrada número fijo",
  serialNumberStart: "El primer número consecutivo de IMEI",
  serialNumberEnd: "El último número consecutivo de IMEI",
  clickSearchFirst: "Por favor haga clic en buscar dispositivo primero!",
  isDeleteDevice:
    "El dispositivo no puede ser eliminado después de ser eliminado. Es imprescindible?",
  //平台错误代码提示
  errorTips: "Operación fallado con codigo de error：",
  error10003: 'Contraseña incorrecta',
  error90010: '¡El dispositivo está fuera de línea y falló el envío del comando personalizado!',
  error70003: 'El valor del control remoto no puede estar vacío',
  error70006: 'No apoya o no tiene derecho a emitir la instrucción.',
  error20001: 'La identificación del vehículo no puede estar vacía',
  error20012: 'El vehículo no está activado',
  error10012: "El viejo error de contrase?a",
  error10017: "Eliminar no, por favor, suprímase el usuario!",
  error10023: "Eliminar no, el usuario dispone de dispositivo",
  error20008: "A?adir no, IMEI ya existe",
  error20006: "Por favor ingrese un IMEI de duracion 15",
  error10019: "Formato incorrecto de telefono de contacto",
  error10024: "No repitas la venta",
  error120003: "Compartir enlaces Desactivado",
  error10025:
    "La información sobre el dispositivo modificado no puede estar vacía",
  error2010: "Por favor, envíe el archivo",
  error20002: "No hay número IMEI",
  error10081: "Número insuficiente de tarjetas de renovación.",
  error10082: 'No es necesario recargar el dispositivo de por vida',
  error3000: 'El rol se ha asignado a la cuenta del sistema y no se puede eliminar',
  error103: 'La cuenta ha sido deshabilitada, comuníquese con su proveedor de servicios.',
  error124: 'No puede funcionar por sí mismo',
  // 登陆相关 login.js
  logining: "INICIAR SESIÓN ...",
  login: "INICIAR SESIÓN",
  userEmpty: "Usuario en blanco",
  pswEmpty: "La contraseña está vacía",
  prompt: "Recordatorio",
  accountOrPswError: "Error de cuenta o contrase?a",
  UserNameAlreadyExist: "La cuenta de usuario ya ha existido",
  noQualified: "No existe la información cualificada ",
  //main.js
  systemName: "WhatsGPS",
  navTitle_user: ["Monitor", "Informe", "Dispositivo"],
  navTitle_dealer: ["INICIO", "Cliente", "Monitor", "Más operaciones"],
  exitStytem: "Salida",
  user: "Usuario",
  UserCenter: "Centro de usuarios",
  alarmInfo: "Alarm",
  confirmExit: "Confirman salida?",
  errorMsg: "Mensaje de error: ",
  logintimeout: "Login tiempo fuera!",
  clearAlarm: "Claro",
  clear: "Clear",
  searchbtn: "Usuario",
  print: "Imprimir",
  export: "Exportación",
  //feedback
  feedback: "Sugerencias",
  feedback_sublime: "Enviar",
  alerttitle: "El título no puede estar en blanco!",
  alertcontent: "La retroalimentación puede estar vacío!",
  submitfail: "La presentación no！",
  saveSuccess: "Salvar a éxito！",
  submitsuccess:
    "Presentado con éxito! Procesaremos su información tan pronto como sea posible~",
  adviceTitle: "Título",
  adviceTitle_p: "Titulo de pregunta y de opinión",
  adviceContent: "Preguntas y opiniones",
  adviceContent_p:
    "Describir brevemente las preguntas y comentarios que desea información, Y vamos a seguir mejorando para usted.",
  contact: "Información de contacto",
  contact_p: "Llenar en su telefono o email",
  //monitor.js
  myMachine: "Equipo",
  all: "Todos",
  online: "En línea",
  offline: "Desconectado",
  unUse: "Sin usar",
  group: "Grupo",
  moveGruop: "Mover al grupo",
  arrearage: "Morosidad",
  noStatus: "No Estado",
  inputMachineName: "Equipo/IMEI",
  defaultGroup: "Aquiescencia",
  offlineLessOneDay: "Desconectado a menos de un día",
  demoUserForbid:
    "La experiencia de los usuarios no pueden usar esta caracteristica",
  shareTrack: "Compartir",
  shareName: "Nombre",
  liveShare: "Pista en tiempo real de intercambio",
  expiration: "Fecha de caducidad",
  getShareLink: "Crear enlace",
  copy: "Copia",
  copySuccess: "Exitosa copia!",
  enlarge: "Agrandar",
  shareExpired: "Compartir referencia vencida",
  LinkFailure: "Referencia Abierto fallado",
  inputShareName: "La cuota de entrada un nombre",
  inputValid: "Por favor, introduzca la correcta falta tiempo",
  //statistics.js
  runOverview: "Mover la vista",
  runSta: "Mover la vista",
  mileageSta: "Informe sobre kilometraje",
  tripSta: "Informe de viaje",
  overSpeedDetail: "Detalles de velocidad",
  stopDetail: "Estancia detalles",
  alarmSta: "Informe de la alarma",
  alarmOverview: "Alarma general",
  alarmDetail: "Detalles de la alarma",
  shortcutQuery: "La consulta",
  today: "Hoy",
  yesterday: "Ayer",
  lastWeek: "La semana pasada",
  thisWeek: "Esta semana",
  thisMonth: "Este mes",
  lastMonth: "El mes pasado",
  mileageNum: "Kilometraje(Km)",
  overSpeedNum: "Exceso de velocidad(km/h)",
  overSpeed: "Exceso de velocidad",
  stopTimes: "Estancia(Times)",
  searchMachine: "Search",
  speedNum: "Velocidad(km/h)",
  querying: "Querying",
  stopTime: "Tiempo de estática",
  HisToryStopTime: "Tiempo de estática",
  clickLookLoc: "Haga clic para ver la direccion",
  lookLoc: "Query ubicacion",
  noData: "No hay datos",
  alarmTime: "La hora de la alarma",
  vibrationLevel: "Nivel de vibraciones",
  vibrationWay: "Tipo de alarma",
  acc: "ACC",
  accStatistics: "Estadísticas del ACC",
  accType: ["Todos", "ACC On", "ACC Off"],
  accstatus: ["on", "off"],
  openAccQuery: "La consulta abierta ACC",
  runtime: "Tiempo de funcionamiento",
  //监控页面修改
  run: "Viajar",
  speed: "La velocidad",
  //设备管理
  machineManage: "Dispositivos",
  deviceTable: "Mis metas",
  status: "Situación",
  havaExpired: "Han caducado",
  expiredIn60: "Expiró en 60",
  expiredIn7: "Expiró en 7",
  normal: "Nomal",
  allMachine: "Todos",
  allMachine1: "Todos los dispositivos",
  expiredIn7Machine: "Expiró en 7 days",
  expiredIn60Machine: "Expiró en 60 days",
  havaExpiredMachine: "Todo el dispositivo",

  //history.js
  replay: "Jugar",
  replaytitle: "Replay",
  choseDate: "Selecciona la fecha",
  from: "A partir de",
  to: "A",
  startTime: "Hora de inicio",
  endTime: "Hora de cerrar",
  pause: "Pausa",
  slow: "Lento",
  mid: "Middle",
  fast: "Rápido",
  startTimeMsg: "Hora de inicio mensaje",
  endTimeMsg: "Tiempo final Mensaje",
  smallEnd:
    "Tiempo final a menos de hora de inicio, Por favor, elige de nuevo!",
  bigInterval: "Intervalo de tiempo de menos de 31 dias!",
  trackisempty: "Pista vacia",
  longitude: "Primaria",
  latitude: "Latitude",
  direction: "Dirección",
  stopMark: "Estancia Mark",
  setStopTimes: [
    {
      text: "1 Minuto",
      value: "1",
    },
    {
      text: "2 Minuto",
      value: "2",
    },
    {
      text: "3 Minuto",
      value: "3",
    },
    {
      text: "5 Minuto",
      value: "5",
    },
    {
      text: "10 Minuto",
      value: "10",
    },
    {
      text: "15 Minuto",
      value: "15",
    },
    {
      text: "20 Minuto",
      value: "20",
    },
    {
      text: "30 Minuto",
      value: "30",
    },
    {
      text: "45 Minuto",
      value: "45",
    },
    {
      text: "1 Horas",
      value: "60",
    },
    {
      text: "6 Horas",
      value: "360",
    },
    {
      text: "12 Horas",
      value: "720",
    },
  ],
  filterDrift: "Eliminar la deriva ubicacion",
  userType: [
    "Administrador",
    "Distribuidor",
    "Usuarios finales",
    "Logística",
    "Alquiler",
    "Auto usuario",
    "Control de riesgos",
    "Profesional",
  ],
  userTypeArr: [
    "Administrador",
    "Distribuidor",
    "Usuarios finales",
    "Logistics",
    "Alquiler",
    "Car user",
    "Profesional",
  ],
  machineType: {
    '0': 'Tipo de dispositivo',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '83':'S03A',
    '84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: [
    "Tipo de alarma",
    "Alarma de vibracion",
    "Apague la alarma",
    "Alarma de batería baja",
    "SOS de alarma",
    "Alarma de exceso de velocidad",
    "Alarma de la GEO-cerca",
    "Desplazamiento de alarma",
    "Alarma de bateria baja externa",
    "Fuera de la zona de alarma",
    "Desmonte la alarma",
    "Alarma de sensor de luz",
    "Alarma de deteccion magnetica",
    "Dismantal alarma",
    "Alarma de Bluetooth",
    "Blindaje de se?al de alarma",
    "Estación base falsa alarma",
    "GEO-cerca en alarma",
    "GEO-cerca en alarma",
    "Alarma de la GEO-cerca",
    "Alarma de puerta abierta",
    "Fatiga conduciendo",
    "Punto de entrada hipoteca",
    "Punto de salida de la hipoteca",
    "Punto de hipotecario estancia",
    "Terminal offline",
    "GEO-cerca en alarma",
    "Alarma de la geo-cerca",
    "GEO-cerca en alarma",
    "Alarma de la geo-cerca",
    "Alarma de combustible",
    "ACC ON Alarma",
    "ACC OFF Alarma",
    "Alarma de colisión",
  ],
  alarmTypeNew:  {
    '40': 'Alarma de alta temperatura',
    '45': 'Alarma de baja temperatura',
    '50': 'alarma de sobretensión',
    '55': 'alarma de subtensión',
    '60': 'Alarma de estacionamiento'
  },
  alarmNotificationType: [
    { type: "Alarma de vibracion", value: 1 },
    { type: "Apague la alarma", value: 2 },
    { type: "Alarma de batería baja", value: 3 },
    { type: "SOS de alarma", value: 4 },
    { type: "Alarma de exceso de velocidad", value: 5 },
    // {type:'Geocerca alarma',value:6},
    { type: "Desplazamiento de alarma", value: 7 },
    { type: "Alarma de bateria baja externa", value: 8 },
    { type: "Fuera de la zona de alarma", value: 9 },
    { type: "Desmonte la alarma", value: 10 },
    { type: "Alarma de sensor de luz", value: 11 },
    { type: "Dismantal alarma", value: 13 },
    { type: "Blindaje de se?al de alarma", value: 15 },
    { type: "Estación base falsa alarma", value: 16 },
    // {type:'Geo - Fence en alarma',value:17},
    // {type:'Geo - Fence en alarma',value:18},
    // {type:'Alarma de la geo - cerca',value:19},
    { type: "Fatiga conduciendo", value: 21 },
    { type: "Punto de entrada hipoteca", value: 22 },
    { type: "Punto de salida de la hipoteca", value: 23 },
    { type: "Punto de hipotecario estancia", value: 24 },
    { type: "Terminal offline", value: 25 },
    // {type:'Geo - Fence en alarma (Risk control)',value:26},
    // {type:'Alarma de la geo - cerca(Risk control)',value:27},
    { type: "Geo - Fence en alarma", value: 26 },
    { type: "Alarma de la geo - cerca", value: 27 },
    { type: "Alarma de combustible", value: 30 },
    { type: "ACC ON Alarma", value: 31 },
    { type: "ACC OFF Alarma", value: 32 },
    { type: "Alarma de colisión", value: 33 },
  ],
  alarmTypeText: "Tipo de alarma",
  alarmNotification: "Notificación",
  pointType: [
    "Tipo de Localizacion",
    "Localizacion por satelite",
    "Compass Localizacion",
    "LBS Ubicacion",
    "WIFI Ubicacion",
  ],

  cardType: [
    "Tipo",
    "Tarjetas de importación",
    "Tarjetas de importación para toda la vida",
    "Tarjetas de importación",
    "La tarjeta de toda la vida",
  ],
  // 东南西北
  directarray: ["Este", "Sur", "Occidente", "Norte"],
  // 方向字段
  directionarray: [
    "Debido al norte",
    "Northeast",
    "Por el este",
    "Sureste",
    "Debido al sur",
    "Al suroeste de",
    "Due Occidente",
    "Noroeste",
  ],
  // 定位方式
  pointedarray: ["Undefined", "GPS", "LAC", "LAC Ubicacion", "WIFI Ubicacion"],

  //map Relevant
  ruler: "Regla",
  distance: "Tráfico",
  baidumap: "Baidu Mapa",
  map: "Mapa",
  satellite: "Satellite",
  ThreeDimensional: "3D",
  baidusatellite: "Baidu satellite",
  googlemap: "Google Mapa",
  googlesatellite: "Google satellite",
  fullscreen: "Pantalla completa",
  noBaidumapStreetView: "Ubicacion Actual en Baidu maps sin vista a la calle",
  noGooglemapStreetView: "Ubicacion Actual en Google Maps sin vista a la calle",
  exitStreetView: "La salida de Street View",
  draw: "Draw",
  finish: "Terminar",
  unknown: "Desconocido",
  realTimeTailAfter: "Rastreo en tiempo real",
  trackReply: "La historia de reproduccion",
  afterRefresh: "Renovar",
  rightClickEnd: "Haga clic derecho en fin ,Radio：",
  rightClickEndGoogle:
    "Haga clic derecho en fin------------------------Radio：",

  //tree Relevant
  currentUserMachineCount: "Usuario actual dispositivo contar",
  childUserMachineCount: "Ni?o dispositivo de usuario Conde",

  //Window relevant

  electronicFence: "Geo Fence",
  drawTrack: "Conjunto GEO - Fence",
  showOrHide: "Show/Ocultar",
  showDeviceName: "Mostrar nombre de dispositivo",
  circleCustom: "Definido por el usuario",
  circle200m: "Diámetro de 200 metros",
  polygonCustom: "Poligono la definicion",
  drawPolygon: "Saca un polygon",
  drawCircle: "Dibujar un círculo",
  radiusMin100:
    "The drawn fence radius is at least 20 meters. Please redraw it. Current fence radius:",
  showAllFences: "Muéstrale a todos los fences",
  lookEF: "Mira valla",
  noEF: "No Geocerca datos detectados",
  hideEF: "Ocultar Geocerca",
  blockUpEF: "Cerca cerca",
  deleteEF: "Eliminar la valla",
  isStartUsing: "Si para que",
  startUsing: "Permitir",
  stopUsing: "Desactivar",
  nowEFrange: "La gama actual de valla",
  enableSucess: "Que exito",
  unableSucess: "Desactivar el éxito",
  sureDeleteMorgage: "Eliminar punto de hipoteca",
  enterMorgageName: "Introduzca nombre del punto de Morgage",
  openMorgagelongStayAlarm: "Empezar a mortgage Point stay",
  openMorgageinOutAlarm: "Inicio de entrada y salida punto de hipoteca",
  setEFSuccess: "Set Geocerca con éxito",
  setElectronicFence: "Conjunto GEO-Fence",
  drawFence: "Llamar la valla",
  drawMorgagePoint: "Draw Hipoteca Punto",
  customFence: "Valla personalizacion",
  enterFenceTips: "Entrar en la alarma",
  leaveFenceTips: "Deja la alarma",
  inputFenceName: "Pls nombre entrada parcela",
  relation: "Bundle",
  relationDevice: "Dispositivo asociado",
  unRelation: "No Asociado",
  hadRelation: "Ya asociados",
  quickRelation: "Uno haga clic en paquete",
  cancelRelation: "Cancelar Bundle",
  relationSuccess: "Exitosa Bundle",
  cancelRelationSuccess: "Cancelar el éxito",
  relationFail: "Cancelar no",
  deviceList: "Lista de dispositivos",
  isDeleteFence: "Si eliminar la valla",
  choseRelationDeviceFirst:
    "Por favor, seleccione primero el dispositivo para ser asociado!",
  choseCancelRelationDeviceFirst:
    "Pls Por favor, seleccione primero el dispositivo para no asociados!",
  selectOneTips: "Please Por favor, seleccione al menos un método de alarma",
  radius: "Diámetro",
  //设置二押点页面
  setMortgagePoint: "Establecer un punto de hipoteca",

  circleMortage: "Círculo hipoteca Point",
  polygonMorgage: "Poligono morgage Point",
  morgageSet: "Establecimiento de un punto de hipoteca",
  operatePrompt: "Operar pronto",
  startDrawing: "Haga clic para empezar a dibujar",
  drawingtip1:
    "Haga clic izquierdo del raton para empezar a dibujar, hacer doble clic para terminar el dibujo",
  drawingtip2: "Clic con el botón izquierdo del ratón y arrastre para dibujar",

  /************************************************/
  endTrace: "Final trace",
  travelMileage: "Viajes kilometraje",
  /************************************************/
  myAccount: "Cuenta",
  serviceProvide: "Proveedor",
  completeInfo:
    "Información completa, tales como contactos, número de teléfono",
  clientName: "Nombre del cliente",
  loginAccount: "Cuenta",
  linkMan: "Contacto",
  linkPhone: "teléfono/Mob",
  clientNameEmpty: "Nombre de cliente vacia!",
  updateSuccess: "Actualizar el éxito!",
  /************************************************/
  oldPsw: "Antiguo código",
  newPsw: "Nuevo código",
  confirmPsw: "Confirmar la contraseña",
  pswNoSame: "Contraseña inconsistente",
  pswUpdateSuccess: "El éxito de Generador de actualizacion!",
  email: "Dirección de correo electrónico",
  oldPwdWarn: "Por favor, introduzca la contrase?a antigua",
  newPwdWarn: "Por favor, introduzca una nueva contrase?a",
  pwdConfirmWarn: "Por favor confirmar nueva contrase?a",
  /************************************************/
  //Custom popup components
  resetPswFailure: "Generador de no descansar",
  notification: "Notificación",
  isResetPsw_a: "Seguro para resetear la contrase?a?",
  isResetPsw_b: "`s Contrase?a?",
  pwsResetSuccess_a: "La contraseña ha sido reprogramada",
  pwsResetSuccess_b: "`s password to 123456",
  /************************************************/
  machineSearch: "Informacion del dispositivo",
  search: "Search",
  clientRelation: "La relacion del cliente",
  machineDetail: "Detalles",
  machineDetail2: "Detalles",
  machineCtrl: "Instrucción",
  transfer: "Movimiento",
  belongCustom: "Pertenecen al cliente",
  addImeiFirst: "Añadir IMEI primero!",
  addUserFirst: "Añadir usuario!",
  transferSuccess: "Mover el éxito!",
  multiAdd: "Lote agregar",
  multiImport: "Importar",
  multiRenew: "Renovación de lotes",
  //批量修改设备begin
  editDevice:'Modificar modelo de dispositivo',
  deviceAfter: 'Modelo de dispositivo (después de la modificación)',
  editDeviceTips:'¡Confirme que el dispositivo que se va a modificar es el mismo modelo y no está activo!',
  pleaseChoseDevice: '¡Primero seleccione el dispositivo que desee modificar!',
  editResult:'Editar resultado',
  successCount:'Dispositivo modificado con éxito:',
  failCount:'Dispositivos fallidos:',
  //批量修改设备end
  multiDelete: "Lote borrar",
  canNotAddImei: "IMEI no existe, no puede añadir IMEI",
  importTime: "Tiempo de importación",
  loginName: "Cuenta",
  platformDue: "Plataforma debido",
  machinePhone: "La tarjeta de SIM",
  userDue: "Usuario gracias",
  overSpeedAlarm: "Alarma de exceso de velocidad",
  changeIcon: "Icono",
  dealerNote: "Observación",
  noUserDue: "Ningun usuario debido",
  phoneLengththan3: "Diferente duracion debe ser mayor que 3",
  serialNumberInput: "Número de serie de entrada",

  /************************************************/
  sending: "Enviar.....Por favor, espere...",
  sendFailure: "Enviar falla",
  ctrlName: "Nombre",
  interval: "Intervalo de tiempo",
  intervalError: "Interval format error",
  currectInterval: "Introduzca el intervalo de tiempo correcto.",
  intervalLimit: "Intervalo de tiempo rango 10-720,(Minuto)",
  intervalLimit2: "Intervalo de tiempo rango 10-5400,(Segundo)",
  intervalLimit3: "Intervalo de tiempo rango 5-1440,(Minuto)",
  intervalLimit4: "Intervalo de tiempo rango 3-999,(Segundo)",
  intervalLimit5: "Intervalo de tiempo rango 10-10800,(Segundo)",
  intervalLimit1:
    "Establecer los intervalos de tiempo como 000，Significa cerrar el modo de reback",
  intervalLimit6: "Intervalo de tiempo rango 1-65535,(Minuto)",
  intervalLimit7: "Intervalo de tiempo rango 1-999999,(Segundo)",
  intervalLimit8: "Intervalo de tiempo rango 0-255, 0 significa cerrar",
  intervalLimit9: "Intervalo de tiempo rango 3-10800,(Segundo)",
  intervalLimit10: "Intervalo de tiempo rango 3-86400,(Segundo)",
  intervalLimit11: "Intervalo de tiempo rango 180-86400,(Segundo)",
  intervalLimit22: "Intervalo de tiempo rango 60-86400,(Segundo)",
  intervalLimit23: "Intervalo de tiempo rango 5-60,(Segundo)",
  intervalLimit24: "Intervalo de tiempo rango 10-86400,(Segundo)",
  intervalLimit25: "Intervalo de tiempo rango 5-43200,(Minuto)",
  intervalLimit12: "Intervalo de tiempo rango 10-60,(Segundo)",
  intervalLimit13:
    "Establecer intervalo de tiempo de intervalo 1-24 (significa 1-24 horas) o 101-107 (significa 1-7 días)",
  intervalLimit14:
    "Establecer intervalo de tiempo de intervalo 10-3600, unidad (segundo); predeterminado: 10s",
  intervalLimit15:
    "Establecer intervalo de tiempo de intervalo 180-86400, unidad (segundo); predeterminado: 3600s",
  intervalLimit16:
    "Establecer intervalo de tiempo de intervalo 1-72, unidad (hora); predeterminado: 24 horas",
  intervalLimit17: "Rango de temperatura de ajuste -127-127, unidad (° C)",
  intervalLimit18:
    "Establecer intervalo de tiempo de intervalo 5-18000, unidad (segundo)",
  intervalLimit19:
    "Establecer intervalo de tiempo de intervalo 10-300, unidad (segundo)",
  intervalLimit20:
    "Establecer intervalo de tiempo de intervalo 5-399, unidad (segundos)",
  noInterval: "Por favor, ingrese los intervalos de tiempo!",
  intervalLimit21:
    "Establecer intervalo de tiempo de intervalo 5-300, unidad (segundos)",
  noInterval: "Por favor, ingrese los intervalos de tiempo!",
  intervalTips:
    "Apagar el modo de seguimiento mediante el establecimiento de la hora de alarma despertador",
  phoneMonitorTips:
    "Después de que se envíe el comando, el dispositivo marcará activamente el número de devolución de llamada para la supervisión",
  time1: "Tiempo 1",
  time2: "Tiempo 2",
  time3: "Tiempo 3",
  time4: "Tiempo 4",
  time5: "Tiempo 5",
  intervalNum: "Intervalo(minuto)",
  sun: "Domingo",
  mon: "Lunes ",
  tue: "Martes",
  wed: "Miércoles",
  thu: "Jueves",
  fri: "Viernes",
  sat: "Sábado",
  awakenTime: "Despertar a tiempo",
  centerPhone: "Center diferente",
  inputCenterPhone: "Entrada centro diferente!",
  phone1: "Número 1",
  phone2: "Número 2",
  phone3: "Número 3",
  phone4: "Número 4",
  phone5: "Número 5",
  inputPhone: "Entrada diferente",
  offlineCtrl:
    "Offline Ctrl，Será enviado al device después de que el devisor esté en línea.",
  terNotSupport: "Apoyo de terminal no",
  terReplyFail: "Respuesta no terminal",
  machineInfo: "Informacion del dispositivo",

  /************************************************/
  alarmTypeScreen: "Tipo de alarma pantalla",
  allRead: "Todos leer",
  read: "Leer",
  noAlarmInfo: "La información de alarma no canjeable",
  alarmTip: "Punta: Filtrar este tipo de información de alarma por unchecked",

  /************************************************/
  updatePsw: "Contrase?a",
  resetPsw: "Restablecer la contrase?a",

  /************************************************/
  multiSell: "Venta por lotes",
  sell: "Enviar",
  sellSuccess: "Vender con éxito!",
  modifySuccess: "Modificación exitosa",
  modifyFail: "Modificación fallida",
  multiTransfer: "Lote mover",
  multiUserExpires: "Users batch modification expires",
  batchModifying: "Modificación del lote",
  userTransfer: "Mover",
  machineRemark: "Observación",
  sendCtrl: "Enviar la orden",
  ctrl: "Comando",
  ctrlLog: "Control de control",
  ctrlLogTips: "Historial de comandos",
  s06Ctrls: ["Parar el motor", "Restaurar motor", "Query ubicacion"],
  ctrlType: "Nombre de comando",
  resInfo: "Resultado",
  resTime: "Tiempo de respuesta",
  ctrlSendTime: "Enviar a tiempo",
  // csv文件导入上传
  choseCsv: "Seleccione el archivo CSV",
  choseFile: "Seleccione el archivo",
  submit: "Las ventas",
  targeDevice: "El dispositivo equipo",
  csvTips_1: "1.Graba el archivo excel como formato CSV",
  csvTips_2: "2.Importar el archivo CSV en el sistema",
  importExplain: "Instrucciones de importación:",
  fileDemo: "Formato de archivo de ejemplo",

  // add
  sendType: "Tipo",
  onlineCtrl: "Linea de comando",
  offCtrl: "Fuera de linea de comando",
  resStatus: [
    "No se envia",
    "invalid",
    "Se ha publicado",
    "Ejecutar con éxito",
    "La ejecución no",
    "No hay respuesta",
  ],
  /************************************************/
  addSubordinateClient: "Añadir cuenta",
  noSubordinateClient: "No sub-cuenta",
  superiorCustomerEmpty: "Elige superior",
  noCustomerName: "Nombre del cliente",
  noLoginAccount: "Usuario",
  noPsw: "Sin contraseña",
  noConfirmPsw: "Confirmar contraseña",
  pswNotAtypism: "No atypism generador!",
  addSuccess: "Añadir el éxito",
  superiorCustomer: "Superior",
  addVerticalImei: "Añadir vertical de IMEI",
  noImei: "No IMEI",
  addImei_curr: "Entrada número IMEI",
  no: "Unidad",
  aRowAImei: "Un IMEI de una línea",

  /*
   * dealer  Beginning of interface translation
   *
   * */
  //main.js
  imeiOrUserEmpty: "IMEI o usuario vacío!",
  accountEmpty: "IMEI/nombre /cuenta es necesario",
  queryNoUser: "Ningun usuario",
  queryNoIMEI: "No de IMEI",
  imeiOrClientOrAccount: "IMEI/nombre/cuenta ",
  dueSoon: "A punto de expirar",
  recentlyOffline: "Desconectado",
  choseSellDeviceFirst: "Por favor, seleccione el equipo se vende primera!",
  choseDeviceFirst: "Por favor, seleccione el dispositivo para mover primero!",
  choseDeviceExpiresFirst:
    "Por favor, elija el dispositivo para ser modificado primero!",
  choseRenewDeviceFirst:
    "Por favor, elija el dispositivo para renovar primero!",
  choseDeleteDeviceFirst:
    "Por favor, seleccione el dispositivo para borrar primero!",
  choseClientFirst: "Por favor, seleccione el cliente a moverse primero!",

  //myClient.js
  clientList: "Clientes",
  accountInfo: "La información de la cuenta",
  machineCount: "Dispositivo cantidad",
  stock: "Total",
  inventory: "Valores",
  subordinateClient: "Cuenta de subcuenta",
  datum: "Información",
  monitor: "Monitor",
  dueMachineInfo: "Expiración",
  haveExpired: "Vencida",
  timeRange: [
    "Dentro de los 7 dias",
    "Dentro de 30 días",
    "Dentro de los 60 días",
    "Dentro de 7 - 30 días",
    "Dentro de 30 - 60 días",
  ],
  offlineMachineInfo: "La información de dispositivos fuera de línea",
  timeRange1: [
    "Dentro de una hora",
    "En el plazo de un día",
    "Dentro de los 7 dias",
    "Dentro de un mes",
    "Dentro de los 60 días",
    "Más de 60 días",
    "Entre 1 hora a 7 dias",
    "Entre 1 día a 7 dias",
    "Entre 7 días a 30 días",
    "Entre 30 días a 60 días",
  ],
  offlineTime: "Gama",
  includeSubordinateClient: "Subcuenta",

  stopMachineInfo: "Información estática",
  stopTime1: "Tiempo de estática",
  unUseMachineInfo: "Unactive información",
  unUseMachineCount: "Unactive Amount",

  sellTime: "Vender tiempo",
  detail: "Detalles",
  manageDevice: "Detallado",
  details: "Detalles",
  deleteSuccess: "Borrar el éxito!",
  deleteFail: "Borrar el fracaso!",
  renewalLink: "Enlace de renovación",
  deleteGroupTips: "Ya sea para eliminar un grupo",
  addGroup: "Agregar grupo",
  jurisdictionRange: "Modificar funciones",
  machineSellTransfer: "Venta de maquina",
  monitorMachineGroup: "Monitor",
  jurisdictionArr: [
    "Cliente gestionar",
    "Mensaje gestionar",
    "GEO set",
    "Alarma manejar",
    "Cuenta virtual administrar",
    "Envío de instrucciones",
  ],
  confrimDelSim: "Confirmar eliminar doble tarjeta:",

  // 右键菜单
  sellDevice: "Venta de dispositivo",
  addClient: "Añadir usuario",
  deleteClient: "Eliminar usuario",
  resetPassword: "Restablecer la contrase?a",
  transferClient: "Seguir usuario",
  ifDeleteClient: "Borrar",

  //myAccount
  myWorkPlace: "Mi Cuenta",
  availablePoints: "Equilibrar",
  yearCard: "Tarjeta unaño",
  lifetimeOfCard: "La tarjeta de toda la vida",
  oneyear: "Un año",
  lifetime: "Para toda la vida",
  commonImportPoint: "Tarjetas de importación",
  lifetimeImportPoint: "Tarjetas de importación para toda la vida",
  myServiceProvide: "Proveedor",
  moreOperator: "Operación más",
  dueMachine: "Dispositivo vencido",
  offlineMachine: "Equipo fuera de linea",
  quickSell: "Venta rapida",
  sellTo: "Objetivo",
  machineBelong: "Pertenecen a",
  reset: "Restablecer",
  targetCustomer: "Cliente de la blanco",
  common_lifetimeImport: "Tarjeta unaño(0),La tarjeta de toda la vida(0)",
  cardType1: "Tipo",
  credit: "Cantidad",
  generateImportPoint: "Crear Tarjetas de importación",
  generateImportPointSuc: "Tarjeta de importación exitosa!",
  generateImportPointFail: "Tarjeta de importación no!",
  year_lifeTimeCard:
    "Tarjetas de importación(0),Tarjetas de importación para toda la vida(0)",
  generateRenewPoint: "Crear renovar tarjeta",
  transferTo: "Movimiento",
  transferPoint: "Cantidad",
  transferRenewPoint: "Mover tarjeta de renovación",
  pointHistoryRecord: "Historial de tarjetas",
  newGeneration: "Nuevo",
  operatorType: "Operación",
  consume: "Consumir",
  give: "Dar",
  income: "Ingresos",
  pay: "Salida",
  imeiErr: "Por favor, ingrese el pasado 6 números de IMEI!",
  accountFirstPage: "Inicio",

  /*
   * dealer  End of interface translation
   *
   * */
  // 1.4.8 risk control
  finrisk: "Control del riesgo financiero",
  attention: "Suscribirse",
  cancelattention: "Eliminar suscribete",
  poweroff: "Apagón",
  inout: "Dentro y fuera",
  inoutEF: "Dentro y fuera de la valla",
  longstay: "Estancia de hipoteca",
  secsetting: "Punto de ajuste de hipoteca",
  EFsetting: "Entorno GEO-Fence",
  polygonFence: "Poligono Valla",
  cycleFence: "Ciclo valla",
  haveBeenSetFence: "Se han fijado cerca",
  haveBeenSetPoint: "Se han establecido puntos de hipoteca",
  drawingFailed: "Drawing failed. Please redraw!",
  inoutdot: "Dentro y fuera",
  eleStatistics: "Estadísticas de electricidad",
  noData: "No hay datos",
  // 进出二押点表格
  accountbe: "Cuenta",
  SMtype: "Hipoteca Tipo de punto",
  SMname: "Nombre de punto de hipoteca",
  time: "Tiempo",
  position: "Posición",
  lastele: "Bateria restante",
  statisticTime: "Estadísticas fecha",
  searchalarmType: [
    "Todos",
    "Desconectado",
    "Apagón",
    "En y fuera de la valla",
    "Dentro y fuera",
    "Estancia de hipoteca",
  ],
  remarks: [
    "Hipoteca Punto",
    "Garantía Empresa",
    "Desmontar el punto",
    "Mercado de segunda mano",
  ],
  focusOnly: "Centrarse solo",

  autoRecord: "Grabación automática",
  /******************************************************set command start**********************************8*/
  setCtrl: {
    text: "Comando set",
    value: "",
  },
  moreCtrl: {
    text: 'Más instrucciones',
    value: ''
  },
  sc_openTraceModel: {
    text: "Modelo conjunto trace",
    value: "0",
  },
  sc_closeTraceModel: {
    text: "Cierran rastro modelo",
    value: "1",
  },
  sc_setSleepTime: {
    text: "Establecer la hora de dormir",
    value: "2",
  },
  sc_setAwakenTime: {
    text: "Set despertar a tiempo",
    value: "3",
  },
  sc_setDismantleAlarm: {
    text: "Set desmantelar alarma",
    value: "4",
  },
  sc_setSMSC: {
    text: "Center Número",
    value: "5",
  },
  sc_delSMSC: {
    text: "Borrar el Center Número",
    value: "6",
  },
  sc_setSOS: {
    text: "Añadir SOS",
    value: "7",
  },
  sc_delSOS: {
    text: "Borrar el SOS",
    value: "8",
  },
  sc_restartTheInstruction: {
    text: "Restablecer",
    value: "9",
  },
  sc_uploadTime: {
    text: "Set upload interval",
    value: "10",
  },
  /*Alarm clock time setting
     Timing reback time setting
     Dismantle alarm setting
     Week mode open close*/
  sc_setAlarmClock: {
    text: "Set de reloj de alarma",
    value: "11",
  },
  sc_setTimingRebackTime: {
    text: "Set calendario Reback tiempo",
    value: "12",
  },
  sc_openWeekMode: {
    text: "Modo abrir la semana",
    value: "13",
  },
  sc_closeWeekMode: {
    text: "Modo de cerrar la semana",
    value: "14",
  },

  sc_powerSaverMode: {
    text: "Subir el intervalo",
    value: "15",
  },
  sc_carCatchingMode: {
    text: "El modo de seguimiento",
    value: "16",
  },
  sc_closeDismantlingAlarm: {
    text: "Cerca el desmantelamiento de alarma",
    value: "17",
  },
  sc_openDismantlingAlarm: {
    text: "Abrir el desmantelamiento de alarma",
    value: "18",
  },
  sc_VibrationAlarm: {
    text: "Set alarma de vibracion",
    value: "19",
  },
  sc_timeZone: {
    text: "Configuración de zona horaria",
    value: "20",
  },
  sc_phoneMonitor: {
    text: "escucha telefónica",
    value: "21",
  },
  sc_stopCarSetting: {
    text: "Ajustes de aparcamiento",
    value: "22",
  },
  sc_bindAlarmNumber: {
    text: "Encuadernación del número de alarma",
    value: "23",
  },
  sc_bindPowerAlarm: {
    text: "Alarma de fallo de alimentación",
    value: "24",
  },
  sc_fatigueDrivingSetting: {
    text: "Configuración de conducción de fatiga",
    value: "25",
  },
  sc_peripheralSetting: {
    text: "ajustes periféricos",
    value: "26",
  },
  sc_SMSAlarmSetting: {
    text: "Configurar alerta de SMS",
    value: "27",
  },
  sc_autoRecordSetting: {
    text: "Configuración de grabación automática",
    value: "28",
  },
  sc_monitorCallback: {
    text: "escuchar devolución de llamada",
    value: "29",
  },
  sc_recordCtrl: {
    text: "instrucciones de grabación",
    value: "30",
  },
  sc_unbindAlarmNumber: {
    text: "Desenlazar número de alarma",
    value: "31",
  },
  sc_alarmSensitivitySetting: {
    text: "Ajuste de sensibilidad de alarma de vibración",
    value: "32",
  },
  sc_alarmSMSsettings: {
    text: "Ajustes de alarma de vibración SMS",
    value: "33",
  },
  sc_alarmCallSettings: {
    text: "Ajustes de llamada de alarma de vibración",
    value: "34",
  },
  sc_openFailureAlarmSetting: {
    text: "Desactivar alarma de fallo de alimentación",
    value: "35",
  },
  sc_restoreFactory: {
    text: "Fábrica de restauración",
    value: "36",
  },
  sc_openVibrationAlarm: {
    text: "Encender la alarma de vibración",
    value: "37",
  },
  sc_closeVibrationAlarm: {
    text: "Apaga la alarma de vibración",
    value: "38",
  },
  sc_closeFailureAlarmSetting: {
    text: "apagar la alarma de fallo de alimentación",
    value: "39",
  },
  sc_feulAlarm: {
    text: "Ajuste de alarma de combustible",
    value: "40",
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: "Modo de ahorro de energía",
    value: "41",
  },
  sc_sleepMode: {
    text: "Modo de sueño",
    value: "42",
  },
  sc_alarmMode: {
    text: "Modo de alarma",
    value: "43",
  },
  sc_weekMode: {
    text: "Modo semana",
    value: "44",
  },
  sc_monitorNumberSetting: {
    text: "Configuración del número de monitor",
    value: "45",
  },
  sc_singlePositionSetting: {
    text: "Modo de posicionamientoúnico",
    value: "46",
  },
  sc_timingworkSetting: {
    text: "Modo de trabajo",
    value: "47",
  },
  sc_openLightAlarm: {
    text: "Alarma de sensor de luz abierta",
    value: "48",
  },
  sc_closeLightAlarm: {
    text: "Alarma de sensor de luz cerrada",
    value: "49",
  },
  sc_workModeSetting: {
    text: "Ajuste del modo de trabajo",
    value: "50",
  },
  sc_timingOnAndOffMachine: {
    text: "Ajuste del interruptor de tiempo",
    value: "51",
  },
  sc_setRealTimeTrackMode: {
    text: "Establecer el modo de persecución en tiempo real",
    value: "52",
  },
  sc_setClockMode: {
    text: "Establecer modo de alarma",
    value: "53",
  },
  sc_openTemperatureAlarm: {
    text: "Activar alarma de temperatura",
    value: "54",
  },
  sc_closeTemperatureAlarm: {
    text: "Apague la alarma de temperatura",
    value: "55",
  },
  sc_timingPostbackSetting: {
    text: "Configuración de temporización de devolución de datos ",
    value: "56",
  },
  sc_remoteBoot: {
    text: "Arranque remoto",
    value: "57",
  },
  sc_smartTrack: {
    text: "seguimiento inteligente",
    value: "58",
  },
  sc_cancelSmartTrack: {
    text: "cancelar seguimiento inteligente",
    value: "59",
  },
  sc_cancelAlarm: {
    text: "cancelar alarma",
    value: "60",
  },
  sc_smartPowerSavingMode: {
    text: "Establecer modo de ahorro de energía inteligente",
    value: "61",
  },
  sc_monitorSetting: {
    text: "Monitor",
    value: '62'
  },
  // 指令重构新增翻译
  sc_timedReturnMode: {
    text: 'Modo de retorno temporizado',
    value: '100'
  },
  sc_operatingMode: {
      text: 'Modo de funcionamiento',
      value: '101'
  },
  sc_realTimeMode : {
      text: 'Modo de posicionamiento en tiempo real',
      value: '102'
  },
  sc_alarmMode : {
      text: 'Modo de alarma',
      value: '103'
  },
  sc_weekMode : {
      text: 'Modo semana',
      value: '104'
  },
  sc_antidemolitionAlarm : {
      text: 'Alarma anti-demolición',
      value: '105'
  },
  sc_vibrationAlarm : {
      text: 'Alarma de vibración',
      value: '106'
  },
  sc_monitoringNumber : {
      text: 'Gestión del número de seguimiento',
      value: '107'
  },
  sc_queryMonitoring : {
      text: 'Número de seguimiento de consultas',
      value: '108'
  },
  sc_electricityControl : {
      text: 'Control de petróleo y electricidad',
      value: '109'
  },
  sc_SOSnumber : {
      text: 'Gestión de números SOS',
      value: '110'
  },
  sc_SleepCommand : {
    text: 'Comando de sueño',
    value: '201'
  },
  sc_RadiusCommand : {
      text: 'Radio de desplazamiento',
      value: '202'
  },
  sc_punchTimeMode:{
      text:'打卡模式',
      value:'203'  
  },
  sc_intervelMode:{
      text:'时间段模式',
      value:'204'  
  },
  sc_activeGPS:{
      text:'激活GPS',
      value:'205'  
  },
  sc_lowPowerAlert: {
      text: 'Recordatorio de batería baja',
      value: '206'
  },
  sc_SOSAlert: {
      text: 'SOS报警',
      value: '207'
  },
  mc_cuscom : {
      text: 'Instrucción personalizada',
      value: '1'
  },
  NormalTrack: 'Modo de seguimiento normal',
  listeningToNumber:'¿Está seguro de que desea verificar el número de monitoreo de?',
  versionNumber:'¿Está seguro de que desea verificar el número de versión de?',
  longitudeAndLatitudeInformation:'¿Está seguro de que desea verificar la información de latitud y longitud de?',
  equipmentStatus:'¿Está seguro de que desea verificar el estado de?',
  public_parameter:'¿Está seguro de que desea verificar los parámetros?',
  GPRS_parameter:'¿Está seguro de que desea verificar los parámetros GPRS de?',
  deviceName: '¿Estás seguro de que quieres rodar el dispositivo?',
  SMS_alert:'¿Está seguro de que desea verificar la alarma de recordatorio por SMS de?',
  theBindingNumber:'¿Está seguro de que desea comprobar el número de encuadernación de?',
  intervalTimeRange:'El intervalo de tiempo de ajuste es 001-999, unidad (minuto)',
  pleaseChoose:'Por favor elija',
  RealTimeCarChase:'¿Está seguro de que desea configurar este dispositivo en el modo de persecución de automóviles en tiempo real?',
  inputPhoneNumber: "Ingrese el número de teléfono",
  inputCorPhoneNumber: "Ingrese el número de teléfono correcto",
  autoCallPhone: "Sugerencia: después de que el comando se ejecute correctamente, el terminal marcará automáticamente el número configurado",
  limitTheNumberOfCellPhoneNumbers1:'Este comando admite hasta 5 números de teléfono móvil',
  limitTheNumberOfCellPhoneNumbers2:'Este comando admite hasta 3 números de teléfono móvil',
  equipmentTorestart:'¿Está seguro de que desea reiniciar este dispositivo?',
  remindTheWay:'Manera de recordar',
  alarmWakeUpTime:'Hora de despertar alarma',
  alarmWakeUpTime1:'Hora de despertar alarma 1',
  alarmWakeUpTime2:'Hora de despertar alarma 2',
  alarmWakeUpTime3:'Hora de despertar alarma 3',
  alarmWakeUpTime4:'Hora de despertar  alarma 4',
  sensitivityLevel:'Seleccione el nivel de sensibilidad',
  parking_time:'Tiempo de estacionamiento',
  selectWorkingMode:'Seleccione el modo de trabajo',
  Alarm_value:'Valor de alarma',
  Buffer_value:'Valor de búfer',
  gqg_disconnect:'desconectar',
  gqg_turnOn:'Enciende',
  Return_interval:'Intervalo de retorno',
  gq_startTime:'Hora de inicio',
  gq_restingTime:'Tiempo de descanso',
  gq_Eastern:'Zona horaria del este',
  gq_Western:'Zona horaria occidental',

  gq_driver:'Alarma de conducción por fatiga',
  gq_deviceName:'¿Estás seguro de que quieres rodar este dispositivo?',
  gq_noteAlarm:'¿Está seguro de que desea comprobar la alarma de recordatorio por SMS?',
  gq_restoreOriginal:'¿Está seguro de que desea restaurar este equipo a la fábrica original?',
  gq_normalMode:'Modo normal',
  gq_IntelligentsleepMode:'Modo de suspensión inteligente',
  gq_DeepsleepMode:'Modo de sueño profundo',
  gq_RemotebootMode:'Modo de arranque remoto',
  gq_IntelligentsleepModeTips:'¿Está seguro de que desea configurar el modo de suspensión inteligente?',
  gq_DeepsleepModeTips:'¿Estás seguro de que deseas configurar el modo de suspensión profunda?',
  gq_RemotebootModeTips:'¿Está seguro de que desea configurar el modo de arranque remoto?',
  gq_normalModeTips:'¿Está seguro de que desea establecer el modo normal',
  gq_sleepModeTips:'¿Estás seguro de que deseas configurar este dispositivo en modo de suspensión?',
  gq_Locatethereturnmode:'Modo de retorno de posicionamiento',
  gq_regularWorkingHours:'Período de trabajo cronometrado',
  gq_AlarmType:{
      text: 'Tipo de alarma',
      value: '111'
  },
  IssuedbyThePrompt:'El comando ha sido emitido, espere a que el dispositivo responda',
  platformToinform:'Notificación de plataforma',
  gq_shortNote:'Notificación por SMS', 
  /************指令白话文**********************/
  closeDismantlingAlarm: "Cerca el desmantelamiento de alarma",
  openDismantlingAlarm: "Abrir el desmantelamiento de alarma",
  closeTimingRebackMode: "Cerca de tiempo modo Reback",
  minute: "Minutos",
  timingrebackModeSetting: "Subir el intervalo:",
  setWakeupTime: "Establecer tiempo para despertar:",
  weekModeSetting: "Ajuste del modo de la semana:",
  closeWeekMode: "Modo de cerrar la semana",
  setRealtimeTrackMode: "Establecer el modo de pista en tiempo real",
  fortification: "Fortificación",
  disarming: "Desarmar",
  settimingrebackmodeinterval:
    "El intervalo de tiempo establecido Reback mode:",
  oilCutCommand: "Comando de aceite de corte",
  restoreOilCommand: "El comando restore Oil",
  turnNnTheVehiclesPower: "Encienda la potencia en los vehículos",
  turnOffTehVehiclesPower: "Apagar los vehículos de potencia",
  implementBrakes: "Aplicar los frenos",
  dissolveBrakes: "Disolver los frenos",
  openVoiceMonitorSlarm: "Open monitor de voz alarma",
  closeVoiceMonitorAlarm: "Cerca del monitor de alarma de voz",
  openCarSearchingMode: "Abrir coche buscando el modo",
  closeCarSearchingMode: "Cerca coche buscando el modo",
  unrecognizedCommand: "Unrecognized command",
  commandSendSuccess: "Congratulations! El dispositivo codifica el comando!",

  /********************************************设置指令结束**************************************************/

  /********************************************查询指令开始**************************************************/
  queryCtrl: {
    text: "Query comando",
    value: "",
  },
  /*参数设置查询*/
  qc_softwareVersion: {
    text: "Versión de software de Query",
    value: "1",
  },
  qc_latlngInfo: {
    text: "QUERY latitud y longitud",
    value: "2",
  },
  qc_locationHref: {
    text: "Query La configuración de los parámetros",
    value: "3",
  },
  qc_status: {
    text: "Query Situación",
    value: "4",
  },
  qc_gprs_param: {
    text: "Query GPRS parámetro",
    value: "5",
  },
  qc_name_param: {
    text: "nombre",
    value: "6",
  },
  qc_SMSReminderAlarm_param: {
    text: "Query SMS recordatorio de alarma",
    value: "7",
  },
  qc_bindNumber_param: {
    text: "Número de enlace de consulta",
    value: "8",
  },

  /********************************************查询指令结束**************************************************/

  /*******************************************控制指令开始***************************************************/

  controlCtrl: {
    text: "Mando de control",
    value: "",
  },
  cc_offOilElectric: {
    text: "Suministro de energía eléctrica",
    value: "1",
  },
  cc_recoveryOilElectricity: {
    text: "Recuperación de aceite",
    value: "2",
  },
  cc_factorySettings: {
    text: "Ajuste de fábrica",
    value: "4",
  },
  cc_fortify: {
    text: "Fortificar",
    value: "75",
  },
  cc_disarming: {
    text: "Desarmar",
    value: "76",
  },
  cc_brokenOil: {
    text: "Instrucciones de corte de aceite",
    value: "7",
  },
  cc_RecoveryOil: {
    text: "Circuito de recuperación de aceite",
    value: "8",
  },

  /*******************************************控制指令结束***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: "KM",
  mileage: "Kilometraje",
  importMachine: "Agregar nuevo imei",
  transferImportPoint: "Mover tarjeta de importación",
  machineType1: "Modelo",
  confirmIMEI:
    "Por favor, asegúrese de que la validez de IMEI y modelo antes de importar,Como el funcionamiento es irrevocable.",
  renew: "Renovar",
  deductPointNum: "Cantidad",
  renewSuccess: "Renovar el éxito!",
  wireType: [
    {
      text: "wired",
      value: false,
    },
    {
      text: "wireless",
      value: true,
    },
  ],
  vibrationWays: [
    {
      text: "Platform",
      value: 0,
    },
    {
      text: "Platform+Message",
      value: 1,
    },
    {
      text: "Platform+Message+Phone",
      value: 2,
    },
  ],

  /*
   * 2018-02-02
   * */
  maploadfail:
    "?Lo siento, corriente de carga el mapa no, quieres cambiar a otro mapa?",

  /*
    2018-03-06新增 控制指令
    * */
  cc_openPower: {
    text: "Vehículo abierto poder",
    value: "7",
  },
  cc_closePower: {
    text: "Colse de potencia del vehículo",
    value: "8",
  },
  cc_openBrake: {
    text: "Freno abierto",
    value: "9",
  },
  cc_closeBrake: {
    text: "Cerca de freno",
    value: "10",
  },
  cc_openAlmrmvoice: {
    text: "Abre la alarma",
    value: "11",
  },
  cc_closeAlmrmvoice: {
    text: "Cierre alarma",
    value: "12",
  },
  /*2018-03-06新增 控制指令
   * */
  cc_openFindCar: {
    text: "Buscar vehículos abiertos",
    value: "13",
  },
  cc_closeFindCar: {
    text: "Buscar vehículos cercanos",
    value: "14",
  },
  /*2018-03-19
   * */
  EF: "GeoFence",

  /*
    2018-03-29，扩展字段
    * */
  exData: ["Power", "Voltage", "Combustible", "Temperatura", "Resistencia"],

  /*
    2018-04-10
    * */
  notSta: "LBS no incluidas en las estadísticas",

  // 油量统计
  fuelSetting: "Ajuste de combustible",
  mianFuelTank: "Tanque de combustible principal",
  auxiliaryTank: "Tanque de combustible secundario",
  maximum: "Max",
  minimum: "Min",
  FullTankFuel: "Tanque lleno de combustible",
  fuelMinValue: 'El volumen de combustible no puede ser inferior a 10L',
  standardSetting: "Establecimiento de normas",
  emptyBoxMax: "Max de vacío",
  fullBoxMax: "Max de la plena",
  fuelStatistics: "Estadísticas de combustible",
  settingSuccess: "El éxito",
  settingFail: "Fracasado",
  pleaseInput: "Por favor, ingrese",
  fuelTimes: "Combustible veces",
  fuelTotal: "El combustible total",
  refuelingTime: 'Tiempo de repostaje',
  fuelDate: "Fecha de combustible",
  fuel: "Combustible",
  fuelChange: "Cambio de combustible",
  feulTable: "Tabla de análisis de combustible",
  addFullFilter: "Por favor agregue el filtro completo",
  enterIntNum: "Por favor ingrese un entero positivo",
  // 温度统计
  tempSta: "Estadísticas de temperatura",
  tempTable: "tabla de análisis de temperatura",
  industrySta: "Estadísticas de la industria",
  temperatura: "Temperatura",
  tempRange: "rango de temperatura",
  tempSetting:'Ajuste de temperatura',
  tempSensor:'Sensor de temperatura',
  tempAlert:'¡Después de que el sensor de temperatura se apague, no recibirá datos de temperatura!',
  phoneNumber: "Número de teléfono móvil",
  sosAlarm: "alarma SOS",
  undervoltageAlarm: "alarma de subtensión",
  overvoltageAlarm: "alarma de sobretensión",
  OilChangeAlarm: "Alarma de cambio de aceite",
  accDetection: "detección de ACC",
  PositiveAndNegativeDetection: "Detección positiva y negativa",
  alermValue: "Valor de alarma",
  bufferValue: "Valor de búfer",
  timeZoneDifference: "Diferencia de zona horaria",
  meridianEast: "Este meridiano",
  meridianWest: "Oeste meridiano",
  max12hour: "No puede ser más de 12 horas",
  trackDownload: "pista de descarga",
  download: "Descargar",
  multiReset: "Reinicio a granel",
  resetSuccess: "restablecer el éxito",
  multiResetTips: "Consejos de reinicio a granel",
  point: "punto",
  myplace: "mi lugar",
  addPoint: "agregar punto",
  error10018: ",El equilibrio no es suficiente",
  error110:'El objeto no existe',
  error109:'Límite máximo excedido',
  error20013:'El tipo de dispositivo no existe',
  error90001:'El número de serie del tipo de dispositivo no puede estar vacío',
  error20003:'Imei no puede estar vacío',
  inputName: "Nombre de entrada por favor",
  virtualAccount: "cuenta virtual",
  createTime: "crear tiempo",
  permission: "permiso",
  permissionRange: "rango de permiso",
  canChange: "Función cambiable",
  fotbidPassword: "Contraseña",
  virtualAccountTipsText:
    "Al crear una cuenta virtual, es una cuenta de alias de la cuenta de distribuidor actualmente registrada y puede establecer permisos para la cuenta virtual.",
  noOperationPermission: "sin autorización",
  number: "Número",
  rangeSetting: "Ajuste de rango",
  setting: "Ajuste",
  // 1.6.1
  duration: "Duración",
  voltageSta: "Estadísticas de voltaje",
  voltageAnalysis: "Análisis de voltaje",
  voltageEchart: "Tablas de análisis de voltaje",
  platformAlarm: "Alarma de plataforma",
  platformAlarm1: "teléfono",
  platformAndPhone: 'teléfono+Alarma de plataforma',
  smsAndplatformAlarm: "SMS+Alarma de plataforma",
  smsAndplatformAlarm1: "SMS",
  smsAndplatformAlarmandPhone: "Alarma de plataforma +SMS+ teléfono",
  smsAndplatformAlarmandPhone1: "SMS+ teléfono",
  more_speed: "Km",
  attribute: "Atributo",
  profession: "Profesional",
  locationPoint: "Punto de anclaje",
  openPlatform: "API abierta",
  experience: "Demo",
  onlyViewMonitor: "Solo ver Monitor",
  inputAccountOrUserName:
    "Por favor ingrese un número de cuenta o nombre de usuario",
  noDeviceTips:
    "No encontré información relevante del equipo, verifique clientes",
  noUserTips:
    "No encontré información relevante del usuario, verifique el equipo",
  clickHere: "Haga click aqui",
  pointIntervalSelect: "Intervalo de puntos de seguimiento",
  payment: "Pago",
  pleaceClick: "Haga clic",
  paymentSaveTips:
    "Consejo de seguridad: confirme con el proveedor del servicio la validez del enlace",
  fuelAlarmValue: "Valor de alarma de cantidad de aceite",
  fuelConsumption: "Consumo de combustible",
  client: "Cliente",
  create: "Crear",
  importPoint: "Tarjetas de importación",
  general: "Común",
  lifelong: "De por vida",
  renewalCard: "Renovar tarjetas",
  settingFuelFirst:
    "Primero establezca el valor de alarma de cantidad de aceite antes de enviar el comando!",
  overSpeedSetting: "Ajuste de velocidad",
  kmPerHour: "km/h",
  times: "Veces",
  total: "Total",
  primary: "Señor",
  minor: "Diputado",
  unActiveTips: "El dispositivo no está activado y no se puede utilizar.",
  arrearsTips: "El dispositivo está en mora y no puede usar esta función",
  loading: "Carga",
  expirationReminder: "Recordatorio de vencimiento",
  projectName: "Nombre de proyecto",
  expireDate: "Fecha de caducidad",
  changePwdTips:
    "Su contraseña es demasiado simple y estáen peligro de Seguridad.",
  pwdCheckTips1: "Las sugerencias son de 6 a 20 letras, números o símbolos.",
  pwdCheckTips2: "La contraseña introducida es demasiado débil.",
  pwdCheckTips3: "Su contraseña puede ser más complicada.",
  pwdCheckTips4: "Su contraseña es segura.",
  pwdLevel1: "Débil",
  pwdLevel2: "Media",
  pwdLevel3: "Fuerte",
  comfirmChangePwd: "Confirmar cambio PWD",
  notSetYet: "Aún no fijaron",
  liter: "Litro",
  arrearageDayTips: "Días de vencimiento",
  todayExpire: "Vence hoy",
  forgotPwd: "Olvidaste tu contraseña?",
  forgotPwdTips:
    "Por favor, póngase en contacto con el vendedor para cambiar su contraseña",
  //1.6.7
  commonProblem: "Problema común",
  instructions: "Instrucción",
  webInstructions: "Instrucción WEB",
  appInstructions: "Instrucción APP",
  acceptAlarmNtification: "Notificación de alarma",
  alarmPeriod: "Periodo de alarma",
  whiteDay: "Día",
  blackNight: "Noche",
  allDay: "Día entero",
  alarmEmail: "Correo de alarma",
  muchEmailTips:
    "Se pueden ingresar múltiples buzones, separados por el símbolo ';'",
  newsCenter: "Centro de notificaciones",
  allNews: "Todos",
  unReadNews: "No leído",
  readNews: "Leer",
  allTypeNews: "Tipos de notificación",
  alarmInformation: "Información de alarma",
  titleContent: "Título",
  markRead: "Marcado leído",
  allRead: "Todos leídos",
  allDelete: "Eliminar todos",
  selectFirst: "Seleccione Primero!",
  updateFail: "Actualización fallida!",
  ifAllReadTips: "Todos leen？",
  ifAllDeleteTips: "Eliminar todos",
  stationInfo: "Información de la estación",
  phone: "Teléfono",
  //1.6.72
  plsSelectTime: "Por favor, elija el tiempo!",
  customerNotFound: "No puedo encontrar al cliente",
  Postalcode: "Ubicación",
  accWarning: "Alarma ACC",
  canInputMultiPhone:
    "Puede ingresar múltiples números móviles, por favor use; separe",
  noLocationInfo: "El dispositivo no tiene información de localización.",
  //1.6.9
  fenceName: "Nombre de la cerca",
  fenceManage: "Manejo de cercas",
  circular: "Ronda",
  polygon: "Polígono",
  allFence: "Toda la cerca",
  shape: "Forma",
  stationNews: "Mensaje de la estación",
  phonePlaceholder: 'Se pueden ingresar múltiples números, separados por ","',
  addressPlaceholder:
    'Ingrese la dirección y el código postal en orden, separados por ","',
  isUnbind: "Ya sea para desvincular",
  alarmCar: "Vehículo de alarma",
  alarmAddress: "Ubicación de la alarma",
  chooseAtLeastOneTime: "Seleccione al menos una vez",
  alarmMessage: "No hay detalles sobre esta información de alarma.",
  navigatorBack: "Volver a superior",
  timeOverMessage:
    "El tiempo de vencimiento del usuario no puede ser mayor que el tiempo de vencimiento de la plataforma",
  //1.7.0
  userTypeStr: "Tipo de usuario",
  newAdd: "Nuevo",
  findAll: "Total",
  findStr: "Datos coincidentes",
  customColumn: "Personalizar",
  updatePswErr: "Error de contraseña actualizada",
  professionalUser: "Usuario profesional o no?",
  confirmStr: "Confirmar",
  inputTargetCustomer: "Cliente objetivo de entrada",
  superiorUser: "Usuario superior",
  speedReport: "Informe de velocidad",
  createAccount: "Crear cuenta",
  push: "Empujar",
  searchCreateStr:
    "Operará una cuenta y transferirá el dispositivo a esta cuenta.",
  allowIMEI: "Permitir inicio de sesión IMEI",
  defaultPswTip:
    "La contraseña predeterminada son los últimos 6 dígitos de IMEI",
  createAccountTip: "Cuenta creada y dispositivo transferido exitoso",
  showAll: "Mostrar todo",
  bingmap: "Mapa de Bing",
  areaZoom: "Zoom",
  areaZoomReduction: "Restaurar zoom",
  reduction: "Reducción",
  saveImg: "Guardar como imagen",
  fleetFence: "Valla de flota",
  alarmToSub: "Notificación de alarma",
  bikeFence: "Cerca de bicicleta",
  delGroupTip: "Por favor,borra los puntos de interés primero.",
  isExporting: "Exportar vt...",
  addressResolution: "Dirección actual...",
  simNOTip: "El número de la tarjeta SIM solo puede ser un número",
  unArrowServiceTip:
    "El tiempo de vencimiento del usuario para los siguientes dispositivos es mayor que el tiempo de vencimiento de la plataforma, vuelva a seleccionarlo. El número del dispositivo es:",
  platformAlarmandPhone: "Alarma de plataforma + Tel",
  openLightAlarm: "Alarma de sensor de luz abierta",
  closeLightAlarm: "Alarma de sensor de luz cerrada",
  ACCAlarm: "Alarma ACC",
  translateError: "Transferencia fallida, el usuario objetivo no tiene permiso",
  distanceTip: "Haga clic en Aceptar, haga doble clic para finalizar",
  workMode: "Modo de trabajo",
  workModeType:
    "0: modo normal; 1 modo de sueño inteligente; 2 Modo de sueño profundo",
  clickToStreetMap: "Click para abrir la calle",
  current: "Actual",
  remarkTip:
    "Nota: No venda tarjetas de diferentes tipos de paquetes al mismo tiempo.",
  searchRes: "Resultado de busqueda",
  updateIcon: "Cambiar icono",
  youHaveALarmInfo: "Tienes un mensaje de alerta",
  moveInterval: "Intervalo de ejercicio",
  staticInterval: "Intervalo de tiempo de inactividad",
  notSupportTraffic: "Coming soon.",
  ignite: "Acc ON",
  flameout: "Acc OFF",
  generateRenewalPointSuc: "Genera puntos de renovación exitosamente",
  noGPSsignal: "No posicionado",
  imeiErr2: "Ingrese al menos los últimos 6 dígitos del número imei",
  searchCreateStr2:
    "Esto creará una cuenta y transferirá este dispositivo al nombre de la cuenta",
  addUser: "Agregar usuario",
  alarmTemperature: "Valor de temperatura de alarma",
  highTemperatureAlarm: "Alarma de alta temperatura",
  lowTemperatureAlarm: "Alarma de baja temperatura",
  temperature: "Temperature",
  temperature1: "Temperature1",
  temperature2: "Temperature2",
  temperature3: "Temperature3",
  temperatureTip: "Por favor, introduzca un valor de temperatura！",
  locMode: "Modo de posicionamiento",
  imeiInput: "Por favor, introduzca el número IMEI",
  noResult: "No hay resultados coincidentes.",
  noAddressKey: "Temporalmente no se puede obtener información de dirección",
  deviceGroup: "Agrupación de dispositivos",
  shareManage: "compartir Administrar",
  lastPosition: "Ultima posicion",
  defaultGroup: "Grupo predeterminado",
  tankShape: "forma del tanque",
  standard: "estándar",
  oval: "oval",
  irregular: "irregular",
  //1.8.4
  inputAddressOrLoc: "Ingrese dirección latitud y longitud",
  inputGroupName: "Ingrese el nombre del grupo",
  lock: "bloquear",
  shareHistory: "Compartir pista",
  tomorrow: "mañana",
  threeDay: "tres días",
  shareSuccess: "compartir enlace",
  effective: "válido",
  lapse: "inválido",
  copyShareLink: "copiar compartir enlace",
  openStr: "Abierto",
  closeStr: 'Apagado',
  linkError: "error de enlace",
  inputUserName: "nombre de usuario de entrada",
  barCodeStatistics: "Estadísticas de código de barras",
  barCode: "Código de barras",
  sweepCodeTime: "tiempo de escaneo de código de barras",
  workModeType2:
    "1 Modo de reposo inteligente; 2 modos de reposo profundo; 3 modos de encendido / apagado remoto",
  remoteSwitchMode: "Modo de interruptor remoto",
  saleTime: "Fecha de venta",
  onlineTime: "fecha en línea",
  dayMileage: "Hoy Kilometraje",
  imeiNum: "Número IMEI",
  overSpeedValue: "Umbral de exceso de velocidad",
  shareNoOpen: "Compartir enlace no está habilitado",
  addTo2: "Venta a",
  OverDue: "Caducado",
  openInterface: "Abrir interfaz",
  privacyPolicy: 'Política de privacidad',
  serviceTerm: 'Términos de servicio',
  importError:
    "Error al agregar, el número de dispositivo (IMEI) debe ser un número de 15 dígitos",
  importResult: "Importar resultados",
  totalNum: "total",
  successInfo: "éxito",
  errorInfo: "Fail",
  repeatImei: "IMEI repeat",
  includeAccount: "subcuenta",
  formatError: "Malformado",
  importErrorInfo: "Ingrese el número IMEI de 15 dígitos",
  totalMileage: "Kilometraje total",
  totalOverSpeed: "Sobrevelocidad total (veces)",
  totalStop: "Parada total (veces)",
  totalOil: "Aceite total",
  timeChoose: "Selección de tiempo",
  IntervaloTiempo: "Tiempo de intervalo",
  predeterminado: "Predeterminado",
  idleSpeedStatics: "Estadísticas de velocidad inactiva",
  offlineStatistics: 'Estadísticas sin conexión',
  idleSpeed: "Velocidad de ralentí",
  idleSpeedTimeTip1: "El tiempo de inactividad no puede estar vacío",
  idleSpeedTimeTip2: "El tiempo de inactividad debe ser un entero positivo",
  averageSpeed: "Velocidad media",
  averageOil: "Consumo medio de combustible",
  oilImgTitle: "Tabla de análisis de aceite",
  oilChangeDetail: "Detalles de cambio de combustible",
  machineNameError:
    "El nombre del dispositivo no puede contener símbolos especiales (/ ')",
  remarkError: "La información del comentario no puede exceder las 50 palabras",
  defineColumnTip: "Consultar hasta 12 artículos",
  pswCheckTip:
    "La sugerencia es una combinación de 6-20 dígitos, letras y símbolos",
  chooseGroup: "Seleccione un grupo",
  chooseAgain:
    "Solo puede consultar los datos de los últimos seis meses, seleccione de nuevo！",
  noDataTip: "Sin datos！",
  noMachineNameError: "Seleccione un dispositivo！",
  loginAccountError:
    "La cuenta de inicio de sesión no puede tener 15 dígitos！",
  includeExpire: "Que caduca",
  groupNameTip: "El nombre del grupo no puede estar vacío！",
  outageTips: "¿Estás seguro de que el aceite está cortado?",
  powerSupplyTips: "¿Estás seguro de restaurar el aceite?",
  centerPhoneTips: "Por favor ingrese el número",
  centerPhoneLenTips: "Por favor ingrese 8-20 dígitos",
  passworldillegal: "Hay caracteres ilegales",
  // 2.0.0 POI，权限版本
  singleAdd:'Añadir único',
  batchImport:'Importación por lotes',
  name:'Nombre',
  icon:'Icono',
  defaultGroup:'Grupo predeterminado',
  remark:'Observación',
  uploadFile:'Subir archivo',
  exampleDownload:'Descarga de ejemplo',
  uploadFiles:'Subir archivo',
  poiTips1:'Puede importar puntos de interés cargando un archivo de Excel con información relacionada. Siga el formato del ejemplo para preparar el archivo',
  poiTips2:'Nombre: obligatorio, no más de 32 caracteres',
  poiTips3:'Icono: requerido, ingrese 1,2,3,4',
  poiTips4:'Latitude：Requerido',
  poiTips5:'Longitud: obligatorio',
  poiTips6:'Nombre del grupo: opcional, no más de 32 caracteres. Si no se completa el nombre del grupo, el punto de interés pertenece al grupo predeterminado. Si el nombre del grupo completo es coherente con el nombre del grupo creado, el punto de interés pertenece al grupo creado. No se ha creado el nombre del grupo, el sistema agregará el grupo',
  poiTips7:'Observaciones: opcional, no más de 50 caracteres',
  // 权限相关
  roleLimit: 'Permisos de rol',
  operateLog: 'Registro de operaciones',
  sysAccountManage: 'Cuenta de autoridad',
  rolen: 'Roles',
  rolename: 'Nombre de rol',
  addRole: 'Nuevo rol',
  editRole: 'Editar rol',
  deleteRole: 'Eliminar rol',
  delRoleTip: '¿Está seguro de que desea eliminar este rol?',
  delAccountTip: '¿Está seguro de que desea eliminar esta cuenta?',
  limitconfig: 'Perfil de derechos',
  newAccountTip1: 'La cuenta de autoridad es similar a la cuenta virtual anterior y es la subcuenta del administrador. Los administradores pueden crear cuentas de autoridad y asignar diferentes roles a las cuentas de autoridad, de modo que diferentes cuentas puedan ver diferentes contenidos y operaciones en la plataforma.',
  newAccountTip2: 'Proceso de creación de una cuenta de permiso:',
  newAccountTip31: '1. En la página de administración de funciones,',
  newAccountTip32: 'Nuevo rol',
  newAccountTip33: 'Y configure los permisos para el rol;',
  newAccountTip4: '2. En la página de administración de cuentas de autoridad, cree una nueva cuenta de autoridad y asigne roles a la cuenta.',
  newRoleTip1: 'Los administradores pueden crear roles y configurar diferentes permisos de operación para diferentes roles a fin de satisfacer las necesidades comerciales en diferentes escenarios.',
  newRoleTip2: 'Por ejemplo, configure si un rol financiero tiene permiso para ubicar y monitorear, si tiene permiso para agregar clientes, si tiene permiso para modificar la información del dispositivo, etc.',
  "refuelrate": "Tasa de repostaje",
  "refuellimit": "Cuando el aumento de aceite por minuto es mayor que xxxxL y menor que xxxxL, se considera repostaje.",
  "refueltip": "¡La tasa máxima de repostaje no debe ser inferior a la mínima!",
  viewLimitConf: 'Ver configuración de permisos',
  viewLimit: 'Ver permisos',
  newSysAcc: 'Nueva cuenta del sistema',
  editSysAcc: 'Editar cuenta de permiso',
  virtualAcc: 'Cuenta virtual',
  oriVirtualAcc: 'Cuenta virtual original',
  virtualTip: 'El módulo de cuenta virtual se ha actualizado a un módulo de cuenta del sistema, cree una nueva cuenta del sistema',
  operaTime: 'Tiempo de funcionamiento',
  ipaddr: 'dirección IP',
  businessType: 'Tipo de negocio',
  params: 'Parámetro de solicitud',
  operateType: 'Tipo de operación',
  uAcc: 'cuenta de usuario',
  uName: 'nombre de usuario',
  uType: 'tipo de usuario',
  logDetail: 'Detalles del registro',
  delAccount: 'Borrar cuenta',
  modifyTime: 'Modificar hora',
  unbindlimit: '¡No se puede crear una cerca electrónica de un clic sin los permisos de dispositivo asociados!',
  setSmsTip: 'Si configura la notificación por SMS, primero debe activar la notificación de la plataforma; si la entrega de la notificación de la plataforma es exitosa, la notificación por SMS no es exitosa, debe activar la notificación por SMS nuevamente',
  cusSetComTip: 'Descargo de responsabilidad: el riesgo que conllevan las instrucciones personalizadas no tiene nada que ver con la plataforma',
  cusSetComPas: 'Ingrese la contraseña de la cuenta de inicio de sesión actual',
  cusSetComDes1: 'Las instrucciones personalizadas solo admiten instrucciones en línea.',
  cusSetComDes2: 'Si el dispositivo no responde dentro de los dos minutos posteriores al envío del comando, el proceso finaliza y el estado del comando se considera sin respuesta.',
  cueSetComoffline: 'Das Gerät antwortet nicht und das Senden des benutzerdefinierten Befehls ist fehlgeschlagen!',
  fbType: 'Tipo de Comentarios',
  fbType1: 'Consultivo',
  fbType2: 'Mal funcionamiento',
  fbType3: 'experiencia de usuario',
  fbType4: 'Sugerencias de nuevas funciones',
  fbType5: 'otro',
  upload: 'Subir',
  uploadImg: 'cargar imagen',
  uploadType: 'Sube archivos de tipo .jpg .png .jpeg .gif',
  uploadSize: 'El archivo de carga no puede tener más de 3 M',
  fbManager: 'Gestión de comentarios',
  blManager: 'Gestión de anuncios',
  fbUploadTip: 'Seleccione el tipo de comentarios',
  menuPlatform: "Noticias de la plataforma",
  menuFeedback: "Retroalimentación",
  menuBulletin: "Anuncio de plataforma",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "Comportamiento de conducción",
  BtyjdfghtwsrgGHFEEGRDAF: "Aceleración rápida",
  BtyuwyfgrWERERRTHDAsdDF: "Desaceleración rápida",
  Be2562h253grgsHHJDbRDAF: "Giro cerrado",
  celTemperature:'Temperatura celsius'
};
// 权限tree
lg.limits = {
  "ACC_statistics": "Estadísticas del ACC",
  "Account_Home": "Inicio",
  "Add": "Nuevo",
  "Add_POI": "Agregar PDI",
  "Add_customer": "Añadir usuario",
  "Add_device_group": "Agregar grupo de dispositivos",
  "Add_fence": "Agregar valla",
  "Add_sharing_track": "Agregar pista para compartir",
  "Add_system_account": "Nueva cuenta de permisos",
  "Alarm_details": "Detalles de la alarma",
  "Alarm_message": "Mensaje de alarma",
  "Alarm_overview": "Alarma general",
  "Alarm_statistics": "Informe de la alarma",
  "All_news": "Todos",
  "Associated_equipment": "Dispositivo asociado",
  "Available_points": "Equilibrar",
  "Barcode_statistics": "Estadísticas de código de barras",
  "Batch_Import": "Importación por lotes",
  "Batch_renewal": "Renovación de lotes",
  "Batch_reset": "Reinicio a granel",
  "Bulk_sales": "Venta por lotes",
  "Call_the_police": "Alarma",
  "Customer_details": "Detalles del cliente",
  "Customer_transfer": "Seguir usuario",
  "Delete_POI": "Eliminar PDI",
  "Delete_account": "Borrar cuenta",
  "Delete_customer": "Eliminar usuario",
  "Delete_device": "Eliminar dispositivo",
  "Delete_device_group": "Eliminar grupo de dispositivos",
  "Delete_fence": "Eliminar la valla",
  "Delete_role": "Eliminar rol",
  "Device_List": "Lista de dispositivos",
  "Device_grouping": "Agrupación de dispositivos",
  "Device_transfer": "Transferencia de dispositivo",
  "Due_reminder": "Recordatorio de vencimiento",
  "Edit_details": "Editar detalles",
  "Equipment_management": "Dispositivos",
  "My_clinet": "Dispositivos",
  "Fence": "GeoFence",
  "Fence_management": "Manejo de cercas",
  "Generate": "Generar",
  "Generate_lead-in_points": "Crear Tarjetas de importación",
  "Generate_renewal_points": "Crear renovar tarjeta",
  "Have_read": "Claro",
  "Idle_speed_statistics": "Estadísticas de velocidad inactiva",
  "Import": "Importar",
  "Import_Device": "Agregar nuevo imei",
  "Industry_Statistics": "Estadísticas de la industria",
  "Location_monitoring": "Monitor",
  "Log_management": "Gestión de registros",
  "Mark_read": "Marcado leído",
  "Menu_management": "Gestión de menú",
  "Message_Center": "Centro de notificaciones",
  "Mileage_statistics": "Informe sobre kilometraje",
  "Modify_POI": "Modificar PDI",
  "Modify_device_details": "Modificar los detalles del dispositivo",
  "Modify_device_group": "Modificar grupo de dispositivos",
  "Modify_role": "Modificar rol",
  "Modify_sharing_track": "Modificar la pista para compartir",
  "Modify_user_expiration": "Users batch modification expires",
  "More": "Mas",
  "My_client": "Cliente",
  "New_role": "Nuevo rol",
  "New_users": "Agregar usuario",
  "Oil_statistics": "Estadísticas de combustible",
  "POI_management": "Gestión de puntos de interés",
  "Points_record": "Historial de tarjetas",
  "Push": "Empujar",
  "Quick_sale": "Venta rapida",
  "Renew": "Renovar",
  "Replay": "Replay",
  "Role_management": "Gestión de roles",
  "Run_overview": "Mover la vista",
  "Running_statistics": "Mover la vista",
  "Sales_equipment": "Venta de dispositivo",
  "Set_expiration_reminder": "Establecer recordatorio de vencimiento",
  "Share_track": "Compartir pista",
  "Sharing_management": "compartir Administrar",
  "Speeding_detailed_list": "Detalles de velocidad",
  "Statistical_report": "Informe",
  "Stay_detailed_list": "Estancia detalles",
  "System_account_management": "Cuenta de autoridad",
  "Temperature_statistics": "Estadísticas de temperatura",
  "Transfer": "Mover",
  "Transfer_group": "Grupo de transferencia",
  "Transfer_point": "Mover tarjeta de importación",
  "Transfer_renewal_point": "Mover tarjeta de renovación",
  "Trip_statistics": "Informe de viaje",
  "Unlink": "Desconectar",
  "View": "Vista",
  "View_POI": "Ver PDI",
  "View_device_group": "Ver grupo de dispositivos",
  "View_fence": "Mira valla",
  "View_role": "Ver rol",
  "View_sharing_track": "Ver pista para compartir",
  "Virtual_account": "Cuenta virtual",
  "Voltage_analysis": "Análisis de voltaje",
  "Voltage_statistics": "Estadísticas de voltaje",
  "batch_deletion": "Lote borrar",
  "change_Password": "Contraseña",
  "delete": "Borrar",
  "edit": "Editar",
  "instruction": "Instrucción",
  "modify": "actualizar",
  "monitor": "Monitor",
  "my_account": "INICIO",
  "reset_Password": "restablecer la contraseña",
  "share_it": "Compartir",
  "sub_user": "Cuenta de subcuenta",
  "track": "Seguimiento",
  "Custom_Order": "Instrucción personalizada",
  "GeoKey_Manager": "Gestión de GeoKey",
  "GeoKey_Update": "modificar",
  "GeoKey_Delete": "Eliminar",
  "GeoKey_Add": "añadir",
  "GeoKey_View": "Ver",
  "feedback_manager": "Gestión de comentarios",
  "feedback_list": "Ver",
  "feedback_handle": "Procesando comentarios",
  "proclamat_manager": "Gestión de anuncios",
  "proclamat_manager_list": "Ver anuncio",
  "proclamat_manager_update": "Anuncio de modificación",
  "proclamat_manager_delete": "Eliminar anuncio",
  "proclamat_manager_save": "Nuevo anuncio",
  "device_update_batch_model": "Modificar por lotes el modelo del dispositivo"
}

// 问题文档的内容
lg.questionDocumentArr = [
  [
    "P: La luz indicadora está apagada después de instalar el dispositivo de cableado y está fuera de línea.",
    "R: Después de apagar el automóvil, use el lápiz eléctrico y el medidor universal para medir si el voltaje de la línea conectada está en línea con el rango de voltaje del rastreador de GPS generalmente entre 9 y 36V.<br/>Precauciones de cableado: El personal de instalación y cableado debe comprender la línea del automóvil y tener cierta capacidad práctica para evitar daños en su automóvil causados por un cableado incorrecto.",
  ],
  [
    "P: Dispositivo con cable o dispositivo de rastreo en tiempo real inalámbrico, llamada telefónica o dispositivo de estado de arranque en segundo plano de IoT fuera de línea",
    "A：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Envíe un mensaje de texto para reiniciarlo y espere unos minutos para ver si está en línea. En general, envíe RESET # por favor, póngase en contacto con el distribuidor para determinar.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. La conexión de red es inestable. Mueva el automóvil a un área de buena señal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Después de los pasos anteriores, no ha podido conectarse. Debe comunicarse con el operador de telefonía móvil para verificar si la tarjeta es anormal.",
  ],
  [
    "P: El dispositivo está desconectado en lotes al principio y al final del mes.",
    "R: Verifique si la tarjeta está atrasada. Si es atrasada, recárguela a tiempo y continúe usándola.",
  ],
  [
    "P: El automóvil está conduciendo, la posición del GPS en línea no se actualiza.",
    "A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. El dispositivo de cableado puede enviar el SMS STATUS # para verificar el estado de recepción de la señal del satélite, ver GPS: la búsqueda del satélite es la señal del satélite, esta situación debe verificar la ubicación de la instalación, si se instala de acuerdo con las instrucciones. Mirando hacia arriba, no hay una cubierta de metal en la parte superior.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Envíe el SMS STATUS #, el estado de retorno es GPS: OFF, envíe FACTORY # otra vez, después de recibir la respuesta, haga clic en 5 minutos para ver si hay actualizaciones.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. De acuerdo con los dos métodos anteriores, no se puede eliminar la falla. Póngase en contacto con el vendedor para su reparación.",
  ],
  [
    "P: Por qué la plataforma de carga se está cargando durante mucho tiempo o la pantalla no está llena?",
    "R: La pantalla de alimentación de la plataforma se basa en la información devuelta por el dispositivo para realizar un análisis de datos y determinar la potencia actual del dispositivo. En algunos casos especiales, aparecerá la solución de error de la pantalla de alimentación:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Los datos de alimentación del dispositivo y los datos de posicionamiento del dispositivo se cargan juntos. Si la batería no se ha cambiado durante mucho tiempo, por favor: 1 Lleve el dispositivo a una distancia de entre 100 y 300 metros para actualizar la información de ubicación del dispositivo. Los datos de ubicación y datos se pueden enviar de nuevo a la plataforma para que sea una actualización de la pantalla de alimentación.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. De acuerdo con el cambio del indicador de energía, determine si está completamente cargado (tome S15 como ejemplo). Los pasos de operación son los siguientes: 1 carga de 8 a 10 horas, luego el indicador de energía se vuelve amarillo verde, luego de desconectar la línea de carga, inserte el cable de carga. En 15 minutos, el indicador de encendido se volverá amarillo y verde al máximo, consulte el manual de otros modelos.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, la carga durante mucho tiempo también está llena de electricidad, esta situación puede ser que el voltaje del enchufe de carga sea inferior a 1A, cargue el cabezal de carga de 5V, 1A durante 8-10 horas.",
  ],
  [
    "P: El comando de corte de energía del GPS se ha emitido con éxito. Por qué el automóvil aún no está roto?",
    "Respuesta: Después de que el comando de apagado se emita correctamente, el equipo debe ejecutarse en las siguientes condiciones:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Asegúrese de que el cableado del equipo sea correcto y siga el diagrama de cableado del manual.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, el equipo funciona normalmente, está en estado estático o de conducción, tiene posicionamiento, no está fuera de línea y la velocidad del vehículo no supera los 20 kilómetros por hora.；<br/>Si el vehículo está fuera de línea, no está posicionado, o la velocidad del vehículo supera los 20 kilómetros por hora, el terminal no se ejecutará incluso si el comando de corte de energía se ejecuta correctamente.",
  ],
  [
    "P: Tres años de productos inalámbricos se instalan por primera vez, el dispositivo de visualización no está posicionado o en línea.",
    "A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Encienda el interruptor para verificar si la luz indicadora está parpadeando. Por ejemplo, los indicadores amarillo y verde S18 parpadean al mismo tiempo que lo normal, y la luz intermitente está en la señal de búsqueda. El dispositivo no está encendido. (El estado de los diferentes modelos será diferente. Consulte el manual de otros modelos)<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. La luz indicadora parpadea no en la línea. Si la señal está encendida en una posición deficiente, consígala en un área buena. El área de señal buena no está en la línea, puede apagar durante 1 minuto, reinstalar la tarjeta y luego iniciar la prueba.",
  ],
  [
    "P: El producto de cable se instala por primera vez y el dispositivo de visualización no está posicionado.",
    "A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Observe si el indicador de estado de GPS del terminal es normal. Compruebe el estado del indicador de acuerdo con las instrucciones de los diferentes modelos.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Si la luz indicadora está apagada, el dispositivo no se puede encender normalmente.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, el indicador de la tarjeta (verde amarillo) no está encendido, apague y vuelva a instalar la tarjeta, y luego enciéndalo para ver que la luz normal es normal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;4. Determine si el número de la tarjeta SIM en el dispositivo no está en mora, y si la función de acceso a Internet GPRS es normal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;5. No hay una red GSM en el lugar donde se encuentra el equipo, como el sótano, el túnel, etc., donde la señal es débil, diríjase al lugar donde la cobertura GPRS es buena.<br/>&nbsp;&nbsp;&nbsp;&nbsp;6, la posición del posicionador no debe estar demasiado cerrada, no debe haber objetos metálicos, en la medida de lo posible en la posición de instalación del automóvil. De lo contrario, afecta a la recepción de la señal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;7, arranque normal, parada en el área de señal buena no está en línea, puede volver a emitir el comando de línea para verificar si la interfaz IP y la red de enlace de la tarjeta son normales.",
  ],
  [
    "P: La luz indicadora está apagada después de instalar el dispositivo de cableado y está fuera de línea.",
    "Solución:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Después de apagar el automóvil, use el lápiz eléctrico y el medidor universal para medir si el voltaje de la línea del automóvil conectado está en línea con el rango de voltaje del rastreador de GPS generalmente entre 9 y 36V.<br/>&nbsp;&nbsp;&nbsp;&nbsp;Precauciones de cableado:<br/>&nbsp;&nbsp;&nbsp;&nbsp;El personal de instalación y cableado debe comprender la línea del automóvil y tener cierta capacidad práctica para evitar daños a su automóvil causados por un cableado inadecuado.",
  ],
  [
    "P: Dispositivo con cable o dispositivo de rastreo en tiempo real inalámbrico, llamada telefónica o dispositivo de estado de arranque en segundo plano de IoT fuera de línea",
    "A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Envíe un mensaje de texto para reiniciar, observe unos minutos para ver si está en línea. En general, envíe RESET # por favor, póngase en contacto con el distribuidor para determinar.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. La conexión de red es inestable. Mueva el automóvil a un área de buena señal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Después de los pasos anteriores, no ha podido conectarse. Debe comunicarse con el operador de telefonía móvil para verificar si la tarjeta es anormal.",
  ],
  [
    "P: El equipo está fuera de línea fuera de línea al principio y al final del mes. ",
    "Solución: compruebe si la tarjeta está atrasada. Si está atrasada, recárguela a tiempo y continúe usándola.",
  ],
  [
    "P: El automóvil está conduciendo, la ubicación del GPS en línea no está actualizada ",
    " solución:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. El dispositivo de cableado puede enviar el SMS STATUS # para verificar el estado de recepción de la señal del satélite, ver GPS: la búsqueda del satélite es la señal del satélite, esta situación debe verificar la ubicación de la instalación, si se instala de acuerdo con las instrucciones. Mirando hacia arriba, no hay una cubierta de metal en la parte superior.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Envíe el SMS STATUS #, el estado de retorno es GPS: OFF, envíe FACTORY # nuevamente, luego de recibir la respuesta, OK, observe 5 minutos para ver si la posición está actualizada.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. De acuerdo con los dos métodos anteriores, no se puede eliminar la falla. Póngase en contacto con el vendedor para su reparación.",
  ],
  [
    "P: Por qué la plataforma de carga se está cargando durante mucho tiempo y todavía muestra que no está llena?",
    "La pantalla de alimentación de la plataforma se basa en la información que recibe del dispositivo para realizar un análisis de los datos y determinar la potencia actual del dispositivo. En algunos casos especiales, se producirá un error en la pantalla de alimentación.<br/>Solución:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Los datos de alimentación del dispositivo y los datos de posicionamiento del dispositivo se cargan juntos. Si la batería no se ha cambiado durante mucho tiempo, por favor: 1 Lleve el dispositivo a una distancia de entre 100 y 300 metros para actualizar la información de ubicación del dispositivo. Los datos de ubicación y datos se pueden enviar juntos a la plataforma para que sea una actualización de la pantalla de energía;<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. De acuerdo con el cambio del indicador de energía, determine si está completamente cargado (tome S15 como ejemplo). Los pasos de operación son los siguientes: 1 carga de 8 a 10 horas, luego el indicador de energía se vuelve amarillo verde, luego de desconectar la línea de carga, inserte el cable de carga. En 15 minutos, el indicador de encendido se volverá amarillo y verde al máximo, consulte el manual de otros modelos.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, la carga durante mucho tiempo también está llena de electricidad, esta situación puede ser que el voltaje del enchufe de carga sea inferior a 1A, cargue el cabezal de carga de 5V, 1A durante 8-10 horas.",
  ],
];
lg.webOptDoc = "Próximamente...";
lg.appOptDoc = "Próximamente...";
// 查询参数的帮助
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Query terminal password</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>Query terminal built-in SIM card</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Query owner phone number</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push("<td>Query the value of speed limit</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push("<td>Query the frequency of tracking,the unit is seconds</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push("<td>Query whether tracking is enabled.1:enable,0:disable</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push("<td>Query the illegal migration alarm range,Unit: meter</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push("<td>Query whether SMS alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Query vibration sensitivity is 0 to 15,0 is the highest sensitivity, too high may be false alarm, 15 is the lowest sensitivity</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push("<td>Query whether call alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Query whether GPS filter drift is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the stationary state with no vibration occurs within 5 minutes,and filter all GPS drift</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Query whether sleep function is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the sleeping mode with no vibration occurs within 30 minutes,it will close GPS function and save power </td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Query whether the power off alarm is enabled,1:enbale,0:disabled</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">GPS:</td>');
html.push(
  "<td>Query the satellite signal strength,For example：2300 1223 3431 。。。 a total of 12 sets of four-digit,2300 means: The signal strength from Number 23 satellite is 0,1223 means: The signal strength from Number 12 satellite is 23</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VBAT:</td>');
html.push(
  "<td>Query battery voltage, charging port voltage Charge current For example: VBAT = 3713300:4960750:303500 Indicates that the battery voltage is 3713300uV 3.71v Applied to the charging voltage on the chip 4.96V,Charging current 303mA</td>"
);
html.push("</tr>");
html.push("</table>");
lg.queryparamhelp = html.join("");

// 设置参数的帮助
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Set terminal password,which is only 6 digits</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>Set SIM number of the terminal</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Set the number of mobile phone owners</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push("<td>Set the speed limit value,0-300</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Set up the reported frequency when turn on tracking,Unit:secend</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push("<td>Set up whether open the track,1:open,0:close</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push("<td>Set up the illegal migration alarm range,Unit: meter</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push("<td>Set up whether SMS alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Set up vibration sensitivity of 0 to 15,0 is the highest sensitivity, too high may be false alarm, 15 is the lowest sensitivity</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push("<td>Set up whether call alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Set up whether GPS filter drift is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the stationary state with no vibration occurs within 5 minutes,and filter all GPS drift</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Set up whether sleep function is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the sleeping mode with no vibration occurs within 30 minutes,it will close GPS function and save power</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Set up whether the power off alarm is enabled,1:enbale,0:disabled</td>"
);
html.push("</tr>");
html.push("</table>");
lg.setparamhelp = html.join("");

//批量添加
html = [];
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox"  ' +
    'style="z-index: 999;position:absolute;left:195px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Venta a:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkAdds_treeDiv" +
    "," +
    "bulkAdds_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Platform Due:</td>'
);
html.push("<td>");
html.push(
  '<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Model:</td>'
);
html.push("<td>");
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    "</div>" +
    '<div id="deviceList"></div>' +
    "</div>" +
    "</span>"
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Agregar dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push(
  '<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>'
);
lg.bulkAdds = html.join("");

//批量续费
html = [];
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:92px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Add device:</td>'
);
html.push("<td>");
html.push(
  '<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="re_addNumBox">Actual：<span id="account_re_addNum">0</span>'
);
html.push("</span>");
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<tr>");
html.push(
  '<td style="text-align:right;"><span style="color:red">*</span>Tipo de tarjeta</td>'
);
html.push("<td>");
html.push('<input  type="radio" name="red_cardType"');
html.push(
  'class="easyui-validatebox"  value="3" checked><label>Unaño</label></input>'
);
html.push(
  '<input  type="radio" name="red_cardType" style="margin-left:15px;" '
);
html.push(
  'class="easyui-validatebox" value="4"><label>Para toda la vida</label></input>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td style="text-align: right">Puntos de deduccion</td>');
html.push("<td>");
html.push(
  '<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Usuario expiró:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Observaciones</td>'
);
html.push("<td>");
html.push(
  '<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkRenew = html.join("");

//批量销售，myAccount
html = [];
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:197px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Venta a:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkSales_treeDiv" +
    "," +
    "bulkSales_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push(
  '<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Usuario expiró:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Agregar dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="bs_addNumBox">Actual：<span id="account_bs_addNum">0</span>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="bs_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");

lg.bulkSales = html.join("");

//批量转移1，弹出框
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:152px;top:171px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Agregar dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >Añadir en lotes</a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Move</a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)" >Cancel</a>'
);

lg.bulkTransfer = html.join("");

//批量转移2,myClient
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:142px;top:83px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Agregar dispositivo:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bt_addMachines" style="cursor:pointer" title="' +
    lg.addTo +
    '" src="../../images/main/myAccount/add3.png" />'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);

lg.bulkTransfer2 = html.join("");

//批量转移用户
html = [];
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:141px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">'
);
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("<td></td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");

lg.bulkTransferUser = html.join("");
window.lg = lg