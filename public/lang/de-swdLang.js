var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
var lg = {
  //德语
  //common
  user_guide: 'Benutzerhandbuch',
  remoteSwitch: "Fernschalter",
  pageTitle: "WhatsGPS",
  description:
    site+" basiert auf den neuesten wissenschaftlichen Erkenntnissen und Technologien der Branche und basiert auf der verteilten Verarbeitung von Big Data. WhatsGPS ist die weltweit führende Plattform für Ortungsdienste.",
  pageLang: "Deutsch",
  inputCountTips: "Bitte geben Sie einen Account / IMEI ein",
  inputPasswordTips: "Bitte geben Sie Ihr Passwort ein",
  appDownload: "Client herunterladen",
  siteName: "Positionieren Sie jetzt",
  rememberPassword: "Passwort merken",
  forgetPassword: 'Passwort vergessen',
  noToken: "Bitte geben Sie den Token weiter",
  loginFirst: "Bitte loggen Sie sich zuerst ein",
  move: "Sport",
  stop: "Immer noch",
  query: "Abfrage",
  imeiQuery: "IMEI Nummer Anfrage,",
  delete: "Löschen",
  update: "Ändern",
  cancel: "Abbrechen",
  soft: "Seriennummer",
  more: "Mehr",
  edit: "Bearbeiten",
  useful:'hilfreich',
  useless:'nicht hilfreich',
  about:'About',
  replyFeedback:'Feedback zu "$"',
  add: "Erhöhen",
  addTo: "Hinzufügen",
  addDevice: "Gerät hinzufügen",
  machineName: "Gerätename",
  searchDevice: "Ausrüstung suchen",
  date: "Datum",
  LatestUpdate: "Signal",
  engine: "ACC",
  locTime: "Positionierungszeit",
  locType: "Targeting-Typ",
  startLoc: "Ausgangsposition",
  endLoc: "Endposition",
  address: "Adresse",
  noAddressTips: "Adressinformationen können nicht abgerufen werden",
  lonlat: "Breite und Länge",
  carNO: "Kennzeichen",
  imei: "Gerätenummer (IMEI)",
  IMEI: "IMEI",
  simNO: "SIM-Kartennummer",
  activeTime: "Aktiv",
  expireTime: "Ablaufzeit",
  acceptSubordinateAlarm: "Untergeordneten Alarm annehmen",
  acceptAlarmTips1: "Geprüft",
  acceptAlarmTips2:
    "Sie erhalten Geräte-Alarminformationen von allen untergeordneten Kunden",
  speed: "Geschwindigkeit",
  y: "Jahr",
  M: "Monat",
  d: "Tag",
  h: "Zeit",
  min: "Minute",
  s: "Zweitens",
  _year: "Jahr",
  _month: "Monat",
  _day: "Tag",
  _hour: "Zeit",
  _minute: "Minute",
  _second: "Zweitens",
  confirm: "Bestimmen Sie",
  yes: "Ja",
  car: "Fahrzeug",
  not: "Nein",
  m: "Reis",
  account: "Kontonummer",
  psw: "Passwort",
  save: "Speichern",
  operator: "Bedienung",
  queryNoData: "Es wurden keine Daten abgefragt",
  name: "Name",
  type: "Modell",
  open: "Öffnen",
  close: "Schliessen",
  send: "Senden",
  alarm: "Alarm",
  alarmSetting: "Alarmeinstellung",
  look: "Ansehen",
  tailAfter: "Tracking",
  history: "Wiedergabe",
  dir: "Überschrift",
  locStatus: "Positionierungsstatus",
  machineTypeText: "Modell",
  carUser: "Besitzer",
  machine: "Ausstattung",
  unknowMachineType: "Unbekanntes Modell",
  noCommandRecord: "Das Gerät hat keine Anweisungen",
  type1: "Typ",
  role: "Typ",
  roles: "Typ",
  timeType: "Zeittyp",
  moveSpeed: "Laufgeschwindigkeit",
  signal: "Signal",
  loc: "Positionierung",
  wiretype: "Typ",
  wire: "Verkabelt",
  wireless: "Wireless",
  expire: "Abgelaufen",
  hour: "Stunde",
  hourTo: "Stunde bis",
  remark: "Bemerkungen",
  remarkInfo: "Bemerkungen",
  noPriviledges: "Dieses Konto hat keine Betriebsrechte",
  commandNoOpen:
    "Die aktuelle Gerätebefehlsfunktion wurde noch nicht verwendet.",
  choseDelelePhone:
    "Bitte wählen Sie zuerst die Nummer aus, die Sie löschen möchten.",
  streetView: "Blick auf die Straße",
  wrongFormat: "Eingabeformatfehler",
  inputFiexd: "Bitte geben Sie eine feste Nummer ein",
  serialNumberStart:
    "Bitte geben Sie die Startnummer der Seriennummer ein, die Sie verbinden möchten",
  serialNumberEnd: "Bitte geben Sie die Endnummer fortlaufend ein",
  clickSearchFirst: "Bitte klicken Sie zuerst auf die Gerätenummer!",
  isDeleteDevice:
    "Es kann nicht wiederhergestellt werden, bestätigen Sie das Löschen?",
  //Hinweis zum Plattformfehlercode
  errorTips: "Die Operation ist mit dem Fehlercode fehlgeschlagen",
  error10003: 'Falsches Passwort',
  error90010: 'Das Gerät ist offline und das Senden von benutzerdefinierten Befehlen ist fehlgeschlagen!',
  error70003: 'Der Wert der Fernbedienung darf nicht leer sein',
  error70006: 'Unterstützt die Anweisung nicht oder hat kein Recht, sie zu erteilen',
  error20001: 'Fahrzeug-ID darf nicht leer sein',
  error20012: 'Fahrzeug ist nicht aktiviert',
  error10012: "Alter Passwortfehler",
  error10017: "Löschen fehlgeschlagen, bitte zuerst den Unterbenutzer löschen!",
  error10023: "Löschen fehlgeschlagen, der Benutzer hat ein Gerät",
  error20008: "Hinzufügen fehlgeschlagen, IMEI-Nummer existiert bereits",
  error20006: "Bitte geben Sie die 15-stellige Gerätenummer ein",
  error10019: "Formatfehler des Telefons",
  error10024: "Verkäufe nicht wiederholen",
  error120003: "Der Freigabelink ist abgelaufen",
  error10025: "Geänderte Geräteinformationen dürfen nicht leer sein",
  error2010: "Bitte laden Sie eine Datei hoch",
  error20002: "IMEI-Nummer existiert nicht",
  error10081: "Nicht genügend Verlängerungskarten",
  error10082: 'Das lebenslange Gerät muss nicht aufgeladen werden',
  error3000: 'Die Rolle wurde dem Systemkonto zugewiesen und kann nicht gelöscht werden',
  error103: 'Das Konto wurde deaktiviert. Bitte wenden Sie sich an Ihren Diensteanbieter',
  error124: 'Kann nicht an sich selbst arbeiten',
  // Landung im Zusammenhang login.js
  logining: "Anmelden ... .",
  login: "Einloggen",
  userEmpty: "Benutzername darf nicht leer sein",
  pswEmpty: "Das Passwort darf nicht leer sein",
  prompt: "Tipps",
  accountOrPswError: "Falsches Konto oder Passwort",
  UserNameAlreadyExist: "Das Login-Konto existiert bereits",
  noQualified: "Keine berechtigten Informationen",
  //main.js
  systemName: "Positionieren Sie das Überwachungssystem sofort",
  navTitle_user: ["Bildschirm", "Bericht", "Gerät"],
  navTitle_dealer: [
    "Mein Konto",
    "Mein Kunde",
    "Bildschirm",
    "Weitere Operationen",
  ],
  exitStytem: "Ausfahrt",
  user: "Benutzer",
  UserCenter: "Benutzer",
  alarmInfo: "Alarm",
  confirmExit: "Möchten Sie das System wirklich verlassen?",
  errorMsg: "Grund für den Fehler: ",
  logintimeout:
    "Zeitüberschreitung bei der Anmeldung, bitte melden Sie sich erneut an!",
  clearAlarm: "Klar",
  clear: "Leer",
  searchbtn: "Benutzer suchen",
  print: "Drucken",
  export: "Exportieren",
  // Feedback-Bereich
  feedback: "Rückmeldung",
  feedback_sublime: "Senden",
  alerttitle: "Der Titel darf nicht leer sein!",
  alertcontent: "Feedback darf nicht leer sein!",
  submitfail: "Übermittlung fehlgeschlagen!",
  saveSuccess: "Erfolgreich gespeichert!",
  submitsuccess:
    "Erfolgreich eingereicht! Wir werden Ihr Feedback so schnell wie möglich bearbeiten ~",
  adviceTitle: "Titel",
  adviceTitle_p: "Frage- und Meinungstitel",
  adviceContent: "Fragen und Meinungen",
  adviceContent_p:
    "Beschreiben Sie kurz die Fragen und Kommentare, für die Sie Feedback geben möchten, und wir werden uns für Sie weiter verbessern.",
  contact: "Kontaktinformationen",
  contact_p: "Geben Sie Ihr Telefon oder Ihre E-Mail ein",
  //monitor.js

  myMachine: "Ausstattung",
  all: "Alle",
  online: "Online",
  offline: "Offline",
  unUse: "Neu",
  group: "Gruppierung",
  moveGruop: "Bewegen Sie sich zu",
  arrearage: "Rückstände",
  noStatus: "Staatenlos",
  inputMachineName: "Bitte geben Sie den Gerätenamen / IMEI ein",
  defaultGroup: "Standard",
  offlineLessOneDay: "Offline <1 Tag",
  demoUserForbid: "Erfahrene Benutzer können diese Funktion nicht verwenden",
  shareTrack: "Teilen",
  shareName: "Freigabename",
  liveShare: "Track Sharing in Echtzeit",
  expiration: "Effektive Zeit",
  getShareLink: "Link teilen",
  copy: "Kopieren",
  copySuccess: "Erfolgreiche Kopie!",
  enlarge: "Vergrößern",
  shareExpired: "Der Link zum Teilen ist abgelaufen",
  LinkFailure: "Freigabe-Link konnte nicht geöffnet werden",
  inputShareName: "Bitte geben Sie einen Freigabenamen ein",
  inputValid: "Bitte geben Sie die korrekte gültige Zeit ein",
  //statistics.js
  runOverview: "Betriebsübersicht",
  runSta: "Betriebsstatistik",
  mileageSta: "Meilenstatistik",
  tripSta: "Reisestatistik",
  overSpeedDetail: "Geschwindigkeitsliste",
  stopDetail: "Liste bleiben",
  alarmSta: "Alarmstatistik",
  alarmOverview: "Alarmübersicht",
  alarmDetail: "Alarmliste",
  shortcutQuery: "Schnelle Abfrage",
  today: "Heute",
  yesterday: "Gestern",
  lastWeek: "Letzte Woche",
  thisWeek: "Diese Woche",
  thisMonth: "Diesen Monat",
  lastMonth: "Letzten Monat",
  mileageNum: "Kilometerstand (km)",
  overSpeedNum: "Geschwindigkeit (km / h)",
  overSpeed: "Beschleunigen",
  stopTimes: "Aufenthalt (Zeiten)",
  searchMachine: "Ausstattung",
  speedNum: "Geschwindigkeit (km / h)",
  querying: "Erkundigen",
  stopTime: "Verweilzeit",
  HisToryStopTime: "Bleib",
  clickLookLoc: "Klicken Sie hier, um die Adresse anzuzeigen",
  lookLoc: "Standort anzeigen",
  noData: "Keine Daten",
  alarmTime: "Weckzeit",
  vibrationLevel: "Vibrationspegel",
  vibrationWay: "Alarmmodus",
  acc: "ACC",
  accStatistics: "ACC-Statistiken",
  accType: ["Alle Zustände", "ACC ON", "ACC OFF"],
  accstatus: ["Öffnen Sie, schließen Sie"],
  openAccQuery: "ACC-Abfrage",
  runtime: "Laufzeit",
  //Überwachen der Seitenänderung
  run: "Fahren",
  speed: "Geschwindigkeit",
  //Gerätemanagement
  machineManage: "Gerätemanagement",
  deviceTable: "Mein ziel",
  status: "Zustand",
  havaExpired: "Abgelaufen",
  expiredIn60: "Läuft innerhalb von 60 Tagen ab",
  expiredIn7: "Läuft innerhalb von 7 Tagen ab",
  normal: "Normal",
  allMachine: "Alle Geräte",
  allMachine1: "Alle Geräte",
  expiredIn7Machine: "7 Tage abgelaufen",
  expiredIn60Machine: "60 Tage abgelaufen",
  havaExpiredMachine: "Gerät abgelaufen",

  //history.js
  replay: "Spielen",
  replaytitle: "Wiedergabe",
  choseDate: "Zeit wählen",
  from: "Von",
  to: "Zu",
  startTime: "Startzeit",
  endTime: "Endzeit",
  pause: "Anhalten",
  slow: "Langsam",
  mid: "Medium",
  fast: "Schnell",
  startTimeMsg: "Sie haben keine Startzeit gewählt",
  endTimeMsg: "Sie haben keine Endzeit gewählt",
  smallEnd:
    "Die von Ihnen eingegebene Endzeit ist kürzer als die Startzeit. Bitte neu auswählen!",
  bigInterval:
    "Das eingegebene Zeitintervall darf 31 Tage nicht überschreiten!",
  trackisempty: "Die Flugbahn ist in diesem Zeitraum leer.",
  longitude: "Länge",
  latitude: "Latitude",
  direction: "Richtung",
  stopMark: "Stoppschild",
  setStopTimes: [
    {
      text: "1Minute",
      value: "1",
    },
    {
      text: "2Minuten",
      value: "2",
    },
    {
      text: "3Minuten",
      value: "3",
    },
    {
      text: "5Minuten",
      value: "5",
    },
    {
      text: "10Minuten",
      value: "10",
    },
    {
      text: "15Minuten",
      value: "15",
    },
    {
      text: "20Minuten",
      value: "20",
    },
    {
      text: "30Minuten",
      value: "30",
    },
    {
      text: "45Minuten",
      value: "45",
    },
    {
      text: "1Stunde",
      value: "60",
    },
    {
      text: "6Stunden",
      value: "360",
    },
    {
      text: "12Stunden",
      value: "720",
    },
  ],
  filterDrift: "Filterdrift",
  userType: [
    "Administrator",
    "Händler",
    "Benutzer",
    "Logistik",
    "Vermietung",
    "Fahrzeugbenutzer",
    "Risikokontrolle",
    "professionell",
  ],
  userTypeArr: [
    "Administrator",
    "Händler",
    "Benutzer",
    "Logistik",
    "Leasing",
    "Fahrzeugbenutzer",
    "Risikokontrolle",
    "professionell",
  ],
  machineType: {
    '0': 'Unbekannter Typ',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '83':'S03A',
    '84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: [
    "Unknown Alarm",
    "Vibrationsalarm",
    "off-Alarm",
    "geringer Batteriealarm",
    "SOS für Hilfe",
    "Geschwindigkeitsalarm",
    "einen Zaun Alarm",
    "Verschiebung Alarm",
    "geringer Batteriealarm fremd",
    "die regionale Polizei",
    " disassemble Alarm",
    "lichtempfindlicher Alarm",
    "magnetische Induktion Alarm",
    "Sabotagealarm",
    "Bluetooth-Alarm",
    "Alarmsignal zu maskieren",
    "Pseudo-Basisstationen Alarm",
    "nominierten bar Alarm",
    "nominierte bar Alarm",
    "ein Zaun Alarm",
    "Tür offen Alarm",
    "Ermüdung des Fahrers",
    "in zwei Ladungs ​​Punkt",
    "der beide Ladepunkt",
    "zwei Ladestationen Aufenthalt",
    "Terminal offline",
    "Eingangszaun Alarm",
    "Zaun Alarm ",
    "Eingangszaun Alarm",
    "Zaun Alarm",
    "Ölalarm",
    "ACC ON",
    "ACC OFF",
    "Kollisionsalarm",
  ],
  alarmTypeNew:  {
    '40': 'Hochtemperaturalarm',
    '45': 'Niedrigtemperaturalarm',
    '50': 'Unterspannungsalarm',
    '55': 'Überspannungsalarm',
    '60': 'Parkalarm'
  },
  alarmNotificationType: [
    { type: "Vibrationsalarm", value: 1 },
    { type: "Alarm bei Stromausfall", value: 2 },
    { type: "Alarm bei schwacher Batterie", value: 3 },
    { type: "SOS für Hilfe", value: 4 },
    { type: "Geschwindigkeitswarnung", value: 5 },
    // {type:'Zaun Alarm',value:6},
    { type: "Verdrängungsalarm", value: 7 },
    { type: "geringer Batteriealarm fremd", value: 8 },
    { type: "Alarm außerhalb des Bereichs", value: 9 },
    { type: "Alarm zerlegen", value: 10 },
    { type: "Licht Alarm", value: 11 },

    { type: "Sabotagealarm", value: 13 },

    { type: "Signalabschirmungsalarm", value: 15 },
    { type: "Pseudo-Basisstationsalarm", value: 16 },
    // {type:'Eingangszaunalarm (Plattformentscheidung)',value:17},
    // {type:'Eingangszaunalarm (Terminalentscheidung)',value:18},
    // {type:'Zaun Alarm',value:19},

    { type: "Müdigkeit beim Fahren", value: 21 },
    { type: "Geben Sie zwei Punkte ein", value: 22 },
    { type: "Zwei Punkte", value: 23 },
    { type: "Zwei lange Punkte", value: 24 },
    { type: "Terminal offline", value: 25 },
    // {type:'Eingangszaunalarm (Wetterkontrolle)',value:26},
    // {type:'Zaunalarm (Wetterkontrolle)',value:27}
    { type: "Eingangszaun Alarm", value: 26 },
    { type: "Zaun Alarm", value: 27 },
    { type: "Ölalarm", value: 30 },
    { type: "ACC ON", value: 31 },
    { type: "ACC OFF", value: 32 },
    { type: "Kollisionsalarm", value: 33 },
  ],
  alarmTypeText: "Alarmtyp",
  alarmNotification: "Pusing-Einstellung",
  pointType: [
    "Nicht gezielter ‚‘ GPS ‚‘ Beidou ‚‘ Basisstationsstandort ‚‘ die Positionierung WIFI ",
  ],

  cardType: [
    "Unbekannt Typ ‚‘ Import Jahr Punkt ‚‘ lebenslanger Import Punkt ‚‘ in Karte ‚‘ lebenslange Karte ",
  ],
  // Südosten und Nordwesten
  directarray: ["Osten", "Süden", "Westen", "Norden"],
  // Richtungsfeld
  directionarray: [
    "Nord“, „Nordosten“, „Osten“, „Süd-Ost“, „Süden“ Southwest „“ nach Westen „“ Northwest ",
  ],
  // Positionierungsmethode
  pointedarray: [
    "Nicht positioniert ",
    " GPS ",
    " Basisstation ",
    " Basisstationspositionierung ",
    " WIFI-Positionierung ",
  ],

  //mapVerwandte
  ruler: "Ranging",
  distance: "Verkehrsinformationen",
  baidumap: "Baidu Karte",
  map: "Karte",
  satellite: "Satellit",
  ThreeDimensional: "3D",
  baidusatellite: "Baidu Satellit",
  googlemap: "Google Maps",
  googlesatellite: "Google-Satellit",
  fullscreen: "Vollbild",
  noBaidumapStreetView:
    "Aktueller Standort auf der Baidu-Karte ohne Blick auf die Straße",
  noGooglemapStreetView:
    "Aktueller Standort auf Google Maps ohne Straßenansicht",
  exitStreetView: "Straßenansicht verlassen",
  draw: "Zeichnen",
  finish: "Vervollständigen",
  unknown: "Unbekannt",
  realTimeTailAfter: "Echtzeit-Tracking",
  trackReply: "Track-Wiedergabe",
  afterRefresh: "Nach dem Auffrischen",
  rightClickEnd: "Rechtes Ende, Radius",
  rightClickEndGoogle: "Rechtes Ende - Radius",

  //treeVerwandte
  currentUserMachineCount: "Aktuelle Anzahl der Benutzergeräte",
  childUserMachineCount: "Enthält die Gesamtzahl der Sub-User-Geräte",

  //Fenster bezogen

  electronicFence: "Elektronischer Zaun",
  drawTrack: "Spur zeichnen",
  showOrHide: "Einblenden / Ausblenden",
  showDeviceName: "Gerätename",
  circleCustom: "Runder Brauch",
  circle200m: "Runde 200 Meter",
  polygonCustom: "Polygonanpassung",
  drawPolygon: "Zeichnen eines Polygons",
  drawCircle: "Zeichne einen Kreis",
  radiusMin100:
    "Der minimale Radius des mit r gezeichneten Zauns beträgt 20 Meter. Bitte neu zeichnen. Aktueller Zaunradius:",
  showAllFences: "Zeige alle Zäune",
  lookEF: "Zaun anzeigen",
  noEF: "Es wurden keine elektronischen Zäune gefunden!",
  hideEF: "Versteckter Zaun",
  blockUpEF: "Zaun deaktivieren",
  deleteEF: "Zaun löschen",
  isStartUsing: "Gibt an, ob aktiviert werden soll",
  startUsing: "Aktivieren",
  stopUsing: "Deaktivieren",
  nowEFrange: "Aktuelle Zaunreichweite",
  enableSucess: "Erfolgreich aktiviert",
  unableSucess: "Erfolgreich deaktiviert",
  sureDeleteMorgage: "Stellen Sie sicher, dass Sie den zweiten Punkt löschen",
  enterMorgageName: "Bitte geben Sie den Namen des zweiten Beitrags ein",
  openMorgagelongStayAlarm: "Öffnen Sie den zweiten Langzeitalarm",
  openMorgageinOutAlarm: "Öffnen Sie den eingehenden und ausgehenden Alarm",
  setEFSuccess:
    "Setzen Sie den Zaun erfolgreich und aktivieren Sie die Zaunreichweite",
  setElectronicFence: "Richten Sie den elektronischen Zaun ein",
  drawFence: "Zeichnen eines elektronischen Zauns",
  drawMorgagePoint: "Zeichne zwei Punkte",
  customFence: "Kundenspezifischer Zaun",
  enterFenceTips: "Geben Sie die benutzerdefinierte Eingabeaufforderung ein",
  leaveFenceTips:
    "Verlassen Sie die benutzerdefinierte Zaun-Eingabeaufforderung",
  inputFenceName: "Bitte geben Sie den Zaunnamen ein",
  relation: "Assoziation",
  relationDevice: "Zugehöriges Gerät",
  unRelation: "Nicht zugeordnet",
  hadRelation: "Assoziiert",
  quickRelation: "Ein-Klick-Zuordnung",
  cancelRelation: "Verknüpfung aufheben",
  relationSuccess: "Erfolgreiche Assoziation",
  cancelRelationSuccess: "Trennen Sie sich erfolgreich",
  relationFail: "Assoziationsfehler",
  deviceList: "Geräteliste",
  isDeleteFence: "Gibt an, ob der Zaun gelöscht werden soll",
  choseRelationDeviceFirst:
    "Bitte wählen Sie zuerst das Gerät aus, das Sie zuordnen möchten!",
  choseCancelRelationDeviceFirst:
    "Bitte wählen Sie zuerst das Gerät aus, dessen Verbindung Sie trennen möchten!",
  radius: "Radius",
  //Bitte wählen Sie mindestens eine Warnmethode aus
  setMortgagePoint: "Legen Sie die zweite Seite fest",

  circleMortage: "Runde zwei Punkte",
  polygonMorgage: "Polygonale zwei Punkte",
  morgageSet: "Es wurden zwei Wetten abgeschlossen",
  operatePrompt: "Tipp",
  startDrawing: "Klicken Sie, um mit dem Zeichnen zu beginnen",
  drawingtip1:
    "Klicken Sie mit der Maus, um mit dem Zeichnen zu beginnen, und doppelklicken Sie, um das Zeichnen zu beenden",
  drawingtip2: "Mausklick und Ziehen zum Zeichnen",

  /************************************************/
  endTrace: "Titelwiedergabe ist beendet",
  travelMileage: "Laufleistung ",
  /************************************************/
  myAccount: "Mein Konto",
  serviceProvide: "Dienstleister",
  completeInfo:
    "Bitte geben Sie die folgenden Informationen ein, z. B. Kontaktperson, Telefonnummer. ",
  clientName: "Kundenname",
  loginAccount: "Login-Konto",
  linkMan: "Kontakt",
  linkPhone: "Telefon",
  clientNameEmpty: "Kundenname darf nicht leer sein!",
  updateSuccess: "Erfolgreich aktualisiert!",
  /************************************************/
  oldPsw: "Altes Passwort",
  newPsw: "Neues Passwort",
  confirmPsw: "Passwortbestätigung",
  pswNoSame: "Inkonsistente Passworteingabe",
  pswUpdateSuccess: "Das Passwort wurde erfolgreich geändert!",
  email: "Mailbox",
  oldPwdWarn: "Bitte geben Sie das alte Passwort ein",
  newPwdWarn: "Bitte geben Sie ein neues Passwort ein",
  pwdConfirmWarn: "Bitte bestätigen Sie das neue Passwort",
  /************************************************/
  //Benutzerdefinierte Popup-Baugruppe
  resetPswFailure: "Passwort konnte nicht zurückgesetzt werden",
  notification: "Aufforderung",
  isResetPsw_a: "Wenn Reset‘",
  isResetPsw_b: "’Passwort für?",
  pwsResetSuccess_a: "Hat‘",
  pwsResetSuccess_b: "’Passwort zurückgesetzt auf 123456",
  /************************************************/
  machineSearch: "Gerätesuche",
  search: "Suche",
  clientRelation: "Kundenbeziehung",
  machineDetail: "Einzelheiten",
  machineDetail2: "Gerätedetails",
  machineCtrl: "Anweisung",
  transfer: "Übertragen",
  belongCustom: "Kunde",
  addImeiFirst: "Bitte geben Sie zuerst die IMEI-Nummer an!",
  addUserFirst: "Bitte zuerst Kunden hinzufügen!",
  transferSuccess: "Erfolgreicher Transfer!",
  multiAdd: "In Chargen hinzufügen",
  multiImport: "Batch-Import",
  multiRenew: "Chargenerneuerung",
  //批量修改设备begin
  editDevice:'Gerätemodell ändern',
  deviceAfter: 'Gerätemodell (nach Änderung)',
  editDeviceTips:'Bitte bestätigen Sie, dass das zu ändernde Gerät das gleiche Modell ist und nicht aktiv ist!',
  pleaseChoseDevice: 'Bitte wählen Sie zuerst das Gerät aus, das geändert werden soll!',
  editResult:'Ergebnis bearbeiten',
  successCount:'Gerät erfolgreich modifiziert:',
  failCount:'Fehlerhafte Geräte:',
  //批量修改设备end
  multiDelete: "Massenlöschung",
  canNotAddImei:
    "IMEI existiert nicht und kann nicht zur Liste hinzugefügt werden",
  importTime: "Zeit importieren",
  loginName: "Anmeldename",
  platformDue: "Plattform läuft ab",
  machinePhone: "SIM-Kartennummer",
  userDue: "Nutzer Abgelaufen",
  overSpeedAlarm: "Übergeschwindigkeits-Alarm",
  changeIcon: "Symbol ersetzen",
  dealerNote: "Händleranmerkungen",
  noUserDue: "Bitte geben Sie die Benutzerablaufzeit ein",
  phoneLengththan3: "Die Telefonlänge muss größer als 3 sein",
  serialNumberInput: "Eingabe der Seriennummer",

  /************************************************/
  sending: "Anweisungen senden ..... Bitte warten ...",
  sendFailure: "Senden fehlgeschlagen!",
  ctrlName: "Anweisungsname",
  interval: "Zeitintervall",
  intervalError: "Intervallformatfehler",
  currectInterval: "Bitte geben Sie das richtige Zeitintervall ein!",
  intervalLimit:
    "Stellen Sie den Intervall-Zeitbereich 10-720, Einheit (Minuten) ein",
  intervalLimit2:
    "Stellen Sie den Intervallzeitbereich von 10 bis 5400 in Sekunden ein",
  intervalLimit3:
    "Stellen Sie den Intervallzeitbereich 5-1440 in Einheiten (Minuten) ein.",
  intervalLimit4: "Stellen Sie den Intervall-Zeitbereich 3-999 in Sekunden ein",
  intervalLimit5:
    "Stellen Sie den Intervallzeitbereich von 10 bis 10800 in Einheiten (Sekunden) ein.",
  intervalLimit1:
    "1-999 die Intervall Zeiteinheit (min)",
  intervalLimit6:
    "Stellen Sie den Intervallzeitbereich 1-65535 in Sekunden ein",
  intervalLimit7:
    "Stellen Sie den Intervall-Zeitbereich von 1-999999 in Einheiten (Sekunden) ein.",
  intervalLimit8:
    "Stellen Sie den Intervall-Zeitbereich 0-255 ein. 0 bedeutet Schließen",
  intervalLimit9:
    "Stellen Sie den Intervallzeitbereich von 3 bis 10800 in Einheiten (Sekunden) ein.",
  intervalLimit10:
    "Stellen Sie den Intervall-Zeitbereich 3-86400 in Sekunden ein",
  intervalLimit11:
    "Stellen Sie den Intervall-Zeitbereich 180-86400 in Sekunden ein",
  intervalLimit22:
    "Stellen Sie den Intervall-Zeitbereich 60-86400 in Sekunden ein",
  intervalLimit23:
    "Stellen Sie den Intervall-Zeitbereich 5-60 in Sekunden ein",
  intervalLimit24:
    "Stellen Sie den Intervall-Zeitbereich 10-86400 in Sekunden ein",
  intervalLimit25:
    "Stellen Sie den Intervallzeitbereich 5-43200 in Einheiten (Minuten) ein.",
  intervalLimit12:
    "Stellen Sie den Intervallzeitbereich von 10 bis 60 in Einheiten (Sekunden) ein.",
  intervalLimit13:
    "Stellen Sie den Intervall-Zeitbereich 1-24 (bedeutet 1-24 Stunden) oder 101-107 (bedeutet 1-7 Tage) ein.",
  intervalLimit14:
    "Stellen Sie den Intervallzeitbereich ein10-3600，Einheit (Sekunden)；Standard: 10s ",
  intervalLimit15:
    "Stellen Sie den Intervallzeitbereich ein180-86400，Einheit (Sekunden), Standard: 3600s",
  intervalLimit16:
    "Stellen Sie den Intervallzeitbereich ein1-72，Einheit (Stunde); Standardeinstellung: 24 Stunden",
  intervalLimit17: "Einstelltemperaturbereich -127-127, Einheit (° C)",
  intervalLimit18: "Intervallzeitbereich 5-18000, Einheit (Sekunde) einstellen",
  intervalLimit19: "Intervallzeitbereich 10-300, Einheit (Sekunde) einstellen",
  intervalLimit20: "Intervallzeitbereich 5-399, Einheit (Sekunden) einstellen",
  intervalLimit21: "Intervallzeitbereich 5-300, Einheit (Sekunden) einstellen",
  noInterval: "Bitte geben Sie das Zeitintervall ein!",
  intervalTips:
    "Um den Tracking-Modus auszuschalten, stellen Sie bitte die Weckzeit ein",
  phoneMonitorTips:
    "Nachdem der Befehl gesendet wurde, wählt das Gerät aktiv die Rückrufnummer zur Überwachung.",
  time1: "Zeit1",
  time2: "Zeit2",
  time3: "Zeit3",
  time4: "Zeit4",
  time5: "Zeit5",
  intervalNum: "Intervall (Minuten)",
  sun: "Sonntag",
  mon: "Montag",
  tue: "Dienstag",
  wed: "Mittwoch",
  thu: "Donnerstag",
  fri: "Freitag",
  sat: "Samstag",
  awakenTime: "Wach auf",
  centerPhone: "Nummer des Zentrums",
  inputCenterPhone: "Bitte geben Sie die Zentrumsnummer ein!",
  phone1: "Nummer eins",
  phone2: "Nummer zwei",
  phone3: "Nummer drei",
  phone4: "Nummer vier",
  phone5: "Nummer fünf",
  inputPhone: "Bitte geben Sie die Nummer ein",
  offlineCtrl:
    "Der Offline-Befehl wurde gespeichert und der Offline-Befehl wird automatisch an das Gerät gesendet, nachdem das Gerät online ist.",
  terNotSupport: "Terminal unterstützt nicht",
  terReplyFail: "Terminalantwort fehlgeschlagen",
  machineInfo: "Geräteinformationen",

  /************************************************/
  alarmTypeScreen: "Alarmtyp-Überprüfung",
  allRead: "Alle lasen",
  read: "Lesen",
  noAlarmInfo: "Keine löschbaren Alarminformationen",
  alarmTip:
    "Tipp: Deaktivieren Sie diese Option, um diese Art von Alarminformationen zu filtern",

  /************************************************/
  updatePsw: "Passwort",
  resetPsw: "Passwort zurücksetzen",

  /************************************************/
  multiSell: "Massenverkauf",
  sell: "Vertrieb",
  sellSuccess: "Erfolgreicher Verkauf!",
  modifySuccess: "Erfolgreich geändert",
  modifyFail: "Änderung fehlgeschlagen",
  multiTransfer: "Stapelübertragung",
  multiUserExpires: "Ändern Sie das Ablaufdatum des Benutzers",
  batchModifying: "Stapelbearbeitung",
  userTransfer: "Übertragen",
  machineRemark: "Bemerkungen",
  sendCtrl: "Anweisung senden",
  ctrl: "Anweisung",
  ctrlLog: "Anweisungsliste",
  ctrlLogTips: "Anweisungsliste",
  s06Ctrls: [
    "Remote-Stromabschaltung",
    "Remote-Recovery-Öl und Strom",
    "Abfrage Positionierung",
  ],
  ctrlType: "Anweisungsname",
  resInfo: "Antwortnachricht",
  resTime: "Reaktionszeit",
  ctrlSendTime: "Sendezeit",
  // csvDatei importieren hochladen
  choseCsv: "Bitte wählen Sie eine CSV-Datei",
  choseFile: "Datei auswählen",
  submit: "Senden",
  targeDevice: "Zielgerät",
  csvTips_1: "1, speichern Sie die Excel-Datei als CSV-Format",
  csvTips_2: "2. Importieren Sie die CSV-Datei in das System.",
  importExplain: "Import anweisungen：",
  fileDemo: "Beispiel für ein Dateiformat",

  // Neu
  sendType: "Sendetyp",
  onlineCtrl: "Online-Unterricht",
  offCtrl: "Offline-Unterricht",
  resStatus: [
    "Nicht gesendet, abgelaufen, zugestellt, erfolgreich, fehlgeschlagen, keine Antwort",
  ],
  /************************************************/
  addSubordinateClient: "Fügen Sie einen untergeordneten Benutzer hinzu",
  noSubordinateClient: "Keine untergeordneten Benutzer",
  superiorCustomerEmpty: "Bitte wählen Sie einen übergeordneten Kunden aus",
  noCustomerName: "Bitte geben Sie einen Kundennamen ein",
  noLoginAccount: "Bitte geben Sie einen Login-Account ein",
  noPsw: "Bitte geben Sie Ihr Passwort ein",
  noConfirmPsw: "Bitte geben Sie ein Bestätigungspasswort ein",
  pswNotAtypism:
    "Das eingegebene Passwort und das Bestätigungspasswort sind inkonsistent!",
  addSuccess: "Erfolgreich hinzugefügt",
  superiorCustomer: "Überlegener Kunde",
  addVerticalImei:
    "Bitte geben Sie die IME-Nummer in einer vertikalen Spalte ein",
  noImei: "IMEI existiert nicht und kann nicht zur Liste hinzugefügt werden",
  addImei_curr: "Bitte geben Sie die aktuelle IMEI-Nummer ein",
  no: "Eins",
  aRowAImei: "Geben Sie eine IMEI in einer Zeile ein",

  /*
   * dealer  Die Schnittstellenübersetzung beginnt
   *
   * */
  //main.js
  imeiOrUserEmpty:
    "Die Gerätenummer (IMEI) / der Kundenname darf nicht leer sein!",
  accountEmpty: "Konto darf nicht leer sein!",
  queryNoUser: "Der Benutzer wurde nicht abgefragt",
  queryNoIMEI: "Die IMEI-Nummer wurde nicht abgefragt",
  imeiOrClientOrAccount: "Gerätenummer (IMEI) / Kundenname / Kontonummer",
  dueSoon: "Läuft bald ab",
  recentlyOffline: "Vor kurzem offline",
  choseSellDeviceFirst:
    "Bitte wählen Sie zuerst das Gerät aus, das Sie verkaufen möchten!",
  choseDeviceFirst:
    "Bitte wählen Sie zuerst das Gerät aus, das Sie übertragen möchten!",
  choseDeviceExpiresFirst:
    "Bitte wählen Sie zuerst das Gerät aus, das Sie übertragen möchten!",
  choseRenewDeviceFirst:
    "Bitte wählen Sie zuerst das Gerät aus, das Sie ändern möchten!",
  choseDeleteDeviceFirst:
    "Bitte wählen Sie zuerst das Gerät aus, das Sie löschen möchten!",
  choseClientFirst:
    "Bitte wählen Sie zuerst den Kunden aus, den Sie überweisen möchten!",

  //myClient.js
  clientList: "Kundenliste",
  accountInfo: "Kontoinformationen",
  machineCount: "Anzahl der Geräte",
  stock: "Kauf",
  inventory: "Inventar",
  subordinateClient: "Untergeordneter Benutzer",
  datum: "Informationen",
  monitor: "Überwachung",
  dueMachineInfo: "Abgelaufene Geräteinformationen",
  haveExpired: "Abgelaufen",
  timeRange: [
    "Innerhalb von 7 Tagen",
    "30 Tagen",
    "60 Tagen",
    "7-30 Tagen",
    "30-60 Tagen",
  ],
  offlineMachineInfo: "Offline-Geräteinformationen",
  timeRange1: [
    "In 1H",
    "In 1D",
    "In 7D",
    "In 30D",
    "In 60D",
    "Mehr als 60 Tage",
    "1H-1D",
    "1D-7D",
    "7D-30D",
    "30D-60D",
  ],
  offlineTime: "Offline-Zeit",
  includeSubordinateClient: "Unterkonto einschließen",

  stopMachineInfo: "Stationäre Geräteinformationen",
  stopTime1: "Stationäre Zeit",
  unUseMachineInfo: "Geräteinformationen sind nicht aktiviert",
  unUseMachineCount: "Die Anzahl der nicht aktivierten Geräte beträgt",

  sellTime: "Verkaufszeit",
  detail: "Detailliert",
  manageDevice: "Detailliert",
  details: "Detail",
  deleteSuccess: "Erfolgreich gelöscht!",
  deleteFail: "Löschen fehlgeschlagen!",
  renewalLink: "Erneuerungslink",
  deleteGroupTips: "Gibt an, ob eine Gruppe gelöscht werden soll",
  addGroup: "Gruppe hinzufügen",
  jurisdictionRange: "Kompetenzbereich: Veränderbare Funktion",
  machineSellTransfer: "Ausrüstungsverkaufstransfer",
  monitorMachineGroup: "Gerätegruppierung überwachen",
  jurisdictionArr: [
    "Kundenverwaltung, Nachrichtenverwaltung, Zauneinstellung, Alarminformationen, Verwaltung virtueller Konten, Anweisungen erteilen",
  ],
  confrimDelSim: "Stellen Sie sicher, dass Sie die SIM-Kartennummer löschen:",

  // Rechtsklick-Menü
  sellDevice: "Verkaufsausrüstung",
  addClient: "Neue Kunden hinzufügen",
  deleteClient: "Kunden löschen",
  resetPassword: "Passwort zurücksetzen",
  transferClient: "Kundenübertragung",
  ifDeleteClient: "Ob zu löschen",

  //myAccount
  myWorkPlace: "Werkbank",
  availablePoints: "Verfügbare Punkte",
  yearCard: "Jahreskarte",
  lifetimeOfCard: "Lebenszeitkarte",
  oneyear: "Ein Jahr",
  lifetime: "Lebenslang",
  oneyearPoint: "Ein jahr import punkt",
  commonImportPoint: "Gewöhnlicher Importpunkt",
  lifetimeImportPoint: "Lebenslanger Importpunkt",
  myServiceProvide: "Dienstleister",
  moreOperator: "Weitere Operationen",
  dueMachine: "Gerät abgelaufen",
  offlineMachine: "Offline-Gerät",
  quickSell: "Schnelle Verkäufe",
  sellTo: "Verkauf an",
  machineBelong: "Ausrüstung gehört dazu",
  reset: "Zurücksetzen",
  targetCustomer: "Zielkunde",
  common_lifetimeImport:
    "Normaler Importpunkt (0), lebenslanger Importpunkt (0)",
  cardType1: "Kartentyp",
  credit: "Punkte aufladen",
  generateImportPoint: "Generieren Sie Importpunkte",
  generateImportPointSuc: "Importpunkt erfolgreich generieren!",
  generateImportPointFail: "Importpunkte konnten nicht generiert werden!",
  year_lifeTimeCard: "Jahreskarte (0), Lebenskarte (0)",
  generateRenewPoint: "Erneuerungspunkt generieren",
  transferTo: "Transfer nach",
  transferPoint: "Punkte übertragen",
  transferRenewPoint: "Erneuerungspunkt übertragen",
  pointHistoryRecord: "Gleichgewicht",
  newGeneration: "Neue Generation",
  operatorType: "Vorgangstyp",
  consume: "Verbrauch",
  give: "Geben",
  income: "Einkommen",
  pay: "Ausgaben",
  imeiErr:
    "Damit das Gerät abgefragt werden kann, muss die IMEI-Nummer mindestens 6-stellig sein!",
  accountFirstPage: "Konto-Startseite",

  /*
   * dealer  Ende der Schnittstellenübersetzung
   *
   * */
  // Seite 1.4.8 Wind Control Part Translation
  finrisk: "Finanzielle Risikokontrolle",
  attention: "Achtung",
  cancelattention: "Nicht mehr folgen",
  poweroff: "Ausschalten",
  inout: "Rein und raus",
  inoutEF: "In und aus dem Zaun",
  longstay: "Zweiter Aufenthalt",
  secsetting: "Zweipunkteinstellung",
  EFsetting: "Zauneinstellung",
  polygonFence: "Polygonaler elektronischer Zaun",
  cycleFence: "Runder elektronischer Zaun",
  haveBeenSetFence: "Elektronischer Zaun wurde gesetzt",
  haveBeenSetPoint: "Es wurden zwei Wetten abgeschlossen",
  drawingFailed: "Zeichnung fehlgeschlagen, bitte neu zeichnen",
  inoutdot: "Ein- und Ausstieg",
  eleStatistics: "Elektrizitätsstatistik",
  noData: "Keine Daten",
  // Ein- und Ausstiegsliste
  accountbe: "Konto",
  SMtype: "Zwei-Punkt-Typ",
  SMname: "Zweiter Beitragsname",
  time: "Zeit",
  position: "Lage",
  lastele: "Verbleibende Batterie",
  statisticTime: "Statistische Zeit",
  searchalarmType: [
    "lle, offline, ausschalten, rein und raus aus dem Zaun, rein und raus aus den zwei, zwei langen Aufenthalt",
  ],
  remarks: [
    "Zweite Wette, Garantieunternehmen, Demontagepunkt, Gebrauchtmarkt",
  ],
  focusOnly: "Nur fokussieren",
  // [?]Beschreibung
  interpretSignal:
    "Signal: Das letzte Mal, dass das Gerät mit der Plattform kommuniziert hat",
  interpretPosition:
    "Positionierung: Die letzte GPS-Satellitenpositionierungszeit des Geräts",
  interpretAll:
    "Das Online-Gerät ist nicht im Stillstand positioniert, kommuniziert jedoch weiterhin mit der Plattform",

  autoRecord: "Automatische Aufnahme",
  /******************************************************设置指令开始**********************************8*/
  setCtrl: {
    text: "Anweisung zum Starten setzen",
    value: "",
  },
  moreCtrl: {
      text: 'Weitere Anweisungen',
      value: ''
  },
  sc_openTraceModel: {
    text: "Einstellanleitung",
    value: "0",
  },
  sc_closeTraceModel: {
    text: "Tracking-Modus ausschalten",
    value: "1",
  },
  sc_setSleepTime: {
    text: "Stellen Sie die Schlafzeit ein",
    value: "2",
  },
  sc_setAwakenTime: {
    text: "Weckzeit einstellen",
    value: "3",
  },
  sc_setDismantleAlarm: {
    text: "Manipulationsalarm einstellen",
    value: "4",
  },
  sc_setSMSC: {
    text: "Erhöhen Sie die Zentrumsnummer",
    value: "5",
  },
  sc_delSMSC: {
    text: "Zentrumsnummer löschen",
    value: "6",
  },
  sc_setSOS: {
    text: "SOS hinzufügen",
    value: "7",
  },
  sc_delSOS: {
    text: "SOS entfernen",
    value: "8",
  },
  sc_restartTheInstruction: {
    text: "Befehl neu starten",
    value: "9",
  },
  sc_uploadTime: {
    text: "Legen Sie das Upload-Intervall fest",
    value: "10",
  },
  /*Einstellung der Weckzeit
     Zeitgesteuerte Rückgabezeiteinstellung
     Manipulationsalarmeinstellung
     Wochenmodus ein und aus*/
  sc_setAlarmClock: {
    text: "Stellen Sie die Weckzeit ein",
    value: "11",
  },
  sc_setTimingRebackTime: {
    text: "Stellen Sie die zeitgesteuerte Rückgabezeit ein",
    value: "12",
  },
  sc_openWeekMode: {
    text: "Aktivieren Sie den Wochenmodus",
    value: "13",
  },
  sc_closeWeekMode: {
    text: "Schalten Sie den Wochenmodus aus",
    value: "14",
  },
  sc_powerSaverMode: {
    text: "Stellen Sie den Timing Return-Modus ein",
    value: "15",
  },
  sc_carCatchingMode: {
    text: "Chase-Modus einstellen",
    value: "16",
  },
  sc_closeDismantlingAlarm: {
    text: "Deaktivieren Sie die Einstellungen für den Sabotagealarm",
    value: "17",
  },
  sc_openDismantlingAlarm: {
    text: "Aktivieren Sie die Einstellungen für den Sabotagealarm",
    value: "18",
  },
  sc_VibrationAlarm: {
    text: "Stellen Sie den Vibrationsalarm ein",
    value: "19",
  },
  sc_timeZone: {
    text: "Zeitzoneneinstellung",
    value: "20",
  },
  sc_phoneMonitor: {
    text: "Telefonüberwachung",
    value: "21",
  },
  sc_stopCarSetting: {
    text: "Parkeinstellung",
    value: "22",
  },
  sc_bindAlarmNumber: {
    text: "Verbindliche Alarmnummer",
    value: "23",
  },
  sc_bindPowerAlarm: {
    text: "Alarm bei Stromausfall",
    value: "24",
  },
  sc_fatigueDrivingSetting: {
    text: "Müdigkeit beim Fahren",
    value: "25",
  },
  sc_peripheralSetting: {
    text: "Peripherieeinstellungen",
    value: "26",
  },
  sc_SMSAlarmSetting: {
    text: "SMS Alarm einstellen",
    value: "27",
  },
  sc_autoRecordSetting: {
    text: "Automatische Aufnahmeeinstellungen",
    value: "28",
  },
  sc_monitorCallback: {
    text: "Rückruf überwachen",
    value: "29",
  },
  sc_recordCtrl: {
    text: "Aufnahmeanweisung",
    value: "30",
  },
  sc_unbindAlarmNumber: {
    text: "Alarmnummer entbinden",
    value: "31",
  },
  sc_alarmSensitivitySetting: {
    text: "Empfindlichkeitseinstellung für Vibrationsalarm",
    value: "32",
  },
  sc_alarmSMSsettings: {
    text: "Einstellungen des Vibrationsalarmtelefons",
    value: "33",
  },
  sc_alarmCallSettings: {
    text: "Schalten Sie den Stromausfallalarm ein",
    value: "34",
  },
  sc_openFailureAlarmSetting: {
    text: "Fabrik wiederherstellen",
    value: "35",
  },
  sc_restoreFactory: {
    text: "Fabrik wiederherstellen",
    value: "36",
  },
  sc_openVibrationAlarm: {
    text: "Schalten Sie den Vibrationsalarm ein",
    value: "37",
  },
  sc_closeVibrationAlarm: {
    text: "Schalten Sie den Vibrationsalarm aus",
    value: "38",
  },
  sc_closeFailureAlarmSetting: {
    text: "Schalten Sie den Stromausfallalarm aus",
    value: "39",
  },
  sc_feulAlarm: {
    text: "Alarmeinstellung für die Ölmenge",
    value: "40",
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: "Energiesparmodus",
    value: "41",
  },
  sc_sleepMode: {
    text: "Schlafmodus",
    value: "42",
  },
  sc_alarmMode: {
    text: "Alarmmodus",
    value: "43",
  },
  sc_weekMode: {
    text: "Wochenmodus",
    value: "44",
  },
  sc_monitorNumberSetting: {
    text: "Einstellung der Monitornummer",
    value: "45",
  },
  sc_singlePositionSetting: {
    text: "Single positioning mode",
    value: "46",
  },
  sc_timingworkSetting: {
    text: "Timing Art der Arbeit",
    value: "47",
  },
  sc_openLightAlarm: {
    text: "Alarm Lichtsensor öffnen",
    value: "48",
  },
  sc_closeLightAlarm: {
    text: "Schließen Sie den Lichtsensor-Alarm",
    value: "49",
  },
  sc_workModeSetting: {
    text: "Arbeitsmodus einstellen",
    value: "50",
  },
  sc_timingOnAndOffMachine: {
    text: "Zeitschaltereinstellung",
    value: "51",
  },
  sc_setRealTimeTrackMode: {
    text: "Stellen Sie den Echtzeit-Chase-Modus ein",
    value: "52",
  },
  sc_setClockMode: {
    text: "Alarmmodus einstellen",
    value: "53",
  },
  sc_openTemperatureAlarm: {
    text: "Temperaturalarm einschalten",
    value: "54",
  },
  sc_closeTemperatureAlarm: {
    text: "Temperaturalarm ausschalten",
    value: "55",
  },
  sc_timingPostbackSetting: {
    text: "Timing Postback-Einstellungen",
    value: "56",
  },
  sc_remoteBoot: {
    text: "Remote-Start",
    value: "57",
  },
  sc_smartTrack: {
    text: "Smart Tracking",
    value: "58",
  },
  sc_cancelSmartTrack: {
    text: "Smart Tracking abbrechen",
    value: "59",
  },
  sc_cancelAlarm: {
    text: "Alarm abbrechen",
    value: "60",
  },
  sc_smartPowerSavingMode: {
    text: "Smart Power Saving Mode einstellen",
    value: "61",
  },
  sc_monitorSetting: {
    text: "Monitor",
    value: '62'
  },
  // 指令重构新增翻译
  sc_timedReturnMode: {
    text: 'Zeitgesteuerter Rückgabemodus',
    value: '100'
    },
    sc_operatingMode: {
        text: 'Betriebsart',
        value: '101'
    },
    sc_realTimeMode : {
        text: 'Echtzeit-Positionierungsmodus',
        value: '102'
    },
    sc_alarmMode : {
        text: 'Alarmmodus',
        value: '103'
    },
    sc_weekMode : {
        text: 'Wochenmodus',
        value: '104'
    },
    sc_antidemolitionAlarm : {
        text: 'Abbruchalarm',
        value: '105'
    },
    sc_vibrationAlarm : {
        text: 'Vibrationsalarm',
        value: '106'
    },
    sc_monitoringNumber : {
        text: 'Überwachung der Nummernverwaltung',
        value: '107'
    },
    sc_queryMonitoring : {
        text: 'Abfrageüberwachungsnummer',
        value: '108'
    },
    sc_electricityControl : {
        text: 'Öl- und Stromkontrolle',
        value: '109'
    },
    sc_SOSnumber : {
        text: 'SOS-Nummernverwaltung',
        value: '110'
    },
    sc_SleepCommand : {
      text: 'Schlafbefehl',
      value: '201'
    },
    sc_RadiusCommand : {
        text: 'Verschiebungsradius',
        value: '202'
    },
    sc_punchTimeMode:{
        text:'打卡模式',
        value:'203'  
    },
    sc_intervelMode:{
        text:'时间段模式',
        value:'204'  
    },
    sc_activeGPS:{
        text:'激活GPS',
        value:'205'  
    },
    sc_lowPowerAlert: {
        text: 'Erinnerung an schwache Batterie',
        value: '206'
    },
    sc_SOSAlert: {
        text: 'SOS报警',
        value: '207'
    },
    
    mc_cuscom : {
      text: 'Benutzerdefinierte Anweisung',
      value: '1'
    },
    NormalTrack: 'Normaler Tracking-Modus',
    listeningToNumber:'Sind Sie sicher, dass Sie die Überwachungsnummer von überprüfen möchten?',
    versionNumber:'Sind Sie sicher, dass Sie die Versionsnummer von überprüfen möchten?',
    longitudeAndLatitudeInformation:'Sind Sie sicher, dass Sie die Breiten- und Längengrade von überprüfen möchten?',
    equipmentStatus:'Sind Sie sicher, dass Sie den Status von überprüfen möchten?',
    public_parameter:'Sind Sie sicher, dass Sie die Parameter von überprüfen möchten?',
    GPRS_parameter:'Sind Sie sicher, dass Sie die GPRS-Parameter von überprüfen möchten?',
    deviceName: 'Sind Sie sicher, dass Sie das Gerät rollen möchten?',
    SMS_alert:'Sind Sie sicher, dass Sie den SMS-Erinnerungsalarm von überprüfen möchten?',
    theBindingNumber:'Sind Sie sicher, dass Sie die Bindungsnummer von überprüfen möchten?',
    intervalTimeRange:'Einstellintervall Zeitbereich ist 001-999, Einheit (Minute)',
    pleaseChoose:'Bitte wählen Sie',
    RealTimeCarChase:'Sind Sie sicher, dass Sie dieses Gerät in den Echtzeit-Verfolgungsmodus versetzen möchten?',
    inputPhoneNumber: "Bitte geben Sie die Telefonnummer ein",
    inputCorPhoneNumber: "Bitte geben Sie die richtige Telefonnummer ein",
    autoCallPhone: "Tipp: Nach erfolgreicher Ausführung des Befehls wählt das Endgerät automatisch die eingestellte Nummer",
    limitTheNumberOfCellPhoneNumbers1:'Dieser Befehl unterstützt bis zu 5 Mobiltelefonnummern',
    limitTheNumberOfCellPhoneNumbers2:'Dieser Befehl unterstützt bis zu 3 Mobiltelefonnummern',
    equipmentTorestart:'Sind Sie sicher, dass Sie dieses Gerät neu starten möchten?',
    remindTheWay:'Art der Erinnerung',
    alarmWakeUpTime:'Weckzeit des Alarms',
    alarmWakeUpTime1:'Weckzeit des Alarms 1',
    alarmWakeUpTime2:'Weckzeit des Alarms 2',
    alarmWakeUpTime3:'Weckzeit des Alarms 3',
    alarmWakeUpTime4:'Weckzeit des Alarms 4',
    sensitivityLevel:'Bitte wählen Sie die Empfindlichkeitsstufe',
    parking_time:'Parkzeit',
    selectWorkingMode:'Bitte wählen Sie den Arbeitsmodus',
    Alarm_value:'Alarmwert',
    Buffer_value:'Pufferwert',
    gqg_disconnect:'trennen',
    gqg_turnOn:'Einschalten',
    Return_interval:'Rückgabeintervall',
    gq_startTime:'Startzeit',
    gq_restingTime:'Ruhezeit',
    gq_Eastern:'Östliche Zeitzone',
    gq_Western:'Westliche Zeitzone',

    gq_driver:'Ermüdungsfahralarm',
    gq_deviceName:'Sind Sie sicher, dass Sie dieses Gerät rollen möchten?',
    gq_noteAlarm:'Möchten Sie den SMS-Erinnerungsalarm wirklich überprüfen?',
    gq_restoreOriginal:'Möchten Sie dieses Gerät wirklich in der ursprünglichen Fabrik wiederherstellen?',
    gq_normalMode:'Normaler Modus',
    gq_IntelligentsleepMode:'Smart Sleep-Modus',
    gq_DeepsleepMode:'Tiefschlafmodus',
    gq_RemotebootMode:'Remote-Boot-Modus',
    gq_IntelligentsleepModeTips:'Sind Sie sicher, dass Sie den Smart Sleep-Modus aktivieren möchten?',
    gq_DeepsleepModeTips:'Sind Sie sicher, dass Sie in den Tiefschlafmodus wechseln möchten?',
    gq_RemotebootModeTips:'Sind Sie sicher, dass Sie den Remote-Startmodus aktivieren möchten?',
    gq_normalModeTips:'Sind Sie sicher, dass Sie in den normalen Modus wechseln möchten?',
    gq_sleepModeTips:'Möchten Sie dieses Gerät wirklich in den Ruhemodus versetzen?',
    gq_Locatethereturnmode:'Positionierungsrücklaufmodus',
    gq_regularWorkingHours:'Zeitliche Arbeitszeit',
    gq_AlarmType:{
        text: 'Alarmtyp',
        value: '111'
    },
    IssuedbyThePrompt:'Der Befehl wurde ausgegeben. Warten Sie, bis das Gerät reagiert',
    platformToinform:'Plattformbenachrichtigung',
    gq_shortNote:'SMS-Benachrichtigung', 
  /************Befehlssprache**********************/
  closeDismantlingAlarm:
    "Deaktivieren Sie die Einstellungen für den Sabotagealarm",
  openDismantlingAlarm:
    "Aktivieren Sie die Einstellungen für den Sabotagealarm",
  closeTimingRebackMode:
    "Deaktivieren Sie die Einstellung für den zeitgesteuerten Rückgabemodus",
  minute: "Minute",
  timingrebackModeSetting: "Stellen Sie den Timing-Return-Modus ein:",
  setWakeupTime: "Weckzeit einstellen",
  weekModeSetting: "Stellen Sie den Wochenmodus ein:",
  closeWeekMode: "Deaktivieren Sie die Einstellung für den Tagmodus",
  setRealtimeTrackMode: "Stellen Sie den Echtzeit-Chase-Modus ein",
  fortification: "Befestigung",
  disarming: "Entwaffnen",
  settimingrebackmodeinterval:
    "Stellen Sie das Zeitintervall für den Rückkehrmodus ein:",
  oilCutCommand: "Ölsperrbefehl",
  restoreOilCommand: "Rückgewinnung von Öl- und Gasinstruktionen",
  turnNnTheVehiclesPower: "Fahrzeugleistung aktivieren",
  turnOffTehVehiclesPower: "Schalten Sie das Fahrzeug aus",
  implementBrakes: "Ausführungsbremse",
  dissolveBrakes: "Lösen Sie die Bremsen",
  openVoiceMonitorSlarm: "Aktivieren Sie die Alarmstimme",
  closeVoiceMonitorAlarm: "Alarmton ausschalten",
  openCarSearchingMode: "Schalten Sie den Autosuchmodus ein",
  closeCarSearchingMode: "Schalten Sie den Autosuchmodus aus",
  unrecognizedCommand: "Die Anweisung kann nicht erkannt werden",
  commandSendSuccess:
    "Herzlichen Glückwunsch, der Befehl zur Geräteausführung ist erfolgreich!",
  /********************************************Befehl zum Beenden des Setups**************************************************/

  /********************************************Abfragebefehl beginnt**************************************************/
  queryCtrl: {
    text: "Abfrage Anweisung",
    value: "",
  },
  /*Abfrage der Parametereinstellung*/
  qc_softwareVersion: {
    text: "Software-Version abfragen",
    value: "1",
  },
  qc_latlngInfo: {
    text: "Informationen zu Breiten- und Längengraden abfragen",
    value: "2",
  },
  qc_locationHref: {
    text: "Abfrage Parameterkonfiguration",
    value: "3",
  },
  qc_status: {
    text: "Abfragestatus",
    value: "4",
  },
  qc_gprs_param: {
    text: "GPRS-Parameter abfragen",
    value: "5",
  },
  qc_name_param: {
    text: "Name",
    value: "6",
  },
  qc_SMSReminderAlarm_param: {
    text: "SMS-Erinnerungsalarm abfragen",
    value: "7",
  },
  qc_bindNumber_param: {
    text: "Bindungsnummer abfragen",
    value: "8",
  },

  /********************************************Befehl zum Beenden der Abfrage**************************************************/

  /*******************************************Steuerbefehl beginnt***************************************************/

  controlCtrl: {
    text: "Steueranweisung",
    value: "",
  },
  cc_offOilElectric: {
    text: "Öl ab",
    value: "1",
  },
  cc_recoveryOilElectricity: {
    text: "Rückgewinnung von Öl und Gas",
    value: "2",
  },
  cc_factorySettings: {
    text: "Werksreset",
    value: "4",
  },
  cc_fortify: {
    text: "Befestigung",
    value: "75",
  },
  cc_disarming: {
    text: "Entwaffnen",
    value: "76",
  },
  cc_brokenOil: {
    text: "Anleitung zum Ölschneiden",
    value: "7",
  },
  cc_RecoveryOil: {
    text: "Rückgewinnungsölkreislauf",
    value: "8",
  },

  /*******************************************Befehl zum Beenden der Steuerung***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: "Kilometer",
  mileage: "Kilometerstand",
  importMachine: "Gerät importieren",
  transferImportPoint: "Importpunkt übertragen",
  machineType1: "Ausstattungsmodell",
  confirmIMEI:
    "Bitte bestätigen Sie die IMEI Nummer und das Modell vor dem Betrieb.",
  renew: "Verlängerungsgebühr",
  deductPointNum: "Abzugspunkte",
  renewSuccess: "Erneuerungserfolg!",
  wireType: [
    {
      text: "Verkabelt",
      value: false,
    },
    {
      text: "Wireless",
      value: true,
    },
  ],
  vibrationWays: [
    {
      text: "Plattform",
      value: 0,
    },
    {
      text: "Plattform + SMS",
      value: 1,
    },
    {
      text: "Plattform + SMS + Telefon",
      value: 2,
    },
  ],
  addMachineType: [
    {
      text: "S06",
      value: "3",
    },
    //SO6-Unterebene --- Start -----
    {
      text: "GT06",
      value: "8",
    },
    {
      text: "S08V",
      value: "9",
    },
    {
      text: "S01",
      value: "10",
    },
    {
      text: "S01T",
      value: "11",
    },
    {
      text: "S116",
      value: "12",
    },
    {
      text: "S119",
      value: "13",
    },
    {
      text: "TR06",
      value: "14",
    },
    {
      text: "GT06N",
      value: "15",
    },
    {
      text: "S101",
      value: "16",
    },
    {
      text: "S101T",
      value: "17",
    },
    {
      text: "S06U",
      value: "18",
    },
    {
      text: "S112U",
      value: "19",
    },
    {
      text: "S112B",
      value: "20",
    },
    // SO6 Kind ---- Ende ------
    {
      text: "S15",
      value: "1",
    },
    {
      text: "S05",
      value: "2",
    },
    {
      text: "SW06",
      value: "4",
    },
    {
      text: "S001",
      value: "5",
    },
    {
      text: "S08",
      value: "6",
    },
    {
      text: "S09",
      value: "7",
    },
  ],

  /*
   * 2018-02-02
   * */
  maploadfail:
    "Die aktuelle Karte konnte nicht geladen werden. Wechseln Sie zu einer anderen Karte?",

  /*
    2018-03-06Neuer Steuerbefehl
    * */
  cc_openPower: {
    text: "Schalten Sie das Fahrzeug ein",
    value: "7",
  },
  cc_closePower: {
    text: "Schalten Sie das Fahrzeug aus",
    value: "8",
  },
  cc_openBrake: {
    text: "Die Bremsen des Fahrzeugs anziehen,",
    value: "9",
  },
  cc_closeBrake: {
    text: "Schalten Sie die Fahrzeugbremse aus",
    value: "10",
  },
  cc_openAlmrmvoice: {
    text: "Schalten Sie den Fahrzeugalarm ein",
    value: "11",
  },
  cc_closeAlmrmvoice: {
    text: "Schalten Sie den Fahrzeugalarm aus",
    value: "12",
  },
  /*2018-03-06Neuer Steuerbefehl
   * */
  cc_openFindCar: {
    text: "Schalten Sie das Auto ein, um ein Auto zu finden",
    value: "13",
  },
  cc_closeFindCar: {
    text: "Schalten Sie die Autosuche aus",
    value: "14",
  },

  /*2018-03-19
   * */
  EF: "Zaun",

  /*
    2018-03-29，Erweitertes Feld
    * */
  exData: ["Strom, Voltzahl, Ölmenge, Temperatur", "Widerstand"],

  /*
    2018-04-10
    * */
  notSta: "Positionierung der Basisstation, nicht in der Statistik enthalten",

  // Ölmengenstatistik
  fuelSetting: "Ölmengeneinstellung",
  mianFuelTank: "Haupttank",
  auxiliaryTank: "Sekundärkraftstofftank",
  maximum: "Maximum",
  minimum: "Mindestwert",
  FullTankFuel: "Volles Tankvolumen",
  fuelMinValue: 'Das Kraftstoffvolumen darf 10 l nicht unterschreiten',
  standardSetting: "Standardeinstellung",
  emptyBoxMax: "Maximal leere Kiste",
  fullBoxMax: "Maximal volle Kiste ",
  fuelStatistics: "Ölmengenstatistik",
  settingSuccess: "Erfolgreiches Setup",
  settingFail: "Setup fehlgeschlagen",
  pleaseInput: "Bitte eintreten",
  fuelTimes: "Betankungszeiten",
  fuelTotal: "Gesamtbetankung",
  refuelingTime: 'Auftankzeit',
  fuelDate: "Betankungszeit",
  fuel: "Ölmenge",
  fuelChange: "Ölmengenänderung",
  feulTable: "Ölanalysetisch",
  addFullFilter: "Bitte fügen Sie den vollständigen Filter hinzu",
  enterIntNum: "Bitte geben Sie eine positive ganze Zahl ein",
  // Temperaturstatistik
  tempSta: "Temperaturstatistik",
  tempTable: "Temperaturanalysetabelle",
  industrySta: "Branchenstatistik",
  temperature: "Temperatur",
  temperature1: 'Temperatur1',
  temperature2: 'Temperatur2',
  temperature3: 'Temperatur3',
  tempRange: "Temperaturbereich",
  tempSetting:'Temperatureinstellung',
  tempSensor:'Temperatursensor',
  tempAlert:'Nach dem Ausschalten des Temperatursensors werden keine Temperaturdaten empfangen!',
  phoneNumber: "Handynummer",
  sosAlarm: "SOS-Alarm",
  undervoltageAlarm: "Unterspannungsalarm",
  overvoltageAlarm: "Überspannungsalarm",
  OilChangeAlarm: "Alarm Ölmengenänderung",
  accDetection: "ACC-Erkennung",
  PositiveAndNegativeDetection: "Positive und negative Erkennung",
  alermValue: "Alarmwert",
  bufferValue: "Pufferwert",
  timeZoneDifference: "Zeitzonenunterschied",
  meridianEast: "Meridian Ost",
  meridianWest: "Meridian West",
  max12hour: "Die Eingabedifferenz darf nicht größer als 12 Stunden sein",

  trackDownload: "Track herunterladen",
  download: "Herunterladen",
  multiReset: "Massen-Reset",
  resetSuccess: "Zurücksetzen erfolgreich",
  multiResetTips:
    "Nach dem Zurücksetzen werden alle Testdaten bereinigt, und der Gerätestatus wird als nicht verwendet zurückgesetzt",
  point: "Punkt",
  myplace: "Mein Platz",
  addPoint: "Punkt hinzufügen",
  error10018: "Unzureichende Anzahl von Importpunkten",
  error110:'Objekt existiert nicht',
  error109:'Höchstgrenze überschritten',
  error20013:'Gerätetyp existiert nicht',
  error90001:'Die Seriennummer des Gerätetyps darf nicht leer sein',
  error20003:'Imei kann nicht leer sein',
  inputName: "Bitte geben Sie einen Namen ein",
  virtualAccount: "Virtueller Account",
  createTime: "Erstellungszeit",
  permission: "Erlaubnis",
  permissionRange: "Kompetenzbereich",
  canChange: "Veränderbare Funktion",
  fotbidPassword: "Passwort ändern",
  virtualAccountTipsText:
    "Wenn Sie ein virtuelles Konto erstellen, handelt es sich um ein Alias-Konto des derzeit registrierten Händlerkontos, und Sie können Berechtigungen für das virtuelle Konto festlegen.",
  noOperationPermission: "Das virtuelle Konto hat keine Betriebsrechte",
  number: "Nummer",
  rangeSetting: "Bereichseinstellung",
  setting: "Einstellung",
  // 1.6.1
  duration: "Dauer",
  voltageSta: "Spannungsstatistik",
  voltageAnalysis: "Spannungsanalyse",
  voltageEchart: "Spannungsanalyse-Tabelle",
  platformAlarm: "Plattform-Alarm",
  platformAlarm1: "Telefon",
  platformAndPhone: 'Telefon+Plattformalarm',
  smsAndplatformAlarm: "SMS + Plattformalarm",
  smsAndplatformAlarm1: "SMS",
  smsAndplatformAlarmandPhone: "Plattform Alarm + SMS + Telefon",
  smsAndplatformAlarmandPhone1: "SMS + Telefon",
  more_speed: "Geschwindigkeit",
  attribute: "Attribut",
  profession: "Professionell",
  locationPoint: "Ankerpunkt",
  openPlatform: "Plattform öffnen",
  experience: "Ich möchte erfahren",
  onlyViewMonitor: "Kann nur die Überwachung anzeigen",

  // 1.6.3
  inputAccountOrUserName:
    "Bitte geben Sie eine Kontonummer oder einen Benutzernamen ein",
  noDeviceTips:
    "Haben Sie die relevanten Geräteinformationen nicht gefunden, überprüfen Sie den Kundenpunkt",
  noUserTips:
    "Keine relevanten Benutzerinformationen gefunden, Gerätepunkte überprüfen",
  clickHere: "Hier",
  // 1.6.4
  pointIntervalSelect: "Track-Punkt-Intervall",
  payment: "Bezahlung",
  pleaceClick: "Bitte klicken Sie",
  paymentSaveTips:
    "Sicherheitstipp: Bitte bestätigen Sie mit dem Dienstanbieter die Gültigkeit des Links",
  //1.6.4 hinzugefügt
  fuelAlarmValue: "Alarmwert für die Ölmenge",
  fuelConsumption: "Kraftstoffverbrauch",
  client: "Kunde",
  create: "Neu",
  importPoint: "Punkt importieren",
  general: "Common",
  lifelong: "Lebenslang",
  renewalCard: "Verlängerungskarte",
  settingFuelFirst:
    "Bitte stellen Sie den Alarmwert für die Ölmenge ein, bevor Sie den Befehl senden!",
  overSpeedSetting: "Geschwindigkeitseinstellung",
  kmPerHour: "km/h",
  times: "Times",
  total: "Total",
  primary: "Herr",
  minor: "Stellvertreter",
  unActiveTips:
    "Das Gerät ist nicht aktiviert und kann nicht verwendet werden.",
  arrearsTips: "Das Gerät ist in Verzug und kann diese Funktion nicht nutzen",
  //1.6.5
  loading: "Daten werden geladen ...",
  expirationReminder: "Ablauferinnerung",
  projectName: "Projektname",
  expireDate: "Ablaufzeit",
  changePwdTips:
    "Ihr Passwort ist zu einfach und es besteht ein Sicherheitsrisiko. Bitte ändern Sie Ihr Passwort sofort.",
  pwdCheckTips1: "Vorschläge sind 6-20 Buchstaben, Zahlen oder Symbole",
  pwdCheckTips2: "Das eingegebene Passwort ist zu schwach.",
  pwdCheckTips3: "Ihr Passwort kann komplizierter sein.",
  pwdCheckTips4: "Ihr Passwort ist sicher.",
  pwdLevel1: "Schwach",
  pwdLevel2: "Medium",
  pwdLevel3: "Stark",
  comfirmChangePwd: "Bestimmen Sie das zu ändernde Passwort",
  notSetYet: "Noch nicht festgelegt",
  // 1.6.6
  liter: "l",
  arrearageDayTips: "Fällig in Tagen",
  todayExpire: "Heute fällig",
  forgotPwd: "Passwort vergessen?",
  forgotPwdTips:
    "Bitte kontaktieren Sie den Verkäufer, um Ihr Passwort zu ändern.",
  //1.6.7
  commonProblem: "Häufige Fragen",
  instructions: "Bedienungsanleitung",
  webInstructions: "WEB Anleitung",
  appInstructions: "APP Anleitung",
  acceptAlarmNtification: "Alarmbenachrichtigung erhalten",
  alarmPeriod: "Alarmzeit",
  whiteDay: "Tagsüber",
  blackNight: "Dunkle nacht",
  allDay: "Den ganzen Tag",
  alarmEmail: "Alarmmail",
  muchEmailTips:
    "Es können mehrere Postfächer eingegeben werden, die durch das Symbol „;“ getrennt sind.",
  newsCenter: "Mitteilungszentrum",
  allNews: "Alle Neuigkeiten",
  unReadNews: "Ungelesene Nachricht",
  readNews: "Nachricht lesen",
  allTypeNews: "Alle Nachrichtentypen",
  alarmInformation: "Alarminformationen",
  titleContent: "Titelinhalt",
  markRead: "Als gelesen markiert",
  allRead: "Alle lasen",
  allDelete: "Alles löschen",
  selectFirst: "Bitte wählen Sie es aus, bevor Sie fortfahren!",
  updateFail: "Update fehlgeschlagen!",
  ifAllReadTips: "Sind alle bereit zu lesen?",
  ifAllDeleteTips: "Sind alle gelöscht?",
  stationInfo: "Stationsinformationen",
  phone: "Handy",
  //1.6.72
  plsSelectTime: "Bitte wählen Sie die Zeit!",
  customerNotFound: "Der Kunde konnte nicht gefunden werden",
  //1.6.8
  Postalcode: "Position",
  accWarning: "ACC Alarm",
  canInputMultiPhone:
    "Sie können mehr als eine Mobiltelefonnummer eingeben, bitte benutzen; getrennt",
  noLocationInfo: "Das Gerät hat noch keine Standortinformationen.",
  //1.6.9
  fenceName: "fence name",
  fenceManage: "Zaunmanagement",
  circular: "Runde",
  polygon: "Polygon",
  allFence: "Alles Zaun",
  shape: "Form",
  stationNews: "Site Message",
  phonePlaceholder: 'Sie können mehrere durch "," getrennte Nummern eingeben.',
  addressPlaceholder:
    'Bitte geben Sie die Adresse und die Postleitzahl in der angegebenen Reihenfolge ein, getrennt durch ","',
  isUnbind: "Möchten Sie die Verknüpfung aufheben",
  alarmCar: "Alarmfahrzeug",
  alarmAddress: "Alarmort",
  chooseAtLeastOneTime: "Mindestens einmal auswählen",
  alarmMessage: "Es gibt keine Details zu dieser Alarminformation",
  navigatorBack: "Zurück zum Vorgesetzten",
  timeOverMessage:
    "Benutzerablaufzeit darf nicht länger als Plattformablaufzeit sein",
  //1.7.0
  userTypeStr: "Benutzertyp",
  newAdd: "Neu",
  findAll: "Total",
  findStr: " Übereinstimmende Daten",
  customColumn: "Fertigen Sie besonders an",
  updatePswErr: "Aktualisiertes Passwort fehlgeschlagen",
  professionalUser: "Professioneller Benutzer oder nicht?",
  confirmStr: "Bestätige",
  inputTargetCustomer: "Geben Sie den Zielkunden ein",
  superiorUser: "Überlegener Benutzer",
  speedReport: "Geschwindigkeitsbericht",
  createAccount: "Konto erstellen",
  push: "Drücken",
  searchCreateStr:
    "Es wird ein Konto eingerichtet und das Gerät auf dieses Konto übertragen.",
  allowIMEI: "IMEI-Anmeldung zulassen",
  defaultPswTip:
    "Das Standardkennwort besteht aus den letzten 6 Ziffern der IMEI",
  createAccountTip: "Konto erstellt und Gerät erfolgreich übertragen",
  showAll: "Zeige alle",
  bingmap: "Bing Map",
  areaZoom: "Zoom",
  areaZoomReduction: "Zoom wiederherstellen",
  reduction: "Reduktion",
  saveImg: "Als Bild speichern",
  fleetFence: "Flotte Zaun",
  alarmToSub: "Alarmbenachrichtigung",
  bikeFence: "Fahrradzaun",
  delGroupTip:
    "Löschen fehlgeschlagen, löschen Sie bitte zuerst die interessanten Punkte!",
  isExporting: "Ausfuhr...",
  addressResolution: "Auflösung der Adresse...",
  simNOTip: "Die SIM-Kartennummer kann nur eine Nummer sein",
  unArrowServiceTip:
    "Die Benutzerablaufzeit für die folgenden Geräte ist länger als die Plattformablaufzeit. Wählen Sie sie erneut aus. Die Gerätenummer lautet:",
  platformAlarmandPhone: "Plattform Alarm + Tel",
  openLightAlarm: "Alarm Lichtsensor öffnen",
  closeLightAlarm: "Schließen Sie den Lichtsensor-Alarm",
  ACCAlarm: "ACC-Alarm",
  translateError:
    "Übertragung fehlgeschlagen, Zielbenutzer hat keine Berechtigung",
  distanceTip:
    "Klicken Sie auf OK und doppelklicken Sie, um den Vorgang abzuschließen",
  workMode: "Arbeitsmodus",
  workModeType:
    "0: Normalmodus; 1 Intelligenter Schlafmodus; 2 Tiefschlafmodus",
  clickToStreetMap: "Klicken Sie, um die Straßenkarte zu öffnen",
  current: "Current",
  remarkTip:
    "Bemerkung：Verkaufen Sie nicht gleichzeitig Karten unterschiedlichen Typs",
  searchRes: "Suchergebnis",
  updateIcon: "Symbol ändern",
  youHaveALarmInfo: "Sie haben eine Warnmeldung",
  moveInterval: "Bewegungsintervall",
  staticInterval: "statisches Intervall",
  notSupportTraffic: "Coming soon.",
  ignite: "Acc ON",
  flameout: "Acc OFF",
  generateRenewalPointSuc: "Erneuerungspunkte erfolgreich generieren",
  noGPSsignal: "Nicht positioniert",
  imeiErr2:
    "Bitte geben Sie mindestens die letzten 6 Ziffern der imei-Nummer ein",
  searchCreateStr2:
    "Dadurch wird ein Konto erstellt und dieses Gerät an den Kontonamen übertragen",
  addUser: "Benutzer hinzufügen",
  alarmTemperature: "Alarmtemperaturwert",
  highTemperatureAlarm: "Hochtemperaturalarm",
  lowTemperatureAlarm: "Niedrigtemperaturalarm",
  temperatureTip: "Bitte geben Sie einen Temperaturwert ein！",
  locMode: "Positionierungsmodus",
  imeiInput: "Bitte geben Sie die IMEI-Nummer ein",
  noResult: "Keine übereinstimmenden Ergebnisse",
  noAddressKey:
    "Adressinformationen konnten vorübergehend nicht abgerufen werden",
  deviceGroup: "Gruppierung",
  shareManage: "Teilen",
  lastPosition: "Letzte Position",
  defaultGroup: "Standard",
  tankShape: "Tankform",
  standard: "Standard",
  oval: "Oval",
  irregular: "Irregulär",
  //1.8.4
  inputAddressOrLoc: "Geben Sie Adresse / Breite und Länge ein",
  inputGroupName: "Geben Sie den Gruppennamen ein",
  lock: "Verriegeln",
  shareHistory: "Teilen",
  tomorrow: "Morgen",
  threeDay: "3 Tage",
  shareSuccess: "Freigabe-Link erfolgreich generieren",
  effective: "Wirksam",
  lapse: "Ablauf",
  copyShareLink: "Freigabelink kopieren",
  openStr: "Öffnen",
  closeStr: 'AUS',
  linkError: "Dieser Freigabelink ist abgelaufen",
  inputUserName: "Bitte geben Sie einen Kundennamen ein",
  barCodeStatistics: "Barcode-Statistik",
  barCode: "Barcode",
  sweepCodeTime: "Scan Zeit",
  workModeType2:
    "1 Intelligenter Schlafmodus；2 Tiefschlafmodus；3 Remote Power On / Off-Modus",
  remoteSwitchMode: "Fernschaltmodus",
  saleTime: "Verkaufsdatum",
  onlineTime: "Erscheinungsdatum",
  dayMileage: "Kilometerstand heute",
  imeiNum: "IMEI",
  overSpeedValue: "Überdrehzahlschwelle",
  shareNoOpen: "Freigabelink ist nicht aktiviert",
  addTo2: "Hinzufügen zu",
  overDue: "Abgelaufen",
  openInterface: "Open interface",
  privacyPolicy: 'Datenschutzrichtlinie',
  serviceTerm: 'Nutzungsbedingungen',
  importError:
    "Fehler beim Hinzufügen, die Gerätenummer (IMEI) muss eine 15-stellige Nummer sein",
  importResult: "Ergebnisse importieren",
  totalNum: "total",
  successInfo: "Erfolg",
  errorInfo: "Fail",
  repeatImei: "IMEI repeat",
  includeAccount: "Unterkonto",
  formatError: "Missgebildet",
  importErrorInfo: "Bitte geben Sie eine 15-stellige IMEI-Nummer ein",
  totalMileage: "Gesamtkilometer",
  totalOverSpeed: "Gesamtüberdrehzahl (Zeiten)",
  totalStop: "Total Stop (Zeiten)",
  totalOil: "Total oil",
  timeChoose: "Zeitauswahl",
  intervalTime: "Intervallzeit",
  default: "Standard",
  idleSpeedStatics: "Leerlaufgeschwindigkeitsstatistik",
  offlineStatistics: 'Offline-Statistik',
  idleSpeed: "Leerlaufdrehzahl",
  idleSpeedTimeTip1: "Leerlaufzeit darf nicht leer sein",
  idleSpeedTimeTip2: "Leerlaufzeit muss eine positive ganze Zahl sein",
  averageSpeed: "Durchschnittsgeschwindigkeit",
  averageOil: "Durchschnittlicher Kraftstoffverbrauch",
  oilImgTitle: "Ölanalysediagramm",
  oilChangeDetail: "Details zum Kraftstoffwechsel",
  machineNameError:
    "Der Gerätename darf keine speziellen Symbole (/ ') enthalten.",
  remarkError: "Bemerkungsinformationen dürfen 50 Wörter nicht überschreiten",
  defineColumnTip: "Überprüfen Sie bis zu 12 Artikel",
  pswCheckTip:
    "Der Vorschlag ist eine Kombination aus 6-20 Ziffern, Buchstaben und Symbolen",
  chooseGroup: "Bitte wählen Sie eine Gruppe",
  chooseAgain:
    "Sie können nur die Daten der letzten sechs Monate abfragen, bitte erneut auswählen！",
  noDataTip: "Keine Daten！",
  noMachineNameError: "Bitte wählen Sie ein Gerät aus！",
  loginAccountError: "Anmeldekonto darf nicht 15-stellig sein！",
  includeExpire: "Was abläuft",
  groupNameTip: "Gruppenname darf nicht leer sein！",
  outageTips: "Sind Sie sicher, dass das Öl abgeschnitten ist？",
  powerSupplyTips: "Sind Sie sicher, Öl wiederherzustellen？",
  centerPhoneTips: "Bitte geben Sie die Nummer ein",
  centerPhoneLenTips: "Bitte geben Sie 8-20 Ziffern ein",
  passworldillegal: "Es gibt unzulässige Zeichen",
  // 2.0.0 POI，权限版本
  singleAdd:'Einfach hinzufügen',
  batchImport:'Stapelimport',
  name:'Name',
  icon:'Icon',
  defaultGroup:'Standardgruppe',
  remark:'Markieren',
  uploadFile:'Datei hochladen',
  exampleDownload:'Beispiel Download',
  uploadFiles:'Datei hochladen',
  poiTips1:'Sie können POI importieren, indem Sie eine Excel-Datei mit zugehörigen Informationen hochladen. Bitte folgen Sie dem Format des Beispiels, um die Datei vorzubereiten.',
  poiTips2:'Name: Erforderlich, nicht mehr als 32 Zeichen',
  poiTips3:'Icon: erforderlich, geben Sie 1,2,3,4 ein',
  poiTips4:'Latitude：Erforderlich',
  poiTips5:'Länge：Erforderlich',
  poiTips6:'Gruppenname: Optional, nicht mehr als 32 Zeichen. Wenn der Gruppenname nicht ausgefüllt ist, gehört der POI-Punkt zur Standardgruppe. Wenn der Name der ausgefüllten Gruppe mit dem Namen der erstellten Gruppe übereinstimmt, gehört der POI-Punkt zur erstellten Gruppe. Der Name der Gruppe wurde nicht erstellt. Das System fügt die Gruppe hinzu',
  poiTips7:'Anmerkungen: Optional, nicht mehr als 50 Zeichen',
  // 权限相关
  roleLimit: 'Rollenberechtigungen',
  operateLog: 'Betriebsprotokoll',
  sysAccountManage: 'Behördenkonto',
  rolen: 'Rollen',
  rolename: 'Rollenname',
  addRole: 'Neue Rolle',
  editRole: 'Rolle bearbeiten',
  deleteRole: 'Rolle löschen',
  delRoleTip: 'Möchten Sie diese Rolle wirklich löschen?',
  delAccountTip: 'Möchten Sie dieses Konto wirklich löschen?',
  limitconfig: 'Berechtigungseinstellungen',
  newAccountTip1: 'Das Berechtigungskonto ähnelt dem alten virtuellen Konto und ist das Unterkonto des Administrators. Administratoren können Berechtigungskonten erstellen und Berechtigungskonten unterschiedliche Rollen zuweisen, sodass unterschiedliche Konten unterschiedliche Inhalte und Vorgänge auf der Plattform anzeigen können.',
  newAccountTip2: 'Prozess zum Erstellen eines Berechtigungskontos:',
  newAccountTip31: '1. Auf der Rollenverwaltungsseite',
  newAccountTip32: 'Neue Rolle',
  newAccountTip33: ', Und konfigurieren Sie Berechtigungen für die Rolle.',
  newAccountTip4: '2. Erstellen Sie auf der Seite zur Verwaltung von Berechtigungskonten ein neues Berechtigungskonto und weisen Sie dem Konto Rollen zu.',
  newRoleTip1: 'Administratoren können Rollen erstellen und unterschiedliche Betriebsberechtigungen für unterschiedliche Rollen konfigurieren, um die Geschäftsanforderungen in verschiedenen Szenarien zu erfüllen.',
  newRoleTip2: 'Konfigurieren Sie beispielsweise, ob eine Finanzrolle die Berechtigung zum Suchen und Überwachen hat, ob sie die Berechtigung zum Hinzufügen von Kunden hat, ob sie die Berechtigung zum Ändern von Geräteinformationen hat usw.',
  "refuelrate": "Betankungsrate",
  "refuellimit": "Wenn der Anstieg des Öls pro Minute größer als xxxxL und kleiner als xxxxL ist, wird dies als Auftanken angesehen.",
  "refueltip": "Die maximale Betankungsrate darf nicht unter der minimalen Betankungsrate liegen!",
  viewLimitConf: 'Berechtigungseinstellungen anzeigen',
  viewLimit: 'Berechtigungen anzeigen',
  newSysAcc: 'Neues Systemkonto',
  editSysAcc: 'Berechtigungskonto bearbeiten',
  virtualAcc: 'Virtuelles Konto',
  oriVirtualAcc: 'Ursprüngliches virtuelles Konto',
  virtualTip: 'Das virtuelle Kontomodul wurde zu einem Systemkontomodul aktualisiert. Erstellen Sie ein neues Systemkonto',
  operaTime: 'Betriebszeit',
  ipaddr: 'IP Adresse',
  businessType: 'Unternehmensart',
  params: 'Parameter anfordern',
  operateType: 'Betriebsart',
  uAcc: 'Benutzerkonto',
  uName: 'Nutzername',
  uType: 'Benutzertyp',
  logDetail: 'Protokolldetails',
  delAccount: 'Konto löschen',
  modifyTime: 'Zeit ändern',
  unbindlimit: 'Ohne elektronische Geräteberechtigungen kann kein elektronischer Zaun mit einem Klick erstellt werden!',
  setSmsTip: 'Wenn Sie eine SMS-Benachrichtigung einrichten, müssen Sie zuerst die Plattformbenachrichtigung aktivieren. Wenn die Zustellung der Plattformbenachrichtigung erfolgreich ist und die SMS-Benachrichtigung nicht erfolgreich ist, müssen Sie die SMS-Benachrichtigung erneut aktivieren',
  cusSetComTip: 'Haftungsausschluss: Das durch benutzerdefinierte Anweisungen verursachte Risiko hat nichts mit der Plattform zu tun',
  cusSetComPas: 'Bitte geben Sie das Passwort des aktuellen Login-Kontos ein',
  cusSetComDes1: 'Benutzerdefinierte Anweisungen unterstützen nur Online-Anweisungen.',
  cusSetComDes2: 'Wenn das Gerät nicht innerhalb von zwei Minuten nach dem Senden des Befehls antwortet, wird der Prozess beendet und der Befehlsstatus als keine Antwort beurteilt.',
  cueSetComoffline: 'Das Gerät antwortet nicht und das Senden des benutzerdefinierten Befehls ist fehlgeschlagen!',
  fbType: 'Feedback-Art',
  fbType1: 'Beratend',
  fbType2: 'Fehlfunktion',
  fbType3: 'Benutzererfahrung',
  fbType4: 'Vorschläge für neue Funktionen',
  fbType5: 'andere',
  upload: 'Hochladen',
  uploadImg: 'Bild hochladen',
  uploadType: 'Bitte laden Sie Dateien vom Typ .jpg .png .jpeg .gif hoch',
  uploadSize: 'Die Upload-Datei darf nicht größer als 3 MB sein',
  fbManager: 'Feedback-Management',
  blManager: 'Ankündigungsmanagement',
  fbUploadTip: 'Bitte wählen Sie den Feedback-Typ',
  menuPlatform: "Plattformnachrichten",
  menuFeedback: "Feedback",
  menuBulletin: "Plattformansage",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "Fahrverhalten",
  BtyjdfghtwsrgGHFEEGRDAF: "Schnelle Beschleunigung",
  BtyuwyfgrWERERTHDAsdDF: "Schnelle Verzögerung",
  Be2562h253grgsHHJDbRDAF: "Scharfe Drehung",
  celTemperature:'Celsius-Temperatur.'
};
// 权限tree
lg.limits = {
  "ACC_statistics": "ACC-Statistiken",
  "Account_Home": "Konto-Startseite",
  "Add": "Neu",
  "Add_POI": "POI hinzufügen",
  "Add_customer": "Neue Kunden hinzufügen",
  "Add_device_group": "Gerätegruppe hinzufügen",
  "Add_fence": "Zaun hinzufügen",
  "Add_sharing_track": "Freigabespur hinzufügen",
  "Add_system_account": "Neues Berechtigungskonto",
  "Alarm_details": "Alarmliste",
  "Alarm_message": "Alarmmeldung",
  "Alarm_overview": "Alarmübersicht",
  "Alarm_statistics": "Alarmstatistik",
  "All_news": "Alle Neuigkeiten",
  "Associated_equipment": "Zugehöriges Gerät",
  "Available_points": "Verfügbare Punkte",
  "Barcode_statistics": "Barcode-Statistik",
  "Batch_Import": "Stapelimport",
  "Batch_renewal": "Chargenerneuerung",
  "Batch_reset": "Massen-Reset",
  "Bulk_sales": "Massenverkauf",
  "Call_the_police": "Alarm",
  "Customer_details": "Kundendetails",
  "Customer_transfer": "Kundenübertragung",
  "Delete_POI": "POI löschen",
  "Delete_account": "Konto löschen",
  "Delete_customer": "Kunden löschen",
  "Delete_device": "Gerät löschen",
  "Delete_device_group": "Gerätegruppe löschen",
  "Delete_fence": "Zaun löschen",
  "Delete_role": "Rolle löschen",
  "Device_List": "Geräteliste",
  "Device_grouping": "Gruppierung",
  "Device_transfer": "Geräteübertragung",
  "Due_reminder": "Ablauferinnerung",
  "Edit_details": "Details bearbeite",
  "Equipment_management": "Gerätemanagement",
  "My_clinet": "Gerätemanagement",
  "Fence": "Zaun",
  "Fence_management": "Zaunmanagement",
  "Generate": "generieren",
  "Generate_lead-in_points": "Generieren Sie Importpunkte",
  "Generate_renewal_points": "Erneuerungspunkt generieren",
  "Have_read": "Klar",
  "Idle_speed_statistics": "Leerlaufgeschwindigkeitsstatistik",
  "Import": "Importieren",
  "Import_Device": "Gerät importieren",
  "Industry_Statistics": "Branchenstatistik",
  "Location_monitoring": "Bildschirm",
  "Log_management": "Protokollverwaltung",
  "Mark_read": "Als gelesen markiert",
  "Menu_management": "Menüverwaltung",
  "Message_Center": "Mitteilungszentrum",
  "Mileage_statistics": "Meilenstatistik",
  "Modify_POI": "POI ändern",
  "Modify_device_details": "Gerätedetails ändern",
  "Modify_device_group": "Gerätegruppe ändern",
  "Modify_role": "Rolle ändern",
  "Modify_sharing_track": "Ändern Sie die Freigabespur",
  "Modify_user_expiration": "Ändern Sie das Ablaufdatum des Benutzers",
  "More": "Mehr",
  "My_client": "Mein Kunde",
  "New_role": "Neue Rolle",
  "New_users": "Benutzer hinzufügen",
  "Oil_statistics": "Ölmengenstatistik",
  "POI_management": "POI-Management",
  "Points_record": "Gleichgewicht",
  "Push": "Drücken",
  "Quick_sale": "Schnelle Verkäufe",
  "Renew": "Verlängerungsgebühr",
  "Replay": "Wiedergabe",
  "Role_management": "Rollenverwaltung",
  "Run_overview": "Betriebsübersicht",
  "Running_statistics": "Betriebsstatistik",
  "Sales_equipment": "Verkaufsausrüstung",
  "Set_expiration_reminder": "Legen Sie die Ablauferinnerung fest",
  "Share_track": "Teilen",
  "Sharing_management": "Teilen",
  "Speeding_detailed_list": "Geschwindigkeitsliste",
  "Statistical_report": "Bericht",
  "Stay_detailed_list": "Liste bleiben",
  "System_account_management": "Behördenkonto",
  "Temperature_statistics": "Temperaturstatistik",
  "Transfer": "Übertragen",
  "Transfer_group": "Gruppe übertragen",
  "Transfer_point": "Importpunkt übertragen",
  "Transfer_renewal_point": "Erneuerungspunkt übertragen",
  "Trip_statistics": "Reisestatistik",
  "Unlink": "Verknüpfung aufheben",
  "View": "Ansehen",
  "View_POI": "POI anzeigen",
  "View_device_group": "Gerätegruppe anzeigen",
  "View_fence": "Zaun anzeigen",
  "View_role": "Rolle anzeigen",
  "View_sharing_track": "Freigabespur anzeigen",
  "Virtual_account": "Virtuelles Konto",
  "Voltage_analysis": "Spannungsanalyse",
  "Voltage_statistics": "Spannungsstatistik",
  "batch_deletion": "Massenlöschung",
  "change_Password": "Passwort ändern",
  "delete": "Löschen",
  "edit": "Bearbeiten",
  "instruction": "Anweisung",
  "modify": "Ändern",
  "monitor": "Überwachung",
  "my_account": "Mein Konto",
  "reset_Password": "Passwort zurücksetzen",
  "share_it": "Teilen",
  "sub_user": "Untergeordneter Benutzer",
  "track": "Tracking",
  "Custom_Order": "Benutzerdefinierte Anweisung",
  "GeoKey_Manager": "GeoKey-Management",
  "GeoKey_Update": "ändern",
  "GeoKey_Delete": "löschen",
  "GeoKey_Add": "Hinzufügen",
  "GeoKey_View": "Aussicht",
  "feedback_manager": "Feedback-Management",
  "feedback_list": "Aussicht",
  "feedback_handle": "Feedback verarbeiten",
  "proclamat_manager": "Ankündigungsmanagement",
  "proclamat_manager_list": "Ankündigung anzeigen",
  "proclamat_manager_update": "Änderungsansage",
  "proclamat_manager_delete": "Ansage löschen",
  "proclamat_manager_save": "Neue Ankündigung",
  "device_update_batch_model": "Gerätemodell stapelweise ändern"
}
// Der Inhalt des Problemdokuments
lg.questionDocumentArr = [
  [
    "F: Nach der Installation des Verkabelungsgeräts ist die Kontrollleuchte ausgeschaltet und offline.",
    "A: Nach dem Ausschalten des Fahrzeugs messen Sie mit dem elektrischen Stift und dem Universalmeter, ob die Spannung der angeschlossenen Fahrzeugleitung mit dem GPS-Tracker übereinstimmt. Vorsichtsmaßnahmen für die Verkabelung: Das Installations- und Verkabelungspersonal muss mit der Fahrzeuglinie vertraut sein und über bestimmte praktische Fähigkeiten verfügen, um Schäden an Ihrem Fahrzeug durch falsche Verkabelung zu vermeiden. ",
  ],
  [
    "F: Drahtgebundene Geräte oder drahtlose Echtzeit-Tracking-Geräte, Telefonanrufe oder IoT-Hintergrund-Startstatusgerät offline ",
    "A: 1. Senden Sie eine SMS, um den Computer neu zu starten. Beobachten Sie einige Minuten, um festzustellen, ob der Computer online ist. Senden Sie im Allgemeinen eine RESET-Nummer. Bitte wenden Sie sich an den Händler, um dies zu bestimmen.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Die Netzwerkverbindung ist instabil. Bitte bringen Sie das Fahrzeug in einen guten Signalbereich.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Wenn die oben genannten Schritte nicht verfügbar sind, müssen Sie sich an den Mobilfunkanbieter wenden, um zu überprüfen, ob die Karte nicht normal funktioniert.",
  ],
  [
    "F: Das Gerät ist zu Beginn und am Ende des Monats stapelweise offline.",
    "A: Bitte überprüfen Sie, ob die Karte in Verzug ist. Wenn die Karte in Verzug ist, laden Sie sie bitte rechtzeitig auf und verwenden Sie sie wieder.",
  ],
  [
    "F: Das Auto fährt, die GPS-Online-Position wird nicht aktualisiert.F: Das Auto fährt, die GPS-Online-Position wird nicht aktualisiert.",
    "A:1. Das Kabelgerät kann SMS STATUS # senden, um den Empfangsstatus des Satellitensignals zu überprüfen. Siehe GPS: Satellitensuche Ist das Satellitensignal auf der Suche, muss in dieser Situation der Installationsort überprüft werden, ob es gemäß den Anweisungen installiert wurde. Auf der Oberseite befindet sich keine Metallabdeckung.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. SMS STATUS # senden, Rückgabestatus ist GPS: AUS, bitte FACTORY # erneut senden, nach Erhalt der Antwort OK, 5 Minuten beobachten, um festzustellen, ob ein Update vorliegt.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Nach den beiden oben genannten Methoden kann der Fehler nicht behoben werden. Wenden Sie sich zur Reparatur an den Verkäufer.",
  ],
  [
    "F: Warum zeigt die Ladeplattform beim Laden über einen längeren Zeitraum immer noch an, dass sie nicht voll ist?",
    "A: Die Anzeige der Plattformleistung basiert auf den vom Gerät zurückgegebenen Informationen und führt eine Datenanalyse durch, um die aktuelle Leistung des Geräts zu ermitteln. In einigen besonderen Fällen wird die Fehlerlösung für die Leistungsanzeige angezeigt:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Die Leistungsdaten des Geräts und die Positionsdaten des Geräts werden zusammen hochgeladen. Wenn der Akku längere Zeit nicht gewechselt wurde, gehen Sie wie folgt vor: 1 Bewegen Sie das Gerät 100 bis 300 Meter, um die Standortinformationen des Geräts zu aktualisieren. Die Daten und Standortdaten können zusammen zur Plattform zurückgespeist werden, um eine Auffrischung der Leistungsanzeige zu ermöglichen.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, die Stromanzeige wird bestimmt, ob eine Änderung auf volle Leistung, (als Beispiel für S15) in Übereinstimmung mit den folgenden Schritten: ① 8-10 Stunden des Aufladens und die Stromanzeige, gelb, grün, ziehen Sie das Ladekabel, wird die Ladeleitung eingefügt ist, Die Stromanzeige leuchtet nach 15 Minuten gelbgrün, um den Akku vollständig aufzuladen. Informationen zu anderen Modellen finden Sie in der Bedienungsanleitung.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, das Laden für eine lange Zeit ist auch voll von Elektrizität, diese Situation kann die Ladesteckerspannung niedriger als 1A sein, verwenden Sie bitte die Spannung 5V, 1A Ladekopf, um 8-10 Stunden aufzuladen",
  ],
  [
    "F: Der GPS-Befehl zum Abschalten der Stromversorgung wurde erfolgreich ausgegeben. Warum ist das Auto immer noch nicht kaputt?",
    "Antwort: Nachdem der Ausschaltbefehl erfolgreich erteilt wurde, muss das Gerät unter den folgenden Bedingungen ausgeführt werden:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Vergewissern Sie sich, dass die Verkabelung des Geräts korrekt ist, und folgen Sie dem Schaltplan des Handbuchs.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, das Gerät arbeitet, fahren, oder in einem stationären Zustand, Positionierung, nicht off-line, und die Fahrzeuggeschwindigkeit von nicht mehr als 20 Kilometer, wenn das Fahrzeug ausgeschaltet ist, oder sich das Fahrzeug nicht mehr als 20 Kilometer pro Stunde positioniert, d.h. Wenn der Befehl zum Abschalten der Stromversorgung erfolgreich ausgegeben wird, wird das Terminal nicht ausgeführt. F: Das drahtlose Produkt wird zum ersten Mal seit drei Jahren installiert und das Anzeigegerät ist nicht positioniert oder nicht online.",
    "Antwort: 1. Schalten Sie den Schalter ein, um festzustellen, ob die Anzeigelampe blinkt. Die gelben und grünen Anzeigen blinken gleichzeitig mit dem normalen, langsamen Blinken des Suchsignals und das Gerät leuchtet nicht. (Der Status der verschiedenen Modelle ist unterschiedlich. Weitere Modelle finden Sie im Handbuch.)<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, die Kontrollleuchte blinkt nicht in der Leitung, wenn das Signal verglichen wird Wenn die Differenz aktiviert ist, schalten Sie das Signal in einem guten Bereich ein. Guter Signalbereich ist nicht online, kann ausgeschaltet werden 1 Minute, installieren Sie die Karte erneut und starten Sie den Test.",
  ],
  [
    "F: Das Kabelprodukt wird zum ersten Mal installiert und das Anzeigegerät ist nicht positioniert.",
    "Antwort: 1. Beobachten Sie, ob die GPS-Statusanzeige des Terminals normal ist, überprüfen Sie den Status der Anzeige gemäß den Anweisungen der verschiedenen Modelle.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Wenn die Kontrollleuchte nicht leuchtet, kann das Gerät nicht normal mit Strom versorgt werden.<br/>&nbsp;&nbsp;&nbsp;&nbsp;33, die (grün-gelbe) Kartenanzeige leuchtet nicht, schalten Sie die Karte aus und wieder ein, und schalten Sie sie dann ein, um zu sehen, dass das normale Licht normal ist.<br/>&nbsp;&nbsp;&nbsp;&nbsp;4. Stellen Sie fest, ob die SIM-Kartennummer im Gerät nicht in Verzug ist und ob die GPRS-Internetzugriffsfunktion normal ist.<br/>&nbsp;&nbsp;&nbsp;&nbsp;5. Es gibt kein GSM-Netz an dem Ort, an dem sich die Ausrüstung befindet, wie z. B. im unteren Raum, im Tunnel usw., wo das Signal schwach ist. Bitte öffnen Sie Das Auto wird an einem guten Ort getestet, der von GPRS abgedeckt wird.<br/>&nbsp;&nbsp;&nbsp;&nbsp;6, sollte die Position des Stellungsreglers nicht zu geschlossen sein, keine Metallgegenstände haben, so weit wie möglich in der Einbaulage des Autos. Andernfalls wird der Signalempfang beeinträchtigt.<br/>&nbsp;&nbsp;&nbsp;&nbsp;7, normaler Start, Stopp im Signal-Good-Bereich ist nicht online, Sie können den Zeilenbefehl erneut ausgeben, um zu überprüfen, ob die IP-Schnittstelle und das Karten-Link-Netzwerk normal sind.",
  ],
  [
    "F: Nach der Verkabelung Gerät leuchtet die Installation ist nicht hell, Offline-Lösung, nachdem Sie das Auto, Stift und Universal-Leistungsmesser zu messen, ob die angeschlossene Auto GPS-Tracker Netzspannung entspricht den Spannungsbereich ist in der Regel 9-36V auszuschalten.<br/>&nbsp;&nbsp;&nbsp;&nbsp;Vorsichtsmaßnahmen für die Verdrahtung:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Das Installations- und Verkabelungspersonal muss mit der Fahrzeuglinie vertraut sein und über bestimmte praktische Fähigkeiten verfügen, um Schäden an Ihrem Fahrzeug durch unsachgemäße Verkabelung zu vermeiden.",
  ],
  [
    "F：Kabelgebundenes Gerät oder drahtloses Echtzeit-Tracking-Gerät, das Telefon ist verbunden oder der IoT-Hintergrund ist aktiviert, und das Gerät ist offline.",
    "A: 1. Senden Sie eine SMS, um den Computer neu zu starten. Beobachten Sie einige Minuten, um festzustellen, ob der Computer online ist. Senden Sie im Allgemeinen eine RESET-Nummer. Bitte wenden Sie sich an den Händler, um dies zu bestimmen.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Die Netzwerkverbindung ist instabil. Bitte bringen Sie das Fahrzeug in einen guten Signalbereich.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Nach den obigen Schritten war es nicht möglich, online zu gehen. Sie müssen sich an den Mobilfunkanbieter wenden, um zu überprüfen, ob die Karte nicht normal funktioniert.",
  ],
  [
    "F: Das Gerät ist zu Beginn und am Ende des Monats offline. ",
    " Lösung: Überprüfen Sie, ob es sich um eine Karte handelt Die Zahlungsrückstände sind gesunken. Wenn es sich um einen Zahlungsrückstand handelt, laden Sie ihn bitte rechtzeitig auf und nehmen Sie ihn wieder in Betrieb.",
  ],
  [
    "F: Die Fahrten mit dem Auto, GPS-Position ist nicht Online-Update ‚‘ Lösung: 1 Anschlussvorrichtung kann Textnachricht STATUS # sehen das Satellitensignal empfängt Status siehe GPS senden: suchender Satelliten, der auf der Suche nach Satellitensignal gegeben hat, muss diese Situation überprüfen Installationsort, ob es gemäß den Anweisungen installiert wird. Auf der Oberseite befindet sich keine Metallabdeckung.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. SMS STATUS # senden, der Rückgabestatus ist GPS: AUS, bitte FACTORY # erneut senden, nach Erhalt der Antwort OK, 5 Minuten beobachten, um zu sehen, ob die Position aktualisiert wurde.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Nach den beiden oben genannten Methoden kann der Fehler nicht behoben werden. Wenden Sie sich zur Reparatur an den Verkäufer.",
  ],
  [
    "F: Warum zeigt die Ladeplattform beim Laden über einen längeren Zeitraum immer noch an, dass sie nicht voll ist? Die Anzeige der Plattformleistung basiert auf den vom Gerät zurückgegebenen Informationen, um eine Datenanalyse zur Ermittlung der aktuellen Leistung des Geräts durchzuführen. In einigen besonderen Fällen tritt der Fehler bei der Leistungsanzeige auf. Lösung: 1. Die Leistungsdaten des Geräts werden zusammen mit den Positionsdaten des Geräts hochgeladen.Wenn sich der Akku seit dem Aufladen über einen längeren Zeitraum nicht geändert hat, gehen Sie wie folgt vor: 1 Bewegen Sie das Gerät 100 bis 300 Meter, um die Standortinformationen des Geräts zu aktualisieren. Die Leistungsdaten und Standortdaten können zusammen zur Plattform zurückgespeist werden, um die Leistungsanzeige zu aktualisieren.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, die Stromanzeige wird bestimmt, ob eine Änderung auf volle Leistung, (als Beispiel für S15) in Übereinstimmung mit den folgenden Schritten: ① 8-10 Stunden des Aufladens und die Stromanzeige, gelb, grün, ziehen Sie das Ladekabel, wird die Ladeleitung eingefügt ist, Innerhalb von 15 Minuten leuchtet die Netzanzeige gelb und grün auf, andere Modelle finden Sie in der Bedienungsanleitung.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, das Laden für eine lange Zeit ist auch voll von Elektrizität, diese Situation kann die Ladesteckerspannung niedriger als 1A sein, bitte 5V, 1A Ladekopf für 8-10 Stunden aufladen.",
  ],
];
lg.webOptDoc =
  "<h1>一、Schritte zum Hinzufügen eines Benutzerkontos</h1>" +
  "<ol>" +
  "<li>" +
  "1. Geben Sie im Browser unsere Plattform https://www.gpsnow.net ein und geben Sie das Händlerkonto ein." +
  "Die Nummer wird in die Anmeldeschnittstelle der Plattform eingegeben." +
  "</li>" +
  "<li>" +
  "2. Nach erfolgreicher Anmeldung wird die Startseite der Plattform angezeigt. Klicken Sie oben in der Navigationsleiste auf (siehe Abbildung unten)." +
  "Mein Klient" +
  "</li>" +
  "<li>" +
  "3. Wählen Sie einen Kunden in der Liste aller Kunden aus und wählen Sie dann Hinzufügen, um eine neue untergeordnete Benutzergemeinschaft einzublenden" +
  "Gesicht (wie unten gezeigt)." +
  '<img src="../../lib/document/cn/img/1.png"/>' +
  "</li>" +
  "<li>" +
  "4. Weisen Sie den Benutzertyp entsprechend den Benutzeranforderungen zu (überprüfen Sie die Verwendung der beruflichen Erlaubnis für den S208-Öltemperatursensor)" +
  "Das Gerät zeigt weitere Informationen)" +
  "</li>" +
  "<li>" +
  "5.Der Kundenname kann mit dem Anmeldekonto oder dem gleichen inkonsistent sein." +
  "</li>" +
  "<li>" +
  "6. Nach dem Betreten des Informationspunktes ist die Anzeige erfolgreich abgeschlossen." +
  "</li>" +
  "<li>" +
  "7. Nach erfolgreicher Anmeldung wird die Startseite der Plattform angezeigt. Klicken Sie oben in der Navigationsleiste auf (siehe Abbildung unten)." +
  "Mein Klient" +
  "</li>" +
  "<li>" +
  "8. Nach erfolgreicher Anmeldung wird die Startseite der Plattform angezeigt. Klicken Sie oben in der Navigationsleiste auf (siehe Abbildung unten)." +
  "Mein Klient" +
  "</li>" +
  "</ol>" +
  "<h1>二、Verkaufsausrüstung Schritt</h1>" +
  "<ol>" +
  "<li>" +
  "1. Geben Sie das untergeordnete Benutzerkonto in das Suchfeld ein (siehe unten)." +
  "</li>" +
  "<li>" +
  "2. Wählen Sie das Konto auf der linken Seite und die rechte Maustaste erscheint." +
  '<img src="../../lib/document/cn/img/2.png"/>' +
  "</li>" +
  "<li>" +
  "3. Geben Sie eine Einzel- oder Chargen-IMEI-Nummer ein und geben Sie die Chargen-IMEI-Nummer ein." +
  '<img src="../../lib/document/cn/img/3.png"/>' +
  "</li>" +
  "<li>" +
  '4. Geben Sie den zu bestätigenden Punkt ein und klicken Sie auf "Senden". Das System fordert Sie auf, den Verkauf erfolgreich abzuschließen.' +
  "</li>" +
  "</ol>" +
  "<h1>三、Senden Sie dem Benutzer ein neues Konto, um sich anzumelden.</h1>" +
  "<ol>" +
  "<li>" +
  "1. Der Browser öffnet https://www.gpsnow.net, um die Anmeldeschnittstelle aufzurufen und das Kontopasswort einzugeben (" +
  "Wie unten gezeigt)." +
  "</li>" +
  "<li>" +
  "2. Führen Sie Kunden dazu, den unten stehenden QR-Code für die Android- oder iOS-App der nächsten Woche zu verwenden." +
  "</li>" +
  "<li>" +
  "3. Erstellen Sie im ersten Schritt das Konto und das Kennwort und senden Sie es an den untergeordneten Benutzer, um sich anzumelden." +
  "</li>" +
  '<img src="../../lib/document/cn/img/4.png"/>' +
  '<img src="../../lib/document/cn/img/5.png"/>' +
  "<li>" +
  "Anzeige der Webseitenüberwachungsseite nach der Anmeldung" +
  "</li>" +
  "<li>" +
  "Der Benutzer überwacht das Gerät hier, überprüft die Gerätestatistik und Alarminformationen und zeigt die Gerätedetails an." +
  "Und ändern Sie Ihre eigenen Daten und Ihr Login-Passwort." +
  "</li>" +
  "</ol>";
lg.appOptDoc = "Bleiben Sie dran ...";
// Fragen Sie die Parameter-Hilfe ab
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Fragen Sie das Kennwort des Terminals ab</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push(
  "<td>Fragen Sie die integrierte SIM-Kartennummer des Terminals ab</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Überprüfen Sie die Handynummer des Besitzers</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push("<td>Geschwindigkeitsgrenzwert für Überdrehzahlalarm abfragen</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Fragen Sie die Häufigkeit der Berichterstellung nach dem Start der Verfolgung in Sekunden ab</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push(
  "<td>Fragen Sie ab, ob die Nachverfolgung aktiviert werden soll, 1, um die Nachverfolgung zu aktivieren, und 0, um die Nachverfolgung zu deaktivieren</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push(
  "<td>Fragen Sie den Beurteilungsbereich für den ungültigen Schaltalarm ab. Die Einheit ist Meter</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>Abfrage, ob der Vibrations-SMS-Alarm aktiviert werden soll. 1 ist aktiviert, um 0 auszuschalten</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Abfrage Vibrationsempfindlichkeit 0 ~ 15, 0 ist die höchste Empfindlichkeit, zu hoch kann falsch positiv sein, 15 ist die niedrigste Empfindlichkeit</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>Abfrage, ob der Vibrationstelefonalarm aktiviert werden soll. 1 ist aktiviert, um 0 auszuschalten"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Abfragefilter GPS Driftfunktion eingeschaltet ist, 1 ausgeschaltet ist, 0, wenn der Alarm auf Vibration gedreht wird, nicht innerhalb von 5 Minuten auftritt, kommt dann zum Stillstand, alle GPS Auslenkung des Filter</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Abfrage Sleep-Funktion eingeschaltet ist, ein 0 ausgeschaltet ist, wird die Vibration eingeschaltet, wenn der Alarm innerhalb von 30 Minuten nicht auftritt, in dem Ruhezustand, Kettenspaltung GPS aus, um Energie zu sparen</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Fragen Sie nach, ob die Stromausfall-Alarmfunktion ausgeschaltet werden soll. 1 bedeutet, 0 einzuschalten, um auszuschalten</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">GPS:</td>');
html.push(
  "<td>Fragen Sie die vom GPS empfangene Satellitennummer und -intensität ab, zum Beispiel: 2300 1223 3431. . . Insgesamt 12 vierstelliger zeigt 2300, dass die empfangene Signalstärke eines Satellitennummer 23 00,1223 zeigt an, dass die empfangene Signalstärke des Satelliten 12 bis 23 nummerierte</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VBAT:</td>');
html.push(
  "<td>Abfrage der Batteriespannung, der Ladeschnittstellenspannung, Ladestrom, zum Beispiel: VBAT = 3713300: 4960750: 303500 zeigt die Batteriespannung 3713300uV, d.h. 3.71v, die Ladespannung von 4.96V, Ladestrom von 303mA</td>"
);
html.push("</tr>");
html.push("</table>");
lg.queryparamhelp = html.join("");

// Hilfe beim Einstellen der Parameter
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push(
  "<td>Stellen Sie das Terminal-Passwort ein, das Passwort kann nur 6-stellig sein</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>Stellen Sie die SIM-Kartennummer im Terminal ein</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Legen Sie die Handynummer des Besitzers fest</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push(
  "<td>Stellen Sie den Geschwindigkeitsgrenzwert für den Überdrehzahlalarm ein. Der Bereich sollte zwischen 0 und 300 liegen</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Legen Sie die Berichtsfrequenz in Sekunden fest, nachdem das Tracking aktiviert wurde.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push(
  "<td>Legen Sie fest, ob die Verfolgung aktiviert werden soll, 1 das Aktivieren der Verfolgung und 0 das Deaktivieren der Verfolgung</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push(
  "<td>Stellen Sie den Beurteilungsbereich für den ungültigen Schaltalarm ein. Die Einheit ist Meter</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>Legen Sie fest, ob der Vibrations-SMS-Alarm aktiviert werden soll. 1 wird aktiviert, um 0 auszuschalten"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Stellen Sie die Vibrationsempfindlichkeit auf 0 ~ 15, 0 ist die höchste Empfindlichkeit, zu hoch kann falsch positiv sein, 15 ist die niedrigste Empfindlichkeit</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>Stellen Sie ein, ob der Vibrationstelefonalarm aktiviert werden soll, 1 ist eingeschaltet, 0 ist ausgeschaltet"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Filter einstellen, ob GPS Driftfunktion, a 0 ausgeschaltet sind, wird die Vibration eingeschaltet, wenn der Alarm innerhalb von 5 Minuten nicht auftritt, kommt dann zum Stillstand, alle GPS Auslenkung des Filter</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Legen Sie fest, ob die Sleep-Funktion aktiviert werden soll. 1 wird aktiviert, um 0 zu deaktivieren, wenn es aktiviert ist</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Legen Sie fest, ob die Stromausfall-Alarmfunktion aktiviert werden soll. 1 wird aktiviert, um 0 auszuschalten</td>"
);
html.push("</tr>");
html.push("</table>");
lg.setparamhelp = html.join("");

//In Chargen hinzufügen
html = [];
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox" ' +
    'style="z-index: 999;position:absolute;left:198px;top:88px;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Hinzufügen zu:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkAdds_treeDiv" +
    "," +
    "bulkAdds_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Plattform läuft ab:</td>'
);
html.push("<td>");
html.push(
  '<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Ausstattungsmodell:</td>'
);
html.push("<td>");
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    "</div>" +
    '<div id="deviceList"></div>' +
    "</div>" +
    "</span>"
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Gerät hinzufügen:</td>'
);
html.push("<td>");
html.push(
  '<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push(
  '<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>'
);
lg.bulkAdds = html.join("");

//Chargenerneuerung
html = [];
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:92px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Gerät hinzufügen:</td>'
);
html.push("<td>");
html.push(
  '<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="re_addNumBox">Current：<span id="account_re_addNum">0</span>'
);
html.push("</span>");
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<tr>");
html.push(
  '<td style="text-align:right;"><span style="color:red">*</span>Kartentyp</td>'
);
html.push("<td>");
html.push('<input  type="radio" name="red_cardType"');
html.push(
  'class="easyui-validatebox"  value="3" checked><label>Ein Jahr</label></input>'
);
html.push(
  '<input  type="radio" name="red_cardType" style="margin-left:15px;" '
);
html.push(
  'class="easyui-validatebox" value="4"><label>Lebenslang</label></input>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td style="text-align: right">Abzugspunkte</td>');
html.push("<td>");
html.push(
  '<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Benutzer abgelaufen:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Bemerkungen</td>'
);
html.push("<td>");
html.push(
  '<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkRenew = html.join("");

// //Massenverkauf，myAccount
html = [];
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel treePulldownBox"  style="z-index: 999;position:absolute;left:181px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Verkauf an:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkSales_treeDiv" +
    "," +
    "bulkSales_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push(
  '<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Benutzer abgelaufen:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Gerät hinzufügen:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="bs_addNumBox">Current：<span id="account_bs_addNum">0</span>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="bs_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");

lg.bulkSales = html.join("");

//Stapelübertragung 1, Popup-Box，
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:127px;top:172px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Zielkunde:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Gerät hinzufügen:</td>'
);
html.push("<td>");
html.push(
  '<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >In Chargen hinzufügen</a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Übertragen</a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)">Abbrechen</a>'
);

lg.bulkTransfer = html.join("");

//Stapelübertragung 2,myClient
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:117px;top:84px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Zielkunde:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Gerät hinzufügen:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bt_addMachines" style="cursor:pointer" title="' +
    lg.addTo +
    '" src="../../images/main/myAccount/add3.png" />'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);

lg.bulkTransfer2 = html.join("");

//Übertragen Sie Benutzer in großen Mengen
html = [];
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:126px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Zielkunde:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">'
);
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("<td></td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
lg.bulkTransferUser = html.join("");
window.lg = lg