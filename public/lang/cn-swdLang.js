//简体中文
var host = window.location.host
var site = 'WhatsGPS' //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) {
  //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {
  //马来西亚justrack定制页my.justrack
  site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {
  //台湾客户定制页bngps.top
  site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {
  ////马来西亚evo定制页evo
  site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {
  ////fleet.ownmake.co.uk
  site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {
  ////马来西亚定制页ytrack.my
  site = 'YTRACK'
} else if (host.indexOf('justrackai.com') != -1) {
  ////马来西亚定制页justrackai.com
  site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {
  ////马来西亚定制页justrackai.com
  site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {
  ////barry定制页camino-tech.com
  site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {
  ////geolock.sn
  site = 'GEOLOCK'
} else if (host.indexOf('countertrack') != -1) {
  ////countertrack.com
  site = 'COUNTERTRACK'
} else if (host.indexOf('prevatechgps') != -1) {
  ////countertrack.com
  site = 'Prevatech'
} else if (host.indexOf('forcegps') != -1) {
  ////countertrack.com
  site = 'Forcegps'
}
var lg = {
  //common
  user_guide: '用户指南',
  remoteSwitch: '远程开关机',
  pageTitle: '立即定位全球位置服务平台',
  description: '立即定位（' + site + '）以行业最新科学技术为基础，依托大数据分布式处理，致力于为用户提供智能云定位服务，是全球领先的位置服务平台。',
  pageLang: '简体中文',
  inputCountTips: '请输入账号/IMEI',
  inputPasswordTips: '请输入密码',
  appDownload: '客户端下载',
  siteName: '立即定位',
  rememberPassword: '记住密码',
  forgetPassword: '忘记密码',
  noToken: '请传token',
  loginFirst: '请先登录',
  move: '运动',
  stop: '静止',
  query: '查询',
  imeiQuery: 'IMEI',
  delete: '删除',
  update: '修改',
  cancel: '取消',
  soft: '序号',
  more: '更多',
  edit: '编辑',
  useful: '有帮助',
  useless: '无帮助',
  replyFeedback: '关于“$”的反馈回复',
  add: '增加',
  addTo: '添加',
  addDevice: '添加设备',
  machineName: '设备名称',
  searchDevice: '搜设备',
  date: '日期',
  LatestUpdate: '信号',
  engine: 'ACC',
  locTime: '定位时间',
  locType: '定位类型',
  startLoc: '开始位置',
  endLoc: '结束位置',
  address: '地址',
  noAddressTips: '无法获取地址信息',
  lonlat: '经纬度',
  carNO: '车牌号',
  imei: '设备号（IMEI）',
  IMEI: 'imei',
  simNO: 'SIM卡号',
  activeTime: '激活时间',
  expireTime: '到期时间',
  acceptSubordinateAlarm: '接受下级报警',
  acceptAlarmTips1: '勾选后',
  acceptAlarmTips2: '您会收到所有下级客户的设备报警信息',
  speed: '速度',
  y: '年',
  M: '月',
  d: '天',
  h: '时',
  min: '分',
  s: '秒',
  _year: '年',
  _month: '月',
  _day: '天',
  _hour: '时',
  _minute: '分',
  _second: '秒',
  confirm: '确定',
  yes: '是',
  car: '车辆',
  not: '否',
  m: '米',
  account: '账号',
  psw: '密码',
  save: '保存',
  operator: '操作',
  queryNoData: '未查询到任何数据',
  name: '名称',
  type: '型号',
  open: '开',
  close: '关',
  send: '发送',
  alarm: '报警',
  alarmSetting: '报警设置',
  look: '查看',
  tailAfter: '跟踪',
  history: '回放',
  dir: '航向',
  locStatus: '定位状态',
  machineTypeText: '型号',
  carUser: '车主',
  machine: '设备',
  unknowMachineType: '未知型号',
  noCommandRecord: '该设备无指令',
  type1: '类型',
  role: '类型',
  roles: '类型',
  timeType: '时间类型',
  moveSpeed: '运行速度',
  signal: '信号',
  loc: '定位',
  wiretype: '类型',
  wire: '有线',
  wireless: '无线',
  expire: '到期',
  hour: '小时',
  hourTo: '小时 至',
  remark: '备注',
  remarkInfo: '备注信息',
  noPriviledges: '该账号无操作权限',
  commandNoOpen: '当前设备指令功能暂未开放使用',
  choseDelelePhone: '请先选择要删除的号码',
  streetView: '街景',
  wrongFormat: '输入格式错误',
  inputFiexd: '请输入固定数字',
  serialNumberStart: '请输入要连号的开始数字',
  serialNumberEnd: '请输入要连号的结束数字',
  clickSearchFirst: '请先点击搜索设备号！',
  isDeleteDevice: '设备删除后不可恢复，是否删除？',
  //平台错误代码提示
  errorTips: '操作失败，错误代码为：',
  error10003: '密码错误',
  error90010: '设备不在线，自定义指令发送失败！',
  error70003: '远程控制值不能为空',
  error70006: '不支持或无权下发该指令',
  error20001: '车辆ID不能为空',
  error20012: '车辆未激活',
  error10012: '旧密码错误',
  error10017: '删除失败，请先删除子用户!',
  error10023: '删除失败，该用户有设备',
  error20008: '添加失败，IMEI号已经存在',
  error20006: '请输入15位的设备号',
  error10019: '电话格式错误',
  error10024: '请勿重复销售',
  error120003: '分享链接已过期失效',
  error10025: '修改的设备信息不能为空',
  error2010: '请上传文件',
  error20002: 'IMEI号不存在',
  error10081: '续费卡数量不足',
  error10082: '终身设备无需续费！',
  error3000: '该角色已被分配给系统账号，无法删除',
  error103: '该账号已停用，请联系您的服务商',
  error124: '无法对自身操作',
  // 登陆相关 login.js
  logining: '登录中...',
  login: '登录',
  userEmpty: '用户名不能为空',
  pswEmpty: '密码不能为空',
  prompt: '温馨提示',
  accountOrPswError: '账号或者密码错误',
  UserNameAlreadyExist: '该登录账号已存在',
  noQualified: '没有符合条件的信息',
  //main.js
  systemName: '立即定位监控系统',
  navTitle_user: ['定位监控', '统计报表', '设备管理'],
  navTitle_dealer: ['我的账户', '我的客户', '监控平台', '更多操作'],
  exitStytem: '退出',
  user: '用户',
  UserCenter: '用户',
  alarmInfo: '报警',
  confirmExit: '确定退出系统?',
  errorMsg: '错误原因: ',
  logintimeout: '登录超时,请重新登录!',
  clearAlarm: '清除',
  clear: '清空',
  searchbtn: '搜用户',
  print: '打印',
  export: '导出',
  // 意见反馈部分
  feedback: '反馈',
  feedback_sublime: '提交',
  alerttitle: '标题不能为空！',
  alertcontent: '反馈内容不能为空！',
  submitfail: '提交失败！',
  saveSuccess: '保存成功！',
  submitsuccess: '提交成功！我们会尽快处理您的反馈~',
  adviceTitle: '标题',
  adviceTitle_p: '问题和意见标题',
  adviceContent: '问题与意见',
  adviceContent_p: '简要描述您要反馈的问题和意见，我们将为您不断改进。',
  contact: '联系方式',
  contact_p: '填写您的手机或邮箱',
  //monitor.js

  myMachine: '设备',
  all: '全部',
  online: '在线',
  offline: '离线',
  unUse: '未用',
  group: '分组',
  moveGruop: '移至',
  arrearage: '欠费',
  noStatus: '无状态',
  inputMachineName: '请输入设备名/IMEI',
  defaultGroup: '默认分组',
  offlineLessOneDay: '离线<1天',
  demoUserForbid: '体验用户不能使用此功能',
  shareTrack: '分享',
  shareName: '分享名称',
  liveShare: '实时轨迹分享',
  expiration: '有效时间',
  getShareLink: '生成分享链接',
  copy: '复制',
  copySuccess: '复制成功!',
  enlarge: '放大',
  shareExpired: '分享链接已过期',
  LinkFailure: '分享链接打开失败',
  inputShareName: '请输入分享名称',
  inputValid: '请输入正确的有效时间',
  //statistics.js
  runOverview: '运行总览',
  runSta: '运行统计',
  mileageSta: '里程统计',
  tripSta: '行程统计',
  overSpeedDetail: '超速详单',
  stopDetail: '停留详单',
  alarmSta: '报警统计',
  alarmOverview: '报警总览',
  alarmDetail: '报警详单',
  shortcutQuery: '快捷查询',
  today: '今天',
  yesterday: '昨天',
  lastWeek: '上周',
  thisWeek: '本周',
  thisMonth: '本月',
  lastMonth: '上月',
  mileageNum: '里程（公里）',
  overSpeedNum: '超速(km/h)',
  overSpeed: '超速',
  stopTimes: '停留（次）',
  searchMachine: '设备',
  speedNum: '速度(km/h)',
  querying: '正在查询',
  stopTime: '停留时间',
  HisToryStopTime: '停留',
  clickLookLoc: '点击查看地址',
  lookLoc: '查看位置',
  noData: '暂无数据',
  alarmTime: '报警时间',
  vibrationLevel: '震动等级',
  vibrationWay: '报警方式',
  acc: 'ACC',
  accStatistics: 'ACC统计',
  accType: ['全部状态', 'ACC 打火', 'ACC 熄火'],
  accstatus: ['开', '关'],
  openAccQuery: 'ACC查询',
  runtime: '运行时长',
  //监控页面修改
  run: '行驶',
  speed: '速度',
  //设备管理
  machineManage: '设备管理',
  deviceTable: '我的目标',
  status: '状态',
  havaExpired: '已过期',
  expiredIn60: '60天内到期',
  expiredIn7: '7天内到期',
  normal: '正常',
  allMachine: '全部设备',
  allMachine1: '全部设备',
  expiredIn7Machine: '7天过期',
  expiredIn60Machine: '60天过期',
  havaExpiredMachine: '过期设备',

  //history.js
  replay: '播放',
  replaytitle: '回放',
  choseDate: '选择时间',
  from: '从',
  to: '到',
  startTime: '开始时间',
  endTime: '结束时间',
  pause: '暂停',
  slow: '慢',
  mid: '中',
  fast: '快',
  startTimeMsg: '您还未选择开始时间',
  endTimeMsg: '您还未选择结束时间',
  smallEnd: '您输入的结束时间小于开始时间，请重新选择!',
  bigInterval: '您输入的时间间隔不能超过31天!',
  trackisempty: '该时段内轨迹为空',
  longitude: '经度',
  latitude: '纬度',
  direction: '方向',
  stopMark: '停留标识',
  setStopTimes: [
    {
      text: '1分钟',
      value: '1'
    },
    {
      text: '2分钟',
      value: '2'
    },
    {
      text: '3分钟',
      value: '3'
    },
    {
      text: '5分钟',
      value: '5'
    },
    {
      text: '10分钟',
      value: '10'
    },
    {
      text: '15分钟',
      value: '15'
    },
    {
      text: '20分钟',
      value: '20'
    },
    {
      text: '30分钟',
      value: '30'
    },
    {
      text: '45分钟',
      value: '45'
    },
    {
      text: '1小时',
      value: '60'
    },
    {
      text: '6小时',
      value: '360'
    },
    {
      text: '12小时',
      value: '720'
    }
  ],
  filterDrift: '过滤漂移',
  userType: ['管理员', '经销商', '用户', '物流', '租赁', '车辆用户', '风控', '专业'],
  userTypeArr: ['管理员', '经销商', '用户', '物流', '租赁', '车辆用户', '风控', '专业'],
  machineType: {
    '0': '未知类型',
    '1': 'S15',
    '2': 'S05',
    '93': 'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96': 'S16L',
    '97': 'S16LA',
    '98': 'S16LB',
    '3': 'S06',
    '4': 'SW06',
    '5': 'S001',
    '6': 'S08',
    '7': 'S09',
    '8': 'GT06',
    '9': 'S08V',
    '10': 'S01',
    '11': 'S01T',
    '12': 'S116',
    '13': 'S119',
    '14': 'TR06',
    '15': 'GT06N',
    '16': 'S101',
    '17': 'S101T',
    '18': 'S06U',
    '19': 'S112U',
    '20': 'S112B',
    '21': 'SA4',
    '22': 'SA5',
    '23': 'S208',
    '24': 'S10',
    '25': 'S101E',
    '26': 'S709',
    '99': 'S709L',
    '27': 'S1028',
    '28': 'S102T1',
    '29': 'S288',
    '30': 'S18',
    '31': 'S03',
    '32': 'S08S',
    '33': 'S06E',
    '34': 'S20',
    '35': 'S100',
    '36': 'S003',
    '37': 'S003T',
    '38': 'S701',
    '39': 'S005',
    '40': 'S11',
    '41': 'T2A',
    '42': 'S06L',
    '43': 'S13',
    '86': 'S13-B',
    '44': 'GT800',
    '45': 'S116M',
    '46': 'S288G',
    '47': 'S09L',
    '48': 'S06A',
    '49': 'S300',
    '50': '',
    '51': 'GS03A',
    '52': 'GS03B',
    '53': 'GS05A',
    '54': 'GS05B',
    '55': 'S005T',
    '56': 'AT6',
    '57': 'GT02A',
    '58': 'GT03C',
    '59': 'S5E',
    '60': 'S5L',
    '61': 'S102L',
    '85': 'S105L',
    '62': 'TK103',
    '63': 'TK303',
    '64': 'ET300',
    '65': 'S102A',
    '91': 'S102A-D',
    '66': 'S708',
    '67': 'MT05A',
    '68': 'S709N',
    '69': '',
    '70': 'GS03C',
    '71': 'GS03D',
    '72': 'GS05C',
    '73': 'GS05D',
    '74': 'S116L',
    '75': 'S102',
    '76': 'S102T',
    '77': 'S718',
    '78': 'S19',
    '79': 'S101A',
    '80': 'VT03D',
    '81': 'S5L-C',
    '82': 'S710',
    '83': 'S03A',
    '84': 'C26',
    '87': 'S102M',
    '88': 'S101-B',
    '92': 'LK720',
    '89': 'S116-B',
    '90': 'X3'
  },
  alarmType: [
    '未知告警', //0
    '震动报警', //1
    '断电报警', //2
    '低电报警', //3
    'SOS求救', //4
    '超速报警', //5
    '出围栏报警', //6
    '位移报警', //7
    '外电低电报警', //8
    '出区域报警', //9
    '拆机报警', //10
    '光感报警', //11
    '磁感报警', //12
    '防拆报警', //13
    '蓝牙报警', //14
    '信号屏蔽报警', //15
    '伪基站报警', //16
    '入围栏报警', //17
    '入围栏报警', //18
    '出围栏报警', //19
    '车门打开报警', //20
    '疲劳驾驶', //21
    '入二押点', //22
    '出二押点', //23
    '二押点久留', //24
    '离线报警', //25
    '入围栏报警', //26
    '出围栏报警', //27
    '入围栏报警', //28
    '出围栏报警', //29
    '偷油报警', //30
    'ACC 开报警', //31
    'ACC 关报警', //32
    '碰撞报警', //33,
    '上班迟到', //34,
    '下班早退',
    '打卡报警'
  ],
  alarmTypeNew: {
    // 推送新加
    '40': '高温报警',
    '45': '低温报警',
    '50': '过压报警',
    '55': '低压报警',
    '60': '停车报警'
  },
  alarmNotificationType: [
    { type: '震动报警', value: 1 },
    { type: '断电报警', value: 2 },
    { type: '低电报警', value: 3 },
    { type: 'SOS求救', value: 4 },
    { type: '超速报警', value: 5 },
    // {type:'出围栏报警',value:6},
    { type: '位移报警', value: 7 },
    // { type: '外电低电报警', value: 8 },

    { type: '出区域报警', value: 9 },
    { type: '拆机报警', value: 10 },
    { type: '光感报警', value: 11 },

    { type: '防拆报警', value: 13 },

    { type: '信号屏蔽报警', value: 15 },
    { type: '伪基站报警', value: 16 },
    // {type:'入围栏报警(平台判定)',value:17},
    // {type:'入围栏报警(终端判定)',value:18},
    // {type:'出围栏报警',value:19},

    { type: '疲劳驾驶', value: 21 },
    { type: '入二押点', value: 22 },
    { type: '出二押点', value: 23 },
    { type: '二押点久留', value: 24 },
    { type: '离线报警', value: 25 },
    // {type:'入围栏报警（风控）',value:26},
    // {type:'出围栏报警（风控)',value:27}
    { type: '入围栏报警', value: 26 },
    { type: '出围栏报警', value: 27 },
    { type: '偷油报警', value: 30 },
    { type: 'ACC 开报警', value: 31 },
    { type: 'ACC 关报警', value: 32 },
    { type: '碰撞报警', value: 33 }
  ],
  alarmTypeText: '报警类型',
  alarmNotification: '推送设置',
  pointType: ['未定位', '卫星定位', '北斗定位', '基站定位', 'WIFI定位'],

  cardType: ['未知类型', '一年导入点', '终身导入点', '年卡', '终身卡'],
  // 东南西北
  directarray: ['东', '南', '西', '北'],
  // 方向字段
  directionarray: ['正北', '东北', '正东', '东南', '正南', '西南', '正西', '西北'],
  // 定位方式
  pointedarray: ['未定位', 'GPS', '基站', '基站定位', 'WIFI定位'],

  //map相关
  ruler: '测距',
  distance: '路况信息',
  baidumap: '百度地图',
  map: '地图',
  satellite: '卫星',
  ThreeDimensional: '3D',
  baidusatellite: '百度卫星',
  googlemap: '谷歌地图',
  googlesatellite: '谷歌卫星',
  fullscreen: '全屏',
  noBaidumapStreetView: '当前位置在百度地图没有街景',
  noGooglemapStreetView: '当前位置在谷歌地图没有街景',
  exitStreetView: '退出街景',
  draw: '绘制',
  finish: '完成',
  unknown: '未知',
  realTimeTailAfter: '实时跟踪',
  trackReply: '轨迹回放',
  afterRefresh: '后刷新',
  rightClickEnd: '右键结束,半径：',
  rightClickEndGoogle: '右键结束------------------------半径：',

  //tree相关
  currentUserMachineCount: '当前用户设备数',
  childUserMachineCount: '包含子用户总设备数',

  //窗口相关

  electronicFence: '电子围栏',
  drawTrack: '绘制轨迹',
  showOrHide: '显示/隐藏',
  showDeviceName: '设备名',
  circleCustom: '圆形自定义',
  circle200m: '圆形200米',
  polygonCustom: '多边形自定义',
  drawPolygon: '绘制多边形',
  drawCircle: '绘制圆形',
  radiusMin100: '绘制的围栏半径最小为20米，请重新绘制。当前围栏半径：',
  showAllFences: '显示全部围栏',
  lookEF: '查看围栏',
  noEF: '未查询到电子围栏!',
  hideEF: '隐藏围栏',
  blockUpEF: '停用围栏',
  deleteEF: '删除围栏',
  isStartUsing: '是否启用',
  startUsing: '启用',
  stopUsing: '停用',
  nowEFrange: '当前围栏范围',
  enableSucess: '启用成功',
  unableSucess: '禁用成功',
  sureDeleteMorgage: '确定删除二押点',
  enterMorgageName: '请输入二押点名称',
  openMorgagelongStayAlarm: '开启二押久留报警',
  openMorgageinOutAlarm: '开启进出二押报警',
  setEFSuccess: '设置围栏成功，并启用围栏范围',
  setElectronicFence: '设置电子围栏',
  drawFence: '绘制围栏',
  drawMorgagePoint: '绘制二押点',
  customFence: '自定义围栏',
  enterFenceTips: '进入报警',
  leaveFenceTips: '离开报警',
  inputFenceName: '请输入围栏名称',
  relation: '关联',
  relationDevice: '关联设备',
  unRelation: '未关联',
  hadRelation: '已关联',
  quickRelation: '一键关联',
  cancelRelation: '一键取消',
  relationSuccess: '关联成功',
  cancelRelationSuccess: '取消关联成功',
  relationFail: '关联失败',
  deviceList: '设备列表',
  isDeleteFence: '是否删除围栏',
  choseRelationDeviceFirst: '请先选中要关联的设备!',
  choseCancelRelationDeviceFirst: '请先选中要取消关联的设备!',
  selectOneTips: '请至少选择一项报警方式',
  radius: '半径',
  //设置二押点页面
  setMortgagePoint: '设置二押点',

  circleMortage: '圆形二押点',
  polygonMorgage: '多边形二押点',
  morgageSet: '已设置二押点',
  operatePrompt: '操作提示',
  startDrawing: '点击开始绘制',
  drawingtip1: '鼠标左键单击开始绘制，双击结束绘制',
  drawingtip2: '鼠标左键点击拖动开始绘制',

  /************************************************/
  endTrace: '轨迹回放完毕',
  travelMileage: '行驶里程',
  /************************************************/
  myAccount: '我的账号',
  serviceProvide: '服务商',
  completeInfo: '请完善以下信息，比如联系人、电话。',
  clientName: '客户名称',
  loginAccount: '登录账号',
  linkMan: '联系人',
  linkPhone: '电话',
  clientNameEmpty: '客户名称不能为空!',
  updateSuccess: '更新成功!',
  /************************************************/
  oldPsw: '旧密码',
  newPsw: '新密码',
  confirmPsw: '密码确认',
  pswNoSame: '密码输入不一致',
  pswUpdateSuccess: '密码修改成功!',
  email: '邮箱',
  oldPwdWarn: '请输入旧密码',
  newPwdWarn: '请输入新密码',
  pwdConfirmWarn: '请确认新密码',
  /************************************************/
  //自定义弹窗组件
  resetPswFailure: '重置密码失败',
  notification: '提示',
  isResetPsw_a: '是否要将‘',
  isResetPsw_b: '’重置密码？',
  pwsResetSuccess_a: '已将‘',
  pwsResetSuccess_b: '’密码重置为123456',
  /************************************************/
  machineSearch: '设备搜索',
  search: '搜索',
  clientRelation: '客户关系',
  machineDetail: '详情',
  machineDetail2: '设备详情',
  machineCtrl: '指令',
  transfer: '转移',
  belongCustom: '所属客户',
  addImeiFirst: '请先添加IMEI号!',
  addUserFirst: '请先添加客户!',
  transferSuccess: '转移成功!',
  multiAdd: '批量添加',
  multiImport: '批量导入',
  multiRenew: '批量续费',
  //批量修改设备begin
  editDevice: '修改设备型号',
  deviceAfter: '设备型号（修改后）',
  editDeviceTips: '请确认需修改的设备为同一型号，且为未激活状态！',
  pleaseChoseDevice: '请先选择要修改的设备！',
  editResult: '修改结果',
  successCount: '修改成功的设备：',
  failCount: '修改失败的设备：',
  //批量修改设备end
  multiDelete: '批量删除',
  canNotAddImei: 'IMEI不存在,无法添加到列表中',
  importTime: '导入时间',
  loginName: '登录名',
  platformDue: '平台到期',
  machinePhone: 'SIM卡号',
  userDue: '用户到期',
  overSpeedAlarm: '超速报警',
  changeIcon: '图标',
  dealerNote: '经销商备注',
  noUserDue: '请输入用户到期时间',
  phoneLengththan3: '电话长度必须大于3',
  serialNumberInput: '连号输入',

  /************************************************/
  sending: '正在发送指令.....请稍候...',
  sendFailure: '发送失败!',
  ctrlName: '指令名称',
  interval: '时间间隔',
  intervalError: '时间间隔格式错误',
  currectInterval: '请输入正确的时间间隔！',
  intervalLimit: '设置间隔时间范围10-720，单位（分钟）',
  intervalLimit2: '设置间隔时间范围10-5400，单位（秒）',
  intervalLimit3: '设置间隔时间范围5-1440，单位（分钟）',
  intervalLimit4: '设置间隔时间范围3-999，单位（秒）',
  intervalLimit5: '设置间隔时间范围10-10800，单位（秒）',
  intervalLimit1: '设置间隔时间范围1-999，单位（分钟)',
  intervalLimit6: '设置间隔时间范围1-65535，单位（秒）',
  intervalLimit7: '设置间隔时间范围1-999999，单位（秒）',
  intervalLimit8: '设置间隔时间范围0-255,0表示关闭',
  intervalLimit9: '设置间隔时间范围3-10800，单位（秒）',
  intervalLimit10: '设置间隔时间范围3-86400，单位（秒）',
  intervalLimit11: '设置间隔时间范围180-86400，单位（秒）',
  intervalLimit12: '设置间隔时间范围10-60，单位（秒）',
  intervalLimit13: '设置间隔时间范围1-24（表示1-24小时）或101-107（表示1-7天）',
  intervalLimit14: '设置间隔时间范围10-3600，单位（秒）；默认：10s',
  intervalLimit15: '设置间隔时间范围180-86400，单位（秒）；默认：3600s',
  intervalLimit16: '设置间隔时间范围1-72，单位（小时）；默认：24小时',
  intervalLimit17: '设置温度范围-127-127，單位（℃）',
  intervalLimit18: '设置间隔时间范围5-18000，单位（秒）',
  intervalLimit19: '设置间隔时间范围10-300，单位（秒）',
  intervalLimit20: '设置间隔时间范围5-399，单位（秒）',
  intervalLimit21: '设置间隔时间范围5-300，单位（秒）',
  intervalLimit22: '设置间隔时间范围60-86400，单位（秒）',
  intervalLimit23: '设置间隔时间范围5-60，单位（秒）',
  intervalLimit24: '设置间隔时间范围10-86400，单位（秒）',
  intervalLimit25: '设置间隔时间范围5-43200，单位（分钟）',
  noInterval: '请输入时间间隔!',
  intervalTips: '需关闭追踪模式请设置闹钟唤醒时间',
  phoneMonitorTips: '指令发出后设备会主动拨打回拨号码，以实现监听。',
  time1: '时间1',
  time2: '时间2',
  time3: '时间3',
  time4: '时间4',
  time5: '时间5',
  intervalNum: '间隔(分钟)',
  sun: '周日',
  mon: '周一',
  tue: '周二',
  wed: '周三',
  thu: '周四',
  fri: '周五',
  sat: '周六',
  awakenTime: '唤醒时间点',
  centerPhone: '中心号码',
  inputCenterPhone: '请输入中心号码!',
  phone1: '号码一',
  phone2: '号码二',
  phone3: '号码三',
  phone4: '号码四',
  phone5: '号码五',
  inputPhone: '请输入号码',
  offlineCtrl: '离线指令已保存，设备上线后离线指令将自动下发给设备',
  terNotSupport: '终端不支持',
  terReplyFail: '终端回复失败',
  machineInfo: '设备信息',

  /************************************************/
  alarmTypeScreen: '报警类型筛选',
  allRead: '全部已读',
  read: '已读',
  noAlarmInfo: '暂无可清除的报警信息',
  alarmTip: '提示 : 取消选中即可过滤该类型报警信息',

  /************************************************/
  updatePsw: '密码',
  resetPsw: '重置密码',

  /************************************************/
  multiSell: '批量销售',
  sell: '销售',
  sellSuccess: '销售成功!',
  modifySuccess: '修改成功',
  modifyFail: '修改失败',
  multiTransfer: '批量转移',
  multiUserExpires: '修改用户到期',
  batchModifying: '批量修改',
  userTransfer: '转移',
  machineRemark: '备注',
  sendCtrl: '发送指令',
  ctrl: '指令',
  ctrlLog: '指令记录',
  ctrlLogTips: '指令记录',
  s06Ctrls: ['远程断油电', '远程恢复油电', '查询定位'],
  ctrlType: '指令名称',
  resInfo: '响应信息',
  resTime: '响应时间',
  ctrlSendTime: '发送时间',
  // csv文件导入上传
  choseCsv: '请选择csv文件',
  choseFile: '选择文件',
  submit: '提交',
  targeDevice: '目标设备',
  csvTips_1: '1、将excel文件另存为csv格式',
  csvTips_2: '2、将csv文件导入系统。',
  importExplain: '导入说明：',
  fileDemo: '文件格式示例',

  // 新增
  sendType: '发送类型',
  onlineCtrl: '在线指令',
  offCtrl: '离线指令',
  resStatus: ['未发送', '已失效', '已下发', '执行成功', '执行失败', '无应答'],
  /************************************************/
  addSubordinateClient: '新增下级用户',
  noSubordinateClient: '没有下级用户',
  superiorCustomerEmpty: '请选择上级客户',
  noCustomerName: '请输入客户名称',
  noLoginAccount: '请输入登录账号',
  noPsw: '请输入密码',
  noConfirmPsw: '请输入确认密码',
  pswNotAtypism: '您输入的密码和确认密码不一致!',
  addSuccess: '添加成功',
  superiorCustomer: '上级客户',
  addVerticalImei: '请按一竖列输入IMEI号',
  noImei: 'IMEI均不存在,无法添加到列表中',
  addImei_curr: '请输入IMEI号,当前',
  no: '个',
  aRowAImei: '一行输入一个IMEI',

  /*
   * dealer  界面翻译开始
   *
   * */
  //main.js
  imeiOrUserEmpty: '设备号(IMEI)/客户名不能为空!',
  accountEmpty: '账号不能为空!',
  queryNoUser: '未查询到该用户',
  queryNoIMEI: '未查询到该IMEI号',
  imeiOrClientOrAccount: '设备号(IMEI)/客户名/账号',
  dueSoon: '即将到期',
  recentlyOffline: '最近离线',
  choseSellDeviceFirst: '请先选择要销售的设备!',
  choseDeviceFirst: '请先选择要转移的设备!',
  choseRenewDeviceFirst: '请先选择要续费的设备!',
  choseDeleteDeviceFirst: '请先选择要删除的设备!',
  choseClientFirst: '请先选择要转移的客户!',

  //myClient.js
  clientList: '客户列表',
  accountInfo: '账户信息',
  machineCount: '设备数量',
  stock: '进货',
  inventory: '库存',
  subordinateClient: '下级用户',
  datum: '资料',
  monitor: '监控',
  dueMachineInfo: '到期设备信息',
  haveExpired: '已过期',
  timeRange: ['7天内', '30天内', '60天内', '7-30天内', '30-60天内'],
  offlineMachineInfo: '离线设备信息',
  timeRange1: ['1小时内', '1天内', '7天内', '30天内', '60天内', '60天以上', '1小时-1天内', '1-7天内', '7-30天内', '30-60天内'],
  offlineTime: '离线时长',
  includeSubordinateClient: '包含下级用户',

  stopMachineInfo: '静止设备信息',
  stopTime1: '静止时长',
  unUseMachineInfo: '未启用设备信息',
  unUseMachineCount: '未启用设备数为',

  sellTime: '销售时间',
  detail: '详细',
  manageDevice: '详细',
  details: '明细',
  deleteSuccess: '删除成功!',
  deleteFail: '删除失败!',
  renewalLink: '续费链接',
  deleteGroupTips: '是否删除分组',
  addGroup: '添加组',
  jurisdictionRange: '权限范围:可修改功能',
  machineSellTransfer: '设备销售转移',
  monitorMachineGroup: '监控设备分组',
  jurisdictionArr: ['客户管理', '消息管理', '围栏设置', '报警信息', '虚拟账号管理', '下发指令'],
  confrimDelSim: '确定删除该sim卡号:',

  // 右键菜单
  sellDevice: '销售设备',
  addClient: '新增客户',
  deleteClient: '删除客户',
  resetPassword: '重置密码',
  transferClient: '客户转移',
  ifDeleteClient: '是否删除',

  //myAccount
  myWorkPlace: '工作台',
  availablePoints: '可用点数',
  yearCard: '年卡',
  lifetimeOfCard: '终身卡',
  oneyear: '一年',
  lifeyear: '一年',
  lifetime: '终身',
  oneyearPoint: '一年导入点',
  commonImportPoint: '普通导入点',
  lifetimeImportPoint: '终身导入点',
  myServiceProvide: '服务商',
  moreOperator: '更多操作',
  dueMachine: '到期设备',
  offlineMachine: '离线设备',
  quickSell: '快速销售',
  sellTo: '销售给',
  machineBelong: '设备属于',
  reset: '重置',
  targetCustomer: '目标客户',
  common_lifetimeImport: '普通导入点(0),终身导入点(0)',
  cardType1: '卡类型',
  credit: '充值点数',
  generateImportPoint: '生成导入点',
  generateImportPointSuc: '生成导入点成功!',
  generateImportPointFail: '生成导入点失败!',
  year_lifeTimeCard: '年卡(0),终身卡(0)',
  generateRenewPoint: '生成续费点',
  transferTo: '转移到',
  transferPoint: '转移点数',
  transferRenewPoint: '转移续费点',
  pointHistoryRecord: '点数记录',
  newGeneration: '新生成',
  operatorType: '操作类型',
  consume: '消费',
  give: '给',
  income: '收入',
  pay: '支出',
  imeiErr: '查询的设备,IMEI号必须至少是后6位以上的数字!',
  accountFirstPage: '账户首页',

  /*
   * dealer  界面翻译结束
   *
   * */
  // 网页1.4.8风控部分翻译
  finrisk: '金融风控',
  attention: '关注',
  cancelattention: '取消关注',
  poweroff: '断电',
  inout: '进出二押',
  inoutEF: '进出围栏',
  longstay: '二押久留',
  secsetting: '二押点设置',
  EFsetting: '围栏设置',
  polygonFence: '多边形电子围栏',
  cycleFence: '圆形电子围栏',
  haveBeenSetFence: '已设置电子围栏',
  haveBeenSetPoint: '已设置二押点',
  drawingFailed: '绘制失败，请重新绘制',
  inoutdot: '进出二押点',
  eleStatistics: '电量统计',
  noData: '没有数据',
  // 进出二押点表格
  accountbe: '所属账号',
  SMtype: '二押点类型',
  SMname: '二押点名称',
  time: '时间',
  position: '位置',
  lastele: '剩余电量',
  statisticTime: '统计时间',
  searchalarmType: ['全部', '离线', '断电', '进出围栏', '进出二押', '二押久留'],
  remarks: ['二押点', '担保公司', '拆机点', '二手交易市场'],
  focusOnly: '仅关注',
  // [?]描述
  interpretSignal: '信号：设备最近一次和平台通信时间',
  interpretPosition: '定位：设备最近一次卫星定位时间',
  interpretAll: '在线设备静止时不定位，但仍会与平台通信',

  autoRecord: '自动录音',
  /******************************************************设置指令开始**********************************8*/
  setCtrl: {
    text: '设置指令',
    value: ''
  },
  moreCtrl: {
    text: '更多指令',
    value: ''
  },
  sc_openTraceModel: {
    text: '开启追踪模式',
    value: '0'
  },
  sc_closeTraceModel: {
    text: '关闭追踪模式',
    value: '1'
  },
  sc_setSleepTime: {
    text: '设置休眠时长',
    value: '2'
  },
  sc_setAwakenTime: {
    text: '设置唤醒时间点',
    value: '3'
  },
  sc_setDismantleAlarm: {
    text: '设置防拆报警',
    value: '4'
  },
  sc_setSMSC: {
    text: '中心号码管理',
    value: '5'
  },
  sc_delSMSC: {
    text: '删除中心号码',
    value: '6'
  },
  sc_setSOS: {
    text: '添加SOS',
    value: '7'
  },
  sc_delSOS: {
    text: '删除SOS',
    value: '8'
  },
  sc_restartTheInstruction: {
    text: '重启指令',
    value: '9'
  },
  sc_uploadTime: {
    text: '上传间隔',
    value: '10'
  },
  /*闹钟唤醒时间设置
     定时回传时间设置
     防拆报警设置
     星期模式开启关闭*/
  sc_setAlarmClock: {
    text: '设置闹钟唤醒时间',
    value: '11'
  },
  sc_setTimingRebackTime: {
    text: '设置定时回传时间',
    value: '12'
  },
  sc_openWeekMode: {
    text: '开启星期模式',
    value: '13'
  },
  sc_closeWeekMode: {
    text: '关闭星期模式',
    value: '14'
  },
  sc_powerSaverMode: {
    text: '设置定时回传模式',
    value: '15'
  },
  sc_carCatchingMode: {
    text: '设置追车模式',
    value: '16'
  },
  sc_closeDismantlingAlarm: {
    text: '关闭防拆报警设置',
    value: '17'
  },
  sc_openDismantlingAlarm: {
    text: '打开防拆报警设置',
    value: '18'
  },
  sc_VibrationAlarm: {
    text: '设置震动报警',
    value: '19'
  },
  sc_timeZone: {
    text: '时区设置',
    value: '20'
  },
  sc_phoneMonitor: {
    text: '电话监听',
    value: '21'
  },
  sc_stopCarSetting: {
    text: '停车报警设置',
    value: '22'
  },
  sc_bindAlarmNumber: {
    text: '绑定报警号码',
    value: '23'
  },
  sc_bindPowerAlarm: {
    text: '断电报警',
    value: '24'
  },
  sc_fatigueDrivingSetting: {
    text: '疲劳驾驶设置',
    value: '25'
  },
  sc_peripheralSetting: {
    text: '外设设置',
    value: '26'
  },
  sc_SMSAlarmSetting: {
    text: '设置短信报警',
    value: '27'
  },
  sc_autoRecordSetting: {
    text: '自动录音设置',
    value: '28'
  },
  sc_monitorCallback: {
    text: '监听回拨',
    value: '29'
  },
  sc_recordCtrl: {
    text: '录音指令',
    value: '30'
  },
  sc_unbindAlarmNumber: {
    text: '解除绑定报警号码',
    value: '31'
  },
  sc_alarmSensitivitySetting: {
    text: '震动报警灵敏度设置',
    value: '32'
  },
  sc_alarmSMSsettings: {
    text: '震动报警短信设置',
    value: '33'
  },
  sc_alarmCallSettings: {
    text: '震动报警电话设置',
    value: '34'
  },
  sc_openFailureAlarmSetting: {
    text: '断电报警',
    value: '35'
  },
  sc_restoreFactory: {
    text: '恢复出厂',
    value: '36'
  },
  sc_openVibrationAlarm: {
    text: '震动报警',
    value: '37'
  },
  sc_closeVibrationAlarm: {
    text: '关闭震动报警',
    value: '38'
  },
  sc_closeFailureAlarmSetting: {
    text: '关闭断电报警',
    value: '39'
  },
  sc_feulAlarm: {
    text: '油量报警设置',
    value: '40'
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: '省电模式',
    value: '41'
  },
  sc_sleepMode: {
    text: '休眠模式',
    value: '42'
  },
  sc_alarmMode: {
    text: '闹钟模式',
    value: '43'
  },
  sc_weekMode: {
    text: '星期模式',
    value: '44'
  },
  sc_monitorNumberSetting: {
    text: '监听号码设置',
    value: '45'
  },
  sc_singlePositionSetting: {
    text: '单次定位模式',
    value: '46'
  },
  sc_timingworkSetting: {
    text: '定时工作模式',
    value: '47'
  },
  sc_openLightAlarm: {
    text: '光感报警',
    value: '48'
  },
  sc_closeLightAlarm: {
    text: '关闭光感报警',
    value: '49'
  },
  sc_workModeSetting: {
    text: '工作模式设置',
    value: '50'
  },
  sc_timingOnAndOffMachine: {
    text: '定时开关机设置',
    value: '51'
  },
  sc_setRealTimeTrackMode: {
    text: '设置实时追车模式',
    value: '52'
  },
  sc_setClockMode: {
    text: '设置闹钟模式',
    value: '53'
  },
  sc_openTemperatureAlarm: {
    text: '开启温度报警',
    value: '54'
  },
  sc_closeTemperatureAlarm: {
    text: '关闭温度报警',
    value: '55'
  },
  sc_timingPostbackSetting: {
    text: '定时回传设置',
    value: '56'
  },
  sc_remoteBoot: {
    text: '远程开机',
    value: '57'
  },
  // S16,S16LA,S16LB
  sc_remoteSwitch: {
    text: '远程开关机',
    value: '57'
  },
  sc_smartTrack: {
    text: '智能跟踪',
    value: '58'
  },
  sc_cancelSmartTrack: {
    text: '取消智能追踪',
    value: '59'
  },
  sc_cancelAlarm: {
    text: '取消报警',
    value: '60'
  },
  sc_smartPowerSavingMode: {
    text: '设置智能省电模式',
    value: '61'
  },
  sc_monitorSetting: {
    text: '监听',
    value: '62'
  },
  // 指令重构新增翻译
  sc_timedReturnMode: {
    text: '定时回传模式',
    value: '100'
  },
  sc_operatingMode: {
    text: '工作模式',
    value: '101'
  },
  sc_realTimeMode: {
    text: '实时定位模式',
    value: '102'
  },
  sc_alarmMode: {
    text: '闹钟模式',
    value: '103'
  },
  sc_weekMode: {
    text: '星期模式',
    value: '104'
  },
  sc_antidemolitionAlarm: {
    text: '防拆告警',
    value: '105'
  },
  sc_vibrationAlarm: {
    text: '震动告警',
    value: '106'
  },
  sc_monitoringNumber: {
    text: '监听号码管理',
    value: '107'
  },
  sc_queryMonitoring: {
    text: '查询监听号码',
    value: '108'
  },
  sc_electricityControl: {
    text: '油电控制',
    value: '109'
  },
  sc_SOSnumber: {
    text: 'SOS号码管理',
    value: '110'
  },
  sc_SleepCommand: {
    text: '休眠指令',
    value: '201'
  },
  sc_RadiusCommand: {
    text: '位移半径',
    value: '202'
  },
  sc_punchTimeMode: {
    text: '打卡模式',
    value: '203'
  },
  sc_intervelMode: {
    text: '时间段模式',
    value: '204'
  },
  sc_activeGPS: {
    text: '激活GPS',
    value: '205'
  },
  sc_lowPowerAlert: {
    text: '低电报警',
    value: '206'
  },
  sc_SOSAlert: {
    text: 'SOS报警',
    value: '207'
  },
  mc_cuscom: {
    text: '自定义指令',
    value: '1'
  },
  NormalTrack: '正常追踪模式',
  listeningToNumber: '确定要查询监听号码吗?',
  versionNumber: '确定要查询版本号吗?',
  longitudeAndLatitudeInformation: '确定要查询经纬度信息吗?',
  equipmentStatus: '确定要查询状态吗?',
  public_parameter: '确定要查询参数吗？',
  GPRS_parameter: '确定要查询GPRS参数吗？',
  deviceName: '确定要对设备点名吗？',
  SMS_alert: '确定要查询短信提醒报警吗?',
  theBindingNumber: '确定要查询绑定号码吗？',
  intervalTimeRange: '设置间隔时间范围为001-999，单位（分钟）',
  pleaseChoose: '请选择',
  RealTimeCarChase: '确定要将此设备设置为实时追车模式吗？',
  inputPhoneNumber: '请输入手机号码',
  inputCorPhoneNumber: '请输入正确的手机号码',
  autoCallPhone: '提示：指令执行成功后，终端会自动拨打设置的号码',
  limitTheNumberOfCellPhoneNumbers1: '此指令最多支持5个手机号码',
  limitTheNumberOfCellPhoneNumbers2: '此指令最多支持3个手机号码',
  equipmentTorestart: '确定要重启此设备吗?',
  remindTheWay: '提醒方式',
  alarmWakeUpTime: '闹钟唤醒时间',
  alarmWakeUpTime1: '闹钟唤醒时间1',
  alarmWakeUpTime2: '闹钟唤醒时间2',
  alarmWakeUpTime3: '闹钟唤醒时间3',
  alarmWakeUpTime4: '闹钟唤醒时间4',
  sensitivityLevel: '请选择灵敏度级别',
  parking_time: '停车时长',
  selectWorkingMode: '请选择工作模式',
  Alarm_value: '报警值',
  Buffer_value: '缓冲值',
  gqg_disconnect: '断开',
  gqg_turnOn: '开启',
  Return_interval: '回传间隔',
  gq_startTime: '运动时间',
  gq_restingTime: '静止时间',
  gq_Eastern: '东时区',
  gq_Western: '西时区',

  gq_driver: '疲劳驾驶报警',
  gq_deviceName: '确定要对此设备点名吗？',
  gq_noteAlarm: '确定要查询短信提醒报警吗？',
  gq_restoreOriginal: '确定要将此设备恢复原厂吗？',
  gq_normalMode: '正常模式',
  gq_IntelligentsleepMode: '智能休眠模式',
  gq_DeepsleepMode: '深度休眠模式',
  gq_RemotebootMode: '远程开机模式',
  gq_IntelligentsleepModeTips: '确定要设置为智能休眠模式吗',
  gq_DeepsleepModeTips: '确定要设置为深度休眠模式吗',
  gq_RemotebootModeTips: '确定要设置为远程开机模式吗',
  gq_normalModeTips: '确定要设置为正常模式吗',
  gq_sleepModeTips: '确定要将此设备设置为休眠模式吗',
  gq_Locatethereturnmode: '定位回传模式',
  gq_regularWorkingHours: '定时工作时段',
  punchTimeMode: '打卡模式',
  timeIntervalMode: '打卡模式',
  gq_regularWorkingHours: '定时工作时段',
  gq_AlarmType: {
    text: '报警类型',
    value: '111'
  },
  IssuedbyThePrompt: '指令已经下发，请等待设备响应',
  platformToinform: '平台通知',
  gq_shortNote: '短信通知',
  // gq_:'确定要查询绑定号码吗？'
  /************指令白话文**********************/
  closeDismantlingAlarm: '关闭防拆报警设置',
  openDismantlingAlarm: '打开防拆报警设置',
  closeTimingRebackMode: '关闭定时回传模式设置',
  minute: '分钟',
  timingrebackModeSetting: '设置定时回传模式:',
  setWakeupTime: '设置唤醒时间点:',
  weekModeSetting: '设置星期模式：',
  closeWeekMode: '关闭星期模式设置',
  setRealtimeTrackMode: '设置实时追车模式',
  fortification: '设防',
  disarming: '撤防',
  settimingrebackmodeinterval: '设置定时回传模式间隔:',
  oilCutCommand: '断油电指令',
  restoreOilCommand: '恢复油电指令',
  turnNnTheVehiclesPower: '启用车辆电源',
  turnOffTehVehiclesPower: '关闭车辆电源',
  implementBrakes: '执行刹车',
  dissolveBrakes: '解除刹车',
  openVoiceMonitorSlarm: '启用报警语音',
  closeVoiceMonitorAlarm: '关闭报警语音',
  openCarSearchingMode: '开启寻车模式',
  closeCarSearchingMode: '关闭寻车模式',
  unrecognizedCommand: '无法识别指令',
  commandSendSuccess: '恭喜您，设备执行命令成功！',
  /********************************************设置指令结束**************************************************/

  /********************************************查询指令开始**************************************************/
  queryCtrl: {
    text: '查询指令',
    value: ''
  },
  /*参数设置查询*/
  qc_softwareVersion: {
    text: '查询软件版本',
    value: '1'
  },
  qc_latlngInfo: {
    text: '查询经纬度信息',
    value: '2'
  },
  qc_locationHref: {
    text: '查询参数配置',
    value: '3'
  },
  qc_status: {
    text: '查询状态',
    value: '4'
  },
  qc_gprs_param: {
    text: '查询GPRS参数',
    value: '5'
  },
  qc_name_param: {
    text: '点名',
    value: '6'
  },
  qc_SMSReminderAlarm_param: {
    text: '查询短信提醒报警',
    value: '7'
  },
  qc_bindNumber_param: {
    text: '查询绑定号码',
    value: '8'
  },

  /********************************************查询指令结束**************************************************/

  /*******************************************控制指令开始***************************************************/

  controlCtrl: {
    text: '控制指令',
    value: ''
  },
  cc_offOilElectric: {
    text: '断油电',
    value: '1'
  },
  cc_recoveryOilElectricity: {
    text: '恢复油电',
    value: '2'
  },
  cc_factorySettings: {
    text: '恢复出厂设置',
    value: '4'
  },
  cc_fortify: {
    text: '设防',
    value: '75'
  },
  cc_disarming: {
    text: '撤防',
    value: '76'
  },
  cc_brokenOil: {
    text: '断油指令',
    value: '7'
  },
  cc_RecoveryOil: {
    text: '恢复油路',
    value: '8'
  },

  /*******************************************控制指令结束***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: '公里',
  mileage: '里程',
  importMachine: '导入设备',
  transferImportPoint: '转移导入点',
  machineType1: '设备型号',
  confirmIMEI: '操作前请确认IMEI号和型号',
  renew: '续费',
  deductPointNum: '扣除点数',
  renewSuccess: '续费成功!',
  wireType: [
    {
      text: '有线',
      value: false
    },
    {
      text: '无线',
      value: true
    }
  ],
  vibrationWays: [
    {
      text: '平台',
      value: 0
    },
    {
      text: '平台+短信',
      value: 1
    },
    {
      text: '平台+短信+电话',
      value: 2
    }
  ],
  addMachineType: [
    {
      text: 'S06',
      value: '3'
    },
    // SO6子级-----start-----------
    {
      text: 'GT06',
      value: '8'
    },
    {
      text: 'S08V',
      value: '9'
    },
    {
      text: 'S01',
      value: '10'
    },
    {
      text: 'S01T',
      value: '11'
    },
    {
      text: 'S116',
      value: '12'
    },
    {
      text: 'S119',
      value: '13'
    },
    {
      text: 'TR06',
      value: '14'
    },
    {
      text: 'GT06N',
      value: '15'
    },
    {
      text: 'S101',
      value: '16'
    },
    {
      text: 'S101T',
      value: '17'
    },
    {
      text: 'S06U',
      value: '18'
    },
    {
      text: 'S112U',
      value: '19'
    },
    {
      text: 'S112B',
      value: '20'
    },
    // SO6子级-----end-----------
    {
      text: 'S15',
      value: '1'
    },
    {
      text: 'S05',
      value: '2'
    },
    {
      text: 'SW06',
      value: '4'
    },
    {
      text: 'S001',
      value: '5'
    },
    {
      text: 'S08',
      value: '6'
    },
    {
      text: 'S09',
      value: '7'
    }
  ],

  /*
   * 2018-02-02
   * */
  maploadfail: '当前地图加载失败，是否切换到其它地图?',

  /*
    2018-03-06新增 控制指令
    * */
  cc_openPower: {
    text: '车辆电源',
    value: '7'
  },
  cc_closePower: {
    text: '关闭车辆电源',
    value: '8'
  },
  cc_openBrake: {
    text: '车辆刹车',
    value: '9'
  },
  cc_closeBrake: {
    text: '关闭车辆刹车',
    value: '10'
  },
  cc_openAlmrmvoice: {
    text: '车辆警铃',
    value: '11'
  },
  cc_closeAlmrmvoice: {
    text: '关闭车辆警铃',
    value: '12'
  },
  /*2018-03-06新增 控制指令
   * */
  cc_openFindCar: {
    text: '车辆寻车',
    value: '13'
  },
  cc_closeFindCar: {
    text: '关闭车辆寻车',
    value: '14'
  },

  /*2018-03-19
   * */
  EF: '围栏',

  /*
    2018-03-29，扩展字段
    * */
  exData: ['电量', '电压', '油量', '温度', '电阻'],

  /*
    2018-04-10
    * */
  notSta: '基站定位，不列入统计',

  // 油量统计
  fuelSetting: '油量设置',
  tempSetting: '温感设置',
  mianFuelTank: '主油箱',
  auxiliaryTank: '副油箱',
  maximum: '最大值',
  minimum: '最小值',
  FullTankFuel: '满箱油量',
  fuelMinValue: '满箱油量不能低于10L',
  standardSetting: '标准设定',
  emptyBoxMax: '空箱最大值',
  fullBoxMax: '满箱最大值',
  fuelStatistics: '油量统计',
  settingSuccess: '设置成功',
  settingFail: '设置失败',
  pleaseInput: '请输入',
  fuelTimes: '加油次数',
  fuelTotal: '加油总量',
  refuelingTime: '加油时长',
  fuelDate: '加油时间',
  fuel: '油量',
  fuelChange: '油量变化',
  feulTable: '油量分析表',
  addFullFilter: '请补充完整筛选条件',
  enterIntNum: '请输入正整数',
  // 温度统计
  tempSta: '温度统计',
  tempTable: '温度分析表',
  industrySta: '行业统计',
  temperature: '温度',
  temperature1: '温度1',
  temperature2: '温度2',
  temperature3: '温度3',
  tempRange: '温度范围',
  // 温感开关
  tempSetting: '温度设置',
  tempSensor: '温度感应器',
  tempAlert: '温度感应器关闭后，将不接收温度数据！',
  phoneNumber: '手机号码',
  sosAlarm: 'SOS报警',
  undervoltageAlarm: '低压报警',
  overvoltageAlarm: '过压报警',
  OilChangeAlarm: '油量变化报警',
  accDetection: 'ACC检测',
  PositiveAndNegativeDetection: '正反转检测',
  alermValue: '报警值',
  bufferValue: '缓冲值',
  timeZoneDifference: '时区差值',
  meridianEast: '子午线东',
  meridianWest: '子午线西',
  max12hour: '输入差值不能大于12小时',
  trackDownload: '轨迹下载',
  download: '下载',
  multiReset: '批量重置',
  resetSuccess: '重置成功',
  multiResetTips: '重置后设备激活时间和轨迹等测试数据将清除，设备状态在线或离线重置为未启用。',
  point: '点',
  myplace: '我的地点',
  addPoint: '添加点',
  error10018: '导入点数量不足',
  error110: '对象不存在',
  error109: '超过最大限制',
  error20013: '设备类型不存在',
  error90001: '设备类型序号不能为空',
  error20003: 'Imei不能为空',
  inputName: '请输入名称',
  virtualAccount: '虚拟账号',
  createTime: '创建时间',
  permission: '权限',
  permissionRange: '权限范围',
  canChange: '可修改的功能',
  fotbidPassword: '修改密码',
  virtualAccountTipsText: '创建虚拟账号时，其为当前登录的经销商账号的别名账号，可以为虚拟账号设置权限。',
  noOperationPermission: '虚拟账号无操作权限',
  number: '号码',
  rangeSetting: '范围设置',
  setting: '设置',
  // 1.6.1
  duration: '持续时间',
  voltageSta: '电压统计',
  voltageAnalysis: '电压分析',
  voltageEchart: '电压分析表',
  platformAlarm: '平台报警',
  platformAlarm1: '电话',
  platformAndPhone: '电话+平台报警',
  smsAndplatformAlarm: 'SMS+平台报警',
  smsAndplatformAlarm1: 'SMS',
  smsAndplatformAlarmandPhone: '平台报警+SMS+电话',
  smsAndplatformAlarmandPhone1: 'SMS+电话',
  more_speed: '速度',
  attribute: '属性',
  profession: '专业',
  locationPoint: '定位点',
  openPlatform: '开放平台',
  experience: '我要体验',
  onlyViewMonitor: '仅可查看监控',

  // 1.6.3
  inputAccountOrUserName: '请输入账号或用户名',
  noDeviceTips: '没查到相关的设备信息，查客户点',
  noUserTips: '没查到相关的用户信息，查设备点',
  clickHere: '这里',
  // 1.6.4
  pointIntervalSelect: '轨迹点间隔',
  payment: '缴费',
  pleaceClick: '请点击',
  paymentSaveTips: '安全提示:请与服务商确认链接的有效性',
  //1.6.4新增
  fuelAlarmValue: '油量报警值',
  fuelConsumption: '油耗',
  client: '客户',
  create: '新建',
  importPoint: '导入点',
  general: '普通',
  lifelong: '终身',
  renewalCard: '续费卡',
  settingFuelFirst: '请先设置油量报警值后再发送指令!',
  overSpeedSetting: '超速设置',
  kmPerHour: 'km/h',
  times: '次',
  total: '总计',
  primary: '主',
  minor: '副',
  unActiveTips: '设备未激活，无法使用该功能',
  arrearsTips: '设备已欠费，无法使用该功能',
  //1.6.5
  loading: '数据加载中...',
  expirationReminder: '到期提醒',
  projectName: '项目名称',
  expireDate: '过期时间',
  changePwdTips: '您的密码过于简单，存在安全风险，请马上修改密码。',
  pwdCheckTips1: '建议是6-20位字母、数字或符号',
  pwdCheckTips2: '您输入的密码强度过弱。',
  pwdCheckTips3: '您的密码还可以更复杂些。',
  pwdCheckTips4: '您的密码很安全。',
  pwdLevel1: '弱',
  pwdLevel2: '中',
  pwdLevel3: '强',
  comfirmChangePwd: '确定修改密码',
  notSetYet: '暂不设置',
  // 1.6.6
  liter: '升',
  arrearageDayTips: '天后到期',
  todayExpire: '今天到期',
  forgotPwd: '忘记密码?',
  forgotPwdTips: '请联系卖家修改密码',
  //1.6.7
  commonProblem: '常见问题',
  instructions: '操作说明',
  webInstructions: 'WEB操作说明',
  appInstructions: 'APP操作说明',
  acceptAlarmNtification: '接收报警通知',
  alarmPeriod: '报警时段',
  whiteDay: '白天',
  blackNight: '黑夜',
  allDay: '全天',
  alarmEmail: '报警邮件',
  muchEmailTips: '可输入多个邮箱，用符号‘;’隔开。',
  newsCenter: '消息中心',
  allNews: '全部消息',
  unReadNews: '未读消息',
  readNews: '已读消息',
  allTypeNews: '全部消息类型',
  alarmInformation: '告警信息',
  titleContent: '标题内容',
  markRead: '标记已读',
  allRead: '全部已读',
  allDelete: '全部删除',
  selectFirst: '请先选中再进行操作!',
  updateFail: '更新失败!',
  ifAllReadTips: '是否全部设置为已读？',
  ifAllDeleteTips: '是否全部删除？',
  stationInfo: '站内信息',
  phone: '手机',
  //1.6.72
  plsSelectTime: '请选择时间!',
  customerNotFound: '找不到该客户',
  //1.6.8
  Postalcode: '位置',
  accWarning: 'ACC开报警',
  canInputMultiPhone: '可以输入多个手机号码，请用;隔开',
  noLocationInfo: '该设备尚未有定位信息。',
  //1.6.9
  fenceName: '围栏名称',
  fenceManage: '围栏管理',
  circular: '圆形',
  polygon: '多边形',
  allFence: '全部围栏',
  shape: '形状',
  stationNews: '站内消息',
  phonePlaceholder: '可输入多个号码，以“,”隔开',
  addressPlaceholder: '请按顺序输入地址和邮政编码，以“,”隔开',
  isUnbind: '是否取消关联',
  alarmCar: '报警车辆',
  alarmAddress: '报警地点',
  chooseAtLeastOneTime: '至少选择一个时间',
  alarmMessage: '该条告警信息暂无详情',
  navigatorBack: '返回上级',
  timeOverMessage: '用户到期时间不能大于平台到期时间',
  //1.7.0
  userTypeStr: '用户类型',
  newAdd: '新增',
  findAll: '一共找到',
  findStr: '条匹配的数据',
  customColumn: '自定义列',
  updatePswErr: '更新密码失败',
  professionalUser: '是否专业用户',
  confirmStr: '确认',
  inputTargetCustomer: '请输入目标客户',
  superiorUser: '上级用户',
  speedReport: '速度报表',
  createAccount: '创建账号',
  push: '推送',
  searchCreateStr: '此操作将操作一个账号，同时将此设备转移到该账号名下',
  allowIMEI: '允许IMEI登录',
  defaultPswTip: '默认密码为IMEI后6位',
  createAccountTip: '创建账号并转移设备成功',
  showAll: '显示全部',
  bingmap: '必应地图',
  mapNoFence: '该地图暂未开启围栏功能，请切换至其他地图',
  areaZoom: '区域缩放',
  areaZoomReduction: '区域缩放还原',
  reduction: '还原',
  saveImg: '保存为图片',
  fleetFence: '车队围栏',
  alarmToSub: '报警通知下级',
  bikeFence: '单车围栏',
  delGroupTip: '删除失败，请先删除兴趣点！',
  isExporting: '正在导出...',
  addressResolution: '地址解析中...',
  simNOTip: 'SIM卡号只能为数字',
  unArrowServiceTip: '存在以下设备的用户到期时间大于平台到期时间，请重新选择。设备号为：',
  platformAlarmandPhone: '平台报警+电话',
  openLightAlarm: '开启光感报警',
  closeLightAlarm: '关闭光感报警',
  ACCAlarm: 'ACC报警',
  translateError: '转移失败，目标用户无权限',
  distanceTip: '单击确定地点，双击结束',
  workMode: '工作模式',
  workModeType: '0: 正常模式；1 智能休眠模式；2 深度休眠模式',
  clickToStreetMap: '点击打开街景地图',
  current: '当前',
  remarkTip: '备注：请勿同时销售不同类型套餐的卡',
  searchRes: '搜索结果',
  updateIcon: '更改图标',
  youHaveALarmInfo: '您有一条报警信息',
  moveInterval: '运动时间间隔',
  staticInterval: '静止时间间隔',
  notSupportTraffic: 'Coming soon.',
  ignite: '打火',
  flameout: '熄火',
  generateRenewalPointSuc: '生成续费点成功！',
  noGPSsignal: '未定位',
  imeiErr2: '请输入至少后6位以上IMEI号',
  searchCreateStr2: '此操作将创建一个账号，同时将此设备转移到该账号名下',
  addUser: '新增用户',
  alarmTemperature: '报警温度值',
  highTemperatureAlarm: '高温报警',
  lowTemperatureAlarm: '低温报警',
  temperatureTip: '请输入温度值！',
  // 1.8.3
  noAddressKey: '今天的解析次数已用完',
  locMode: '定位模式',
  imeiInput: '请输入IMEI号',
  noResult: '没有匹配的结果',
  deviceGroup: '设备分组',
  shareManage: '分享管理',
  lastPosition: '最后位置',
  defaultGroup: '默认组',
  tankShape: '油箱形状',
  standard: '标准',
  oval: '椭圆',
  irregular: '不规则',
  //1.8.4
  inputAddressOrLoc: '请输入地址/经纬度',
  inputGroupName: '请输入组名',
  lock: '锁定',
  shareHistory: '分享轨迹',
  tomorrow: '明天',
  threeDay: '三天',
  shareSuccess: '生成分享链接成功',
  effective: '有效',
  lapse: '失效',
  copyShareLink: '复制分享链接',
  openStr: '开启',
  closeStr: '关闭',
  linkError: '此分享链接已失效',
  inputUserName: '请输入客户名称',
  barCodeStatistics: '条码统计',
  barCode: '条码',
  sweepCodeTime: '扫码时间',
  workModeType2: '1 智能休眠模式；2 深度休眠模式；3 远程开关机模式',
  remoteSwitchMode: '远程开关机模式',
  saleTime: '销售日期',
  onlineTime: '上线日期',
  dayMileage: '今日里程',
  imeiNum: 'IMEI号',
  overSpeedValue: '超速阈值',
  shareNoOpen: '分享链接未启用',
  addTo2: '添加到',
  overDue: '过期',
  openInterface: '开放接口',
  privacyPolicy: '隐私政策',
  serviceTerm: '服务条款',
  importError: '添加失败，设备号(IMEI)必须为15位纯数字!',
  importResult: '导入结果',
  totalNum: '共计',
  successInfo: '成功',
  errorInfo: '失败',
  repeatImei: '重复IMEI号',
  includeAccount: '包含子账号',
  formatError: '格式错误',
  importErrorInfo: '请输入15位纯数字的IMEI号',
  // 1.8.5
  totalMileage: '总计里程',
  totalOverSpeed: '总计超速（次）',
  totalStop: '总计停留（次）',
  totalOil: '总计油量',
  timeChoose: '时间选择',
  intervalTime: '间隔时间',
  default: '默认',
  idleSpeedStatics: '怠速统计',
  offlineStatistics: '离线统计',
  idleSpeed: '怠速',
  idleSpeedTimeTip1: '怠速时间不能为空',
  idleSpeedTimeTip2: '怠速时间必须为正整数',
  averageSpeed: '平均速度',
  averageOil: '平均油耗',
  oilImgTitle: '油量分析图',
  oilChangeDetail: '油量变化详情',
  machineNameError: "设备名称不能包含特殊符号( / ' )",
  remarkError: '备注信息不能超过50个字',
  defineColumnTip: '最多勾选12项',
  // 1.8.6
  pswCheckTip: '建议是6-20位数字、字母和符号的组合',
  chooseGroup: '请选择分组',
  chooseAgain: '仅可查询近半年的数据，请重新选择！',
  noDataTip: '暂无数据！',
  noMachineNameError: '请选择设备！',
  loginAccountError: '登录账号不能为15位纯数字！',
  includeExpire: '其中过期',
  groupNameTip: '分组名不能为空！',
  outageTips: '确定要断油电吗？',
  powerSupplyTips: '确定要恢复油电吗？',
  centerPhoneTips: '请输入数字',
  centerPhoneLenTips: '请输入8-20位数字',
  passworldillegal: '存在非法字符',
  // 2.0.0 POI，权限版本
  singleAdd: '单个添加',
  batchImport: '批量导入',
  name: '名称',
  icon: '图标',
  defaultGroup: '默认分组',
  remark: '备注',
  uploadFile: '请上传文件',
  exampleDownload: '模板下载',
  uploadFiles: '上传文件',
  poiTips1: '您可通过上传Excel来导入POI点数据，Excel文件字段需按照模板 以下要求格式输入',
  poiTips2: '名称：必填，不能超过32个字',
  poiTips3: '图标：必填，输入1,2,3,4',
  poiTips4: '经度：必填',
  poiTips5: '纬度：必填',
  poiTips6:
    '组名称：选填，不能超过32个字，若未填写分组名称，该POI点 归属默认分组，若填写的分组名称和已创建的分组名称一致， 该POI点归属到已创建的分组中，若填写的分组名称未被创建过， 系统会新增该分组',
  poiTips7: '备注：选填，不超过50个字',
  // 权限相关
  roleLimit: '角色权限',
  operateLog: '操作日志',
  sysAccountManage: '权限账号',
  rolen: '角色',
  rolename: '角色名称',
  addRole: '新增角色',
  editRole: '编辑角色',
  deleteRole: '删除角色',
  delRoleTip: '确定要删除此角色吗？',
  delAccountTip: '确定要删除此账户吗？',
  limitconfig: '权限配置',
  newAccountTip1:
    '权限账号与旧虚拟账号类似，同为管理员的子账号。管理员可创建权限账号，为权限账号分配不同的角色，以实现不同账号在平台能看到的内容和操作不相同。',
  newAccountTip2: '创建权限账号流程：',
  newAccountTip31: '1. 在角色管理页面，',
  newAccountTip32: '新建角色',
  newAccountTip33: '，并为角色配置权限；',
  newAccountTip4: '2. 在权限账号管理页面，新建权限账号，为账号分配角色。',
  newRoleTip1: '管理员可创建角色，为不同的角色配置不同的操作权限，以满足不同场景下的业务需求。',
  newRoleTip2: '比如配置一个财务角色是否具有定位监控的权限、是否具有添加客户的权限、是否具有修改设备信息的权限等等',
  refuelrate: '加油速率',
  refuellimit: '每分钟的油量增加大于xxxxL时，小于xxxxL时，视为加油。',
  refueltip: '最小加油速率不得大于最大加油速率！',
  viewLimitConf: '查看权限设置',
  viewLimit: '查看权限',
  newSysAcc: '新建系统账号',
  editSysAcc: '编辑权限账号',
  virtualAcc: '虚拟账号',
  oriVirtualAcc: '原虚拟账号',
  virtualTip: '虚拟账号模块已升级为系统账号模块，请新建系统账号',
  operaTime: '操作时间',
  ipaddr: 'IP地址',
  businessType: '业务类型',
  params: '请求参数',
  operateType: '操作类型',
  uAcc: '用户账号',
  uName: '用户名称',
  uType: '用户类型',
  logDetail: '日志详情',
  delAccount: '删除账号',
  modifyTime: '修改时间',
  unbindlimit: '无关联设备权限，无法创建一键电子围栏！',
  setSmsTip: '若设置短信通知，需先开启平台通知；若下发平台通知成功，短信通知未成功，需再次开启短信通知',
  cusSetComTip: '免责声明：通过自定义指令带来的风险与平台无关',
  cusSetComPas: '请输入当前登录账号的密码',
  cusSetComDes1: '自定义指令仅支持在线指令。',
  cusSetComDes2: '指令发送两分钟内设备无响应，则结束进程，判断该指令状态为无响应。',
  cueSetComoffline: '设备无响应，自定义指令发送失败！',
  fbType: '反馈类型',
  fbType1: '问题咨询',
  fbType2: '功能异常',
  fbType3: '用户体验',
  fbType4: '新功能建议',
  fbType5: '其他',
  upload: '上传',
  uploadImg: '上传图片',
  uploadType: '请上传.jpg .png .jpeg .gif类型的文件',
  uploadSize: '上传文件不能大于3M',
  fbManager: '反馈管理',
  blManager: '公告管理',
  fbUploadTip: '请选择反馈类型',
  menuPlatform: '平台消息',
  menuFeedback: '意见反馈',
  menuBulletin: '平台公告',
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: '驾驶行为',
  BtyjdfghtwsrgGHFEEGRDAF: '急加速',
  BtyuwyfgrWERERRTHDAsdDF: '急减速',
  Be2562h253grgsHHJDbRDAF: '急转弯',
  celTemperature: '摄氏温度'
}
// 权限tree
lg.limits = {
  ACC_statistics: 'ACC统计',
  Account_Home: '账户首页',
  Add: '新增',
  Add_POI: '添加POI',
  Add_customer: '新增客户',
  Add_device_group: '添加设备分组',
  Add_fence: '添加围栏',
  Add_sharing_track: '添加分享轨迹',
  Add_system_account: '新增权限账号',
  Alarm_details: '报警详单',
  Alarm_message: '报警消息',
  Alarm_overview: '报警总览',
  Alarm_statistics: '报警统计',
  All_news: '全部消息',
  Associated_equipment: '关联设备',
  Available_points: '可用点数',
  Barcode_statistics: '条码统计',
  Batch_Import: '批量导入',
  Batch_renewal: '批量续费',
  Batch_reset: '批量重置',
  Bulk_sales: '批量销售',
  Call_the_police: '报警',
  Customer_details: '客户详情',
  Customer_transfer: '客户转移',
  Delete_POI: '删除POI',
  Delete_account: '删除账号',
  Delete_customer: '删除客户',
  Delete_device: '删除设备',
  Delete_device_group: '删除设备分组',
  Delete_fence: '删除围栏',
  Delete_role: '删除角色',
  Device_List: '设备列表',
  Device_grouping: '设备分组',
  Device_transfer: '设备转移',
  Due_reminder: '到期提醒',
  Edit_details: '修改详情',
  Equipment_management: '设备管理',
  My_clinet: '我的设备',
  Fence: '围栏',
  Fence_management: '围栏管理',
  Generate: '生成',
  'Generate_lead-in_points': '生成导入点',
  Generate_renewal_points: '生成续费点',
  Have_read: '清除',
  Idle_speed_statistics: '怠速统计',
  Import: '导入',
  Import_Device: '导入设备',
  Industry_Statistics: '行业统计',
  Location_monitoring: '定位监控',
  Log_management: '日志管理',
  Mark_read: '标记已读',
  Menu_management: '菜单管理',
  Message_Center: '消息中心',
  Mileage_statistics: '里程统计',
  Modify_POI: '修改POI',
  Modify_device_details: '修改设备详情',
  Modify_device_group: '修改设备分组',
  Modify_role: '修改角色',
  Modify_sharing_track: '修改分享轨迹',
  Modify_user_expiration: '修改用户到期',
  More: '更多',
  My_client: '我的客户',
  New_role: '新增角色',
  New_users: '新增用户',
  Oil_statistics: '油量统计',
  POI_management: 'POI管理',
  Points_record: '点数记录',
  Push: '推送',
  Quick_sale: '快速销售',
  Renew: '续费',
  Replay: '回放',
  Role_management: '角色管理',
  Run_overview: '运行总览',
  Running_statistics: '运行统计',
  Sales_equipment: '销售设备',
  Set_expiration_reminder: '设置到期提醒',
  Share_track: '分享轨迹',
  Sharing_management: '分享管理',
  Speeding_detailed_list: '超速详单',
  Statistical_report: '统计报表',
  Stay_detailed_list: '停留详单',
  System_account_management: '权限账号',
  Temperature_statistics: '温度统计',
  Transfer: '转移',
  Transfer_group: '转移分组',
  Transfer_point: '转移导入点',
  Transfer_renewal_point: '转移续费点',
  Trip_statistics: '行程统计',
  Unlink: '取消关联',
  View: '查看',
  View_POI: '查看POI',
  View_device_group: '查看设备分组',
  View_fence: '查看围栏',
  View_role: '查看角色',
  View_sharing_track: '查看分享轨迹',
  Virtual_account: '虚拟账号',
  Voltage_analysis: '电压分析',
  Voltage_statistics: '电压统计',
  batch_deletion: '批量删除',
  change_Password: '修改密码',
  delete: '删除',
  edit: '编辑',
  instruction: '指令',
  modify: '修改',
  monitor: '监控',
  my_account: '我的账户',
  reset_Password: '重置密码',
  share_it: '分享',
  sub_user: '下级用户',
  track: '跟踪',
  Custom_Order: '自定义指令',
  GeoKey_Manager: 'GeoKey管理',
  GeoKey_Update: '修改',
  GeoKey_Delete: '删除',
  GeoKey_Add: '添加',
  GeoKey_View: '查看',
  feedback_manager: '反馈管理',
  feedback_list: '查看',
  feedback_handle: '处理反馈',
  proclamat_manager: '公告管理',
  proclamat_manager_list: '查看公告',
  proclamat_manager_update: '修改公告',
  proclamat_manager_delete: '删除公告',
  proclamat_manager_save: '新增公告',
  device_update_batch_model: '批量修改设备型号'
}

// 问题文档的内容
lg.questionDocumentArr = [
  [
    '问：接线设备安装后指示灯不亮，处于离线状态',
    '答：请您熄火汽车后，用电笔和万能表测量所接汽车线路电压是否符合GPS追踪器电压范围一般是9-36V。<br/>接线注意事项：安装接线人员需对汽车线路有了解，并有一定动手能力，以免接线不当给您的爱车造成损害。'
  ],
  [
    '问：有线设备或者无线实时追踪设备，电话打通或者物联网后台开机状态设备离线',
    '答：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1.发短信重启，观察几分钟看是否上线。一般发RESET#具体请联系经销商确定。<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.所在网络连接不稳定，请移动车子到信号良好区域。<br/>&nbsp;&nbsp;&nbsp;&nbsp;3.以上步骤操作后，还未能上线，需联系移动运营商查看卡是否异常。'
  ],
  ['问：月初和月底设备批量离线', '答：请您查询卡是否是欠费停机，如果是欠费请及时充值，恢复使用。'],
  [
    '问：车子行驶，GPS在线位置不更新',
    '答：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1.接线设备可以发短信STATUS#查看卫星信号接收状态，看到GPS：searching satellite就是卫星信号一直处于搜索中，这种情况需要检查安装位置，是否有按照说明书要求去安装的。正面朝上，上方不能有金属遮挡。<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.发短信STATUS#，返回状态是GPS：OFF，请再发FACTORY#，收到回复OK后，观察5分钟看位置是否有更新<br/>&nbsp;&nbsp;&nbsp;&nbsp;3.按照以上2种方法还不能排除故障，请联系卖家返修。'
  ],
  [
    '问：为何充电充了很久电量平台还是显示不满电呢？',
    '答：平台电量显示是依据设备反馈回来的信息做一个数据解析来判断设备当前电量的，在某些特殊情况下会出现电量显示误差解决方案：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、设备电量数据与设备定位数据是一起上传的，如果充电许久，电量未曾发生变化，请您：①带上您的设备移动100-300米的位置，让设备的位置信息更新，使其电量数据与位置数据可以一起反馈到平台从而是电量显示刷新。<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、根据电源指示灯变化判定是否满电，（以S15为例）操作步骤如下：①充电8-10个小时，然后电源指示灯变黄绿色后，拔掉充电线后，在插入充电线，15分钟内电源指示灯再变为变黄绿色即为满电；其它型号请查看说明书。<br/>&nbsp;&nbsp;&nbsp;&nbsp;3、充很久也充不满电，这种情况有可能是充电插头电压低于1A，请您用电压5V、1A的充电头充8-10小时即可。'
  ],
  [
    '问: 支持GPS断油电指令下发成功了，为什么车都油电还是没有断呢？',
    '答:断油电指令下发成功后，必须在以下条件满足设备才会执行断油电: <br/>&nbsp;&nbsp;&nbsp;&nbsp;1、确保设备的接线正确，按照说明书接线图接线。<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、设备正常工作，处于静止或行驶状态，有定位，未离线，且车辆时速不超过20公里；<br/>如果车辆处于离线、未定位或车辆时速超过20 公里以上，即使断油电指令下发成功，终端也是不会执行的。'
  ],
  [
    '问：三年无线产品第一次安装好，显示设备未定位或者在线',
    '答：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、打开开关观察指示灯是否闪烁,例如S18 黄色与绿色指示灯同时快闪为正常，慢闪在搜索信号中，不亮设备故障。（不同型号指示灯状态会不一样，其它型号请看说明书）<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、指示灯闪烁不上线，如果在信号较差位置开机请拿到信号良好区域开机。信号良好区域也不上线，可关机1分钟，重新装卡再开机测试。'
  ],
  [
    '问：有线产品第一次安装好，显示设备未定位',
    '答：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、观察终端GPS 状态指示灯是否正常，按照不同型号的说明书查看指示灯状态。<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、指示灯不亮设备没能正常通电，换地方取电测试。<br/>&nbsp;&nbsp;&nbsp;&nbsp;3、(绿黄色)卡指示灯不亮，断电重新安装卡，再通电看到常亮为正常。<br/>&nbsp;&nbsp;&nbsp;&nbsp;4、确定设备里的SIM卡号没有欠费停机情况，GPRS上网功能是否正常。<br/>&nbsp;&nbsp;&nbsp;&nbsp;5、设备所在地方无GSM网络，例如底下室，隧道等，信号弱的地区，请开车到GPRS覆盖好的地方测试。<br/>&nbsp;&nbsp;&nbsp;&nbsp;6、定位器的安装位置不要过于封闭，不要有金属物摭挡，在车内的安装位置尽量靠上。不然影响信号接收。<br/>&nbsp;&nbsp;&nbsp;&nbsp;7、正常开机，停在信号良好区域不上线，可重新发上线指令查看IP接口以及卡链接网络是否正常。'
  ],
  [
    '问：接线设备安装后指示灯不亮，处于离线状态',
    '解决方案:<br/>&nbsp;&nbsp;&nbsp;&nbsp;请您熄火汽车后，用电笔和万能表测量所接汽车线路电压是否符合GPS追踪器电压范围一般是9-36V。<br/>&nbsp;&nbsp;&nbsp;&nbsp;接线注意事项：<br/>&nbsp;&nbsp;&nbsp;&nbsp;安装接线人员需对汽车线路有了解，并有一定动手能力，以免接线不当给您的爱车造成损害。'
  ],
  [
    '问：有线设备或者无线实时追踪设备，电话打通或者物联网后台开机状态设备离线',
    '答：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1.发短信重启，观察几分钟看是否上线。一般发RESET#具体请联系经销商确定。<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.所在网络连接不稳定，请移动车子到信号良好区域。<br/>&nbsp;&nbsp;&nbsp;&nbsp;3.以上步骤操作后，还未能上线，需联系移动运营商查看卡是否异常。'
  ],
  ['问：月初和月底设备批量离线', '解决方案：请您查询卡是否是欠费停机，如果是欠费请及时充值，恢复使用。'],
  [
    '问：车子行驶，GPS在线位置不更新',
    '解决方案:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1.接线设备可以发短信STATUS#查看卫星信号接收状态，看到GPS：searching satellite就是卫星信号一直处于搜索中，这种情况需要检查安装位置，是否有按照说明书要求去安装的。正面朝上，上方不能有金属遮挡。<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.发短信STATUS#，返回状态是GPS：OFF，请再发FACTORY#，收到回复OK后，观察5分钟看位置是否有更新。<br/>&nbsp;&nbsp;&nbsp;&nbsp;3.按照以上2种方法还不能排除故障，请联系卖家返修。'
  ],
  [
    '问：为何充电充了很久电量平台还是显示不满电呢？',
    '平台电量显示是依据设备反馈回来的信息做一个数据解析来判断设备当前电量的，在某些特殊情况下会出现电量显示误差<br/>解决方案：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、设备电量数据与设备定位数据是一起上传的，如果充电许久，电量未曾发生变化，请您：①带上您的设备移动100-300米的位置，让设备的位置信息更新，使其电量数据与位置数据可以一起反馈到平台从而是电量显示刷新；<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、根据电源指示灯变化判定是否满电，（以S15为例）操作步骤如下：①充电8-10个小时，然后电源指示灯变黄绿色后，拔掉充电线后，在插入充电线，15分钟内电源指示灯再变为变黄绿色即为满电；其它型号请查看说明书。<br/>&nbsp;&nbsp;&nbsp;&nbsp;3、充很久也充不满电，这种情况有可能是充电插头电压低于1A，请您用电压5V、1A的充电头充8-10小时即可。'
  ]
]
lg.webOptDoc =
  '<h1>一、新增用户帐号步骤</h1>' +
  '<ol>' +
  '<li>' +
  '1. 在浏览器输入我们的平台地址<span id="platformAddress">https://www.gpsnow.net</span>，输入经销商帐号进入到平台的登录界面。' +
  '</li>' +
  '<li>' +
  '2. 登录成功后您会看见平台的首页，在顶部的导航栏（如下图）点击“我的客户”。' +
  '</li>' +
  '<li>' +
  '3. 在全部客户列表选中一个客户,然后选着添加，弹出新增下级用户界面（如下图）。' +
  '<img src="../../lib/document/cn/img/1.png"/>' +
  '</li>' +
  '<li>' +
  '4. 根据用户需求分配用户类型（勾选专业权限用途是为S208油感温感项目设备显示更多信息）。' +
  '</li>' +
  '<li>' +
  '5. 客户名称可与登陆帐号不一致，也可一致。' +
  '</li>' +
  '<li>' +
  '6. 输入完信息点确定，显示添加成功完成。' +
  '</li>' +
  '<li>' +
  '7. 登录成功后您会看见平台的首页，在顶部的导航栏（如下图）点击“我的客户”' +
  '</li>' +
  '<li>' +
  '8. 登录成功后您会看见平台的首页，在顶部的导航栏（如下图）点击“我的客户”' +
  '</li>' +
  '</ol>' +
  '<h1>二、销售设备步骤</h1>' +
  '<ol>' +
  '<li>' +
  '1. 在搜索框输入下级用户帐号（如下图）' +
  '</li>' +
  '<li>' +
  '2. 在左边选中帐号，鼠标右键出现销售设备点击。' +
  '<img src="../../lib/document/cn/img/2.png"/>' +
  '</li>' +
  '<li>' +
  '3. 输入单个或者批量IMEI号，输入批量IMEI号。' +
  '<img src="../../lib/document/cn/img/3.png"/>' +
  '</li>' +
  '<li>' +
  '4. 输入完点确定，再点提交，系统提示销售成功完成。' +
  '</li>' +
  '</ol>' +
  '<h1>三、把建立好新帐号发给用户登陆。</h1>' +
  '<ol>' +
  '<li>' +
  '1. 浏览器打开https://www.gpsnow.net进入登陆界面，输入帐号密码（如下图）。' +
  '</li>' +
  '<li>' +
  '2. 引导客户使用扫描下方二维码，下周安卓端或者IOS端APP。' +
  '</li>' +
  '<li>' +
  '3. 把第一步建立好帐号与密码发给下级用户登陆。' +
  '</li>' +
  '<img src="../../lib/document/cn/img/4.png"/>' +
  '<img src="../../lib/document/cn/img/5.png"/>' +
  '<li>' +
  '登陆后网页端监控页面展示' +
  '</li>' +
  '<li>' +
  '用户在此监控设备，查看设备各项统计和报警信息，查看设备详情，以及修改自己资料和登录密码。' +
  '</li>' +
  '</ol>'
lg.appOptDoc = '敬请期待...'
// 查询参数的帮助
var html = []
html.push('<table class="cmdTable" width="100%" border="0">')
html.push('<tr>')
html.push('<td class="cmdLabel" width="60">PSW:</td>')
html.push('<td>查询终端密码</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">PHONE:</td>')
html.push('<td>查询终端内置SIM卡号码</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">USER:</td>')
html.push('<td>查询车主手机号码</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">SPEED:</td>')
html.push('<td>查询超速报警限速值</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">FREQ:</td>')
html.push('<td>查询开启追踪后的上报频率,单位是秒</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">TRACE:</td>')
html.push('<td>查询是否开启追踪,1是开启追踪,0是关闭追踪</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">RADIUS:</td>')
html.push('<td>查询非法移位报警判定范围,单位是米</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VIB:</td>')
html.push('<td>查询是否开启振动短信报警,1为开启0为关闭')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VIBL:</td>')
html.push('<td>查询振动灵敏度0~15,0为最高灵敏度,太高可能会误报,15为最低灵敏度</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VIBCALL:</td>')
html.push('<td>查询是否开启振动电话报警,1为开启0为关闭')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VIBGPS:</td>')
html.push('<td>查询是否开启GPS过滤漂移功能,1为开启0为关闭,如果开启则防盗器在5分钟内没有发生振动,则进入静止状态,过滤所有GPS漂移点</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">SLEEP:</td>')
html.push('<td>查询是否开启休眠功能,1为开启0为关闭,如果开启则防盗器在30分钟内没有发生振动,则进入休眠状态,关闭GPS断链,从而节省电量</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">POF:</td>')
html.push('<td>查询是否开启断电报警功能,1为开启0为关闭</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">GPS:</td>')
html.push(
  '<td>查询GPS接收的卫星编号和强度,例如：2300 1223 3431 。。。 一共12组四位数,2300表示接收到编号23卫星信号强度为00,1223表示接收到编号为12的卫星信号强度为23</td>'
)
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VBAT:</td>')
html.push(
  '<td>查询电池电压，充电接口电压，充电电流大小,例如：VBAT=3713300：4960750：303500 表示电池电压为3713300uV，即3.71v,充电电压为4.96V，充电电流303mA</td>'
)
html.push('</tr>')
html.push('</table>')
lg.queryparamhelp = html.join('')

// 设置参数的帮助
html = []
html.push('<table class="cmdTable" width="100%" border="0">')
html.push('<tr>')
html.push('<td class="cmdLabel" width="60">PSW:</td>')
html.push('<td>设置终端密码,密码只能为6位数字</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">PHONE:</td>')
html.push('<td>设置终端内SIM卡号码</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">USER:</td>')
html.push('<td>设置车主手机号码</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">SPEED:</td>')
html.push('<td>设置超速报警限速值,范围应当在0~300之间</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">FREQ:</td>')
html.push('<td>设置开启追踪后的上报频率,单位是秒</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">TRACE:</td>')
html.push('<td>设置是否开启追踪,1是开启追踪,0是关闭追踪</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">RADIUS:</td>')
html.push('<td>设置非法移位报警判定范围,单位是米</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VIB:</td>')
html.push('<td>设置是否开启振动短信报警,1为开启0为关闭')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VIBL:</td>')
html.push('<td>设置振动灵敏度0~15,0为最高灵敏度,太高可能会误报,15为最低灵敏度</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VIBCALL:</td>')
html.push('<>设置是否开启振动电话报警,1为开启0为关闭')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">VIBGPS:</td>')
html.push('<td>设置是否开启GPS过滤漂移功能,1为开启0为关闭,如果开启则防盗器在5分钟内没有发生振动,则进入静止状态,过滤所有GPS漂移点</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">SLEEP:</td>')
html.push('<td>设置是否开启休眠功能,1为开启0为关闭,如果开启则防盗器在30分钟内没有发生振动,则进入休眠状态,关闭GPS断链,从而节省电量</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td class="cmdLabel">POF:</td>')
html.push('<td>设置是否开启断电报警功能,1为开启0为关闭</td>')
html.push('</tr>')
html.push('</table>')
lg.setparamhelp = html.join('')

//批量添加
html = []
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox" ' + 'style="z-index: 999;position:absolute;left:120px;top:88px;height:200px;width:250px;"> '
)
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> ')
html.push('</div>')
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">')
html.push('<tr>')
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加到:</td>')
html.push('<td>')
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    'bulkAdds_treeDiv' +
    ',' +
    'bulkAdds_seller' +
    ')" style="width:250px;height:28px;">'
)
html.push('<input  type="hidden" id="bulkAdds_userId" >')
html.push('</td>')
html.push('<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td style="text-align: right"><span style="color:red;">*</span>平台到期:</td>')
html.push('<td>')
html.push('<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">')
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td style="text-align: right"><span style="color:red;">*</span>设备型号:</td>')
html.push('<td>')
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    '</div>' +
    '<div id="deviceList"></div>' +
    '</div>' +
    '</span>'
)
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加设备:</td>')
html.push('<td>')
html.push('<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' + lg.addTo + '" style="cursor:pointer"></img>')
html.push('</td>')
html.push('</tr>')
html.push('</table>')
html.push('<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>')
lg.bulkAdds = html.join('')

//批量续费
html = []
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:92px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
)
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> ')
html.push('</div>')
html.push('<form id="bs_form">')
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">')
html.push('<tr>')
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加设备:</td>')
html.push('<td>')
html.push('<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' + lg.addTo + '" style="cursor:pointer"></img>')
html.push('<span class="re_addNumBox">当前：<span id="account_re_addNum">0</span>')
html.push('</span>')
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td>')
html.push('</td>')
html.push('<td>')
html.push('<table id="account_re_machineList" style="width:400px;"></table>')
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td style="text-align:right;"><span style="color:red">*</span>卡类型</td>')
html.push('<td>')
html.push('<input  type="radio" name="red_cardType"')
html.push('class="easyui-validatebox"  value="3" checked><label>一年</label></input>')
html.push('<input  type="radio" name="red_cardType" style="margin-left:15px;" ')
html.push('class="easyui-validatebox" value="4"><label>终身</label></input>')
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td style="text-align: right">扣除点数</td>')
html.push('<td>')
html.push('<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">')
html.push('</td>')
html.push('</tr>')
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>用户到期:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>')
html.push('<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>备注</td>')
html.push('<td>')
html.push('<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>')
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td>')
html.push('</td>')
html.push('<td>')
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
)
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
)
html.push('</td>')
html.push('</tr>')
html.push('</table>')
html.push('</form>')
lg.bulkRenew = html.join('') //已废弃

// //批量销售，myAccount
html = []
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel treePulldownBox"  style="z-index: 999;position:absolute;left:120px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
)
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> ')
html.push('</div>')
html.push('<form id="bs_form">')
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">')
html.push('<tr>')
html.push('<td style="text-align: right"><span style="color:red;">*</span>销售给:</td>')
html.push('<td>')
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    'bulkSales_treeDiv' +
    ',' +
    'bulkSales_seller' +
    ')" style="width:250px;height:28px;">'
)
html.push('<input  type="hidden" id="bulkSales_userId" >')
html.push('<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>')
html.push('</td>')
html.push('</tr>')
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>用户到期:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>')
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加设备:</td>')
html.push('<td>')
html.push('<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' + lg.addTo + '" style="cursor:pointer"></img>')
html.push('<span class="bs_addNumBox">当前：<span id="account_bs_addNum">0</span>')
html.push('</span>')
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td>')
html.push('</td>')
html.push('<td>')
html.push('<table id="account_bs_machineList" style="width:400px;"></table>')
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td>')
html.push('</td>')
html.push('<td>')
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '" class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
)
html.push(
  '<a id="bs_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
)
html.push('</td>')
html.push('</tr>')
html.push('</table>')
html.push('</form>')

lg.bulkSales = html.join('')

//批量转移1，弹出框
// html = [];
// html.push('<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:127px;top:172px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
// html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
// html.push('</div>');
// html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>目标客户:</td>');
// html.push('<td>');
// html.push('<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
// html.push('<input  type="hidden" id="bulkTransfer_userId" >');
// html.push('</td>');
// html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
// html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>添加设备:</td>');
// html.push('<td>');
// html.push('<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >批量添加</a>');
// html.push('</td>');
// html.push('</tr>');
// html.push('</table>');
// html.push('<div style="padding-left:30px;padding-top:10px;">');
// html.push('<table id="bt_machineList" style="width:500px;"></table>');
// html.push('</div>');
// html.push('<a id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >转移</a>');
// html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)">取消</a>');

// lg.bulkTransfer = html.join('');

// //批量转移2,myClient
// html = [];
// html.push('<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:117px;top:84px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
// html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
// html.push('</div>');
// html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>目标客户:</td>');
// html.push('<td>');
// html.push('<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
// html.push('<input  type="hidden" id="bulkTransfer_userId" >');
// html.push('</td>');
// html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
// html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>添加设备:</td>');
// html.push('<td>');
// html.push('<img  id="bt_addMachines" style="cursor:pointer" src="../../images/main/myAccount/add3.png" />');
// html.push('</td>');
// html.push('</tr>');
// html.push('</table>');
// html.push('<div style="padding-left:30px;padding-top:10px;">');
// html.push('<table id="bt_machineList" style="width:500px;"></table>');
// html.push('</div>');
// html.push('<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
// html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');

// lg.bulkTransfer2 = html.join('');

//批量转移用户
html = []
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:116px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
)
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> ')
html.push('</div>')
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">')
html.push('<tr>')
html.push('<td style="text-align: right"><span style="color:red;">*</span>目标客户:</td>')
html.push('<td>')
html.push('<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">')
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
)
html.push('</td>')
html.push('<td></td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td>')
html.push('</td>')
html.push('<td>')
html.push('<table id="bt_userList" style="width:400px;"></table>')
html.push('</td>')
html.push('</tr>')
html.push('<tr>')
html.push('<td>')
html.push('</td>')
html.push('<td>')
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
)
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
)
html.push('</td>')
html.push('</tr>')
html.push('</table>')

lg.bulkTransferUser = html.join('')

window.lg = lg
