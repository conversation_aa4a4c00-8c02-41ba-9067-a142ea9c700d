var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
    site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
    site = 'Forcegps'
  }
var lg = {  //越南语
    //common common
    user_guide: 'Hướng dẫn sử dụng',
    remoteSwitch: "Công tắc từ xa",
    pageTitle: 'WhatsGPS Global Tracking System|Vehicle GPS Tracker|3G Tracker|mini 4G Tracker|GPSNow|Car Locator',
    description: site + " is dedicated to providing users with intelligent cloud location services. It is the world's leading location service platform.",
    pageLang: 'Tiếng Việt',
    inputCountTips: 'Số tài khoản / IMEI',
    inputPasswordTips: 'mật khẩu',
    appDownload: 'Tải xuống ứng dụng',
    rememberPassword: 'Nhớ tôi',
    forgetPassword: 'Quên mật khẩu',
    siteName: 'WhatsGPS',
    noToken: " Hãy gửi Token",
    loginFirst: 'Đăng nhập trước',
    move: 'Di chuyển',
    stop: 'Dừng',
    query: 'Kiểm tra',
    imeiQuery: 'IMEI',
    delete: 'Xóa',
    update: 'Cập nhật',
    cancel: 'Đóng',
    soft: 'sắp xếp',
    more: 'Thêm',
    edit: 'Sửa',
    useful:'Hữu ích',
    useless:'vô ích',
    replyFeedback:'Phản hồi về "$"',
    add: 'Thêm',
    addTo: 'Thêm',
    addDevice: 'Thêm thiết bị',
    machineName: 'Tên thiết bị',
    searchDevice: 'Thiết bị',
    date: 'Ngày giờ',
    LatestUpdate: 'Cập nhật',
    engine: 'ACC',
    locTime: 'Thời gian GPS',
    locType: 'Kiểu vị trí',
    startLoc: 'Vị trí bắt đầu',
    endLoc: 'Vị trí kết thúc',
    address: 'Địa chỉ nhà',
    noAddressTips: "Không thể lấy thông tin địa chỉ",
    lonlat: 'Vĩ độ và Kinh độ',
    carNO: 'Plate No.',
    imei: 'IMEI',
    IMEI: "IMEI",
    simNO: 'Thẻ điện thoại',
    activeTime: 'Thời gian kích hoạt',
    expireTime: 'Thời gian kết thúc',
    acceptSubordinateAlarm: 'Nhận báo động',
    acceptAlarmTips1: 'Sau khi xác nhận',
    acceptAlarmTips2: 'Như thế này, tin tức báo động của tất cả các nhà kính',
    speed: 'Tốc độ',
    y: "Năm",
    M: "Tháng",
    d: "Ngày",
    h: "Giờ",
    min: "Phút",
    s: "Giây",
    _year: 'Năm',
    _month: 'Tháng',
    _day: 'Ngày',
    _hour: 'Giờ',
    _minite: 'Phút',
    _second: 'Giây',
    confirm: 'Xác nhận',
    yes: "Vâng",
    car: 'Chiếc xe',
    not: "Không có",
    m: 'Mét',
    account: 'Tài khoản',
    psw: 'Mật khẩu',
    save: 'Lưu',
    operator: 'Vận hành',
    queryNoData: 'Truy vấn không có dữ liệu',
    name: 'Tên',
    type: 'Loại',
    open: 'Mở',
    close: 'Đóng',
    send: 'Gửi',
    alarm: 'Thông báo',
    alarmSetting: 'Đặt thông báo',
    look: 'Xem',
    tailAfter: 'Theo dõi',
    history: 'Xem lại',
    dir: 'Dir',
    locStatus: "Trạng thái vị trí",
    machineTypeText: 'Model',
    carUser: 'Người dùng ô tô',
    machine: 'Mục tiêu',
    unknowMachineType: 'Máy không xác định',
    noCommandRecord: 'Các lệnh không phù hợp cho thiết bị này',
    type1: 'Kiểu',
    role: 'Nhóm',
    roles: 'Các nhóm',
    timeType: 'Kiểu thời gian',
    moveSpeed: 'Tốc độ di chuyển',
    signal: 'Tín hiệu',
    loc: 'Vị trí',
    wiretype: 'Kiểu',
    wire: 'Có dây',
    wireless: 'Không dây',
    expire: 'Quá hạn',
    hour: 'Giờ',
    hourTo: 'Giờ đến',
    remark: 'Ghi chú',
    remarkInfo: 'Ghi chú',
    noPriviledges: 'Tài khoản không có quyền hoạt động',
    commandNoOpen: 'Lệnh thiết bị hiện tại chưa mở để sử dụng',
    choseDelelePhone: 'Vui lòng chọn số cần xóa đầu tiên',
    streetView: 'Cảnh đường phố',
    wrongFormat: 'Lỗi định dạng đầu vào',
    inputFiexd: 'Nhập số cố định',
    serialNumberStart: 'Bắt đầu dãy IMEI',
    serialNumberEnd: 'Kết thúc dãy IMEI',
    clickSearchFirst: 'Click vào thiết bị tìm kiếm trước tiên',
    isDeleteDevice: 'Điều đó không rõ ràng gì khi được khẳng định. Nó bị xóa rồi chứ?',
    //平台错误代码提示
    errorTips: 'Thao tác không thành công với mã lỗi£º',
    error10003: 'Sai mật khẩu',
    error90010: 'Thiết bị đang ngoại tuyến và không gửi được lệnh tùy chỉnh!',
    error70003: 'Giá trị điều khiển từ xa không được để trống',
    error70006: 'Không hỗ trợ hoặc không có quyền đưa ra hướng dẫn',
    error20001: 'ID phương tiện không được để trống',
    error20012: 'Xe chưa được kích hoạt',
    error10012: 'Mật khẩu cũ lỗi',
    error10017: 'Xóa không thành công, vui lòng xóa người dùng con!',
    error10023: 'Xóa không thành công, người dùng còn thiết bị',
    error20008: 'Thêm không thành công, IMEI đã tồn tại',
    error20006: 'Vui lòng nhập IMEI độ dài 15',
    error10019: 'Định dạng sai số điện thoại liên hệ',
    error10024: 'Không lặp lại bán hàng',
    error120003: 'Đã tắt chia sẻ link',
    error10025: 'Thông tin thiết bị đã sửa không để trống',
    error2010: 'Vui lòng tải lên tệp',
    error20002: 'Không có IMEI tồn tại',
    error10081: 'Không đủ số lượng thẻ gia hạn',
    error10082: 'Không cần sạc lại cho thiết bị suốt đời',
    error3000: 'Peran telah ditetapkan ke akun sistem dan tidak dapat dihapus',
    error103: 'Tài khoản đã bị vô hiệu hóa, vui lòng liên hệ với nhà cung cấp dịch vụ của bạn',
    error124: 'Không thể tự hoạt động',
    // 登陆相关 login.js
    logining: 'Đang đăng nhập...',
    login: 'Đăng nhập',
    userEmpty: 'Người dùng trống',
    pswEmpty: 'Mật khẩu trống',
    prompt: 'Nhắc nhở',
    accountOrPswError: 'Lỗi tài khoản hoặc mật khẩu',
    UserNameAlreadyExist: 'Tài khoản đăng nhập đã tồn tại',
    noQualified: 'Không có thông tin đủ điều kiện',
    //main.js
    systemName: 'WhatsGPS',
    navTitle_user: ['Giám sát', 'Báo cáo', 'Thiết bị'],
    navTitle_dealer: ['Trang chủ', 'Kinh doanh', 'Giám sát', 'Thêm hoạt động'],
    exitStytem: 'Thoát',
    user: 'người dùng',
    UserCenter: 'Trung tâm người dùng',
    alarmInfo: 'Thông báo',
    confirmExit: 'Xác nhận thoát?',
    errorMsg: 'Thông báo lỗi: ',
    logintimeout: 'Hết thời gian đăng nhập!',
    clearAlarm: 'Xóa',
    searchbtn: 'người dùng',
    print: 'In',
    export: 'Xuất',
    //feedback
    feedback: 'Phản hồi',
    feedback_sublime: 'Gửi đi',
    alerttitle: 'Tiêu đề không được để trống!',
    alertcontent: 'Phản hồi không được để trống!',
    submitfail: 'Gửi không thành công£¡',
    saveSuccess: 'Lưu thành công£¡',
    submitsuccess: 'Gửi thành công! Chúng tôi sẽ xử lý phản hồi của bạn sớm nhất có thể ~',
    adviceTitle: 'Tiêu đề',
    adviceTitle_p: 'Tiêu đề câu hỏi và ý kiến',
    adviceContent: 'Câu hỏi và ý kiến',
    adviceContent_p: 'Mô tả ngắn gọn các câu hỏi và nhận xét mà bạn muốn phản hồi và chúng tôi sẽ tiếp tục cải thiện cho bạn.',
    contact: 'Thông tin liên lạc',
    contact_p: 'Điền vào điện thoại hoặc email của bạn',
    //monitor.js
    myMachine: 'Thiết bị',
    all: 'Tất cả',
    online: 'Trực tuyến',
    offline: 'Ngoại tuyến',
    unUse: 'Không sử dụng',
    group: 'Nhóm',
    moveGruop: 'Di chuyển đến nhóm',
    arrearage: 'Còn Thiếu',
    noStatus: 'Không có trạng thái',
    inputMachineName: 'Mục tiêu/IMEI',
    defaultGroup: 'Nhóm mặc định',
    offlineLessOneDay: 'Ngoại tuyến<Một ngày',
    demoUserForbid: 'Người dùng trải nghiệm không thể sử dụng tính năng này',
    shareTrack: 'Chia sẻ vị trí',
    shareName: 'Tên',
    liveShare: 'Chia sẻ theo dõi thời gian thực',
    expiration: 'Thời gian hết hạn',
    getShareLink: 'Tạo link',
    copy: 'Sao chép',
    copySuccess: 'Sao chép thành công!',
    enlarge: 'Phóng to',
    shareExpired: 'Liên kết chia sẻ đã hết hạn',
    LinkFailure: 'Không thể mở liên kết',
    inputShareName: 'Nhập tên chia sẻ',
    inputValid: 'Vui lòng nhập thời gian lỗi chính xác',
    //statistics.js
    runOverview: "Tổng quan về di chuyển",
    runSta: 'Tổng quan về di chuyển',
    mileageSta: 'Báo cáo quãng đường',
    tripSta: 'Báo cáo di chuyển',
    overSpeedDetail: 'Chi tiết quá cước',
    stopDetail: 'Chi tiết dừng đỗ',
    alarmSta: 'Báo cáo thông báo',
    alarmOverview: 'Tổng quan thông báo',
    alarmDetail: 'Chi tiết thông báo',
    shortcutQuery: 'Kiểm tra nhanh',
    today: 'Hôm nay',
    yesterday: 'Hôm qua',
    lastWeek: 'Tuần trước',
    thisWeek: 'Tuần này',
    thisMonth: 'Tháng này',
    lastMonth: 'Tháng trước',
    mileageNum: 'Khoảng cách(Km)',
    overSpeedNum: 'Quá tốc độ(km/h)',
    overSpeed: 'Quá tốc độ',
    stopTimes: 'Dừng(Thời gian)',
    searchMachine: 'Tìm kiếm',
    speedNum: 'Tốc độ(km/h)',
    querying: 'Truy vấn',
    stopTime: 'Thời gian dừng',
    HisToryStopTime: 'Thời gian dừng',
    clickLookLoc: 'Click để xem địa chỉ',
    lookLoc: 'Vị trí truy vấn',
    noData: 'Không có dữ liệu',
    alarmTime: 'Thời gian thông báo',
    vibrationLevel: 'Mức độ rung',
    vibrationWay: 'Loại thông báo',
    acc: 'ACC',
    accStatistics: 'Thống kê ACC',
    accType: ['Tất cả', 'ACC Mở', 'ACC Tắt'],
    accstatus: ['Mở', 'Tắt'],
    openAccQuery: 'Mở truy vấn ACC',
    runtime: 'Thời gian chạy',
    //监控页面修改
    run: 'Du lịch',
    speed: 'Tốc độ',
    //设备管理
    machineManage: 'Thiết bị',
    deviceTable: 'Mục tiêu của tôi',
    status: 'Trạng thái',
    havaExpired: 'Đã hết hạn',
    expiredIn60: 'Hết hạn sau 60',
    expiredIn7: 'Hết hạn sau 7',
    normal: 'Bình thường',
    allMachine: 'Tất cả',
    allMachine1: 'Tất cả thiết bị',
    expiredIn7Machine: 'Hết hạn sau 7 ngày',
    expiredIn60Machine: 'Hết hạn sau 60 ngày',
    havaExpiredMachine: 'Mục tiêu hết hạn',

    //history.js
    replay: 'Chạy',
    replaytitle: '  Phát lại',
    choseDate: 'Chọn ngày',
    from: 'Từ',
    to: 'Tới',
    startTime: 'Start time',
    endTime: 'End time',
    pause: 'Dừng',
    slow: 'Chậm',
    mid: 'Giữa',
    fast: 'Nhanh',
    startTimeMsg: 'Thời gian bắt đầu msg',
    endTimeMsg: 'Thời gian kết thúc msg',
    smallEnd: "Thời gian kết thúc ít hơn thời gian bắt đầu. Vui lòng chọn lại",
    bigInterval: 'Khoảng thời gian ít hơn 31 ngày',
    trackisempty: "Theo dõi trống",
    longitude: 'Kinh độ',
    latitude: 'Vĩ độ',
    direction: 'Phương hướng',
    stopMark: 'Điểm đánh dấu',
    setStopTimes: [
        {
            text: '1 Phút',
            value: '1'
        },
        {
            text: '2 Phút',
            value: '2'
        },
        {
            text: '3 Phút',
            value: '3'
        },
        {
            text: '5 Phút',
            value: '5'
        },
        {
            text: '10 Phút',
            value: '10'
        },
        {
            text: '15 Phút',
            value: '15'
        },
        {
            text: '20 Phút',
            value: '20'
        },
        {
            text: '30 Phút',
            value: '30'
        },
        {
            text: '45 Phút',
            value: '45'
        },
        {
            text: '1 Giờ',
            value: '60'
        },
        {
            text: '6 Giờ',
            value: '360'
        },
        {
            text: '12 Giờ',
            value: '720'
        },
    ],
    filterDrift: 'Xóa nhảy vị trí',
    userType: ['Admin', 'Đại lý', 'Người dùng', 'Hậu cần', 'Cho thuê', 'Người dùng ô tô', 'Kiểm soát rủi ro', 'Chuyên nghiệp'],
    userTypeArr: ["Admin", "Đại lý", "Người dùng", 'Hậu cần', "Cho thuê", 'Người dùng ô tô', 'Chuyên nghiệp'],
    machineType: {
        '0':'Loại máy',
        '1':'S15',
        '2':'S05',
        '93':'S05L',
        '94': 'S309',
        '95': 'S15L',
        '96':'S16L',
        '97':'S16LA',
        '98':'S16LB',
        '3':'S06',
        '4':'SW06',
        '5':'S001',
        '6':'S08',
        '7':'S09',
        '8':'GT06',
        '9':'S08V',
        '10':'S01',
        '11':'S01T',
        '12':'S116',
        '13':'S119',
        '14':'TR06',
        '15':'GT06N',
        '16':'S101',
        '17':'S101T',
        '18':'S06U',
        '19':'S112U',
        '20':'S112B',
        '21':'SA4',
        '22':'SA5',
        '23':'S208',
        '24':'S10',
        '25':'S101E',
        '26':'S709',
        '99':'S709L',
        '27':'S1028',
        '28':'S102T1',
        '29':'S288',
        '30':'S18',
        '31':'S03',
        '32':'S08S',
        '33':'S06E',
        '34':'S20',
        '35':'S100',
        '36':'S003',
        '37':'S003T',
        '38':'S701',
        '39':'S005',
        '40':'S11',
        '41':'T2A',
        '42':'S06L',
        '43':'S13',
        '86':'S13-B',
        '44':'GT800',
        '45':'S116M',
        '46':'S288G',
        '47':'S09L',
        '48':'S06A',
        '49':'S300',
        '50':'',
        '51':'GS03A',
        '52':'GS03B',
        '53':'GS05A',
        '54':'GS05B',
        '55':'S005T',
        '56':'AT6',
        '57':'GT02A',
        '58':'GT03C',
        '59':'S5E',
        '60':'S5L',
        '61':'S102L',
        '85':'S105L',
        '62':'TK103',
        '63':'TK303',
        '64':'ET300',
        '65':'S102A',
        '91':'S102A-D',
        '66':'S708',
        '67':'MT05A',
        '68':'S709N',
        '69':'',
        '70':'GS03C',
        '71':'GS03D',
        '72':'GS05C',
        '73':'GS05D',
        '74':'S116L',
        '75':'S102',
        '76':'S102T',
        '77':'S718',
        '78':'S19',
        '79':'S101A',
        '80':'VT03D',
        '81':'S5L-C',
        '82':'S710',
        '83':'S03A',
        '84':'C26',
        '87':'S102M',
        '88':'S101-B',
        '92':'LK720',
        '89':'S116-B',
        '90':'X3'
      },
    alarmType: ['Loại báo động', 'Báo động rung động', 'Báo động tắt nguồn', 'Báo động Pin yếu', 'Báo động SOS', 'Báo động quá tốc độ', 'Báo động ra khỏi hàng rào', 'Báo động chuyển vị trí', 'Báo động pin ngoài yếu',
        'Báo động ngoài khu vực', 'Báo động tháo rời', 'Báo động cảm biến ánh sáng', 'Báo động cảm ứng từ', 'Tắt báo động', 'Báo động Bluetooth', 'Báo động che chắn tín hiệu', 'Báo động trạm gốc sai', 'Báo động vào hàng rào', 'Báo động vào hàng rào',
        'Báo động ra khỏi hàng rào', 'Báo động mở cửa', 'Lái xe mệt mỏi', 'Điểm thế chấp nhập cảnh', 'Thoát khỏi điểm thay thế', 'Điểm thay thế', 'Thiết bị đầu cuối ngoại tuyến', 'Báo động vào hàng rào', 'Báo động ra hàng rào', 'Báo động vào hàng rào', 'Báo động ra hàng rào', 'Báo động nhiên liệu', 'Báo động ACC ON', 'Báo động ACC OFF', 'Báo động va chạm'],
    alarmTypeNew:  {
        '40': 'Báo động nhiệt độ cao',
        '45': 'Báo động nhiệt độ thấp',
        '50': 'Báo động quá áp',
        '55': 'Báo động điện áp thấp',
        '60': 'Báo động đậu xe'
    },
    alarmNotificationType: [
        { type: 'Báo động rung', value: 1 },
        { type: 'Báo động tắt nguồn', value: 2 },
        { type: 'Báo động Pin yếu', value: 3 },
        { type: 'Báo động SOS', value: 4 },
        { type: 'Báo động quá tốc độ', value: 5 },
        // {type:'Báo động ra khỏi hàng rào',value:6},
        { type: 'Báo động chuyển vị trí', value: 7 },
        { type: 'Báo động pin ngoài yếu', value: 8 },
        { type: 'Báo động ngoài khu vực', value: 9 },
        { type: 'Báo động tháo rời', value: 10 },
        { type: 'Báo động cảm biến ánh sáng', value: 11 },
        { type: 'Tắt báo động', value: 13 },
        { type: 'Báo động che chắn tín hiệu', value: 15 },
        { type: 'Báo động trạm gốc sai', value: 16 },
        // {type:'Báo động vào hàng rào',value:17},
        // {type:'Báo động vào hàng rào',value:18},
        // {type:'Báo động vào hàng rào',value:19},
        { type: 'Lái xe mệt mỏi', value: 21 },
        { type: 'Điểm thay thế', value: 22 },
        { type: 'Thoát khỏi điểm thay thế', value: 23 },
        { type: 'Điểm thay thế', value: 24 },
        { type: 'Thiết bị đầu cuối offline', value: 25 },
        // {type:'Báo động hàng rào địa lý (Kiểm soát rủi ro)',value:26},
        // {type:'Báo động hàng rào địa lý (Kiểm soát rủi ro)',value:27},
        { type: 'Báo động vào hàng rào', value: 26 },
        { type: 'Báo động ra hàng rào', value: 27 },
        { type: 'Báo động nhiên liệu', value: 30 },
        { type: 'Báo động ACC ON', value: 31 },
        { type: 'Báo động ACC OFF', value: 32 },
        { type: 'Báo động va chạm', value: 33 }
    ],
    alarmTypeText: 'Loại báo động',
    alarmNotification: 'Thông báo',
    pointType: ['Loại điểm', 'Vị trí vệ tinh', 'Vị trí la bàn', 'Vị trí LBS', 'Vị trí WIFI'],
    cardType: ['Loại', 'Card mới', 'Card mới vĩnh viễn', 'Card mới', 'Card vĩnh viễn'],
    // 东南西北
    directarray: ["Đông", "Nam", "Tây", "Bắc"],
    // 方向字段
    directionarray: ["Về phía Bắc", "Đông Bắc", "Về phía Đông", "Đông Nam", "Về phía Nam", "Tây Nam", "Về phía Tây", "Tây Bắc"],
    // 定位方式
    pointedarray: ["Chưa xác định", "GPS", "LAC", "Vị trí LAC", "Vị trí WIFI"],


    //map Relevant 
    ruler: 'Cái Thước',
    distance: 'Giao thông',
    baidumap: "Bản đồ Baidu",
    map: 'Bản đồ',
    satellite: 'Vệ tinh',
    ThreeDimensional: '3D',
    baidusatellite: "Vệ tinh Baidu",
    googlemap: "Bản đồ Google",
    googlesatellite: "Vệ tinh Google",
    fullscreen: "Toàn màn hình",
    noBaidumapStreetView: 'Vị trí hiện tại trên Bản đồ Baidu mà không có Chế độ xem phố',
    noGooglemapStreetView: 'Vị trí hiện tại trên Google Maps mà không có Chế độ xem phố',
    exitStreetView: 'Thoát chế độ xem đường phố',
    draw: "Vẽ",
    finish: "Kết thúc",
    unknown: 'Không biết',
    realTimeTailAfter: 'Theo dõi thời gian thực',
    trackReply: 'Xem lại lịch sử',
    afterRefresh: "Làm mới",
    rightClickEnd: "Bên phải Nhấp vào cuối, bán kính£º",
    rightClickEndGoogle: "Bên phải nhấn vào cuối------------------------bán kính£º",


    //tree Relevant 
    currentUserMachineCount: 'số lượng người dùng hiện tại',
    childUserMachineCount: 'Đếm số lượng người dùng máy con',

    //Window relevant

    electronicFence: 'Hàng rào',
    drawTrack: 'Đặt hàng rào',
    showOrHide: 'Hiện/Ẩn',
    showDeviceName: 'Hiển thị tên thiết bị',
    circleCustom: 'Đã xác định người dùng',
    circle200m: 'Bán kính 200m',
    polygonCustom: 'Định nghĩa đa giác',
    drawPolygon: 'Vẽ đa giác',
    drawCircle: 'Vẽ một vòng tròn',
    radiusMin100: 'Bán kính hàng rào được vẽ ít nhất là 20 mét. Vui lòng vẽ lại. Bán kính hàng rào hiện tại:',
    showAllFences: 'Hiển thị tất cả hàng rào',
    lookEF: 'Kiểm tra hàng rào',
    noEF: 'Không phát hiện thấy dữ liệu data ',
    hideEF: 'Ẩn vị trí hàng rào',
    blockUpEF: 'Đóng hàng rào',
    deleteEF: 'Xóa hàng rào',
    isStartUsing: 'Cho phép bật',
    startUsing: 'Bật',
    stopUsing: 'vô hiệu hóa',
    nowEFrange: 'Phạm vi hàng rào hiện tại',
    enableSucess: 'bật thành công',
    unableSucess: 'tắt thành công',
    sureDeleteMorgage: 'Xóa điểm cố định',
    enterMorgageName: 'Nhập tên điểm cố định',
    openMorgagelongStayAlarm: 'Bắt đầu ở lại điểm cố định',
    openMorgageinOutAlarm: 'Bắt đầu vào và ra điểm cố định',
    setEFSuccess: 'Đặt hàng rào thành công',
    setElectronicFence: 'Thiết lập hàng rào điện tử',
    drawFence: 'Vẽ hàng rào',
    drawMorgagePoint: 'Vẽ điểm cố định',
    customFence: 'Tùy chỉnh hàng rào',
    enterFenceTips: 'Đang báo động',
    leaveFenceTips: 'Để lại báo thức',
    inputFenceName: 'Vui lòng nhập tên hàng rào',
    relation: 'Gói',
    relationDevice: 'Thiết bị liên kết',
    unRelation: 'Không liên kết',
    hadRelation: 'Đã liên kết',
    quickRelation: 'Một - nhấp chuột',
    cancelRelation: 'Hủy gói',
    relationSuccess: 'Gói thành công',
    cancelRelationSuccess: 'Hủy bỏ thành công',
    relationFail: 'Hủy bỏ thất bại',
    deviceList: 'Danh sách thiết bị',
    isDeleteFence: 'Xóa hàng rào',
    choseRelationDeviceFirst: 'Trước tiên hãy chọn thiết bị để được liên kết!',
    choseCancelRelationDeviceFirst: 'Trước tiên hãy chọn thiết bị không liên kết!',
    selectOneTips: 'Vui lòng chọn ít nhất 1 phương thức báo động',
    radius: 'Bán kính',
    //设置二押点页面
    setMortgagePoint: 'Thiết lập điểm thế chấp',

    circleMortage: 'Điểm thế chấp vòng tròn',
    polygonMorgage: 'Điểm thế chấp đa giác',
    morgageSet: 'Đã thiết lập điểm thế chấp',
    operatePrompt: 'Hành động thao tác',
    startDrawing: 'Nhấp để bắt đầu vẽ',
    drawingtip1: 'Click chuột trái để bắt đầu vẽ, nhấp đúp chuột để kết thúc bản vẽ',
    drawingtip2: 'Click chuột trái và kéo để bắt đầu vẽ',

    /************************************************/
    endTrace: 'Kết thúc theo dõi',
    travelMileage: 'Travel Mileage',
    /************************************************/
    myAccount: 'Tài Khoản',
    serviceProvide: 'Nhà cung cấp',
    completeInfo: 'Đầy đủ thông tin, chẳng hạn như danh bạ,số điện thoại',
    clientName: 'Tên khách hàng',
    loginAccount: 'Tài khoản',
    linkMan: 'Liên hệ',
    linkPhone: 'Điện thoại/ đám đông',
    clientNameEmpty: "Tên khách hàng trống!",
    updateSuccess: 'Cập nhật thành công!',
    /************************************************/
    oldPsw: 'Mật khẩu cũ',
    newPsw: 'Mật khẩu mới',
    confirmPsw: 'Xác nhận mật khẩu',
    pswNoSame: 'Mật khẩu không giống nhau',
    pswUpdateSuccess: "Cập nhật mật khẩu thành công!",
    email: 'Email',
    oldPwdWarn: 'Vui lòng nhập mật khẩu cũ',
    newPwdWarn: 'Vui lòng nhập mật khẩu mới',
    pwdConfirmWarn: 'Vui lòng xác nhận mật khẩu mới',
    /************************************************/
    //Custom popup components
    resetPswFailure: 'Đặt lại mật khẩu không thành công',
    notification: 'Thông báo',
    isResetPsw_a: 'Bạn có chắc chắn muốn đặt lại ',
    isResetPsw_b: '`s mật khẩu?',
    pwsResetSuccess_a: 'Đã đặt lại ',
    pwsResetSuccess_b: '`s Mật khẩu để 123456',
    /************************************************/
    machineSearch: 'thông tin thiết bị',
    search: 'Tìm kiếm',
    clientRelation: 'Quan hệ khách hàng',
    machineDetail: 'Chi tiết',
    machineDetail2: 'Chi tiết',
    machineCtrl: 'Hướng dẫn',
    transfer: 'Di chuyển',
    belongCustom: 'Thuộc về khách hàng',
    addImeiFirst: 'Thêm IMEI đầu tiên!',
    addUserFirst: 'thêm người dùng đầu tiên!',
    transferSuccess: 'Di chuyển thành công!',
    multiAdd: 'thêm tất cả',
    multiImport: 'Nhập hàng loạt',
    multiRenew: 'Đổi mới hàng loạt',
    //批量修改设备begin
    editDevice:'Sửa đổi kiểu thiết bị',
    deviceAfter: 'Mô hình thiết bị (sau khi sửa đổi)',
    editDeviceTips:'Vui lòng xác nhận thiết bị được sửa đổi là cùng một kiểu máy và không hoạt động!',
    pleaseChoseDevice: 'Vui lòng chọn thiết bị cần sửa đổi trước!',
    editResult:'Chỉnh sửa kết quả',
    successCount:'Đã sửa đổi thành công thiết bị:',
    failCount:'Thiết bị không thành công:',
    //批量修改设备end
    multiDelete: 'Xóa hàng loạt',
    canNotAddImei: 'IMEI không tồn tại, không thể thêm IMEI',
    importTime: 'Ngày sản xuất',
    loginName: 'tài Khoản',
    platformDue: 'Do nền tảng',
    machinePhone: 'Thẻ sim',
    userDue: 'Do người dùng',
    overSpeedAlarm: 'Cảnh báo quá tốc độ',
    changeIcon: 'Biểu tượng',
    dealerNote: 'Ghi chú',
    noUserDue: 'Không có người dùng nào',
    phoneLengththan3: 'độ dài điện thoại phải lớn hơn 3',
    serialNumberInput: 'Nhập số Seri',
    /************************************************/
    sending: 'Đang gửi.....Vui lòng chờ...',
    sendFailure: 'Send failure',
    ctrlName: 'Tên',
    interval: 'Khoảng thời gian',
    intervalError: 'Interval format error',
    currectInterval: "Vui lòng nhập khoảng thời gian chính xác!",
    intervalLimit: 'khoảng thời gian từ 10-720, (Phút)',
    intervalLimit2: 'Khoảng thời gian từ 10-5400, (thứ hai)',
    intervalLimit3: 'khoảng thời gian từ 5-1440, (Phút)',
    intervalLimit4: 'Khoảng thời gian từ 3-999, (Thứ hai)',
    intervalLimit5: 'Khoảng thời gian từ 10-10800,(Thứ hai)',
    intervalLimit1: 'Khoảng thời gian từ 1-999, (Phút) Đặt khoảng thời gian as000 £ ¬ có nghĩa là đóng chế độ reback thời gian',
    intervalLimit6: 'Khoảng thời gian từ 1-65535,(Thứ hai)',
    intervalLimit7: 'Khoảng thời gian từ 1-999999,(Thứ hai)',
    intervalLimit8: 'Khoảng thời gian từ 0-255, 0 có nghĩa là đóng',
    intervalLimit9: 'Khoảng thời gian từ 3-10800,(Thứ hai)',
    intervalLimit10: 'Khoảng thời gian từ 3-86400, (thứ hai)',
    intervalLimit11: 'Khoảng thời gian từ 180-86400, (thứ hai)',
    intervalLimit22: 'Khoảng thời gian từ 60-86400, (thứ hai)',
    intervalLimit23: 'Khoảng thời gian từ 5-60, (thứ hai)',
    intervalLimit24: 'Khoảng thời gian từ 10-86400, (thứ hai)',
    intervalLimit25: 'khoảng thời gian từ 5-43200, (Phút)',
    intervalLimit12: 'Khoảng thời gian từ 10-60,(Thứ hai)',
    intervalLimit13: 'Đặt khoảng thời gian khoảng 1-24 (có nghĩa là 1-24 giờ) hoặc 101-107 (có nghĩa là 1-7 ngày)',
    intervalLimit14: 'Đặt phạm vi thời gian khoảng 10-3600, đơn vị (giây); mặc định: 10 giây',
    intervalLimit15: 'Đặt phạm vi thời gian khoảng 180-86400, đơn vị (giây); mặc định: 3600 giây',
    intervalLimit16: 'Đặt phạm vi thời gian khoảng 1-72, đơn vị (giờ); mặc định: 24 giờ',
    intervalLimit17:'Cài đặt phạm vi nhiệt độ -127-127, đơn vị (° C)',
    intervalLimit18: 'Đặt phạm vi thời gian khoảng 5-18000, đơn vị (giây)',
    intervalLimit19: 'Đặt phạm vi thời gian khoảng 10-300, đơn vị (giây)',
    intervalLimit20: 'Đặt phạm vi thời gian khoảng 5-399, đơn vị (giây)',
    intervalLimit21: 'Đặt phạm vi thời gian khoảng 5-300, đơn vị (giây)',
    noInterval: 'Vui lòng nhập khoảng thời gian!',
    intervalTips: 'Tắt chế độ theo dõi bằng cách đặt thời gian báo thức',
    phoneMonitorTips: 'Sau khi lệnh được gửi, thiết bị sẽ chủ động quay số gọi lại để theo dõi',
    time1: 'Thời gian1',
    time2: 'Thời gian2',
    time3: 'Thời gian3',
    time4: 'Thời gian4',
    time5: 'Thời gian5',
    intervalNum: 'Khoảng thời gian (phút)',
    sun: 'Mặt trời.',
    mon: 'Thứ hai.',
    tue: 'Thứ ba.',
    wed: 'Thứ tư.',
    thu: 'Thứ năm.',
    fri: 'Thứ sáu.',
    sat: 'Thứ bảy.',
    awakenTime: 'Thời gian thức',
    centerPhone: 'Trung tâm điện thoại',
    inputCenterPhone: 'Đầu vào điện thoại trung tâm!',
    phone1: 'Số 1',
    phone2: 'Số 2',
    phone3: 'Số 3',
    phone4: 'Số 4',
    phone5: 'Số 5',
    inputPhone: 'Đầu vào điện thoại',
    offlineCtrl: 'Offline ctrl £ ¬Các hướng dẫn ngoại tuyến sẽ tự động được gửi đến thiết bị sau khi thiết bị trực tuyến',
    terNotSupport: 'Thiết bị đầu cuối không hỗ trợ',
    terReplyFail: 'Phản hồi đầu cuối không thành công',
    machineInfo: 'Thông tin thiết bị',

    /************************************************/
    alarmTypeScreen: 'Màn hình kiểu báo động',
    allRead: 'Tất cả được đọc',
    read: 'đọc',
    noAlarmInfo: 'Không có thông tin báo động đáng tin cậy',
    alarmTip: 'Mẹo: Lọc loại thông tin cảnh báo này bằng cách bỏ chọn',

    /************************************************/
    updatePsw: 'Mật khẩu',
    resetPsw: 'Đặt lại mật khẩu',

    /************************************************/
    multiSell: 'Bán hàng loạt',
    sell: 'Gửi',
    sellSuccess: 'Bán thành công!',
    modifySuccess: 'Sửa đổi thành công',
    modifyFail: 'Sửa đổi thất bại',
    multiTransfer: 'Di chuyển hàng loạt',
    multiUserExpires: 'Sửa đổi thông tin người dùng sẽ hết hạn',
    batchModifying: 'Số lượng lớn thay đổi',
    userTransfer: 'Chuyển khoản',
    machineRemark: 'Ghi chú',
    sendCtrl: 'Gửi lệnh',
    ctrl: "Lệnh",
    ctrlLog: 'Nhật ký ctrl',
    ctrlLogTips: 'Lịch sử lệnh',
    s06Ctrls: ['Dừng động cơ ', ' khôi phục động cơ ', ' vị trí truy vấn'],
    ctrlType: 'Tên lệnh',
    resInfo: 'kết quả',
    resTime: 'Thời gian đáp ứng',
    ctrlSendTime: 'Gửi thời gian',
    // csv文件导入上传
    choseCsv: 'Chọn tệp CSV',
    choseFile: 'Chọn tập tin',
    submit: 'Gửi',
    targeDevice: 'Thiết bị chính',
    csvTips_1: '1. Lưu tệp excel dưới định dạng CSV',
    csvTips_2: '2.Nhập tệp CSV vào hệ thống',
    importExplain: 'Nhập hướng dẫn:',
    fileDemo: 'Ví dụ về định dạng tệp',

    // add
    sendType: 'Loại',
    onlineCtrl: 'Lệnh trực tuyến',
    offCtrl: 'Lệnh ngoại tuyến',
    resStatus: ['Không được gửi', 'Không hợp lệ', 'đã được ban hành', 'Thực thi thành công', 'Thực thi không thành công', 'Không có câu trả lời'],
    /************************************************/
    addSubordinateClient: 'Thêm tài khoản phụ',
    noSubordinateClient: 'Không có tài khoản phụ',
    superiorCustomerEmpty: 'Chọn cấp trên',
    noCustomerName: "Tên khách hàng",
    noLoginAccount: 'Người dùng',
    noPsw: 'Mật khẩu',
    noConfirmPsw: 'Xác nhận mật khẩu',
    pswNotAtypism: 'Mật khẩu không chính xác!',
    addSuccess: 'Thêm thành công',
    superiorCustomer: 'Cấp trên',
    addVerticalImei: 'Thêm IMEI dọc',
    noImei: 'Không có IMEI',
    addImei_curr: 'Nhập số IMEI',
    no: 'Đơn vị',
    aRowAImei: 'Một IMEI cho một dòng',

    /*
    * dealer  Beginning of interface translation 
    *
    * */
    //main.js
    imeiOrUserEmpty: "IMEI hoặc không có người dùng!",
    accountEmpty: 'IMEI/Tên/Tài khoản bắt buộc',
    queryNoUser: "Truy vấn NoUser",
    queryNoIMEI: "Truy vấn không có IMEI",
    imeiOrClientOrAccount: 'IMEI/tên/tài khoản',
    dueSoon: 'Hết hạn',
    recentlyOffline: 'Ngoại tuyến',
    choseSellDeviceFirst: 'Vui lòng chọn thiết bị sẽ được bán trước!',
    choseDeviceFirst: 'Vui lòng chọn thiết bị để di chuyển trước!',
    choseDeviceExpiresFirst: 'Vui lòng chọn thiết bị cần sửa đổi trước!',
    choseRenewDeviceFirst: 'Vui lòng chọn thiết bị để gia hạn trước!',
    choseDeleteDeviceFirst: 'Vui lòng chọn thiết bị để xóa trước tiên!',
    choseClientFirst: 'Vui lòng chọn khách hàng để di chuyển trước!',

    //myClient.js
    clientList: 'Khách hàng',
    accountInfo: 'Thông tin tài khoản',
    machineCount: 'Số lượng thiết bị',
    stock: 'Toàn bộ',
    inventory: 'Chứng khoán',
    subordinateClient: 'Tài khoản phụ',
    datum: 'Thông tin',
    monitor: 'Màn hình',
    dueMachineInfo: 'Hết hạn',
    haveExpired: 'Đã hết hạn',
    timeRange: ['Trong vòng 7 ngày', 'Trong vòng 30 ngày', 'Trong vòng 60 ngày', 'Trong vòng 7-30 ngày', 'Trong vòng 30-60 ngày'],
    offlineMachineInfo: 'Thông tin thiết bị ngoại tuyến',
    timeRange1: ['Trong vòng một giờ', 'Trong vòng một ngày', 'Trong vòng 7 ngày', 'Trong vòng một tháng', 'Trong vòng 60 ngày', 'Trên 60 ngày', 'Từ 1 giờ đến 7 ngày', 'Từ 1 ngày đến 7 ngày', 'Từ 7 đến 30 ngày', 'Từ 30 ngày đến 60 ngày'],
    offlineTime: 'Phạm vi',
    includeSubordinateClient: 'Tài khoản phụ',

    stopMachineInfo: 'Thông tin tĩnh',
    stopTime1: 'Thời gian tĩnh',
    unUseMachineInfo: 'Thông tin không hoạt động',
    unUseMachineCount: 'Số tiền không hoạt động',

    sellTime: 'Bán thời gian',
    detail: 'Chi tiết',
    manageDevice: 'Quản lý thiết bị',
    details: 'Chi tiết',
    deleteSuccess: 'Xóa thành công!',
    deleteFail: 'Xóa thất bại!',
    renewalLink: 'Gia hạn liên kết',
    deleteGroupTips: "Có nên xóa một nhóm",
    addGroup: 'Thêm nhóm',
    jurisdictionRange: 'Sửa đổi chức năng',
    machineSellTransfer: 'Bán máy',
    monitorMachineGroup: 'Màn hình',
    jurisdictionArr: ['Quản lý khách hàng', 'Quản lý thư', 'Bộ GEO', 'Quản lý báo thức', 'Quản lý tài khoản ảo', 'Hướng dẫn công văn'],
    confrimDelSim: 'Xác nhận xóa thẻ SIM:',

    // 右键菜单
    sellDevice: 'Bán thiết bị',
    addClient: 'Thêm người dùng',
    deleteClient: 'Xóa người dùng',
    resetPassword: 'Đặt lại mật khẩu',
    transferClient: 'Di chuyển người dùng',
    ifDeleteClient: 'Xóa',

    //myAccount
    myWorkPlace: 'Tài khoản của tôi',
    availablePoints: 'Cân đối',
    yearCard: 'Thẻ hàng năm',
    lifetimeOfCard: 'Thẻ suốt đời',
    oneyear: 'Hàng năm',
    lifetime: 'Trọn đời',
    commonImportPoint: 'Thẻ mới',
    lifetimeImportPoint: 'Thẻ mới suốt đời',
    myServiceProvide: 'Nhà cung cấp',
    moreOperator: 'Hoạt động nhiều hơn',
    dueMachine: 'Thiết bị đã hết hạn',
    offlineMachine: 'Thiết bị ngoại tuyến',
    quickSell: 'Bán hàng loạt',
    sellTo: 'Mục tiêu',
    machineBelong: 'Thuộc về',
    reset: 'Cài lại',
    targetCustomer: 'Mục tiêu khách hàng',
    common_lifetimeImport: 'Thẻ hàng năm (0), thẻ suốt đời (0)',
    cardType1: 'Kiểu',
    credit: 'Số lượng',
    generateImportPoint: 'Tạo thẻ mới',
    generateImportPointSuc: 'Nhập thẻ thành công!',
    generateImportPointFail: 'Lỗi nhập thẻ!',
    year_lifeTimeCard: 'Thẻ mới (0), Thẻ mới cho suốt đời (0)',
    generateRenewPoint: 'Tạo thẻ gia hạn',
    transferTo: 'Di chuyển',
    transferPoint: 'Số lượng',
    transferRenewPoint: 'Chuyển thẻ gia hạn',
    pointHistoryRecord: 'Lịch sử thẻ',
    newGeneration: 'Mới',
    operatorType: 'Hoạt động',
    consume: 'Tiêu thụ',
    give: 'Cho',
    income: 'Thu nhập',
    pay: 'Chi phí',
    imeiErr: 'Xin vui lòng nhập 6 số IMEI cuối cùng!',
    accountFirstPage: 'Nhà',


    /*
    * dealer  End of interface translation
    *
    * */
    // 1.4.8 risk control
    finrisk: 'kiểm soát rủi ro tài chính',
    attention: 'Đăng ký',
    cancelattention: 'Xóa đăng ký',
    poweroff: 'Tắt nguồn',
    inout: 'Trong và ngoài',
    inoutEF: 'Vào và ra khỏi hàng rào',
    longstay: 'Lưu trú thế chấp',
    secsetting: 'Cài đặt điểm thế chấp ',
    EFsetting: 'Cài đặt hàng rào địa lý',
    polygonFence: 'Hàng rào đa giác',
    cycleFence: 'Chu kỳ hàng rào',
    haveBeenSetFence: 'Đã được đặt hàng rào',
    haveBeenSetPoint: 'Đã được thiết lập điểm thế chấp',
    drawingFailed: 'Bản vẽ không thành công. Vui lòng vẽ lại!',
    inoutdot: 'Trong và ngoài',
    eleStatistics: 'Thống kê điện',
    noData: 'Không có dữ liệu',
    // 进出二押点表格
    accountbe: 'Tài khoản',
    SMtype: 'Kiểu điểm thế chấp',
    SMname: 'Tên điểm thế chấp',
    time: 'Thời gian',
    position: 'Chức vụ',
    lastele: 'Pin còn lại',
    statisticTime: 'Ngày thống kê',
    searchalarmType: ['Tất cả', 'Ngoại tuyến', 'Tắt nguồn', 'Trong và ngoài hàng rào', 'Trong và ngoài', 'Lưu trú thế chấp'],
    remarks: ['Điểm thế chấp', 'Công ty bảo lãnh', 'Điểm tháo rời', 'Thị trường cũ'],
    focusOnly: 'Chỉ tập trung',

    autoRecord: 'Ghi âm tự động',
    /******************************************************set command start**********************************8*/
    setCtrl: {
        text: 'Đặt lệnh',
        value: ''
    },
    moreCtrl: {
        text: 'Thêm hướng dẫn',
        value: ''
    },
    sc_openTraceModel: {
        text: 'Đặt mô hình theo dõi',
        value: '0'
    },
    sc_closeTraceModel: {
        text: 'Đóng mô hình theo dõi',
        value: '1'
    },
    sc_setSleepTime: {
        text: 'Đặt thời gian ngủ',
        value: '2'
    },
    sc_setAwakenTime: {
        text: 'Đặt thời gian thức tỉnh',
        value: '3'
    },
    sc_setDismantleAlarm: {
        text: 'Đặt báo thức tháo dỡ',
        value: '4'
    },
    sc_setSMSC: {
        text: 'Số trung tâm',
        value: '5'
    },
    sc_delSMSC: {
        text: 'Xóa SMSC',
        value: '6'
    },
    sc_setSOS: {
        text: 'Thêm SOS',
        value: '7'
    },
    sc_delSOS: {
        text: 'Xóa SOS',
        value: '8'
    },
    sc_restartTheInstruction: {
        text: 'Cài đặt lại',
        value: '9'
    },
    sc_uploadTime: {
        text: 'Đặt khoảng thời gian tải lên',
        value: '10'
    },
    /*Alarm clock time setting
     Timing reback time setting
     Dismantle alarm setting
     Week mode open close*/
    sc_setAlarmClock: {
        text: 'Đặt đồng hồ báo thức',
        value: '11'
    },
    sc_setTimingRebackTime: {
        text: 'Đặt thời gian reback',
        value: '12'
    },
    sc_openWeekMode: {
        text: 'Chế độ mở tuần',
        value: '13'
    },
    sc_closeWeekMode: {
        text: 'Đóng chế độ tuần',
        value: '14'
    },

    sc_powerSaverMode: {
        text: 'Khoảng thời gian tải lên',
        value: '15'
    },
    sc_carCatchingMode: {
        text: 'Chế độ theo dõi',
        value: '16'
    },
    sc_closeDismantlingAlarm: {
        text: 'Đóng báo động tháo dỡ',
        value: '17'
    },
    sc_openDismantlingAlarm: {
        text: 'Mở báo động tháo dỡ',
        value: '18'
    },
    sc_VibrationAlarm: {
        text: 'Đặt báo động rung',
        value: '19'
    },
    sc_timeZone: {
        text: 'Cài đặt múi giờ',
        value: '20'
    },
    sc_phoneMonitor: {
        text: 'Nghe điện thoại',
        value: '21'
    },
    sc_stopCarSetting: {
        text: 'Cài đặt đỗ xe',
        value: '22'
    },
    sc_bindAlarmNumber: {
        text: 'Liên kết số báo động',
        value: '23'
    },
    sc_bindPowerAlarm: {
        text: 'Báo động mất điện',
        value: '24'
    },
    sc_fatigueDrivingSetting: {
        text: 'Thiết lập lái xe mệt mỏi',
        value: '25'
    },
    sc_peripheralSetting: {
        text: 'Cài đặt ngoại vi',
        value: '26'
    },
    sc_SMSAlarmSetting: {
        text: 'Đặt thông báo SMS',
        value: '27'
    },
    sc_autoRecordSetting: {
        text: 'Cài đặt ghi âm tự động',
        value: '28'
    },
    sc_monitorCallback: {
        text: 'Nghe gọi lại',
        value: '29'
    },
    sc_recordCtrl: {
        text: 'Hướng dẫn ghi âm',
        value: '30'
    },
    sc_unbindAlarmNumber: {
        text: 'Hủy liên kết số báo động',
        value: '31'
    },
    sc_alarmSensitivitySetting: {
        text: 'Cài đặt độ nhạy báo động rung',
        value: '32'
    },
    sc_alarmSMSsettings: {
        text: 'Rung báo cài đặt SMS',
        value: '33'
    },
    sc_alarmCallSettings: {
        text: 'Cài đặt cuộc gọi báo thức rung',
        value: '34'
    },
    sc_openFailureAlarmSetting: {
        text: 'Tắt báo động mất điện',
        value: '35'
    },
    sc_restoreFactory: {
        text: 'Khôi phục nhà máy',
        value: '36'
    },
    sc_openVibrationAlarm: {
        text: 'Bật báo thức rung',
        value: '37'
    },
    sc_closeVibrationAlarm: {
        text: 'Tắt báo thức rung',
        value: '38'
    },
    sc_closeFailureAlarmSetting: {
        text: 'Tắt báo động mất điện',
        value: '39'
    },
    sc_feulAlarm: {
        text: 'Đặt báo thức lượng dầu',
        value: '40'
    },
    //1.6.72
    sc_PowerSavingMode: {
        text: 'Chế độ tiết kiệm năng lượng',
        value: '41'
    },
    sc_sleepMode: {
        text: 'Chế độ ngủ',
        value: '42'
    },
    sc_alarmMode: {
        text: 'Chế độ báo thức',
        value: '43'
    },
    sc_weekMode: {
        text: 'Chế độ tuần',
        value: '44'
    },
    sc_monitorNumberSetting: {
        text: 'Cài đặt số màn hình',
        value: '45'
    },
    sc_singlePositionSetting: {
        text: 'Đơn vị trí',
        value: '46'
    },
    sc_timingworkSetting: {
        text: 'Thời gian hoạt động',
        value: '47'
    },
    sc_openLightAlarm: {
        text: 'Báo động cảm biến mở',
        value: '48'
    },
    sc_closeLightAlarm: {
        text: 'Đóng đèn báo động cảm biến',
        value: '49'
    },
    sc_workModeSetting: {
        text: 'Cài đặt chế độ làm việc',
        value: '50'
    },
    sc_timingOnAndOffMachine: {
        text: 'Cài đặt chuyển đổi thời gian',
        value: '51'
    },
    sc_setRealTimeTrackMode: {
        text: 'Đặt chế độ đuổi theo thời gian thực',
        value: '52'
    },
    sc_setClockMode: {
        text: 'Đặt chế độ báo thức',
        value: '53'
    },
    sc_openTemperatureAlarm:{
        text:'Bật báo động nhiệt độ',
        value:'54'
    },
    sc_closeTemperatureAlarm:{
        text:'Tắt báo động nhiệt độ',
        value:'55'
    },
    sc_timingPostbackSetting: {
        text: 'Cài đặt thời gian gửi lại',
        value: '56'
    },
    sc_remoteBoot: {
        text: 'Khởi động từ xa',
        value: '57'
    },
    sc_smartTrack: {
        text: 'Theo dõi thông minh',
        value: '58'
    },
    sc_cancelSmartTrack: {
        text: 'Hủy theo dõi thông minh',
        value: '59'
    },
    sc_cancelAlarm: {
        text: 'Hủy báo thức',
        value: '60'
    },
    sc_smartPowerSavingMode: {
        text: 'Đặt chế độ tiết kiệm năng lượng thông minh',
        value: '61'
    },
    sc_monitorSetting: {
        text: "Giám sát",
        value: '62'
    },
     // 指令重构新增翻译
    sc_timedReturnMode: {
        text: 'Chế độ trả lại theo thời gian',
        value: '100'
    },
    sc_operatingMode: {
        text: 'Chế độ hoạt động',
        value: '101'
    },
    sc_realTimeMode : {
        text: 'Chế độ định vị thời gian thực',
        value: '102'
    },
    sc_alarmMode : {
        text: 'Chế độ báo thức',
        value: '103'
    },
    sc_weekMode : {
        text: 'Chế độ tuần',
        value: '104'
    },
    sc_antidemolitionAlarm : {
        text: 'Báo động chống phá dỡ',
        value: '105'
    },
    sc_vibrationAlarm : {
        text: 'Báo động rung',
        value: '106'
    },
    sc_monitoringNumber : {
        text: 'Quản lý số giám sát',
        value: '107'
    },
    sc_queryMonitoring : {
        text: 'Số giám sát truy vấn',
        value: '108'
    },
    sc_electricityControl : {
        text: 'Kiểm soát dầu và điện',
        value: '109'
    },
    sc_SOSnumber : {
        text: 'Quản lý số SOS',
        value: '110'
    },
    sc_SleepCommand : {
        text: 'Lệnh ngủ',
        value: '201'
    },
    sc_RadiusCommand : {
        text: 'Bán kính dịch chuyển',
        value: '202'
    },
    sc_punchTimeMode:{
        text:'打卡模式',
        value:'203'  
    },
    sc_intervelMode:{
        text:'时间段模式',
        value:'204'  
    },
    sc_activeGPS:{
        text:'激活GPS',
        value:'205'  
    },
    sc_lowPowerAlert: {
        text: 'Nhắc nhở pin yếu',
        value: '206'
    },
    sc_SOSAlert: {
        text: 'SOS报警',
        value: '207'
    },
    mc_cuscom : {
        text: 'Hướng dẫn tùy chỉnh',
        value: '1'
    },
    NormalTrack: 'Chế độ theo dõi bình thường',
    listeningToNumber:'Bạn có chắc chắn muốn kiểm tra số giám sát của không?',
    versionNumber:'Bạn có chắc chắn muốn kiểm tra số phiên bản của không?',
    longitudeAndLatitudeInformation:'Bạn có chắc chắn muốn kiểm tra thông tin kinh độ và vĩ độ của không?',
    equipmentStatus:'Bạn có chắc chắn muốn kiểm tra trạng thái của không?',
    public_parameter:'Bạn có chắc chắn muốn kiểm tra các thông số của XXX không?',
    GPRS_parameter:'Bạn có chắc chắn muốn kiểm tra các thông số GPRS của không?',
    deviceName: 'Bạn có chắc chắn muốn cuộn thiết bị không?',
    SMS_alert:'Bạn có chắc chắn muốn kiểm tra báo thức nhắc nhở qua SMS của không?',
    theBindingNumber:'Bạn có chắc chắn muốn kiểm tra số ràng buộc của không?',
    intervalTimeRange:'Khoảng thời gian cài đặt là 001-999, đơn vị (phút)',
    pleaseChoose:'Hãy chọn',
    RealTimeCarChase: ' Bạn có chắc chắn muốn đặt thiết bị này ở chế độ rượt đuổi ô tô trong thời gian thực không?',
    inputPhoneNumber: "Vui lòng nhập số điện thoại",
    inputCorPhoneNumber: "Vui lòng nhập số điện thoại chính xác",
    autoCallPhone: "Mẹo: Sau khi lệnh được thực hiện thành công, thiết bị đầu cuối sẽ tự động quay số đã đặt",
    limitTheNumberOfCellPhoneNumbers1:'Lệnh này hỗ trợ tối đa 5 số điện thoại di động',
    limitTheNumberOfCellPhoneNumbers2:'Lệnh này hỗ trợ tối đa 3 số điện thoại di động',
    equipmentTorestart:'Bạn có chắc chắn muốn khởi động lại thiết bị này không',
    remindTheWay:'Cách nhắc nhở',
    alarmWakeUpTime:'Thời gian đánh thức báo thức',
    alarmWakeUpTime1:'Thời gian đánh thức báo thức 1',
    alarmWakeUpTime2:'Thời gian đánh thức báo thức 2',
    alarmWakeUpTime3:'Thời gian đánh thức báo thức 3',
    alarmWakeUpTime4:'Thời gian đánh thức báo thức 4',
    sensitivityLevel:'Vui lòng chọn mức độ nhạy',
    parking_time:'Thời gian đậu xe',
    selectWorkingMode:'Vui lòng chọn chế độ làm việc',
    Alarm_value:'Giá trị báo động',
    Buffer_value:'Giá trị bộ đệm',
    gqg_disconnect:'ngắt kết nối',
    gqg_turnOn:'Bật',
    Return_interval:'Khoảng thời gian trở lại',
    gq_startTime:'Thời gian bắt đầu',
    gq_restingTime:'Thời gian nghỉ ngơi',
    gq_Eastern:'Múi giờ miền Đông',
    gq_Western:'Múi giờ phương Tây',

    gq_driver:'Báo động khi lái xe mệt mỏi',
    gq_deviceName:'Bạn có chắc chắn muốn cuộn thiết bị này không?',
    gq_noteAlarm:'Bạn có chắc chắn muốn kiểm tra báo thức nhắc nhở qua SMS không?',
    gq_restoreOriginal:'Bạn có chắc chắn muốn khôi phục thiết bị này về trạng thái ban đầu không?',
    gq_normalMode:'Chế độ bình thường',
    gq_IntelligentsleepMode:'Chế độ ngủ thông minh',
    gq_DeepsleepMode:'Chế độ ngủ sâu',
    gq_RemotebootMode:'Chế độ khởi động từ xa',
    gq_IntelligentsleepModeTips:'Bạn có chắc chắn muốn đặt ở chế độ ngủ thông minh không',
    gq_DeepsleepModeTips:'Bạn có chắc chắn muốn đặt ở chế độ ngủ sâu không',
    gq_RemotebootModeTips:'Bạn có chắc chắn muốn đặt ở chế độ khởi động từ xa không',
    gq_normalModeTips:'Bạn có chắc chắn muốn đặt ở chế độ bình thường',
    gq_sleepModeTips:'Bạn có chắc chắn muốn đặt thiết bị này ở chế độ ngủ không?',
    gq_Locatethereturnmode:'Chế độ quay lại định v',
    gq_regularWorkingHours:'Thời gian làm việc định giờ',
    gq_AlarmType:{
        text: 'Loại báo động',
        value: '111'
    },   
    IssuedbyThePrompt:'Lệnh đã phát ra, vui lòng đợi thiết bị phản hồi',
    platformToinform:'Thông báo nền tảng',
    gq_shortNote:'Thông báo SMS', 
    /************指令白话文**********************/
    closeDismantlingAlarm: 'Đóng báo động tháo dỡ',
    openDismantlingAlarm: 'Mở báo động tháo dỡ',
    closeTimingRebackMode: 'Đóng thời gian reback chế độ',
    minute: 'Phút',
    timingrebackModeSetting: 'Khoảng thời gian tải lên:',
    setWakeupTime: 'Đặt thời gian đánh thức:',
    weekModeSetting: 'Cài đặt chế độ tuần:',
    closeWeekMode: 'Đóng chế độ tuần',
    setRealtimeTrackMode: 'Đặt chế độ theo dõi thời gian thực',
    fortification: 'Pháo đài',
    disarming: 'Giải Giáp',
    settimingrebackmodeinterval: 'Đặt khoảng thời gian reback chế độ:',
    oilCutCommand: 'Lệnh cắt dầu',
    restoreOilCommand: 'Khôi phục lại lệnh dầu',
    turnNnTheVehiclesPower: 'Bật nguồn xe',
    turnOffTehVehiclesPower: 'Tắt nguồn xe',
    implementBrakes: 'Triển khai phanh',
    dissolveBrakes: 'Giải thể phanh',
    openVoiceMonitorSlarm: 'Mở báo động màn hình bằng giọng nói',
    closeVoiceMonitorAlarm: 'Đóng báo động màn hình bằng giọng nói',
    openCarSearchingMode: 'Mở chế độ tìm kiếm xe',
    closeCarSearchingMode: 'Đóng chế độ tìm kiếm xe',
    unrecognizedCommand: 'Không nhận dạng được lệnh',

    /********************************************设置指令结束**************************************************/


    /********************************************查询指令开始**************************************************/
    queryCtrl: {
        text: 'Lệnh truy vấn',
        value: ''

    },
    /*参数设置查询*/
    qc_softwareVersion: {

        text: 'Phien bản phần mềm truy vấn',
        value: '1'
    },
    qc_latlngInfo: {
        text: 'Truy vấn vĩ độ và kinh độ',
        value: '2'
    },
    qc_locationHref: {
        text: 'Cấu hình tham số truy vấn',
        value: '3'
    },
    qc_status: {
        text: 'Trạng thái truy vấn',
        value: '4'
    },
    qc_gprs_param: {
        text: 'Truy vấn GPRS param',
        value: '5'
    },
    qc_name_param: {
        text: 'tên',
        value: '6'
    },
    qc_SMSReminderAlarm_param: {
        text: 'Truy vấn SMS nhắc nhở báo động',
        value: '7'
    },
    qc_bindNumber_param: {
        text: 'Truy vấn số ràng buộc',
        value: '8'
    },

    /********************************************查询指令结束**************************************************/

    /*******************************************控制指令开始***************************************************/

    controlCtrl: {
        text: 'Lệnh điều khiển',
        value: ''
    },
    cc_offOilElectric: {
        text: 'Tắt dầu điện',
        value: '1'
    },
    cc_recoveryOilElectricity: {
        text: 'Phục hồi điện dầu',
        value: '2'
    },
    cc_factorySettings: {
        text: 'Thiết lập nhà máy',
        value: '4'
    },
    cc_fortify: {
        text: 'Tăng cường',
        value: '75'
    },
    cc_disarming: {
        text: 'Giải giáp',
        value: '76'
    },
    cc_brokenOil: {
        text: 'Hướng dẫn cắt dầu',
        value: '7'
    },
    cc_RecoveryOil: {
        text: 'Phục hồi mạch dầu',
        value: '8'
    },

    /*******************************************控制指令结束***************************************************/


    /*
* m--》min
* 2018-01-23
* */
    km: 'KM', mileage: 'Mileage', importMachine: 'Thêm IMEI mới', transferImportPoint: 'Di chuyển thẻ mới',
    machineType1: 'Mô hình', confirmIMEI: 'Hãy đảm bảo tính hợp lệ của số IMEI và Tên mẫu trước khi nhập, vì thao tác không thể thu hồi.',
    renew: 'Thay mới', deductPointNum: 'Số lượng', renewSuccess: 'Gia hạn thành công!',
    wireType: [
        {
            text: 'Có dây',
            value: false
        },
        {
            text: 'Không dây',
            value: true
        }
    ],
    vibrationWays: [
        {
            text: 'Nền tảng',
            value: 0
        },
        {
            text: 'Nền tảng+Tin nhắn',
            value: 1
        },
        {
            text: 'Nền tảng+Tin nhắn+Điện thoại',
            value: 2
        }
    ],
    addMachineType: [{
        text: 'S06',
        value: '3'
    },
    // SO6子级-----start-----------
    {
        text: 'GT06',
        value: '8'
    }, {
        text: 'S08V',
        value: '9'
    }, {
        text: 'S01',
        value: '10'
    }, {
        text: 'S01T',
        value: '11'
    }, {
        text: 'S116',
        value: '12'
    }, {
        text: 'S119',
        value: '13'
    }, {
        text: 'TR06',
        value: '14'
    }, {
        text: 'GT06N',
        value: '15'
    }, {
        text: 'S101',
        value: '16'
    }, {
        text: 'S101T',
        value: '17'
    }, {
        text: 'S06U',
        value: '18'
    }, {
        text: 'S112U',
        value: '19'
    }, {
        text: 'S112B',
        value: '20'
    }
        // SO6子级-----end-----------
        , {
        text: 'S15/S02F',
        value: '1'
    }, {
        text: 'S05',
        value: '2'
    }, {
        text: 'SW06',
        value: '4'
    }, {
        text: 'S001',
        value: '5'
    }, {
        text: 'S08',
        value: '6'
    }, {
        text: 'S09',
        value: '7'
    }],

    /*
   * 2018-02-02
   * */
    maploadfail: " Xin lỗi, Tải bản đồ hiện tại không thành công, Bạn có muốn chuyển sang bản đồ khác không?",

    /*
    2018-03-06新增 控制指令
    * */
    cc_openPower: {
        text: 'Mở xe điện',
        value: '7'
    },
    cc_closePower: {
        text: 'Đóng xe điện',
        value: '8'
    },
    cc_openBrake: {
        text: 'Mở phanh',
        value: '9'
    },
    cc_closeBrake: {
        text: 'Đóng phanh',
        value: '10'
    },
    cc_openAlmrmvoice: {
        text: 'Mở báo động',
        value: '11'
    },
    cc_closeAlmrmvoice: {
        text: 'Đóng báo động',
        value: '12'
    },
    /*2018-03-06新增 控制指令
    * */
    cc_openFindCar: {
        text: 'Mở tìm kiếm xe',
        value: '13'
    },
    cc_closeFindCar: {
        text: 'Đóng tìm kiếm xe',
        value: '14'
    },
    /*2018-03-19
    * */
    EF: 'Địa chất',

    /*
    2018-03-29，扩展字段
    * */
    exData: ['Điện', 'Điện áp', 'Nhiên liệu', 'Nhiệt độ', 'Sức cản'],

    /*
    2018-04-10
    * */
    notSta: 'LBS không có trong thống kê',

    // 油量统计
    fuelSetting: 'Nhiên liệu đặt',
    mianFuelTank: 'Thùng nhiên liệu chính',
    auxiliaryTank: 'Bình xăng thứ cấp',
    maximum: 'Giá trị tối đa',
    minimum: 'Giá trị tối thiểu',
    FullTankFuel: 'Cả thùng chứa nhiên liệu',
    fuelMinValue: 'Thể tích nhiên liệu không được nhỏ hơn 10L',
    standardSetting: 'Thiết lập tiêu chuẩn',
    emptyBoxMax: 'Bồn chứa đầy đủ giá trị tối đa',
    fullBoxMax: 'Giá trị lớn nhất',
    fuelStatistics: 'Nhiên liệu thống kê',
    settingSuccess: 'Thành công',
    settingFail: 'Thất bại',
    pleaseInput: 'Hãy nhập vào',
    fuelTimes: 'Kỷ nguyên nhiên liệu',
    fuelTotal: 'Tổng lượng nhiên liệu',
    refuelingTime: 'Thời gian tiếp nhiên liệu',
    fuelDate: 'Nhiên liệu ngày',
    fuel: 'Nhiên liệu',
    fuelChange: 'Nhiên liệu đã thay đổi ',
    feulTable: 'Bảng phân tích nhiên liệu',
    addFullFilter: 'Vui lòng thêm bộ lọc đầy đủ',
    enterIntNum: 'Vui lòng nhập một số nguyên dương',
    // 温度统计
    tempSta: 'Thống kê nhiệt độ',
    tempTable: 'Bảng phân tích nhiệt độ',
    industrySta: 'Thống kê ngành',
    temperature: 'Nhiệt độ',
    temperature1: 'Nhiệt độ1',
    temperature2: 'Nhiệt độ2',
    temperature3: 'Nhiệt độ3',
    tempRange: 'Phạm vi nhiệt độ',
    tempSetting:'Cài đặt nhiệt độ',
    tempSensor:'Cảm biến nhiệt độ',
    tempAlert:'Sau khi tắt cảm biến nhiệt độ, nó sẽ không nhận được dữ liệu nhiệt độ!',
    phoneNumber: 'Số điện thoại di động',
    sosAlarm: 'Báo động SOS',
    undervoltageAlarm: 'Báo động điện áp thấp',
    overvoltageAlarm: 'Báo động quá áp',
    OilChangeAlarm: 'Báo động thay dầu',
    accDetection: 'Phát hiện ACC',
    PositiveAndNegativeDetection: 'Phát hiện tích cực và tiêu cực',
    alermValue: 'Giá trị báo động',
    bufferValue: 'Giá trị đệm',
    timeZoneDifference: 'Chênh lệch múi giờ',
    meridianEast: 'Kinh tuyến Đông',
    meridianWest: 'Kinh tuyến tây',
    max12hour: 'Không thể hơn 12 giờ',
    trackDownload: 'theo dõi Tải xuống',
    download: 'Tải về',
    multiReset: 'Đặt lại hàng loạt',
    resetSuccess: 'thiết lập lại thành công',
    multiResetTips: 'Mẹo đặt lại hàng loạt',
    point: 'điểm',
    myplace: 'chỗ của tôi',
    addPoint: 'thêm điểm',
    error10018: 'Số dư không đủ',
    error110:'Đối tượng không tồn tại',
    error109:'Đã vượt quá giới hạn tối đa',
    error20013:'Loại thiết bị không tồn tại',
    error90001:'Số sê-ri của loại thiết bị không được để trống',
    error20003:'Imei không được để trống',
    inputName: 'Tên đầu vào',
    virtualAccount: 'tài khoản ảo',
    createTime: 'tạo thời gian',
    permission: 'sự cho phép',
    permissionRange: 'phạm vi cho phép',
    canChange: 'Chức năng thay đổi',
    fotbidPassword: 'Sửa lại mật khẩu',
    virtualAccountTipsText: 'Khi tạo tài khoản ảo, đó là tài khoản bí danh của tài khoản đại lý hiện đã đăng ký và có thể đặt quyền cho tài khoản ảo.',
    noOperationPermission: 'không cho phép',
    number: 'con số',
    rangeSetting: 'thiết lập phạm vi',
    setting: 'cài đặt',
    // 1.6.1
    duration: 'Thời gian',
    voltageSta: 'Thống kê điện áp',
    voltageAnalysis: 'Phân tích điện áp',
    voltageEchart: 'Phân tích điện áp đồng hồ',
    platformAlarm: 'Nền tảng báo động',
    platformAlarm1: 'điện thoại',
    platformAndPhone: 'điện thoại+Nền tảng báo động',
    smsAndplatformAlarm: 'SMS+Nền tảng báo động',
    smsAndplatformAlarm1: 'SMS',
    smsAndplatformAlarmandPhone: 'Nền tảng báo động +SMS+ điện thoại',
    smsAndplatformAlarmandPhone1: 'SMS+ điện thoại',
    more_speed: 'Km',
    attribute: 'Thuộc tính',
    profession: 'Chuyên nghiệp',
    locationPoint: 'Điểm neo',
    openPlatform: 'Nền tảng mở',
    experience: 'bản giới thiệu',
    onlyViewMonitor: 'Chỉ xem Màn hình',
    inputAccountOrUserName: 'Vui lòng nhập số tài khoản hoặc tên người dùng',
    noDeviceTips: 'Không tìm thấy thông tin thiết bị liên quan, kiểm tra khách hàng',
    noUserTips: 'Không tìm thấy thông tin người dùng liên quan, kiểm tra thiết bị',
    clickHere: 'Nhấn vào đây',
    pointIntervalSelect: 'Theo dõi khoảng thời gian',
    payment: 'Thanh toán',
    pleaceClick: 'Nhấp vào',
    paymentSaveTips: 'Mẹo an toàn: Vui lòng xác nhận với nhà cung cấp dịch vụ về tính hợp lệ của liên kết',
    fuelAlarmValue: 'Giá trị báo động lượng dầu',
    fuelConsumption: 'Tiêu thụ nhiên liệu',
    client: 'Khách hàng',
    create: 'Tạo nên',
    importPoint: 'Thẻ mới',
    general: 'Thường gặp',
    lifelong: 'Trọn đời',
    renewalCard: 'Thẻ gia hạn',
    settingFuelFirst: 'Đầu tiên đặt giá trị cảnh báo lượng dầu trước khi gửi lệnh!',
    overSpeedSetting: 'Cài đặt tốc độ',
    kmPerHour: 'km/h',
    times: 'Thời đại',
    total: 'Tổng cộng',
    primary: 'Chúa',
    minor: 'Phó',
    unActiveTips: 'Thiết bị không được kích hoạt và không thể được sử dụng.',
    arrearsTips: 'Thiết bị đang bị truy thu và không thể sử dụng tính năng này',
    loading: 'Tải...',
    expirationReminder: 'Nhắc trước khi ra',
    projectName: 'Tên dự án',
    expireDate: 'Ngày tháng',
    changePwdTips: 'Mật khẩu của bạn quá đơn giản, tồn tại rủi ro an toàn, xin hãy ngay lập tức thay đổi mật khẩu',
    pwdCheckTips1: 'Gợi ý là 6-20 chữ cái, số hoặc ký hiệu',
    pwdCheckTips2: 'Bạn đã nhập vào mật khẩu của cường độ yếu',
    pwdCheckTips3: 'Mật khẩu của bạn có thể phức tạp hơn',
    pwdCheckTips4: 'Mật khẩu của bạn rất an toàn.',
    pwdLevel1: 'Yếu',
    pwdLevel2: 'Trong',
    pwdLevel3: 'Mạnh',
    comfirmChangePwd: 'Xác nhận đổi đường dẫn',
    notSetYet: 'Chưa đặt',
    liter: 'Lít',
    arrearageDayTips: 'Ngày',
    todayExpire: 'Do hôm nay',
    forgotPwd: 'Quên mật khẩu của bạn?',
    forgotPwdTips: 'Vui lòng liên hệ với người bán để thay đổi mật khẩu của bạn.',
    //1.6.7
    commonProblem: 'Vấn đề chung',
    instructions: 'Hướng dẫn',
    webInstructions: 'Hướng dẫn WEB',
    appInstructions: 'Hướng dẫn APP',
    acceptAlarmNtification: 'Thông báo báo động',
    alarmPeriod: 'Thời gian báo động',
    whiteDay: 'Ngày',
    blackNight: 'Đêm',
    allDay: 'Cả ngày',
    alarmEmail: 'Thư báo động',
    muchEmailTips: 'Có thể nhập nhiều hộp thư, được phân tách bằng ký hiệu ‘; Cách.',
    newsCenter: 'Trung tâm thông báo',
    allNews: 'Tất cả',
    unReadNews: 'Chưa đọc',
    readNews: 'Đọc',
    allTypeNews: 'Các loại thông bá',
    alarmInformation: 'Thông tin báo động',
    titleContent: 'Tiêu đề',
    markRead: 'Đánh dấu đã đọc',
    allRead: 'Tất cả đã đọc',
    allDelete: 'Xóa tất cả',
    selectFirst: 'Chọn Đầu tiên!',
    updateFail: 'Cập nhật thất bại!',
    ifAllReadTips: 'Tất cả đã đọc?',
    ifAllDeleteTips: 'Xóa tất cả?',
    stationInfo: 'Thông tin trạm',
    phone: 'Điện thoại',
    //1.6.72
    plsSelectTime: 'Hãy chọn thời gian!',
    customerNotFound: 'Không thể tìm thấy khách hàng',
    Postalcode: 'Địa điểm',
    accWarning: 'Báo động ACC',
    canInputMultiPhone: 'Bạn có thể nhập nhiều số điện thoại, vui lòng sử dụng; riêng biệt',
    noLocationInfo: 'Thiết bị chưa có thông tin về vị trí.',
    //1.6.9
    fenceName: 'tên hàng rào',
    fenceManage: 'Quản lý hàng rào',
    circular: 'vòng tròn',
    polygon: 'đa giác',
    allFence: 'Tất cả hàng rào',
    shape: 'hình dạng',
    stationNews: 'Tin nhắn trang web',
    phonePlaceholder: 'Bạn có thể nhập nhiều số, cách nhau bởi ","',
    addressPlaceholder: 'Vui lòng nhập địa chỉ và mã bưu chính theo thứ tự, cách nhau bởi ","',
    isUnbind: 'Bạn có muốn hủy liên kết',
    alarmCar: 'Xe báo động',
    alarmAddress: 'vị trí báo động',
    chooseAtLeastOneTime: 'Chọn ít nhất một lần',
    alarmMessage: 'Không có thông tin chi tiết về thông tin báo động này',
    navigatorBack: 'trở lại cấp trên',
    timeOverMessage: 'Thời gian hết hạn của người dùng không thể lớn hơn thời gian hết hạn của nền tảng',
    //1.7.0
    userTypeStr: 'Loại người dùng',
    newAdd: 'Mới',
    findAll: 'Tổng cộng',
    findStr: 'Kết hợp dữ liệu',
    customColumn: 'Tùy chỉnh',
    updatePswErr: 'Cập nhật mật khẩu không thành công',
    professionalUser: 'Người dùng chuyên nghiệp hay không?',
    confirmStr: 'Xác nhận',
    inputTargetCustomer: 'Khách hàng mục tiêu đầu vào',
    superiorUser: 'Người dùng cao cấp',
    speedReport: 'Báo cáo tốc độ',
    createAccount: 'Tạo tài khoản',
    push: 'Đẩy',
    searchCreateStr: 'Nó sẽ vận hành một tài khoản và chuyển thiết bị sang tài khoản này.',
    allowIMEI: 'Cho phép đăng nhập IMEI',
    defaultPswTip: 'Mật khẩu mặc định là 6 chữ số cuối của IMEI',
    createAccountTip: 'Tạo tài khoản và chuyển thiết bị thành công',
    showAll: 'Hiển thị tất cả',
    bingmap: 'Bản đồ Bing',
    areaZoom: 'Thu phóng',
    areaZoomReduction: 'Khôi phục thu phóng',
    reduction: 'Giảm',
    saveImg: 'Lưu dưới dạng hình ảnh',
    fleetFence: 'Hạm đội hàng rào',
    alarmToSub: 'Thông báo báo động',
    bikeFence: 'Hàng rào xe đạp',
    delGroupTip: 'Xóa thất bại, xin vui lòng xóa điểm quan tâm đầu tiên!',
    isExporting: 'Xuất khẩu ...',
    addressResolution: 'Địa chỉ giải quyết ...',
    simNOTip: 'Số thẻ SIM chỉ có thể là một số',
    unArrowServiceTip: 'Thời gian hết hạn của người dùng cho các thiết bị sau lớn hơn thời gian hết hạn của nền tảng, vui lòng chọn lại. Số thiết bị là:',
    platformAlarmandPhone: 'Báo động nền tảng + Điện thoại',
    openLightAlarm: 'Báo động cảm biến mở',
    closeLightAlarm: 'Đóng đèn báo động cảm biến',
    ACCAlarm: 'Báo động ACC',
    translateError: 'Chuyển không thành công, người dùng mục tiêu không được phép',
    distanceTip: 'Nhấn OK, nhấp đúp để hoàn tất',
    workMode: 'Chế độ làm việc',
    workModeType: '0: Chế độ bình thường; 1 Chế độ ngủ thông minh; 2 chế độ ngủ sâu',
    clickToStreetMap: 'Nhấn vào để mở bản đồ xem đường',
    current: 'Hiện tại',
    remarkTip: 'Lưu ý: Không bán thẻ của các loại gói khác nhau cùng một lúc',
    searchRes: 'Kết quả tìm kiếm',
    updateIcon: 'Thay đổi biểu tượng',
    youHaveALarmInfo: 'Bạn có một thông điệp cảnh báo',
    moveInterval: 'Khoảng thời gian tập thể dục',
    staticInterval: 'Khoảng thời gian không hoạt động',
    notSupportTraffic: 'Coming soon.',
    ignite:'Acc ON',
    flameout:'TẮT',
    generateRenewalPointSuc:'Tạo điểm đổi mới thành công',
    noGPSsignal:'Không định vị',
    imeiErr2:'Vui lòng nhập ít nhất 6 chữ số cuối của số imei',
    searchCreateStr2:'Điều này sẽ tạo một tài khoản và chuyển thiết bị này sang tên tài khoản',
    addUser:'Thêm người dùng',
    alarmTemperature:'Giá trị nhiệt độ báo động',
    highTemperatureAlarm:'Báo động nhiệt độ cao',
    lowTemperatureAlarm:'Báo động nhiệt độ thấp',
    temperatureTip:'Vui lòng nhập giá trị nhiệt độ！',
    locMode:'chế độ định vị',
    imeiInput:'Vui lòng nhập số IMEI',
    noResult:'Không có kết quả phù hợp',
    noAddressKey:'Tạm thời không thể lấy thông tin địa chỉ',
    deviceGroup: 'Quản lý nhóm',
    shareManage: 'Quản lý cổ phần ',
    lastPosition: 'Vị trí cuối cùng',
    defaultGroup: 'Nhóm mặc định',
    tankShape: 'Hình dạng bình xăng',
    standard: 'Tiêu chuẩn',
    oval: 'hình trái xoan',
    irregular: 'không thường xuyên',
    //1.8.4
    inputAddressOrLoc:'Vui lòng nhập địa chỉ / vĩ độ và kinh độ',
    inputGroupName:'Tên nhóm đầu vào',
    lock:'Khóa',
    shareHistory:'Chia sẻ lịch sử phát lại',
    tomorrow:'Ngày mai',
    threeDay:'3 ngày',
    shareSuccess:'Chia sẻ liên kết thành công',
    effective:'có hiệu lực',
    lapse:'không hợp lệ',
    copyShareLink:'Sao chép liên kết thành công',
    openStr:'TRÊN',
    closeStr:'Tắt',
    linkError:'Lỗi liên kết',
    inputUserName:'Tên người dùng đầu vào',
    barCodeStatistics:'thống kê mã vạch',
    barCode:'Mã vạch',
    sweepCodeTime:'Thời gian quét',
    workModeType2: '1 Chế độ ngủ thông minh 2 Chế độ ngủ sâu 3 Chế độ chuyển đổi từ xa',
    remoteSwitchMode: 'Chế độ chuyển đổi từ xa',
     saleTime :'Ngày mở bán',
     onlineTime :'Thời gian trực tuyến',
     dayMileage:'Dặm hôm nay',
     imeiNum:'Số IMEI',
     overSpeedValue:'Giá trị vượt mức',
     shareNoOpen:'Liên kết chia sẻ không được kích hoạt',
     addTo2:'Khách Hàng',
     overDue:'Đã hết hạn',
    openInterface:'Giao diện mở',
    privacyPolicy: 'Chính sách quyền riêng tư',
    serviceTerm: 'Điều khoản dịch vụ',
    importError:'Không thể thêm, số thiết bị (IMEI) phải là số có 15 chữ số',
    importResult:'Nhập kết quả',
    totalNum:'toàn bộ',
    successInfo:'sự thành công',
    errorInfo:'Thất bại',
    repeatImei:'Lặp lại IMEI',
includeAccount:'tài khoản phụ',
formatError:'Không đúng định dạng',
importErrorInfo:'Vui lòng nhập số IMEI 15 chữ số',
totalMileage: 'Tổng số dặm',
    totalOverSpeed: 'Tổng số quá mức (lần)',
    totalStop: 'Tổng số lần dừng (lần)',
    totalOil: 'Tổng lượng dầu',
    timeChoose: 'Lựa chọn thời gian',
    intervalTime: 'Khoảng thời gian',
     default: 'Mặc định',
     idleSpeedStatics: 'Thống kê tốc độ không tải',
     offlineStatistics: 'Thống kê ngoại tuyến',
     idleSpeed: 'Tốc độ không tải',
     idleSpeedTimeTip1: 'Thời gian nhàn rỗi không thể để trống',
     idleSpeedTimeTip2: 'Thời gian nhàn rỗi phải là số nguyên dương',
    averageSpeed: 'Tốc độ trung bình',
    averageOil: 'Tiêu thụ nhiên liệu trung bình',
    oilImgTitle: 'Biểu đồ phân tích dầu',
    oilChangeDetail: 'Chi tiết thay đổi nhiên liệu',
machineNameError:"Tên thiết bị không thể chứa các ký hiệu đặc biệt (/ ')",
remarkError:'Thông tin ghi chú không thể vượt quá 50 từ',
defineColumnTip:'Kiểm tra tới 12 mục',
pswCheckTip: 'Gợi ý là sự kết hợp của 6-20 chữ số, chữ cái và ký hiệu',
chooseGroup: 'Vui lòng chọn một nhóm',
     chooseAgain: 'Bạn chỉ có thể truy vấn dữ liệu trong sáu tháng qua, vui lòng chọn lại',
     noDataTip: 'Không có dữ liệu',
     noMachineNameError: 'Vui lòng chọn một thiết bị！',
     loginAccountError: 'Tài khoản đăng nhập không thể có 15 chữ số！',
     includeExpire: 'Mà hết hạn',
groupNameTip: 'Tên nhóm không thể để trống',
outageTips:'Bạn có chắc chắn cắt dầu?',
    powerSupplyTips:'Bạn có chắc chắn phục hồi dầu?',//越南
    centerPhoneTips:'Vui lòng nhập số',
    centerPhoneLenTips: 'Vui lòng nhập 8-20 chữ số',
    passworldillegal: "Có các ký tự không hợp lệ",
    // 2.0.0 POI，权限版本
  singleAdd:'đơn thêm',
  batchImport:'nhập khẩu hàng loạt',
  name:'tên',
  icon:'Icon',
  defaultGroup:'nhóm mặc định',
  remark:'nhãn hiệu',
  uploadFile:'tải tệp',
  exampleDownload:' tải xuống ví dụ',
  uploadFiles:' tải tệp',
  poiTips1:'Bạn có thể nhập POI bằng cách tải lên tệp Excel với thông tin liên quan. Vui lòng làm theo định dạng của ví dụ để chuẩn bị tệp',
  poiTips2:'Bạn có thể nhập POI bằng cách tải lên tệp Excel với thông tin liên quan. Vui lòng làm theo định dạng của ví dụ để chuẩn bị tệp',
  poiTips3:'Icon: bắt buộc, nhập 1,2,3,4',
  poiTips4:'Latitude：Bắt buộc',
  poiTips5:'Longitude：Bắt buộc',
  poiTips6:'Tên nhóm: Tùy chọn, không quá 32 ký tự. Nếu tên nhóm không được điền, điểm POI thuộc về nhóm mặc định. Nếu tên nhóm đã điền phù hợp với tên nhóm đã tạo thì điểm POI thuộc về nhóm đã tạo. Tên nhóm chưa được tạo, hệ thống sẽ thêm nhóm',
  poiTips7:'Nhãn hiệu: Tùy chọn, không quá 50 ký tự',
  // 权限相关
  roleLimit: 'Quyền của vai trò',
  operateLog: 'Nhật ký hoạt động',
  sysAccountManage: 'Tài khoản ủy quyền',
  rolen: 'Vai trò',
  rolename: 'Tên vai trò',
  addRole: 'Vai trò mới',
  editRole: 'Chỉnh sửa vai trò',
  deleteRole: 'Xóa vai trò',
  delRoleTip: 'Bạn có chắc chắn muốn xóa vai trò này không?',
  delAccountTip: 'Bạn có chắc chắn muốn xóa tài khoản này không?',
  limitconfig: 'Cài đặt quyền',
  newAccountTip1: 'Tài khoản quyền hạn tương tự như tài khoản ảo cũ và là tài khoản phụ của quản trị viên. Quản trị viên có thể tạo tài khoản cấp quyền và gán các vai trò khác nhau cho các tài khoản cấp quyền để các tài khoản khác nhau có thể xem nội dung và hoạt động khác nhau trên nền tảng.',
  newAccountTip2: 'Quy trình tạo tài khoản quyền:',
  newAccountTip31: '1. Nella pagina di gestione dei ruoli,',
  newAccountTip32: 'Nuovo ruolo',
  newAccountTip33: ' E configurare le autorizzazioni per il ruolo;',
  newAccountTip4: '2. Trên trang quản lý tài khoản quyền hạn, hãy tạo một tài khoản quyền hạn mới và gán các vai trò cho tài khoản.',
  newRoleTip1: 'Quản trị viên có thể tạo các vai trò và định cấu hình các quyền hoạt động khác nhau cho các vai trò khác nhau để đáp ứng nhu cầu kinh doanh trong các tình huống khác nhau.',
  newRoleTip2: 'Ví dụ: định cấu hình xem vai trò tài chính có quyền định vị và giám sát hay không, vai trò đó có quyền thêm khách hàng hay không, vai trò tài chính có quyền sửa đổi thông tin thiết bị hay không, v.v.',
  "refuelrate": "Tỷ lệ tiếp nhiên liệu",
  "refuellimit": "Khi lượng dầu tăng trong một phút lớn hơn xxxxL và nhỏ hơn xxxxL, nó được coi là tiếp nhiên liệu.",
  "refueltip": "Tỷ lệ tiếp nhiên liệu tối đa không được nhỏ hơn tỷ lệ tiếp nhiên liệu tối thiểu!",
  viewLimitConf: 'Xem cài đặt quyền',
  viewLimit: 'Xem quyền',
  newSysAcc: 'Tài khoản hệ thống mới',
  editSysAcc: 'Chỉnh sửa tài khoản quyền',
  virtualAcc: 'Tài khoản ảo',
  oriVirtualAcc: 'Tài khoản ảo ban đầu',
  virtualTip: 'Mô-đun tài khoản ảo đã được nâng cấp thành mô-đun tài khoản hệ thống, vui lòng tạo một tài khoản hệ thống mới',
  operaTime: 'Thời gian hoạt động',
  ipaddr: 'địa chỉ IP',
  businessType: 'Loại hình kinh doanh',
  params: 'Parameter permintaan',
  operateType: 'Loại hoạt động',
  uAcc: 'tài khoản người dùng',
  uName: 'tên tài khoản',
  uType: 'kiểu người dùng',
  logDetail: 'Chi tiết nhật ký',
  delAccount: 'Xóa tài khoản',
  modifyTime: 'Sửa đổi thời gian',
  unbindlimit: 'Không thể tạo hàng rào điện tử bằng một cú nhấp chuột mà không có quyền thiết bị liên quan!',
  setSmsTip: 'Nếu bạn thiết lập thông báo SMS, trước tiên bạn cần bật thông báo nền tảng; nếu thông báo nền tảng được gửi thành công, thông báo SMS không thành công, bạn cần bật lại thông báo SMS',
  cusSetComTip: 'Tuyên bố từ chối trách nhiệm: Rủi ro do hướng dẫn tùy chỉnh mang lại không liên quan gì đến nền tảng',
  cusSetComPas: 'Vui lòng nhập mật khẩu của tài khoản đăng nhập hiện tại',
  cusSetComDes1: 'Hướng dẫn tùy chỉnh chỉ hỗ trợ hướng dẫn trực tuyến.',
  cusSetComDes2: 'Nếu thiết bị không phản hồi trong vòng hai phút sau khi gửi lệnh, quá trình sẽ bị chấm dứt và trạng thái lệnh được đánh giá là không có phản hồi.',
  cueSetComoffline: 'Thiết bị không phản hồi và không gửi được lệnh tùy chỉnh!',
  fbType: 'Loại phản hồi',
  fbType1: 'Tham mưu',
  fbType2: 'Sự cố',
  fbType3: 'Kinh nghiệm người dùng',
  fbType4: 'Đề xuất tính năng mới',
  fbType5: 'khác',
  upload: 'Tải lên',
  uploadImg: 'tải lên hình ảnh',
  uploadType: 'Vui lòng tải lên các tệp thuộc loại .jpg .png .jpeg .gif',
  uploadSize: 'Tệp tải lên không được lớn hơn 3M',
  fbManager: 'Quản lý phản hồi',
  blManager: 'Quản lý thông báo',
  fbUploadTip: 'Vui lòng chọn loại phản hồi',
  menuPlatform: "Tin tức nền tảng",
  menuFeedback: "Phản hồi",
  menuBulletin: "Thông báo nền tảng",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "Hành vi thúc đẩy",
  BtyjdfghtwsrgGHFEEGRDAF: "Tăng tốc nhanh",
  BtyuwyfgrWERERERRTHDAsdDF: "Giảm tốc nhanh",
  Be2562h253grgsHHJDbRDAF: "Rẽ ngoặt",
  celTemperature:'Nhiệt độ celsius'
}
// 权限tree
lg.limits = {
    "ACC_statistics": "Thống kê ACC",
    "Account_Home": "Nhà",
    "Add": "Mới",
    "Add_POI": "Thêm POI",
    "Add_customer": "Thêm người dùng'",
    "Add_device_group": "Thêm nhóm thiết bị",
    "Add_fence": "Thêm hàng rào",
    "Add_sharing_track": "Thêm bài hát chia sẻ",
    "Add_system_account": "Tài khoản quyền mới",
    "Alarm_details": "Chi tiết thông báo",
    "Alarm_message": "Tin nhắn báo động",
    "Alarm_overview": "Tổng quan thông báo",
    "Alarm_statistics": "Báo cáo thông báo",
    "All_news": "Tất cả",
    "Associated_equipment": "Thiết bị liên kết",
    "Available_points": "Cân đối",
    "Barcode_statistics": "thống kê mã vạch",
    "Batch_Import": "nhập khẩu hàng loạt",
    "Batch_renewal": "Đổi mới hàng loạt",
    "Batch_reset": "Đặt lại hàng loạt",
    "Bulk_sales": "Bán hàng loạt",
    "Call_the_police": "Thông báo",
    "Customer_details": "Chi tiết khách hàng",
    "Customer_transfer": "Di chuyển người dùng",
    "Delete_POI": "Xóa POI",
    "Delete_account": "Xóa tài khoản",
    "Delete_customer": "Xóa người dùng",
    "Delete_device": "Xóa thiết bị",
    "Delete_device_group": "Xóa nhóm thiết bị",
    "Delete_fence": "Xóa hàng rào",
    "Delete_role": "Xóa vai trò",
    "Device_List": "Danh sách thiết bị",
    "Device_grouping": "Quản lý nhóm",
    "Device_transfer": "Chuyển thiết bị",
    "Due_reminder": "Nhắc trước khi ra",
    "Edit_details": "Chỉnh sửa chi tiết",
    "Equipment_management": "Thiết bị",
    "My_clinet": "Thiết bị",
    "Fence": "Địa chất",
    "Fence_management": "Quản lý hàng rào",
    "Generate": "tạo ra",
    "Generate_lead-in_points": "Tạo thẻ mới",
    "Generate_renewal_points": "Tạo thẻ gia hạn",
    "Have_read": "Xóa",
    "Idle_speed_statistics": "Thống kê tốc độ không tải",
    "Import": "Nhập khẩu",
    "Import_Device": "Thêm IMEI mới",
    "Industry_Statistics": "Thống kê ngành",
    "Location_monitoring": "Giám sát",
    "Log_management": "Quản lý nhật ký",
    "Mark_read": "Đánh dấu đã đọc",
    "Menu_management": "Quản lý menu",
    "Message_Center": "Trung tâm thông báo",
    "Mileage_statistics": "Báo cáo quãng đường",
    "Modify_POI": "Sửa đổi POI",
    "Modify_device_details": "Sửa đổi chi tiết thiết bị",
    "Modify_device_group": "Sửa đổi nhóm thiết bị",
    "Modify_role": "Sửa đổi vai trò",
    "Modify_sharing_track": "Sửa đổi bài hát chia sẻ",
    "Modify_user_expiration": "Sửa đổi thông tin người dùng sẽ hết hạn",
    "More": "Thêm",
    "My_client": "Kinh doanh",
    "New_role": "Vai trò mới",
    "New_users": "Thêm người dùng",
    "Oil_statistics": "Nhiên liệu thống kê",
    "POI_management": "Quản lý POI",
    "Points_record": "Lịch sử thẻ",
    "Push": "Đẩy",
    "Quick_sale": "Bán hàng loạt",
    "Renew": "Thay mới",
    "Replay": " Phát lại",
    "Role_management": "Quản lý vai trò",
    "Run_overview": "Tổng quan về di chuyển",
    "Running_statistics": "Tổng quan về di chuyển",
    "Sales_equipment": "Bán thiết bị",
    "Set_expiration_reminder": "Đặt lời nhắc hết hạn",
    "Share_track": "Chia sẻ lịch sử phát lại",
    "Sharing_management": "Quản lý cổ phần",
    "Speeding_detailed_list": "Chi tiết quá cước",
    "Statistical_report": "Báo cáo",
    "Stay_detailed_list": "Chi tiết dừng đỗ",
    "System_account_management": "Tài khoản ủy quyền",
    "Temperature_statistics": "Thống kê nhiệt độ",
    "Transfer": "Chuyển khoản",
    "Transfer_group": "Chuyển nhóm",
    "Transfer_point": "Di chuyển thẻ mới",
    "Transfer_renewal_point": "Chuyển thẻ gia hạn",
    "Trip_statistics": "Báo cáo di chuyển",
    "Unlink": "Hủy liên kết",
    "View": "Xem",
    "View_POI": "Xem POI",
    "View_device_group": "Xem nhóm thiết bị",
    "View_fence": "Kiểm tra hàng rào",
    "View_role": "Xem vai trò",
    "View_sharing_track": "Xem bài chia sẻ",
    "Virtual_account": "Tài khoản ảo",
    "Voltage_analysis": "Phân tích điện áp",
    "Voltage_statistics": "Thống kê điện áp",
    "batch_deletion": "Xóa hàng loạt",
    "change_Password": "Sửa lại mật khẩu",
    "delete": "Xóa",
    "edit": "Sửa",
    "instruction": "Hướng dẫn",
    "modify": "Cập nhật",
    "monitor": "Màn hình",
    "my_account": "Trang chủ",
    "reset_Password": "Đặt lại mật khẩu",
    "share_it": "Chia sẻ vị trí",
    "sub_user": "Tài khoản phụ",
    "track": "Theo dõi", 
    "Custom_Order": "Hướng dẫn tùy chỉnh",
    "GeoKey_Manager": "Quản lý GeoKey",
    "GeoKey_Update": "sửa đổi",
    "GeoKey_Delete": "xóa bỏ",
    "GeoKey_Add": "Thêm vào",
    "GeoKey_View": "Lượt xem",
    "feedback_manager": "Quản lý phản hồi",
    "feedback_list": "Lượt xem",
    "feedback_handle": "Xử lý phản hồi",
    "proclamat_manager": "Quản lý thông báo",
    "proclamat_manager_list": "Xem thông báo",
    "proclamat_manager_update": "Thông báo sửa đổi",
    "proclamat_manager_delete": "Xóa thông báo",
    "proclamat_manager_save": "Thông báo mới",
    "device_update_batch_model": "Sửa đổi hàng loạt kiểu thiết bị"
}
// 问题文档的内容
lg.questionDocumentArr = [
    ['Q: Đèn báo tắt sau khi thiết bị đấu dây được cài đặt và ngoại tuyến.', 'Trả lời: Sau khi bạn tắt xe, sử dụng bút điện và đồng hồ vạn năng để đo xem điện áp đường dây được kết nối có phù hợp với dải điện áp của bộ theo dõi GPS thường là 9-36V hay không. <br/> Biện pháp phòng ngừa dây điện: Nhân viên lắp đặt và đi dây cần có hiểu biết về dòng xe và có khả năng thực hành nhất định để tránh thiệt hại cho xe của bạn do hệ thống dây điện không phù hợp.'],
    ['Q: Thiết bị có dây hoặc thiết bị theo dõi thời gian thực không dây, cuộc gọi điện thoại hoặc thiết bị trạng thái khởi động nền IoT ngoại tuyến', 'Trả lời：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Gửi tin nhắn văn bản để khởi động lại, quan sát một vài phút để xem nó có trực tuyến không. Nói chung gửi RESET # vui lòng liên hệ với đại lý để xác định. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Kết nối mạng không ổn định. Vui lòng di chuyển xe đến khu vực tín hiệu tốt. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 3. Sau các bước trên, bạn không thể truy cập trực tuyến. Bạn cần liên hệ với nhà điều hành di động để kiểm tra xem thẻ có bất thường không.'],
    ['Q: Thiết bị ngoại tuyến theo đợt vào đầu và cuối tháng.', 'Trả lời: Vui lòng kiểm tra xem thẻ có bị truy thu hay không. Nếu bị truy thu, vui lòng nạp lại đúng lúc và tiếp tục sử dụng.'],
    ['Q: Xe đang lái, vị trí GPS trực tuyến không được cập nhật.', 'Trả lời：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Thiết bị đấu dây có thể gửi tin nhắn văn bản TÌNH TRẠNG # để kiểm tra trạng thái nhận tín hiệu vệ tinh. Xem GPS: vệ tinh tìm kiếm là tín hiệu vệ tinh luôn trong tìm kiếm, Trong trường hợp này, cần phải kiểm tra vị trí cài đặt và liệu nó có được cài đặt theo hướng dẫn hay không. Mặt ngửa lên, không có nắp kim loại trên đỉnh. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Gửi SMS TÌNH TRẠNG #, trạng thái trả về là GPS: TẮT, vui lòng gửi lại NHÀ MÁY #, sau khi nhận được trả lời OK, quan sát 5 phút để xem có bản cập nhật nào không. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 3. Theo hai phương pháp trên, lỗi không thể được loại bỏ. Vui lòng liên hệ với người bán để sửa chữa.'],
    ['Q: Tại sao nền tảng sạc trong một thời gian dài vẫn cho thấy nó không đầy?', 'Trả lời: Hiển thị công suất nền tảng dựa trên thông tin mà thiết bị trả về để thực hiện phân tích dữ liệu để xác định công suất hiện tại của thiết bị. Trong một số trường hợp đặc biệt, sẽ xảy ra lỗi hiển thị nguồn. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Dữ liệu nguồn của thiết bị và dữ liệu định vị thiết bị được tải lên cùng nhau. Nếu pin được sạc trong một thời gian dài, nguồn điện không thay đổi. Làm ơn：①Mang thiết bị của bạn di chuyển 100-300 mét để cập nhật thông tin vị trí của thiết bị để dữ liệu pin và dữ liệu vị trí của thiết bị có thể được đưa trở lại nền tảng cùng nhau để làm mới màn hình pin. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2、Theo sự thay đổi của đèn chỉ báo nguồn để xác định xem nó có được sạc đầy hay không (lấy S15 làm ví dụ), các bước thao tác như sau: ①Sau khi sạc 8-10 giờ, đèn báo nguồn sẽ chuyển sang màu vàng và xanh lục. Sau khi rút cáp sạc, hãy cắm cáp sạc và đèn báo nguồn sẽ chuyển sang màu vàng và xanh lục trong vòng 15 phút. Vui lòng tham khảo hướng dẫn sử dụng cho các kiểu máy khác. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 3、Sạc trong một thời gian dài cũng đầy điện, tình huống này có thể là điện áp phích cắm sạc thấp hơn 1A, vui lòng sạc pin với 5V, 1A trong 8-10 giờ.'],
    ['Q: Lệnh cắt điện GPS đã được ban hành thành công. Tại sao xe vẫn không bị hỏng?', 'Trả lời: Sau khi lệnh tắt nguồn được cấp thành công, thiết bị phải được thực thi theo các điều kiện sau: <br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Đảm bảo nối dây của thiết bị đúng và tuân theo sơ đồ nối dây của sách hướng dẫn. <br/>&nbsp;&nbsp;&nbsp;&nbsp;2, thiết bị hoạt động bình thường, ở trạng thái tĩnh hoặc lái xe, có định vị, không ngoại tuyến và tốc độ xe không vượt quá 20 km; <br/>Nếu phương tiện ngoại tuyến, không được định vị hoặc tốc độ xe vượt quá 20 km mỗi giờ, thiết bị đầu cuối sẽ không thực thi ngay cả khi lệnh cắt điện được ban hành thành công.'],
    ['Q: Ba năm sản phẩm không dây được cài đặt lần đầu tiên, thiết bị hiển thị không được định vị hoặc trực tuyến.', 'Trả lời：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Bật công tắc để kiểm tra xem đèn báo có nhấp nháy hay không. Ví dụ: đèn báo màu vàng và xanh lục S18 nhấp nháy cùng lúc với bình thường và đèn nhấp nháy có trong tín hiệu tìm kiếm. Thiết bị không sáng. (Trạng thái của các mô hình khác nhau sẽ khác nhau. Vui lòng tham khảo hướng dẫn sử dụng cho các mô hình khác) <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Đèn báo nhấp nháy không nằm trên đường dây. Nếu tín hiệu được bật ở vị trí kém, vui lòng nhận tín hiệu ở khu vực tốt. Khu vực tín hiệu tốt không trực tuyến, có thể tắt trong 1 phút, cài đặt lại thẻ và sau đó khởi động thử nghiệm.'],
    ['Q: Sản phẩm cáp được cài đặt lần đầu tiên và thiết bị hiển thị không được định vị.', 'Trả lời：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Quan sát xem chỉ báo trạng thái GPS của thiết bị đầu cuối có bình thường hay không. Kiểm tra trạng thái của chỉ báo theo hướng dẫn của các kiểu máy khác nhau. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Nếu đèn báo tắt, thiết bị không thể được cấp nguồn bình thường. <br/>&nbsp;&nbsp;&nbsp;&nbsp; Đèn báo thẻ 3, (màu vàng xanh) không sáng, tắt nguồn và cài đặt lại thẻ, sau đó bật nguồn để thấy ánh sáng bình thường là bình thường. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 4. Xác định xem số thẻ SIM trong thiết bị có bị truy thu hay không và chức năng truy cập Internet GPRS có bình thường không. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 5. Không có mạng GSM ở nơi đặt thiết bị, chẳng hạn như phòng dưới cùng, đường hầm, v.v., nơi tín hiệu yếu, vui lòng lái xe đến nơi có độ phủ sóng GPRS tốt. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 6, vị trí của bộ định vị không được quá kín, không có các vật bằng kim loại, càng xa càng tốt trong vị trí lắp đặt của xe. Nếu không, nó ảnh hưởng đến việc tiếp nhận tín hiệu. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 7, khởi động bình thường, dừng trong vùng tín hiệu tốt không trực tuyến, bạn có thể phát lại lệnh dòng để kiểm tra xem giao diện IP và mạng liên kết thẻ có bình thường không.']
]
lg.webOptDoc = 'Sắp có ...';
lg.appOptDoc = 'Sắp có ...';
// 查询参数的帮助
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push('<tr>');
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push('<td>Truy vấn mật khẩu chế độ đầu cuối</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">Điện thoại:</td>');
html.push('<td>Thẻ SIM tích hợp đầu cuối truy vấn</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">USER:</td>');
html.push('<td>Số điện thoại của chủ sở hữu truy vấn</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SPEED:</td>');
html.push('<td>Truy vấn giá trị giới hạn tốc độ</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">FREQ:</td>');
html.push('<td>Truy vấn tần suất theo dõi, đơn vị là giây</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">TRACE:</td>');
html.push('<td>Truy vấn xem theo dõi có được bật hay không.1:bật,0:tắt</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push('<td>Truy vấn phạm vi báo động di chuyển bất hợp pháp,Đơn vị tính: meter</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIB:</td>');
html.push('<td>Truy vấn xem báo thức SMS có được bật hay không,1:bật,0:tắt');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBL:</td>');
html.push('<td>Độ nhạy rung truy vấn là 0 đến 15,0 là độ nhạy cao nhất, quá cao có thể là báo động sai, 15 là độ nhạy thấp nhất</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push('<td>Truy vấn xem báo thức cuộc gọi có được bật hay không,1:bật,0:tắt'); html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push('<td>Truy vấn cho dù lọc GPS trôi dạt được kích hoạt,1:sử dụng,0:vô hiệu hóa, nếu bạn bật thiết bị chống trộm sẽ nhập trạng thái tĩnh không có rung xảy ra trong vòng 5 phút và lọc tất cả trôi GPS</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push('<td>Truy vấn chức năng ngủ có được bật hay không,1:sử dụng,0:vô hiệu hóa, nếu bạn bật thiết bị chống trộm sẽ vào chế độ ngủ không có rung xảy ra trong vòng 30 phút, nó sẽ đóng chức năng GPS và tiết kiệm điện</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">POF:</td>');
html.push('<td>Truy vấn xem báo động tắt nguồn được bật chưa,1:sử dụng,0:vô hiệu hóa</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">GPS:</td>');
html.push('<td> Truy vấn cường độ tín hiệu vệ tinh, Ví dụ £º2300 1223 3431 ¡£ ¡£ ¡£ tổng cộng 12 bộ gồm bốn chữ số,2300 nghĩa là: Cường độ tín hiệu từ vệ tinh số 23 là 0,1223: Cường độ tín hiệu từ Số 12</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VBAT:</td>');
html.push('<td> Truy vấn điện áp pin, sạc điện áp cổng Phí dòng điện Ví dụ: VBAT = 3713300:4960750:303500 Chỉ ra rằng điện áp pin là 3713300uV 3.71v Áp dụng cho điện áp sạc trên chip 4.96V,Sạc hiện tại 303mA </td>');
html.push('</tr>');
html.push('</table>');
lg.queryparamhelp = html.join("");

// 设置参数的帮助
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push('<tr>');
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push('<td> Đặt mật khẩu thiết bị đầu cuối,chỉ có 6 chữ số</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">PHONE:</td>');
html.push('<td>Đặt số SIM của thiết bị đầu cuối</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">USER:</td>');
html.push('<td>Đặt số chủ sở hữu điện thoại di động</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SPEED:</td>');
html.push('<td> Đặt giá trị giới hạn tốc độ,0-300</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">FREQ:</td>');
html.push('<td>Thiết lập tần suất được báo cáo khi bật theo dõi,Đơn vị: giây</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">TRACE:</td>');
html.push('<td> Thiết lập xem mở bản nhạc,1:mở,0:đóng</td>'); html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push('<td> Thiết lập phạm vi báo động di chuyển bất hợp pháp,Đơn vị đo:meter</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIB:</td>');
html.push('<td>Thiết lập cho dù báo thức SMS được bật,1:sử dụng,0:vô hiệu hóa'); html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBL:</td>');
html.push('<td>Thiết lập độ nhạy rung 0 đến 15,0 là độ nhạy cao nhất, quá cao có thể là báo động sai, 15 là độ nhạy thấp nhất</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push('<td>Thiết lập cho dù báo thức cuộc gọi được bật,1: sử dụng,0:vô hiệu hóa');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push('<td> Thiết lập cho dù bộ lọc GPS trôi dạt được kích hoạt,1:sử dụng,0:vô hiệu hóa, nếu bạn bật thiết bị chống trộm sẽ nhập trạng thái tĩnh không có rung xảy ra trong vòng 5 phút, và lọc tất cả trôi GPS</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push('<td> Cài đặt chức năng ngủ có được bật hay không,1:sử dụng,0:vô hiệu hóa, nếu bạn bật thiết bị chống trộm sẽ vào chế độ ngủ không có rung xảy ra trong vòng 30 phút, nó sẽ đóng chức năng GPS và tiết kiệm pin</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">POF:</td>');
html.push('<td>Thiết lập cho dù tắt nguồn báo thức,1:sử dụng,0:vô hiệu hóa </td>');
html.push('</tr>');
html.push('</table>');
lg.setparamhelp = html.join("");


//批量添加
html = [];
html.push('<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox"  ' +
    'style="z-index: 999;position:absolute;left:145px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Khách Hàng:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkAdds_seller" onkeydown="MyAccount.searchUser(event,' + 'bulkAdds_treeDiv' + ',' + 'bulkAdds_seller' + ')" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Do nền tảng:</td>');
html.push('<td>');
html.push('<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Mô hình:</td>');
html.push('<td>');
html.push('<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    '</div>' +
    '<div id="deviceList"></div>' +
    '</div>' +
    '</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Bổ sung:</td>');
html.push('<td>');
html.push('<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>');
lg.bulkAdds = html.join('');

//批量续费
html = [];
html.push('<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> ');
html.push('</div>');
html.push('<form id="bs_form">');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Thêm thiết bị:</td>');
html.push('<td>');
html.push('<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('<span class="re_addNumBox">Hiện tại：<span id="account_re_addNum">0</span>');
html.push('</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<tr>');
html.push('<td style="text-align:right;"><span style="color:red">*</span>Kiểu</td>');
html.push('<td>');
html.push('<input  type="radio" name="red_cardType"');
html.push('class="easyui-validatebox"  value="3" checked><label>Hàng năm</label></input>');
html.push('<input  type="radio" name="red_cardType" style="margin-left:15px;" ');
html.push('class="easyui-validatebox" value="4"><label>Trọn đời</label></input>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right">Số lượng</td>');
html.push('<td>');
html.push('<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">');
html.push('</td>');
html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Do người dùng:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Ghi chú</td>');
html.push('<td>');
html.push('<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="re_renewMachines" title="'+lg.renew+'" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="re_reset" title="'+lg.reset+'"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('</form>');
lg.bulkRenew = html.join('');

//批量销售，myAccount
html = [];
html.push('<div id="bulkSales_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:143px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> ');
html.push('</div>');
html.push('<form id="bs_form">');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Khách hàng:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkSales_seller" onkeydown="MyAccount.searchUser(event,' + 'bulkSales_treeDiv' + ',' + 'bulkSales_seller' + ')" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push('<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>');
html.push('</td>');
html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Do người dùng:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Bổ sung:</td>');
html.push('<td>');
html.push('<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('<span class="bs_addNumBox">Hiện tại：<span id="account_bs_addNum">0</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="bs_sellMachines" title="'+lg.sell+'"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="bs_reset" class="swd-gray-btn" title="'+lg.reset+'"  style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('</form>');

lg.bulkSales = html.join('');


//批量转移1，弹出框
html = [];
html.push('<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:152px;top:171px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Khách hàng mục tiêu:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Bổ sung:</td>');
html.push('<td>');
html.push('<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >Thêm hàng loạt</a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push('</div>');
html.push('<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Di chuyển</a>');
html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Hủy bỏ</a>');

lg.bulkTransfer = html.join('');

//批量转移2,myClient
html = [];
html.push('<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:142px;top:83px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>target client:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Bổ sung:</td>');
html.push('<td>');
html.push('<img  id="bt_addMachines" style="cursor:pointer" title="'+lg.addTo+'" src="../../images/main/myAccount/add3.png" />');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push('</div>');
html.push('<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');

lg.bulkTransfer2 = html.join('');


//批量转移用户
html = [];
html.push('<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:199px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>khách hàng mục tiêu:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>');
html.push('</td>');
html.push('<td></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');

lg.bulkTransferUser = html.join('');

window.lg = lg




