var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
var lg = {
  //印尼语
  //common
  user_guide: 'Panduan pengguna',
  remoteSwitch: "Sakelar Jarak Jauh",
  pageTitle: "WhatsGPS",
  description:
    "Berdasarkan ilmu pengetahuan dan teknologi terbaru dalam industri, WhatsGPS didasarkan pada pemrosesan terdistribusi data besar dan berkomitmen untuk menyediakan pengguna dengan layanan lokasi cloud cerdas. Ini adalah platform layanan lokasi terkemuka di dunia..",
  pageLang: "Bahasa indonesia",
  inputCountTips: "Silakan masukkan akun / IMEI",
  inputPasswordTips: "Silakan masukkan kata sandi Anda",
  appDownload: "Unduhan klien",
  siteName: "Posisikan sekarang",
  rememberPassword: "Ingat kata sandi",
  forgetPassword: 'Tidak ingat kata sandi',
  noToken: "Tolong sampaikan token",
  loginFirst: "Silakan masuk dulu",
  move: "Olah raga",
  stop: "Tetap saja",
  query: "Pertanyaan",
  imeiQuery: "Permintaan nomor IMEI,",
  delete: "Hapus",
  update: "Ubah",
  cancel: "Batalkan",
  soft: "Nomor seri",
  more: "Lebih banyak",
  useful:'berguna',
  useless:'tidak membantu',
  about:'About',
  replyFeedback:'Masukan tentang "$"',
  edit: "Edit",
  add: "Meningkat",
  addTo: "Tambah",
  addDevice: "Tambahkan perangkat",
  machineName: "Nama perangkat",
  searchDevice: "Peralatan pencarian",
  date: "Tanggal",
  LatestUpdate: "Sinyal",
  engine: "ACC",
  locTime: "Waktu posisi",
  locType: "Jenis penargetan",
  startLoc: "Posisi awal",
  endLoc: "Posisi akhir",
  address: "Alamat",
  noAddressTips: "Tidak dapat memperoleh informasi alamat",
  lonlat: "Lintang dan bujur",
  carNO: "Nomor plat",
  imei: "IMEI",
  IMEI: "IMEI",
  simNO: "Nomor kartu SIM",
  activeTime: "Waktu aktivasi",
  expireTime: "Waktu kedaluwarsa",
  acceptSubordinateAlarm: "Terima alarm bawahan",
  acceptAlarmTips1: "Diperiksa",
  acceptAlarmTips2:
    "Anda akan menerima informasi alarm perangkat dari semua pelanggan bawahan",
  speed: "Kecepatan",
  y: "Tahun",
  M: "Bulan",
  d: "Hari",
  h: "Waktu",
  min: "Menit",
  s: "Kedua",
  _year: "Tahun",
  _month: "Bulan",
  _day: "Hari",
  _hour: "Waktu",
  _minute: "Menit",
  _second: "Kedua",
  confirm: "Tentukan",
  yes: "Ya",
  car: "Kendaraan",
  not: "Tidak",
  m: "Beras",
  account: "Nomor akun",
  psw: "Kata sandi",
  save: "Simpan",
  operator: "Operasi",
  queryNoData: "Tidak ada data yang ditanyakan",
  name: "Nama",
  type: "Model",
  open: "Buka",
  close: "Tutup",
  send: "Kirim",
  alarm: "Alarm",
  alarmSetting: "Pengaturan alarm",
  look: "Lihat",
  tailAfter: "Pelacakan",
  history: "Pemutaran",
  dir: "Tajuk",
  locStatus: "Status posisi",
  machineTypeText: "Model",
  carUser: "Pemilik",
  machine: "Peralatan",
  unknowMachineType: "Model tidak dikenal",
  noCommandRecord: "Perangkat tidak memiliki instruksi",
  type1: "Jenis",
  role: "Jenis",
  roles: "Jenis",
  timeType: "Jenis waktu",
  moveSpeed: "Kecepatan lari",
  signal: "Sinyal",
  loc: "Penentuan posisi",
  wiretype: "Jenis",
  wire: "Wired",
  wireless: "Nirkabel",
  expire: "Kedaluwarsa",
  hour: "Jam",
  hourTo: "Jam untuk",
  remark: "Komentar",
  remarkInfo: "Komentar",
  noPriviledges: "Akun ini tidak memiliki hak operasi",
  commandNoOpen: "Fungsi perintah perangkat saat ini belum digunakan.",
  choseDelelePhone:
    "Silakan pilih nomor yang ingin Anda hapus terlebih dahulu.",
  streetView: "Street view",
  wrongFormat: "Kesalahan format input",
  inputFiexd: "Silakan masukkan nomor terprogram",
  serialNumberStart:
    "Silakan masukkan nomor awal dari nomor seri yang ingin Anda hubungkan",
  serialNumberEnd: "Silakan masukkan nomor akhir untuk menjadi berturut-turut",
  clickSearchFirst: "Silakan klik nomor perangkat pencarian terlebih dahulu!",
  isDeleteDevice:
    "Tidak dapat dipulihkan setelah perangkat dihapus. Apakah itu dihapus?",
  //Petunjuk kode kesalahan platform
  errorTips: "Operasi gagal dengan kode kesalahan",
  error10003: 'kata sandi salah',
  error90010: 'Perangkat sedang offline, dan pengiriman perintah khusus gagal!',
  error70003: 'Nilai remote control tidak boleh kosong',
  error70006: 'Tidak mendukung atau tidak memiliki hak untuk mengeluarkan instruksi',
  error20001: 'ID Kendaraan tidak boleh kosong',
  error20012: 'Kendaraan tidak diaktifkan',
  error10012: "Kesalahan kata sandi lama",
  error10017: "Hapus gagal, harap hapus dulu sub-pengguna!",
  error10023: "Penghapusan gagal, pengguna memiliki perangkat",
  error20008: "Tambah gagal, nomor IMEI sudah ada",
  error20006: "Silakan masukkan nomor perangkat 15 digit",
  error10019: "Kesalahan format telepon",
  error10024: "Jangan ulangi penjualan",
  error120003: "Tautan berbagi telah kedaluwarsa",
  error10025: "Informasi perangkat yang dimodifikasi tidak boleh kosong",
  error2010: "Unggah file",
  error20002: "Nomor IMEI tidak ada",
  error10081: "Jumlah kartu perpanjangan yang kurang",
  error10082: 'Tidak perlu mengisi ulang untuk perangkat seumur hidup',
  error3000: 'Peran telah ditetapkan ke akun sistem dan tidak dapat dihapus',
  error103: 'Akun telah dinonaktifkan, harap hubungi penyedia layanan Anda',
  error124: 'Tidak bisa beroperasi sendiri',
  // Terkait pendaratan login.js
  logining: "Masuk ... ",
  login: "Login",
  userEmpty: "Nama pengguna tidak boleh kosong",
  pswEmpty: "Kata sandi tidak boleh kosong",
  prompt: "Kiat",
  accountOrPswError: "Akun atau kata sandi salah",
  UserNameAlreadyExist: "Akun login sudah ada",
  noQualified: "Tidak ada informasi yang memenuhi syarat",
  //main.js
  systemName: "Segera posisikan sistem pemantauan ",
  navTitle_user: [
    "Sistem penentuan posisi",
    "laporan statistik",
    "manajemen peralatan",
  ],
  navTitle_dealer: [
    "Akun saya",
    "pelanggan saya",
    " platform pemantauan",
    "Lebih banyak operasi",
  ],
  exitStytem: "Keluar",
  user: "Pengguna",
  UserCenter: "Pengguna",
  alarmInfo: "Pengguna",
  confirmExit: "Alarm",
  errorMsg: "Apakah Anda yakin untuk keluar dari sistem? ",
  logintimeout: "Alasan kesalahan:",
  clearAlarm: "Kosong",
  clear: "Kosong",
  searchbtn: "Cari pengguna",
  print: "Cetak",
  export: "Ekspor",
  // Bagian umpan balik
  feedback: "Umpan balik",
  feedback_sublime: "Kirim",
  alerttitle: "Judul tidak boleh kosong!",
  alertcontent: "Umpan balik tidak boleh kosong!",
  submitfail: "Pengiriman gagal!",
  saveSuccess: "Berhasil disimpan!",
  submitsuccess:
    "Berhasil dikirim! Kami akan memproses umpan balik Anda sesegera mungkin ~",
  adviceTitle: "Judul",
  adviceTitle_p: "Judul pertanyaan dan opini",
  adviceContent: "Pertanyaan dan opini",
  adviceContent_p:
    "Jelaskan secara singkat pertanyaan dan komentar yang Anda ingin umpan balik, dan kami akan terus meningkatkan untuk Anda.",
  contact: "Informasi kontak",
  contact_p: "Isi telepon atau email Anda",
  //monitor.js

  myMachine: "Peralatan",
  all: "Semua",
  online: "Online",
  offline: "Offline",
  unUse: "Tidak digunakan",
  group: "Pengelompokan",
  moveGruop: "Pindah ke",
  arrearage: "Tunggakan",
  noStatus: "Tanpa kewarganegaraan",
  inputMachineName: "Silakan masukkan nama perangkat / IMEI",
  defaultGroup: "Pengelompokan bawaan",
  offlineLessOneDay: "Offline <1 hari",
  demoUserForbid: "Pengalaman pengguna tidak dapat menggunakan fitur ini",
  shareTrack: "Bagikan",
  shareName: "Bagikan nama",
  liveShare: "Berbagi trek waktu nyata",
  expiration: "Waktu yang efektif",
  getShareLink: "Buat tautan berbagi",
  copy: "Salin",
  copySuccess: "Salinan yang berhasil!",
  enlarge: "Perbesar",
  shareExpired: "Tautan berbagi telah kedaluwarsa",
  LinkFailure: "Buka tautan berbagi gagal",
  inputShareName: "Silakan masukkan nama saham",
  inputValid: "Silakan masukkan waktu valid yang benar",
  //statistics.js
  runOverview: "Tinjauan operasi",
  runSta: "Statistik operasional",
  mileageSta: "Statistik jarak tempuh",
  tripSta: "Statistik perjalanan",
  overSpeedDetail: "Daftar ngebut",
  stopDetail: "Daftar tetap",
  alarmSta: "Statistik alarm",
  alarmOverview: "Ikhtisar alarm",
  alarmDetail: "Ikhtisar alarm",
  shortcutQuery: "Permintaan cepat",
  today: "Hari ini",
  yesterday: "Kemarin",
  lastWeek: "Minggu lalu",
  thisWeek: "Minggu ini",
  thisMonth: "Bulan ini",
  lastMonth: "Bulan lalu",
  mileageNum: "Jarak tempuh (km)",
  overSpeedNum: "Ngebut (km / jam)",
  overSpeed: "Ngebut",
  stopTimes: "Tinggal (kali)",
  searchMachine: "Peralatan",
  speedNum: "Kecepatan (km / jam)",
  querying: "Bertanya",
  stopTime: "Waktu tinggal",
  HisToryStopTime: "Tinggal",
  clickLookLoc: "Klik untuk melihat alamat",
  lookLoc: "Lihat lokasi",
  noData: "Tidak ada data",
  alarmTime: "Waktu alarm",
  vibrationLevel: "Tingkat getaran",
  vibrationWay: "Mode alarm",
  acc: "ACC",
  accStatistics: "Statistik ACC",
  accType: ["Semua negara, ACC menyala, ACC padam"],
  accstatus: ["Buka, tutup"],
  openAccQuery: "Permintaan ACC",
  runtime: "Waktu berjalan",
  //Memodifikasi modifikasi halaman
  run: "Mengemudi",
  speed: "Kecepatan",
  //Manajemen peralatan
  machineManage: "Manajemen peralatan",
  deviceTable: "Tujuan saya",
  status: "Negara",
  havaExpired: "Kedaluwarsa",
  expiredIn60: "Kedaluwarsa dalam 60 hari",
  expiredIn7: "Berakhir dalam 7 hari",
  normal: "Normal",
  allMachine: "Semua peralatan",
  allMachine1: "Semua peralatan",
  expiredIn7Machine: "7 hari kedaluwarsa",
  expiredIn60Machine: "60 hari kedaluwarsa",
  havaExpiredMachine: "Perangkat kedaluwarsa",

  //history.js
  replay: "Main",
  replaytitle: "  Pemutaran",
  choseDate: "Pilih waktu",
  from: "Dari",
  to: "Untuk",
  startTime: "Mulai waktu",
  endTime: "Waktu akhir",
  pause: "Tangguhkan",
  slow: "Lambat",
  mid: "Sedang",
  fast: "Cepat",
  startTimeMsg: "Anda belum memilih waktu mulai",
  endTimeMsg: "Anda belum memilih waktu akhir",
  smallEnd:
    "Waktu akhir yang Anda masukkan kurang dari waktu mulai, silakan pilih kembali!",
  bigInterval:
    "Interval waktu yang Anda masukkan tidak boleh melebihi 31 hari!",
  trackisempty: "Lintasan kosong selama periode ini,",
  longitude: "Bujur",
  latitude: "Lintang",
  direction: "Arahan",
  stopMark: "Tanda berhenti",
  setStopTimes: [
    {
      text: "1 menit",
      value: "1",
    },
    {
      text: "2 menit",
      value: "2",
    },
    {
      text: "3menit",
      value: "3",
    },
    {
      text: "5menit",
      value: "5",
    },
    {
      text: "10menit",
      value: "10",
    },
    {
      text: "15menit",
      value: "15",
    },
    {
      text: "20menit",
      value: "20",
    },
    {
      text: "30menit",
      value: "30",
    },
    {
      text: "45menit",
      value: "45",
    },
    {
      text: "1 jam",
      value: "60",
    },
    {
      text: "6 jam",
      value: "360",
    },
    {
      text: "12 jam",
      value: "720",
    },
  ],
  filterDrift: "Saringan melayang",
  userType: [
    "Administrator",
    "dealer",
    "pengguna",
    "logistik",
    "persewaan",
    "pengguna kendaraan",
    "pengontrolan risiko",
    "profesional",
  ],
  userTypeArr: [
    "Administrator",
    "dealer",
    "pengguna",
    "logistik",
    "leasing",
    "pengguna kendaraan",
    "pengontrol risiko",
    "profesional",
  ],
  machineType: {
    '0':'Jenis tidak dikenal',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '83':'S03A',
    '84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: [
    "Alarm tidak dikenal",
    "Alarm getaran",
    "Alarm kegagalan daya",
    "Alarm daya rendah",
    "SOS bantuan",
    "Alarm kecepatan berlebih",
    "Alarm pagar keluar",
    "Alarm perpindahan",
    "Alarm daya rendah di luar",
    "Alarm regional",
    "Bongkar alarm",
    "Alarm rasa cahaya",
    "Alarm rasa magnetik",
    "Alarm anti-pelucutan senjata",
    "Alarm Bluetooth",
    "Alarm sinyal topeng",
    "Alarm stasiun pseudo",
    " Alarm pagar",
    "Alarm enklosur",
    "keluar dari alarm pagar",
    "alarm pintu terbuka",
    "mengemudi lelah",
    "masukkan dua titik",
    "dari dua titik",
    "dua titik lama lama tinggal",
    "terminal offline",
    "Ke dalam alarm pagar",
    "keluar dari alarm pagar",
    "ke alarm pagar",
    "keluar dari alarm pagar",
    "alarm kuantitas minyak",
    "ACC ON",
    "ACC OFF",
    "Alarm tabrakan",
  ],
  alarmTypeNew:  {
    '40': "Alarm suhu tinggi",
    '45': "Alarm suhu rendah",
    '50': "Alarm tegangan lebih",
    '55': "Alarm tegangan rendah",
    '60': 'Alarm parkir'
  },
  alarmNotificationType: [
    { type: "Alarm getaran", value: 1 },
    { type: "Alarm kegagalan daya", value: 2 },
    { type: "Alarm baterai rendah", value: 3 },
    { type: "SOS untuk bantuan", value: 4 },
    { type: "Alarm kecepatan", value: 5 },
    // {type:'Alarm pagar',value:6},
    { type: "Alarm perpindahan", value: 7 },
    { type: "Alarm daya rendah di luar", value: 8 },
    { type: "Alarm di luar area", value: 9 },
    { type: "Bongkar alarm", value: 10 },
    { type: "Alarm ringan", value: 11 },

    { type: "Alarm rusak", value: 13 },

    { type: "Alarm pelindung sinyal", value: 15 },
    { type: "Alarm stasiun pseudo semu", value: 16 },
    // {type:'Alarm pagar masuk (keputusan platform)',value:17},
    // {type:'Alarm pagar masuk (keputusan terminal)',value:18},
    // {type:'Alarm pagar',value:19},

    { type: "Mengemudi kelelahan", value: 21 },
    { type: "Masukkan dua poin", value: 22 },
    { type: "Dua poin", value: 23 },
    { type: "Dua poin panjang", value: 24 },
    { type: "Terminal offline", value: 25 },
    // {type:'Alarm pagar masuk (kontrol cuaca)',value:26},
    // {type:'Alarm pagar (kontrol cuaca)',value:27}
    { type: "Alarm pagar masuk", value: 26 },
    { type: "Alarm pagar", value: 27 },
    { type: "alarm kuantitas minyak", value: 30 },
    { type: "ACC ON", value: 31 },
    { type: "ACC OFF", value: 32 },
    { type: "Alarm tabrakan", value: 33 },
  ],
  alarmTypeText: "Jenis alarm",
  alarmNotification: "Pengaturan push",
  pointType: [
    "Tidak diposisikan",
    "penentuan posisi satelit",
    "penentuan posisi Beidou",
    "penentuan posisi stasiun basis",
    "penentuan posisi WIFI",
  ],

  cardType: [
    "enis",
    "titik impor satu tahun",
    "titik impor seumur hidup",
    "kartu tahun",
    "kartu seumur hidup",
    "tidak dikenal",
  ],
  // Tenggara dan barat laut
  directarray: ["Timur", "Selatan", "Barat", "Utara"],
  // Bidang arah
  directionarray: [
    "Zhengbei",
    "Timur Laut",
    "Zhengdong",
    "Tenggara",
    "Zhengnan",
    "Barat Daya",
    "Zhengxi",
    "Barat Laut",
  ],
  // Metode penentuan posisi
  pointedarray: [
    "Tidak diposisikan ",
    " GPS ",
    " stasiun basis ",
    " penentuan posisi stasiun basis ",
    " penentuan posisi WIFI ",
  ],

  //mapTerkait
  ruler: "Mulai",
  distance: "Informasi lalu lintas",
  baidumap: "Peta Baidu",
  map: "Peta",
  satellite: "Satelit",
  ThreeDimensional: "3D",
  baidusatellite: "Satelit Baidu",
  googlemap: "Google Maps",
  googlesatellite: "Satelit Google",
  fullscreen: "Layar penuh",
  noBaidumapStreetView: "Lokasi saat ini di peta Baidu tanpa tampilan jalan",
  noGooglemapStreetView: "Lokasi saat ini di Google Maps tanpa tampilan jalan",
  exitStreetView: "Keluar dari tampilan jalan",
  draw: "Gambar",
  finish: "Lengkap",
  unknown: "Tidak dikenal",
  realTimeTailAfter: "Pelacakan real-time",
  trackReply: "Lacak pemutaran",
  afterRefresh: "Setelah menyegarkan",
  rightClickEnd: "Ujung kanan, jari-jari",
  rightClickEndGoogle: "Radius ujung kanan",

  //treeTerkait
  currentUserMachineCount: "Jumlah perangkat pengguna saat ini",
  childUserMachineCount: "Berisi jumlah total perangkat sub-pengguna",

  //Jendela terkait

  electronicFence: "Pagar elektronik",
  drawTrack: "Draw track",
  showOrHide: "Tampilkan / sembunyikan",
  showDeviceName: "Nama perangkat",
  circleCustom: "Kebiasaan bulat",
  circle200m: "Putaran 200 meter",
  polygonCustom: "Kustomisasi poligon",
  drawPolygon: "Menggambar poligon",
  drawCircle: "Gambar sebuah lingkaran",
  radiusMin100:
    "Jari-jari minimum pagar yang ditarik oleh r adalah 20 meter. Harap digambar ulang. Radius pagar saat ini:",
  showAllFences: "Tampilkan semua pagar",
  lookEF: "Lihat pagar",
  noEF: "Tidak ditemukan pagar elektronik!",
  hideEF: "Pagar tersembunyi",
  blockUpEF: "Nonaktifkan pagar",
  deleteEF: "Hapus pagar",
  isStartUsing: "Apakah akan diaktifkan",
  startUsing: "Aktifkan",
  stopUsing: "Nonaktifkan",
  nowEFrange: "Rentang pagar saat ini",
  enableSucess: "Berhasil diaktifkan",
  unableSucess: "Berhasil dinonaktifkan",
  sureDeleteMorgage: "Pastikan untuk menghapus poin kedua",
  enterMorgageName: "Silakan masukkan nama posting kedua",
  openMorgagelongStayAlarm: "Buka alarm cuti panjang kedua",
  openMorgageinOutAlarm: "Buka alarm masuk dan keluar",
  setEFSuccess: "Atur pagar dengan sukses dan aktifkan jangkauan pagar",
  setElectronicFence: "Siapkan pagar elektronik",
  drawFence: "Menggambar pagar elektronik",
  drawMorgagePoint: "Gambar dua poin",
  customFence: "Pagar kustom",
  enterFenceTips: "Masukkan prompt pagar khusus",
  leaveFenceTips: "Tinggalkan custom custom prompt",
  inputFenceName: "Silakan masukkan nama pagar",
  relation: "Asosiasi",
  relationDevice: "Perangkat terkait",
  unRelation: "Tidak terkait",
  hadRelation: "Terkait",
  quickRelation: "Asosiasi satu klik",
  cancelRelation: "Putuskan tautan",
  relationSuccess: "Asosiasi yang sukses",
  cancelRelationSuccess: "Berhasil memutuskan hubungan",
  relationFail: "Kegagalan asosiasi",
  deviceList: "Daftar perangkat",
  isDeleteFence: "Apakah akan menghapus pagar",
  choseRelationDeviceFirst:
    "Silakan pilih perangkat yang ingin Anda kaitkan dulu!",
  choseCancelRelationDeviceFirst:
    "Silakan pilih perangkat yang ingin Anda batalkan tautannya terlebih dahulu!",
  selectOneTips: "Silakan pilih setidaknya satu metode peringatan",
  radius: "Radius",
  //Atur halaman kedua
  setMortgagePoint: "Atur dua poin",

  circleMortage: "Putaran dua poin",
  polygonMorgage: "Poligon dua poin",
  morgageSet: "Dua taruhan telah ditetapkan",
  operatePrompt: "Operasi cepat",
  startDrawing: "Klik untuk mulai menggambar",
  drawingtip1:
    "Klik kiri mouse untuk mulai menggambar, klik dua kali untuk mengakhiri gambar",
  drawingtip2: "Klik kiri mouse dan seret untuk mulai menggambar",

  /************************************************/
  endTrace: "Pemutaran trek selesai",
  travelMileage: "Jarak tempuh,",
  /************************************************/
  myAccount: "Akun saya",
  serviceProvide: "Penyedia layanan",
  completeInfo:
    "Silakan lengkapi informasi berikut, seperti orang yang dapat dihubungi, nomor telepon. ",
  clientName: "Nama pelanggan",
  loginAccount: "Masuk akun",
  linkMan: "Kontak",
  linkPhone: "Telepon",
  clientNameEmpty: "Nama pelanggan tidak boleh kosong!",
  updateSuccess: "Berhasil diperbarui!",
  /************************************************/
  oldPsw: "Kata sandi lama",
  newPsw: "Kata sandi baru",
  confirmPsw: "Konfirmasi kata sandi",
  pswNoSame: "Input kata sandi tidak konsisten",
  pswUpdateSuccess: "Kata sandi berhasil diubah!",
  email: "Kotak surat",
  oldPwdWarn: "Silakan masukkan kata sandi lama",
  newPwdWarn: "Silakan masukkan kata sandi baru",
  pwdConfirmWarn: "Harap konfirmasi kata sandi baru",
  /************************************************/
  //Perakitan pop-up khusus
  resetPswFailure: "Gagal mereset kata sandi",
  notification: "Prompt",
  isResetPsw_a: "Apakah kamu mau‘",
  isResetPsw_b: "’Setel ulang kata sandi?",
  pwsResetSuccess_a: "Memiliki‘",
  pwsResetSuccess_b: "’Reset kata sandi ke 123456",
  /************************************************/
  machineSearch: "Pencarian perangkat",
  search: "Cari",
  clientRelation: "Hubungan pelanggan",
  machineDetail: "Detail",
  machineDetail2: "Detail perangkat",
  machineCtrl: "Instruksi",
  transfer: "Transfer",
  belongCustom: "Pelanggan",
  addImeiFirst: "Silakan tambahkan nomor IMEI terlebih dahulu!",
  addUserFirst: "Silakan tambahkan pelanggan terlebih dahulu!",
  transferSuccess: "Transfer berhasil!",
  multiAdd: "Tambahkan dalam batch",
  multiImport: "Impor batch",
  multiRenew: "Pembaruan batch",
  //批量修改设备begin
  editDevice:'Ubah model perangkat',
  deviceAfter: 'Model perangkat (setelah modifikasi)',
  editDeviceTips:'Harap konfirmasi perangkat yang akan dimodifikasi adalah model yang sama dan tidak aktif!',
  pleaseChoseDevice: 'Pilih perangkat yang akan dimodifikasi terlebih dahulu!',
  editResult:'Edit hasil',
  successCount:'Perangkat berhasil diubah:',
  failCount:'Perangkat yang gagal:',
  //批量修改设备end
  multiDelete: "Hapus secara massal",
  canNotAddImei: "IMEI tidak ada dan tidak dapat ditambahkan ke daftar",
  importTime: "Waktu impor",
  loginName: "Nama login",
  platformDue: "Platform kedaluwarsa",
  machinePhone: "Nomor kartu SIM",
  userDue: "Pengguna kedaluwarsa",
  overSpeedAlarm: "Ikon kecepatan",
  changeIcon: "Ganti ikon",
  dealerNote: "Keterangan dealer",
  noUserDue: "Silakan masukkan waktu kedaluwarsa pengguna",
  phoneLengththan3: "Panjang telepon harus lebih dari 3",
  serialNumberInput: "Input nomor seri",

  /************************************************/
  sending: "Mengirim instruksi ..... Harap tunggu ...",
  sendFailure: "Gagal kirim!",
  ctrlName: "Nama instruksi",
  interval: "Interval waktu",
  intervalError: "Kesalahan format interval",
  currectInterval: "Harap masukkan interval waktu yang benar!",
  intervalLimit: "Atur rentang waktu interval 10-720, satuan (menit)",
  intervalLimit2:
    "Atur rentang waktu interval dari 10 hingga 5400, dalam detik",
  intervalLimit3: "Atur rentang waktu interval 5-1440, dalam satuan (menit)",
  intervalLimit4: "Atur rentang waktu interval 3-999, dalam detik",
  intervalLimit5:
    "Atur rentang waktu interval dari 10 hingga 10800 dalam satuan (detik)",
  intervalLimit1:
    "Atur rentang waktu interval 1-999, satuan (menit) 000, yang berarti mematikan mode pengembalian waktu.",
  intervalLimit6: "Atur rentang waktu interval 1-65535, dalam detik",
  intervalLimit7:
    "Tetapkan rentang waktu interval dari 1-999999 dalam satuan (detik)",
  intervalLimit8: "Atur rentang waktu interval 0-255, 0 berarti untuk menutup",
  intervalLimit9:
    "Atur rentang waktu interval dari 3 hingga 10800 dalam satuan (detik)",
  intervalLimit10: "Atur rentang waktu interval 3-86400, dalam detik",
  intervalLimit11: "Atur rentang waktu interval 180-86400, dalam detik",
  intervalLimit22: "Atur rentang waktu interval 60-86400, dalam detik",
  intervalLimit23: "Atur rentang waktu interval 5-60, dalam detik",
  intervalLimit24: "Atur rentang waktu interval 10-86400, dalam detik",
  intervalLimit25: "Atur rentang waktu interval 5-43200, dalam satuan (menit)",
  intervalLimit12:
    "Atur rentang waktu interval dari 10 hingga 60 dalam satuan (detik)",
  intervalLimit13:
    "Tetapkan rentang waktu interval 1-24 (berarti 1-24 jam) atau 101-107 (berarti 1-7 hari)",
  intervalLimit14:
    "Tetapkan rentang waktu interval10-3600，Unit (detik); standar：10s",
  intervalLimit15:
    "Tetapkan rentang waktu interval180-86400，Unit (detik); standar：3600s",
  intervalLimit16:
    "Tetapkan rentang waktu interval1-72,Unit (jam)；Default: 24 jam",
  intervalLimit17: "Pengaturan kisaran suhu -127-127, satuan (° C)",
  intervalLimit18: "Tetapkan rentang waktu interval 5-18000, satuan (detik)",
  intervalLimit19: "Atur rentang waktu interval 10-300, satuan (detik)",
  intervalLimit20: "Atur rentang waktu interval 5-399, satuan (detik)",
  intervalLimit21: "Atur rentang waktu interval 5-300, satuan (detik)",
  noInterval: "Silakan masukkan interval waktu!",
  intervalTips:
    "Harus mematikan mode pelacakan, harap setel waktu bangun alarm",
  phoneMonitorTips:
    "Setelah perintah dikirim, perangkat akan secara aktif memanggil nomor panggil balik untuk pemantauan.",
  time1: "Waktu 1",
  time2: "Waktu 2",
  time3: "Waktu 3",
  time4: "Waktu 4",
  time5: "Waktu 5",
  intervalNum: "Interval (menit)",
  sun: "Minggu",
  mon: "Senin",
  tue: "Selasa",
  wed: "Rabu",
  thu: "Kamis",
  fri: "Jumat",
  sat: "Sabtu",
  awakenTime: "Bangun waktu",
  centerPhone: "Nomor pusat",
  inputCenterPhone: "Silakan masukkan nomor pusat!",
  phone1: "Nomor satu",
  phone2: "Nomor dua",
  phone3: "Nomor tiga",
  phone4: "Nomor empat",
  phone5: "Nomor lima",
  inputPhone: "Silakan masukkan nomornya",
  offlineCtrl:
    "Perintah offline telah disimpan, dan perintah offline akan secara otomatis dikirim ke perangkat setelah perangkat online.",
  terNotSupport: "Terminal tidak mendukung",
  terReplyFail: "Balasan terminal gagal",
  machineInfo: "Informasi perangkat",

  /************************************************/
  alarmTypeScreen: "Penyaringan jenis alarm",
  allRead: "Semua sudah dibaca",
  read: "Baca",
  noAlarmInfo: "Tidak ada informasi alarm yang jelas",
  alarmTip: "Kiat: Hapus centang untuk memfilter jenis informasi alarm ini",

  /************************************************/
  updatePsw: "Kata sandi",
  resetPsw: "Setel ulang kata sandi",

  /************************************************/
  multiSell: "Penjualan massal",
  sell: "Penjualan",
  sellSuccess: "Penjualan yang sukses!",
  modifySuccess: "Dimodifikasi dengan sukses",
  modifyFail: "Modifikasi gagal",
  multiTransfer: "Transfer batch",
  multiUserExpires: "Ubah kedaluwarsa pengguna",
  batchModifying: "Sunting kumpulan",
  userTransfer: "Transfer",
  machineRemark: "Komentar",
  sendCtrl: "Kirim instruksi",
  ctrl: "Instruksi",
  ctrlLog: "Catatan instruksi",
  ctrlLogTips: "Catatan instruksi",
  s06Ctrls: [
    "Cutoff daya jarak jauh",
    "oli pemulihan jarak jauh dan listrik",
    "positioning pertanyaan",
  ],
  ctrlType: "Nama instruksi",
  resInfo: "Pesan tanggapan",
  resTime: "Waktu respon",
  ctrlSendTime: "Mengirim waktu",
  // csvUnggah file impor
  choseCsv: "Silakan pilih file csv",
  choseFile: "Pilih file",
  submit: "Kirim",
  targeDevice: "Perangkat target",
  csvTips_1: "1, simpan file excel sebagai format csv",
  csvTips_2: "2. Impor file csv ke dalam sistem.",
  importExplain: "Petunjuk impor:",
  fileDemo: "Contoh format file",

  // Baru
  sendType: "Jenis kirim",
  onlineCtrl: "Instruksi online",
  offCtrl: "Instruksi offline",
  resStatus: [
    "Tidak terkirim, kedaluwarsa, terkirim, sukses, gagal, tidak ada respons",
  ],
  /************************************************/
  addSubordinateClient: "Tambahkan pengguna bawahan",
  noSubordinateClient: "Tidak ada pengguna bawahan",
  superiorCustomerEmpty: "Silakan pilih pelanggan yang unggul",
  noCustomerName: "Silakan masukkan nama pelanggan",
  noLoginAccount: "Silakan masukkan akun login",
  noPsw: "Silakan masukkan kata sandi Anda",
  noConfirmPsw: "Silakan masukkan kata sandi konfirmasi",
  pswNotAtypism:
    "Kata sandi yang Anda masukkan dan kata sandi konfirmasi tidak konsisten!",
  addSuccess: "Berhasil ditambahkan",
  superiorCustomer: "Pelanggan yang unggul",
  addVerticalImei: "Silakan masukkan nomor IME di kolom vertikal",
  noImei: "IMEI tidak ada dan tidak dapat ditambahkan ke daftar",
  addImei_curr: "Silakan masukkan nomor IMEI, saat ini",
  no: "Satu",
  aRowAImei: "Masukkan IMEI dalam satu baris",

  /*
   * dealer  Terjemahan antarmuka dimulai
   *
   * */
  //main.js
  imeiOrUserEmpty:
    "Nomor perangkat (IMEI) / nama pelanggan tidak boleh kosong!",
  accountEmpty: "Akun tidak boleh kosong!",
  queryNoUser: "Pengguna tidak ditanya",
  queryNoIMEI: "Nomor IMEI tidak diminta",
  imeiOrClientOrAccount: "Nomor Peralatan (IMEI) / Nama Pelanggan / Nomor Akun",
  dueSoon: "Segera kedaluwarsa",
  recentlyOffline: "Baru offline",
  choseSellDeviceFirst: "Silakan pilih perangkat yang ingin Anda jual dulu!",
  choseDeviceFirst:
    "Silakan pilih perangkat yang ingin Anda transfer terlebih dahulu!",
  choseDeviceExpiresFirst:
    "Silakan pilih perangkat yang ingin Anda modifikasi terlebih dahulu!",
  choseRenewDeviceFirst:
    "Silakan pilih perangkat yang ingin Anda perpanjang dulu!",
  choseDeleteDeviceFirst: "Silakan pilih perangkat yang ingin Anda hapus dulu!",
  choseClientFirst: "Silakan pilih pelanggan yang ingin Anda transfer dulu!",

  //myClient.js
  clientList: "Daftar pelanggan",
  accountInfo: "Informasi akun",
  machineCount: "Jumlah perangkat",
  stock: "Beli",
  inventory: "Persediaan",
  subordinateClient: "Pengguna bawahan",
  datum: "Informasi",
  monitor: "Pemantauan",
  dueMachineInfo: "Informasi perangkat kedaluwarsa",
  haveExpired: "Kedaluwarsa",
  timeRange: ["Dalam 7 hari", "30 hari", "60 hari", "7-30 hari", "30-60 hari"],
  offlineMachineInfo: "Informasi perangkat offline",
  timeRange1: [
    "Dalam 1 jam",
    "1 hari",
    "7 hari",
    "30 hari",
    "60 hari",
    "60 hari atau lebih",
    "1 jam - 1 hari",
    "1-7 hari",
    "7-30 hari",
    "30-60 hari",
  ],
  offlineTime: "Waktu offline",
  includeSubordinateClient: "Berisi pengguna bawahan",

  stopMachineInfo: "Informasi perangkat stasioner",
  stopTime1: "Waktu stasioner",
  unUseMachineInfo: "Informasi perangkat tidak diaktifkan",
  unUseMachineCount: "Jumlah perangkat yang tidak diaktifkan adalah",

  sellTime: "Waktu penjualan",
  detail: "Detail",
  manageDevice: "Detail",
  details: "Detail",
  deleteSuccess: "Berhasil dihapus!",
  deleteFail: "Penghapusan gagal!",
  renewalLink: "Tautan perpanjangan",
  deleteGroupTips: "Apakah akan menghapus grup atau tidak",
  addGroup: "Tambahkan grup",
  jurisdictionRange: "Lingkup otoritas: fungsi yang dapat dimodifikasi",
  machineSellTransfer: "Transfer penjualan peralatan",
  monitorMachineGroup: "Pemantauan pengelompokan perangkat",
  jurisdictionArr: [
    "Manajemen pelanggan, manajemen pesan, pengaturan pagar, informasi alarm, manajemen akun virtual, mengeluarkan instruksi",
  ],
  confrimDelSim: "Pastikan untuk menghapus nomor kartu sim:",

  // Menu klik kanan
  sellDevice: "Peralatan penjualan",
  addClient: "Tambahkan pelanggan baru",
  deleteClient: "Hapus pelanggan",
  resetPassword: "Setel ulang kata sandi",
  transferClient: "Transfer pelanggan",
  ifDeleteClient: "Apakah akan dihapus atau tidak",

  //myAccount
  myWorkPlace: "Meja kerja",
  availablePoints: "Poin yang tersedia",
  yearCard: "Kartu tahun",
  lifetimeOfCard: "Kartu seumur hidup",
  oneyear: "Satu tahun",
  lifetime: "Seumur hidup",
  oneyearPoint: "Titik impor satu tahun",
  commonImportPoint: "Titik impor biasa",
  lifetimeImportPoint: "Titik impor seumur hidup",
  myServiceProvide: "Penyedia layanan",
  moreOperator: "Lebih banyak operasi",
  dueMachine: "Perangkat kedaluwarsa",
  offlineMachine: "Perangkat offline",
  quickSell: "Penjualan cepat",
  sellTo: "Dijual ke",
  machineBelong: "Peralatan milik",
  reset: "Setel ulang",
  targetCustomer: "Targetkan pelanggan",
  common_lifetimeImport: "Titik impor normal (0), titik impor seumur hidup (0)",
  cardType1: "Jenis kartu",
  credit: "Isi ulang poin",
  generateImportPoint: "Hasilkan poin impor",
  generateImportPointSuc: "Hasilkan titik impor dengan sukses!",
  generateImportPointFail: "Gagal menghasilkan poin impor!",
  year_lifeTimeCard: "Kartu tahun (0), kartu seumur hidup (0)",
  generateRenewPoint: "Hasilkan titik pembaruan",
  transferTo: "Pindah ke",
  transferPoint: "Poin transfer",
  transferRenewPoint: "Transfer titik pembaruan",
  pointHistoryRecord: "Catatan titik",
  newGeneration: "Generasi baru",
  operatorType: "Jenis operasi",
  consume: "Konsumsi",
  give: "Berikan",
  income: "Penghasilan",
  pay: "Pengeluaran",
  imeiErr:
    "Untuk perangkat yang akan ditanyakan, nomor IMEI harus setidaknya 6 digit terakhir!",
  accountFirstPage: "Beranda Akun",

  /*
   * dealer  Akhir terjemahan antarmuka
   *
   * */
  // Halaman 1.4.8 Terjemahan Bagian Kontrol Angin
  finrisk: "Pengendalian risiko keuangan",
  attention: "Perhatian",
  cancelattention: "Berhenti mengikuti",
  poweroff: "Matikan",
  inout: "Masuk dan keluar",
  inoutEF: "Masuk dan keluar dari pagar",
  longstay: "Tinggal kedua",
  secsetting: "Pengaturan dua titik",
  EFsetting: "Pengaturan pagar",
  polygonFence: "Pagar elektronik poligonal",
  cycleFence: "Pagar elektronik bundar",
  haveBeenSetFence: "Pagar elektronik telah ditetapkan",
  haveBeenSetPoint: "Dua taruhan telah ditetapkan",
  drawingFailed: "Gambar gagal, harap digambar ulang制",
  inoutdot: "Masuk dan keluar",
  eleStatistics: "Statistik kelistrikan",
  noData: "Tidak ada data",
  // Masuk dan keluar daftar
  accountbe: "Akun",
  SMtype: "Jenis dua titik",
  SMname: "Nama kiriman kedua",
  time: "Waktu",
  position: "Lokasi",
  lastele: "Baterai yang tersisa",
  statisticTime: "Waktu statistik",
  searchalarmType: [
    "Semua, offline, matikan, masuk dan keluar dari pagar, masuk dan keluar dari dua, dua tinggal lama",
  ],
  remarks: [
    "Taruhan kedua, perusahaan penjamin, titik pembongkaran, pasar perdagangan bekas",
  ],
  focusOnly: "Hanya fokus,",
  // [?]Deskripsi
  interpretSignal:
    "Sinyal: terakhir kali perangkat berkomunikasi dengan platform",
  interpretPosition:
    "Posisi: Waktu penentuan posisi satelit terakhir dari perangkat",
  interpretAll:
    "Perangkat online tidak diposisikan saat diam, tetapi masih akan berkomunikasi dengan platform",

  autoRecord: "Rekaman otomatis",
  /******************************************************Tetapkan instruksi untuk memulai**********************************8*/
  setCtrl: {
    text: "Instruksi pengaturan",
    value: "",
  },
  moreCtrl: {
    text: 'Lebih banyak instruksi',
    value: ''
  },
  sc_openTraceModel: {
    text: "Aktifkan mode pelacakan",
    value: "0",
  },
  sc_closeTraceModel: {
    text: "Matikan mode pelacakan",
    value: "1",
  },
  sc_setSleepTime: {
    text: "Atur waktu tidur",
    value: "2",
  },
  sc_setAwakenTime: {
    text: "Setel waktu bangun",
    value: "3",
  },
  sc_setDismantleAlarm: {
    text: "Setel alarm kerusakan",
    value: "4",
  },
  sc_setSMSC: {
    text: "Tambah nomor tengah",
    value: "5",
  },
  sc_delSMSC: {
    text: "Hapus nomor pusat",
    value: "6",
  },
  sc_setSOS: {
    text: "Tambahkan SOS",
    value: "7",
  },
  sc_delSOS: {
    text: "Hapus SOS",
    value: "8",
  },
  sc_restartTheInstruction: {
    text: "Mulai kembali perintah",
    value: "9",
  },
  sc_uploadTime: {
    text: "Setel interval unggah",
    value: "10",
  },
  /*Pengaturan waktu bangun alarm
     Pengaturan waktu pengembalian waktu
     Pengaturan alarm tamper
     Mode minggu aktif dan nonaktif*/
  sc_setAlarmClock: {
    text: "Atur waktu bangun alarm",
    value: "11",
  },
  sc_setTimingRebackTime: {
    text: "Atur waktu pengembalian waktunya",
    value: "12",
  },
  sc_openWeekMode: {
    text: "Aktifkan mode minggu",
    value: "13",
  },
  sc_closeWeekMode: {
    text: "Matikan mode minggu",
    value: "14",
  },
  sc_powerSaverMode: {
    text: "Atur mode pengembalian waktu",
    value: "15",
  },
  sc_carCatchingMode: {
    text: "Setel mode pengejaran",
    value: "16",
  },
  sc_closeDismantlingAlarm: {
    text: "Matikan pengaturan alarm tamper",
    value: "17",
  },
  sc_openDismantlingAlarm: {
    text: "Aktifkan pengaturan alarm tamper",
    value: "18",
  },
  sc_VibrationAlarm: {
    text: "Atur alarm getaran",
    value: "19",
  },
  sc_timeZone: {
    text: "Pengaturan zona waktu",
    value: "20",
  },
  sc_phoneMonitor: {
    text: "Pemantauan telepon",
    value: "21",
  },
  sc_stopCarSetting: {
    text: "Pengaturan parkir",
    value: "22",
  },
  sc_bindAlarmNumber: {
    text: "Mengikat nomor alarm",
    value: "23",
  },
  sc_bindPowerAlarm: {
    text: "Alarm kegagalan daya",
    value: "24",
  },
  sc_fatigueDrivingSetting: {
    text: "Pengaturan mengemudi yang lelah",
    value: "25",
  },
  sc_peripheralSetting: {
    text: "Pengaturan periferal",
    value: "26",
  },
  sc_SMSAlarmSetting: {
    text: "Setel alarm SMS",
    value: "27",
  },
  sc_autoRecordSetting: {
    text: "Pengaturan perekaman otomatis",
    value: "28",
  },
  sc_monitorCallback: {
    text: "Monitor panggilan balik",
    value: "29",
  },
  sc_recordCtrl: {
    text: "Merekam instruksi",
    value: "30",
  },
  sc_unbindAlarmNumber: {
    text: "Batalkan nomor alarm",
    value: "31",
  },
  sc_alarmSensitivitySetting: {
    text: "Pengaturan sensitivitas alarm getaran",
    value: "32",
  },
  sc_alarmSMSsettings: {
    text: "Pengaturan SMS alarm getaran",
    value: "33",
  },
  sc_alarmCallSettings: {
    text: "Pengaturan telepon alarm getaran",
    value: "34",
  },
  sc_openFailureAlarmSetting: {
    text: "Nyalakan alarm kegagalan daya",
    value: "35",
  },
  sc_restoreFactory: {
    text: "Kembalikan pabrik",
    value: "36",
  },
  sc_openVibrationAlarm: {
    text: "Nyalakan alarm getaran",
    value: "37",
  },
  sc_closeVibrationAlarm: {
    text: "Matikan alarm getaran",
    value: "38",
  },
  sc_closeFailureAlarmSetting: {
    text: "Matikan alarm kegagalan daya",
    value: "39",
  },
  sc_feulAlarm: {
    text: "Pengaturan alarm kuantitas oli",
    value: "40",
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: "Mode hemat daya",
    value: "41",
  },
  sc_sleepMode: {
    text: "Mode tidur",
    value: "42",
  },
  sc_alarmMode: {
    text: "Mode alarm",
    value: "43",
  },
  sc_weekMode: {
    text: "Mode minggu",
    value: "44",
  },
  sc_monitorNumberSetting: {
    text: "Pengaturan nomor monitor",
    value: "45",
  },
  sc_singlePositionSetting: {
    text: "Mode penentuan posisi tunggal",
    value: "46",
  },
  sc_timingworkSetting: {
    text: "Mode waktunya",
    value: "47",
  },
  sc_openLightAlarm: {
    text: "Lampu Sensor terbuka",
    value: "48",
  },
  sc_closeLightAlarm: {
    text: "Tutup lampu Sensor Alarm",
    value: "49",
  },
  sc_workModeSetting: {
    text: "Pengaturan Mode Kerja",
    value: "50",
  },
  sc_timingOnAndOffMachine: {
    text: "Pengaturan Switch Waktu",
    value: "51",
  },
  sc_setRealTimeTrackMode: {
    text: "Tetapkan mode pelacakan real-time",
    value: "52",
  },
  sc_setClockMode: {
    text: "Setel mode alarm",
    value: "53",
  },
  sc_openTemperatureAlarm: {
    text: "Nyalakan alarm suhu",
    value: "54",
  },
  sc_closeTemperatureAlarm: {
    text: "Matikan alarm suhu",
    value: "55",
  },
  sc_timingPostbackSetting: {
    text: "Pengaturan waktu postback",
    value: "56",
  },
  sc_remoteBoot: {
    text: "Boot jarak jauh",
    value: "57",
  },
  sc_smartTrack: {
    text: "Pelacakan cerdas",
    value: "58",
  },
  sc_cancelSmartTrack: {
    text: "Batalkan Pelacakan Cerdas",
    value: "59",
  },
  sc_cancelAlarm: {
    text: "Batalkan alarm",
    value: "60",
  },
  sc_smartPowerSavingMode: {
    text: "Setel Mode Hemat Daya Pintar",
    value: "61",
  },
  sc_monitorSetting: {
    text: "Memantau",
    value: '62'
  },
  // 指令重构新增翻译
  sc_timedReturnMode: {
    text: 'Mode pengembalian berjangka waktu',
    value: '100'
  },
  sc_operatingMode: {
      text: 'Mode operasi',
      value: '101'
  },
  sc_realTimeMode : {
      text: 'Mode pemosisian waktu nyata',
      value: '102'
  },
  sc_alarmMode : {
      text: 'Mode alarm',
      value: '103'
  },
  sc_weekMode : {
      text: 'Mode minggu',
      value: '104'
  },
  sc_antidemolitionAlarm : {
      text: 'Alarm anti-pembongkaran',
      value: '105'
  },
  sc_vibrationAlarm : {
      text: 'Alarm getaran',
      value: '106'
  },
  sc_monitoringNumber : {
      text: 'Memantau manajemen nomor',
      value: '107'
  },
  sc_queryMonitoring : {
      text: 'Nomor pemantauan kueri',
      value: '108'
  },
  sc_electricityControl : {
      text: 'Kontrol minyak dan listrik',
      value: '109'
  },
  sc_SOSnumber : {
      text: 'Manajemen nomor SOS',
      value: '110'
  },
  sc_SleepCommand : {
    text: 'Perintah tidur',
    value: '201'
  },
  sc_RadiusCommand : {
      text: 'Radius perpindahan',
      value: '202'
  },
    sc_punchTimeMode:{
        text:'打卡模式',
        value:'203'  
    },
    sc_intervelMode:{
        text:'时间段模式',
        value:'204'  
    },
    sc_activeGPS:{
        text:'激活GPS',
        value:'205'  
    },
    sc_lowPowerAlert: {
        text: 'Pengingat Baterai Rendah',
        value: '206'
    },
    sc_SOSAlert: {
        text: 'SOS报警',
        value: '207'
    },
  mc_cuscom : {
      text: 'Instruksi khusus',
      value: '1'
  },
  NormalTrack: 'Mode pelacakan normal Normal',
  listeningToNumber:'Anda yakin ingin memeriksa nomor pemantauan?',
  versionNumber:'Anda yakin ingin memeriksa nomor versi?',
  longitudeAndLatitudeInformation:'Anda yakin ingin memeriksa informasi lintang dan bujur?',
  equipmentStatus:'Anda yakin ingin memeriksa status?',
  public_parameter:'Anda yakin ingin memeriksa parameter dari？',
  GPRS_parameter:'Anda yakin ingin memeriksa parameter GPRS dari？',
  deviceName: 'Anda yakin ingin memutar perangkat？',
  SMS_alert:'Anda yakin ingin memeriksa alarm pengingat SMS?',
  theBindingNumber:'Anda yakin ingin memeriksa nomor penjilidan？',
  intervalTimeRange:'Pengaturan rentang waktu adalah 001-999, unit (menit)',
  pleaseChoose:'Silakan pilih',
  RealTimeCarChase: 'Anda yakin ingin menyetel perangkat ini ke mode mengejar mobil waktu nyata?',
  inputPhoneNumber: "Silakan masukkan nomor telepon",
  inputCorPhoneNumber: "Silakan masukkan nomor telepon yang benar",
  autoCallPhone: "Tip: Setelah perintah berhasil dijalankan, terminal akan secara otomatis memanggil nomor yang ditetapkan",
  limitTheNumberOfCellPhoneNumbers1:'Perintah ini mendukung hingga 5 nomor ponsel',
  limitTheNumberOfCellPhoneNumbers2:'Perintah ini mendukung hingga 3 nomor ponsel',
  equipmentTorestart:'Anda yakin ingin menghidupkan ulang perangkat ini',
  remindTheWay:'Cara mengingatkan',
  alarmWakeUpTime:'Waktu bangun alarm',
  alarmWakeUpTime1:'Waktu bangun alarm 1',
  alarmWakeUpTime2:'Waktu bangun alarm 2',
  alarmWakeUpTime3:'Waktu bangun alarm 3',
  alarmWakeUpTime4:'Waktu bangun alarm 4',
  sensitivityLevel:'Pilih tingkat sensitivitas',
  parking_time:'Waktu parkir',
  selectWorkingMode:'Pilih mode kerja',
  Alarm_value:'Nilai alarm',
  Buffer_value:'Nilai buffer',
  gqg_disconnect:'putuskan sambungan',
  gqg_turnOn:'Nyalakan',
  Return_interval:'Interval pengembalian',
  gq_startTime:'Waktu mulai',
  gq_restingTime:'Waktu istirahat',
  gq_Eastern:'Zona waktu timur',
  gq_Western:'Zona waktu Barat',

  gq_driver:'Alarm mengemudi kelelahan',
  gq_deviceName:'Apakah Anda yakin ingin menggulung perangkat ini?',
  gq_noteAlarm:'Anda yakin ingin memeriksa alarm pengingat SMS?',
  gq_restoreOriginal:'Anda yakin ingin mengembalikan peralatan ini ke pabrik aslinya?',
  gq_normalMode:'Mode normal',
  gq_IntelligentsleepMode:'Mode tidur pintar',
  gq_DeepsleepMode:'Mode tidur nyenyak',
  gq_RemotebootMode:'Mode boot jarak jauh',
  gq_IntelligentsleepModeTips:'Yakin ingin menyetel ke mode tidur cerdas',
  gq_DeepsleepModeTips:'Yakin ingin menyetel ke mode tidur nyenyak',
  gq_RemotebootModeTips:'Anda yakin ingin menyetel ke mode boot jarak jauh',
  gq_normalModeTips:'Apakah Anda yakin ingin mengatur ke mode normal',
  gq_sleepModeTips:'Anda yakin ingin menyetel perangkat ini ke mode tidur?',
  gq_Locatethereturnmode:'Mode pengembalian posisi',
  gq_regularWorkingHours:'Jangka waktu kerja',
  gq_AlarmType:{
      text: 'Jenis alarm',
      value: '111'
  },
  IssuedbyThePrompt:'Perintah telah dikeluarkan, mohon tunggu perangkat merespon',
  platformToinform:'Pemberitahuan platform',
    gq_shortNote:'Pemberitahuan SMS', 
  /************Perintah vernakular**********************/
  closeDismantlingAlarm: "Matikan pengaturan alarm tamper",
  openDismantlingAlarm: "Aktifkan pengaturan alarm tamper",
  closeTimingRebackMode:
    "Matikan pengaturan mode pengembalian yang diatur waktunya",
  minute: "Menit",
  timingrebackModeSetting: "Atur interval mode pengembalian waktu:",
  setWakeupTime: "Setel waktu bangun",
  weekModeSetting: "Atur mode minggu:",
  closeWeekMode: "Matikan pengaturan mode hari",
  setRealtimeTrackMode: "Setel mode pengejaran waktu-nyata",
  fortification: "Benteng",
  disarming: "Melucuti",
  settimingrebackmodeinterval: "Atur interval mode pengembalian waktu:",
  oilCutCommand: "Perintah pemotongan minyak",
  restoreOilCommand: "Pemulihan instruksi minyak dan gas",
  turnNnTheVehiclesPower: "Aktifkan daya kendaraan",
  turnOffTehVehiclesPower: "Matikan daya kendaraan",
  implementBrakes: "Rem eksekusi",
  dissolveBrakes: "Lepaskan rem",
  openVoiceMonitorSlarm: "Aktifkan suara alarm",
  closeVoiceMonitorAlarm: "Matikan suara alarm",
  openCarSearchingMode: "Aktifkan mode pencarian mobil",
  closeCarSearchingMode: "Matikan mode pencarian mobil",
  unrecognizedCommand: "Tidak dapat mengenali instruksi",
  commandSendSuccess: "Selamat, perintah eksekusi perangkat berhasil!",
  /********************************************Perintah pengaturan akhir**************************************************/

  /********************************************Perintah pertanyaan dimulai**************************************************/
  queryCtrl: {
    text: "Instruksi permintaan",
    value: "",
  },
  /*Kueri pengaturan parameter*/
  qc_softwareVersion: {
    text: "Versi perangkat lunak kueri",
    value: "1",
  },
  qc_latlngInfo: {
    text: "Informasi lintang dan bujur kueri",
    value: "2",
  },
  qc_locationHref: {
    text: "Konfigurasi parameter kueri",
    value: "3",
  },
  qc_status: {
    text: "Status permintaan",
    value: "4",
  },
  qc_gprs_param: {
    text: "Parameter GPRS kueri",
    value: "5",
  },
  qc_name_param: {
    text: "Nama",
    value: "6",
  },
  qc_SMSReminderAlarm_param: {
    text: "Alarm pengingat SMS permintaan",
    value: "7",
  },
  qc_bindNumber_param: {
    text: "Nomor penjilidan kueri",
    value: "8",
  },

  /********************************************Perintah akhir permintaan**************************************************/

  /*******************************************Perintah kontrol dimulai***************************************************/

  controlCtrl: {
    text: "Instruksi kontrol",
    value: "",
  },
  cc_offOilElectric: {
    text: "Minyak dimatikan",
    value: "1",
  },
  cc_recoveryOilElectricity: {
    text: "Pemulihan minyak dan gas",
    value: "2",
  },
  cc_factorySettings: {
    text: "Pemulihan minyak dan gas",
    value: "4",
  },
  cc_fortify: {
    text: "Benteng",
    value: "75",
  },
  cc_disarming: {
    text: "Melucuti",
    value: "76",
  },
  cc_brokenOil: {
    text: "Instruksi pemotongan minyak",
    value: "7",
  },
  cc_RecoveryOil: {
    text: "Sirkuit oli pemulihan",
    value: "8",
  },

  /*******************************************Perintah akhir kontrol***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: "Kilometer",
  mileage: "Jarak tempuh",
  importMachine: "Impor perangkat",
  transferImportPoint: "Transfer titik impor",
  machineType1: "Model peralatan",
  confirmIMEI: "Harap konfirmasi nomor dan model IMEI sebelum operasi.",
  renew: "Biaya perpanjangan",
  deductPointNum: "Poin pengurangan",
  renewSuccess: "Keberhasilan pembaruan!",
  wireType: [
    {
      text: "Wired",
      value: false,
    },
    {
      text: "Nirkabel",
      value: true,
    },
  ],
  vibrationWays: [
    {
      text: "Platform",
      value: 0,
    },
    {
      text: "Platform + SMS",
      value: 1,
    },
    {
      text: "Platform + SMS + Telepon",
      value: 2,
    },
  ],
  addMachineType: [
    {
      text: "S06",
      value: "3",
    },
    // SO6 sub-level --- mulai -----
    {
      text: "GT06",
      value: "8",
    },
    {
      text: "S08V",
      value: "9",
    },
    {
      text: "S01",
      value: "10",
    },
    {
      text: "S01T",
      value: "11",
    },
    {
      text: "S116",
      value: "12",
    },
    {
      text: "S119",
      value: "13",
    },
    {
      text: "TR06",
      value: "14",
    },
    {
      text: "GT06N",
      value: "15",
    },
    {
      text: "S101",
      value: "16",
    },
    {
      text: "S101T",
      value: "17",
    },
    {
      text: "S06U",
      value: "18",
    },
    {
      text: "S112U",
      value: "19",
    },
    {
      text: "S112B",
      value: "20",
    },
    //SO6 anak ---- end ------
    {
      text: "S15",
      value: "1",
    },
    {
      text: "S05",
      value: "2",
    },
    {
      text: "SW06",
      value: "4",
    },
    {
      text: "S001",
      value: "5",
    },
    {
      text: "S08",
      value: "6",
    },
    {
      text: "S09",
      value: "7",
    },
  ],

  /*
   * 2018-02-02
   * */
  maploadfail: "Peta saat ini gagal dimuat. Apakah Anda beralih ke peta lain?",

  /*
    2018-03-06Perintah kontrol baru
    * */
  cc_openPower: {
    text: "Nyalakan daya kendaraan",
    value: "7",
  },
  cc_closePower: {
    text: "Matikan daya kendaraan",
    value: "8",
  },
  cc_openBrake: {
    text: "Nyalakan rem kendaraan,",
    value: "9",
  },
  cc_closeBrake: {
    text: "Matikan rem kendaraan",
    value: "10",
  },
  cc_openAlmrmvoice: {
    text: "Nyalakan alarm kendaraan",
    value: "11",
  },
  cc_closeAlmrmvoice: {
    text: "Matikan alarm kendaraan",
    value: "12",
  },
  /*2018-03-06Perintah kontrol baru
   * */
  cc_openFindCar: {
    text: "Nyalakan mobil untuk menemukan mobil",
    value: "13",
  },
  cc_closeFindCar: {
    text: "Matikan pencarian mobil",
    value: "14",
  },

  /*2018-03-19
   * */
  EF: "Pagar",

  /*
    2018-03-29，
    * */
  exData: ["Listrik","tegangan", "jumlah oli", "suhu","perlawanan"],

  /*
    2018-04-10
    * */
  notSta: "Penempatan stasiun base, tidak termasuk dalam statistik ",

  // Statistik kuantitas minyak
  fuelSetting: "Pengaturan kuantitas minyak",
  mianFuelTank: "Tangki bahan bakar utama",
  auxiliaryTank: "Tangki bahan bakar sekunder",
  maximum: "Maksimum",
  minimum: "Nilai minimum",
  FullTankFuel: "Volume tangki penuh",
  fuelMinValue: 'Volume bahan bakar tidak boleh kurang dari 10L',
  standardSetting: "Pengaturan standar",
  emptyBoxMax: "Kotak kosong maksimum",
  fullBoxMax: "Kotak penuh maksimum ",
  fuelStatistics: "Statistik kuantitas minyak",
  settingSuccess: "Penyiapan yang berhasil",
  settingFail: "Penyiapan gagal",
  pleaseInput: "Silakan masuk",
  fuelTimes: "Waktu pengisian bahan bakar",
  fuelTotal: "Total pengisian bahan bakar",
  refuelingTime: 'Waktu Pengisian Bahan Bakar',
  fuelDate: "Waktu pengisian bahan bakar",
  fuel: "Jumlah minyak",
  fuelChange: "Perubahan kuantitas minyak",
  feulTable: "Tabel analisis minyak",
  addFullFilter: "Silakan tambahkan filter lengkap",
  enterIntNum: "Silakan masukkan bilangan bulat positif",
  // Statistik suhu
  tempSta: "Statistik suhu",
  tempTable: "Tabel analisis suhu",
  industrySta: "Statistik industri",
  temperature: "Suhu",
  temperature1: "Suhu1",
  temperature2: "Suhu2",
  temperature3: "Suhu3",
  tempRange: "Kisaran suhu",
  tempSetting:'Pengaturan suhu',
  tempSensor:'Sensor suhu',
  tempAlert:'Setelah sensor suhu dimatikan, ia tidak akan menerima data suhu!',
  phoneNumber: "Nomor ponsel",
  sosAlarm: "Alarm SOS",
  undervoltageAlarm: "Alarm tegangan rendah",
  overvoltageAlarm: "Alarm tegangan lebih",
  OilChangeAlarm: "Kuantitas ganti oli berubah",
  accDetection: "Deteksi ACC",
  PositiveAndNegativeDetection: "Deteksi positif dan negatif",
  alermValue: "Nilai alarm",
  bufferValue: "Nilai penyangga",
  timeZoneDifference: "Perbedaan zona waktu",
  meridianEast: "Meridian Timur",
  meridianWest: "Meridian barat",
  max12hour: "Perbedaan input tidak boleh lebih dari 12 jam",

  trackDownload: "Lacak unduhan",
  download: "Unduh",
  multiReset: "Reset massal",
  resetSuccess: "Berhasil mengatur ulang",
  multiResetTips:
    "Data uji seperti waktu dan trek aktivasi perangkat akan dihapus setelah reset, dan status perangkat akan diatur ulang ke online atau offline.",
  point: "Point",
  myplace: "Tempat saya",
  addPoint: "Tambahkan poin",
  error10018: "Jumlah poin impor tidak mencukupi",
  error110:'Objek tidak ada',
  error109:'Melampaui batas maksimum',
  error20013:'Jenis perangkat tidak ada',
  error90001:'Nomor seri jenis perangkat tidak boleh kosong',
  error20003:'Imei tidak boleh kosong',
  inputName: "Silakan masukkan nama",
  virtualAccount: "Akun virtual",
  createTime: "Waktu pembuatan",
  permission: "Izin",
  permissionRange: "Lingkup otoritas",
  canChange: "Fungsi yang dapat dimodifikasi",
  fotbidPassword: "Kata sandi",
  virtualAccountTipsText:
    "Saat membuat akun virtual, itu adalah akun alias dari akun dealer yang terdaftar saat ini, dan dapat mengatur izin untuk akun virtual tersebut.",
  noOperationPermission: "Akun virtual tidak memiliki hak operasi",
  number: "Nomor",
  rangeSetting: "Pengaturan rentang",
  setting: "Pengaturan",
  // 1.6.1
  duration: "Durasi",
  voltageSta: "Statistik tegangan",
  voltageAnalysis: "Analisis tegangan",
  voltageEchart: "Tabel analisis tegangan",
  platformAlarm: "Alarm platform",
  platformAlarm1: "telepon",
  platformAndPhone: 'telepon+alarm platform',
  smsAndplatformAlarm: "SMS + alarm platform",
  smsAndplatformAlarm1: "SMS",
  smsAndplatformAlarmandPhone: "Platform alarm + SMS + telepon",
  smsAndplatformAlarmandPhone1: " SMS + telepon",
  more_speed: "Kecepatan",
  attribute: "Atribut",
  profession: "Profesional",
  locationPoint: "Titik jangkar",
  openPlatform: "Platform terbuka",
  experience: "Saya ingin mengalami",
  onlyViewMonitor: "Hanya dapat melihat pemantauan",

  // 1.6.3
  inputAccountOrUserName: "Silakan masukkan nomor akun atau nama pengguna",
  noDeviceTips:
    "Tidak menemukan informasi peralatan yang relevan, periksa titik pelanggan",
  noUserTips:
    "Tidak menemukan informasi pengguna yang relevan, periksa poin peralatan",
  clickHere: "Di sini",
  // 1.6.4
  pointIntervalSelect: "Interval titik lintasan",
  payment: "Pembayaran",
  pleaceClick: "Silakan klik",
  paymentSaveTips:
    "Tip Keselamatan: Harap konfirmasi dengan penyedia layanan validitas tautannya",
  //1.6.4 ditambahkan
  fuelAlarmValue: "Nilai alarm kuantitas minyak",
  fuelConsumption: "Konsumsi bahan bakar",
  client: "Pelanggan",
  create: "Baru",
  importPoint: "Titik impor",
  general: "Umum",
  lifelong: "Seumur hidup",
  renewalCard: "Kartu perpanjangan",
  settingFuelFirst:
    "Silakan tentukan nilai alarm kuantitas minyak sebelum mengirim perintah!",
  overSpeedSetting: "Pengaturan kecepatan",
  kmPerHour: "Km / jam",
  times: "Waktu",
  total: "Total",
  primary: "Tuhan",
  minor: "Wakil",
  unActiveTips: "Perangkat tidak diaktifkan dan tidak dapat digunakan.",
  arrearsTips: "Perangkat menunggak dan tidak dapat menggunakan fitur ini",
  //1.6.5
  loading: "Memuat data ....",
  expirationReminder: "Pengingat kedaluwarsa",
  projectName: "Nama proyek",
  expireDate: "Waktu kedaluwarsa",
  changePwdTips:
    "Kata sandi Anda terlalu sederhana dan ada risiko keamanan. Segera ubah kata sandi Anda.",
  pwdCheckTips1: "Saran adalah 6-20 huruf, angka atau simbol",
  pwdCheckTips2: "Kata sandi yang Anda masukkan terlalu lemah.",
  pwdCheckTips3: "Kata sandi Anda bisa lebih rumit.",
  pwdCheckTips4: "Kata sandi Anda aman.。",
  pwdLevel1: "Lemah",
  pwdLevel2: "Sedang",
  pwdLevel3: "Kuat",
  comfirmChangePwd: "Tentukan kata sandi untuk diubah",
  notSetYet: "Belum diatur",
  // 1.6.6
  liter: "l",
  arrearageDayTips: "Karena dalam beberapa hari",
  todayExpire: "Jatuh tempo hari ini",
  forgotPwd: "Lupa kata sandi Anda?",
  forgotPwdTips: "Silakan hubungi penjual untuk mengubah kata sandi Anda.",
  //1.6.7
  commonProblem: "Masalah umum",
  instructions: "Tutorial",
  webInstructions: "Instruksi web",
  appInstructions: "Instruksi aplikasi",
  acceptAlarmNtification: "Terima pemberitahuan alarm",
  alarmPeriod: "Periode alarm",
  whiteDay: "Siang hari",
  blackNight: "Malam yang gelap",
  allDay: "Sepanjang hari",
  alarmEmail: "Surat alarm",
  muchEmailTips:
    "Beberapa kotak surat dapat dimasukkan, dipisahkan oleh simbol ‘;’.",
  newsCenter: "Isi dokumen masalah",
  allNews: "Semua berita",
  unReadNews: "Pesan belum dibaca",
  readNews: "Baca pesan",
  allTypeNews: "Semua jenis pesan",
  alarmInformation: "Semua jenis pesan",
  titleContent: "Konten judul",
  markRead: "Ditandai baca",
  allRead: "Semua sudah dibaca",
  allDelete: "Hapus semua",
  selectFirst: "Silakan pilih sebelum melanjutkan!",
  updateFail: "Pembaruan gagal!",
  ifAllReadTips: "Apakah sudah siap untuk membaca?",
  ifAllDeleteTips: "Apakah semua sudah dihapus?",
  stationInfo: "Informasi stasiun",
  phone: "Ponsel",
  //1.6.72
  plsSelectTime: "Silakan pilih waktu!",
  customerNotFound: "Tidak dapat menemukan klien",
  //1.6.8
  Postalcode: "posisi",
  accWarning: "Diter telpon polisi",
  canInputMultiPhone:
    "Banyak nomor ponsel bisa dimasukkan untuk digunakan; pemisah",
  noLocationInfo: "Perangkat ini belum memiliki informasi lokasi.",
  //1.6.9
  fenceName: "nama pagar",
  fenceManage: "Manajemen Pagar",
  circular: "lingkaran",
  polygon: "poligon",
  allFence: "Semua pagar",
  shape: "bentuk",
  stationNews: "Pesan Situs",
  phonePlaceholder:
    'Anda dapat memasukkan beberapa nomor, dipisahkan dengan ","',
  addressPlaceholder:
    'Silakan masukkan alamat dan kode pos secara berurutan, dipisahkan dengan ","',
  isUnbind: "Apakah Anda ingin memutuskan tautan",
  alarmCar: "Kendaraan alarm",
  alarmAddress: "lokasi alarm",
  chooseAtLeastOneTime: "Pilih setidaknya satu kali",
  alarmMessage: "Tidak ada detail informasi alarm ini",
  navigatorBack: "kembali ke atasan",
  timeOverMessage:
    "Waktu kedaluwarsa pengguna tidak boleh lebih dari waktu kedaluwarsa platform",
  //1.7.0
  userTypeStr: "Jenis Pengguna",
  newAdd: "Baru",
  findAll: "Total",
  findStr: "Mencocokkan Data",
  customColumn: "Sesuaikan",
  updatePswErr: "Kata Sandi yang Diperbarui Gagal",
  professionalUser: "Pengguna Profesional atau Tidak?",
  confirmStr: "Konfirmasi",
  inputTargetCustomer: "Masukkan Target Pelanggan",
  superiorUser: "Pengguna Unggul",
  speedReport: "Laporan Kecepatan",
  createAccount: "Buat Akun",
  push: "Dorong",
  searchCreateStr:
    "Ini akan mengoperasikan akun dan mentransfer perangkat ke akun ini.",
  allowIMEI: "Izinkan Login IMEI",
  defaultPswTip: "Kata sandi default adalah 6 digit terakhir IMEI",
  createAccountTip: "Akun yang dibuat dan perangkat yang ditransfer berhasil",
  showAll: "Tampilkan Semua",
  bingmap: "Peta Bing",
  areaZoom: "Zoom",
  areaZoomReduction: "Kembalikan zoom",
  reduction: "Pengurangan",
  saveImg: "Simpan sebagai gambar",
  fleetFence: "Armada pagar",
  alarmToSub: "Alarm notifikasi bawahan",
  bikeFence: "Pagar sepeda",
  delGroupTip:
    "Penghapusan gagal, harap hapus tempat-tempat menarik terlebih dahulu! ",
  isExporting: "Mengekspor ...",
  addressResolution: "Resolusi alamat ...",
  simNOTip: "Nomor kartu SIM hanya dapat berupa angka",
  unArrowServiceTip:
    "Waktu kedaluwarsa pengguna untuk perangkat berikut ini lebih besar daripada waktu kedaluwarsa platform, silakan pilih kembali. Nomor perangkat adalah:",
  platformAlarmandPhone: "Platform Alarm + Tel",
  openLightAlarm: "Lampu Sensor terbuka",
  closeLightAlarm: "Tutup lampu Sensor Alarm",
  ACCAlarm: "Alarm ACC",
  translateError: "Transfer gagal, pengguna target tidak memiliki izin",
  distanceTip: "Klik OK, klik dua kali untuk menyelesaikan",
  workMode: "Mode Kerja",
  workModeType: "0: Mode Normal; 1 Mode Tidur Cerdas; 2 Mode Tidur Pulas",
  clickToStreetMap: "Klik untuk membuka peta tampilan jalan",
  current: "arus",
  remarkTip:
    "Komentar：Jangan menjual kartu dari jenis yang berbeda secara bersamaan",
  searchRes: "Hasil Pencarian",
  updateIcon: "Ubah ikon",
  youHaveALarmInfo: "Anda memiliki pesan peringatan",
  moveInterval: "Interval pergerakan",
  staticInterval: "Interval statis ",
  notSupportTraffic: "Coming soon.",
  ignite: "Acc ON",
  flameout: "Acc OFF",
  generateRenewalPointSuc: "Hasilkan poin pembaruan dengan sukses",
  noGPSsignal: "Tidak diposisikan",
  imeiErr2: "Silakan masukkan setidaknya 6 digit terakhir dari nomor imei",
  searchCreateStr2:
    "Ini akan membuat akun dan mentransfer perangkat ini ke nama akun",
  addUser: "Tambahkan pengguna",
  alarmTemperature: "Nilai suhu alarm",
  highTemperatureAlarm: "Alarm suhu tinggi",
  lowTemperatureAlarm: "Alarm suhu rendah",
  temperatureTip: "Silakan masukkan nilai suhu！",
  locMode: "Mode penentuan posisi",
  imeiInput: "Silakan masukkan nomor IMEI",
  noResult: "Tidak ada hasil yang cocok",
  noAddressKey: "Untuk sementara tidak dapat memperoleh informasi alamat",
  deviceGroup: "Pengelompokan",
  shareManage: "Bagikan",
  lastPosition: "Posisi terakhir",
  defaultGroup: "Default",
  tankShape: "Bentuk tangki",
  standard: "Standar",
  oval: "Lonjong",
  irregular: "Tidak teratur",
  //1.8.4
  inputAddressOrLoc: "Masukkan alamat / lintang dan bujur",
  inputGroupName: "Masukkan nama grup",
  lock: "Mengunci",
  shareHistory: "Bagikan trek",
  tomorrow: "Besok",
  threeDay: "Tiga hari",
  shareSuccess: "Berhasil membuat tautan berbagi",
  effective: "Efektif",
  lapse: "Selang",
  copyShareLink: "Salin tautan berbagi",
  openStr: "Buka",
  closeStr: 'Mati',
  linkError: "Tautan berbagi ini telah kedaluwarsa",
  inputUserName: "Silakan masukkan nama pelanggan",
  barCodeStatistics: "Statistik Kode Batang",
  barCode: "Kode batang",
  sweepCodeTime: "Waktu pemindaian",
  workModeType2:
    "1 mode tidur cerdas；2 Mode tidur nyenyak；3 Mode nyala / mati daya jarak jauh",
  remoteSwitchMode: "Mode sakelar jarak jauh",
  saleTime: "Tanggal penjualan",
  onlineTime: "Tanggal peluncuran",
  dayMileage: "Jarak tempuh hari ini",
  imeiNum: "IMEI",
  overSpeedValue: "Ambang batas kecepatan berlebih",
  shareNoOpen: "Tautan berbagi tidak diaktifkan",
  addTo2: "Tambahkan",
  Overdue: "Kedaluwarsa",
  openInterface: "Buka antarmuka",
  privacyPolicy: 'Kebijakan privasi',
  serviceTerm: 'Terms Of Service',
  importError:
    "Gagal menambahkan, nomor perangkat (IMEI) harus berupa angka 15 digit",
  importResult: "Impor hasil",
  totalNum: "total",
  successInfo: "sukses",
  errorInfo: "Gagal",
  repeatImei: "IMEI repeat",
  includeAccount: "Include sub accounts",
  termasukAkun: "sub-akun",
  formatError: "Bentuknya salah",
  importErrorInfo: "Silakan masukkan 15 digit nomor IMEI",
  totalMileage: "Total Mileage",
  totalOverSpeed: "Total overspeed (kali)",
  totalStop: "Total stop (kali)",
  totalOil: "Total minyak",
  timeChoose: "Pemilihan waktu",
  intervalTime: "Interval time",
  default: "Default",
  idleSpeedStatics: "Statistik kecepatan siaga",
  offlineStatistics: 'Statistik Offline',
  idleSpeed: "Idle speed",
  idleSpeedTimeTip1: "Waktu idle tidak boleh kosong",
  idleSpeedTimeTip2: "Waktu idle harus bilangan bulat positif",
  averageSpeed: "Kecepatan rata-rata",
  averageOil: "Konsumsi bahan bakar rata-rata",
  oilImgTitle: "Bagan analisis minyak",
  oilChangeDetail: "Rincian perubahan bahan bakar",
  machineNameError: "Nama perangkat tidak boleh mengandung simbol khusus (/ ')",
  remarkError: "Informasi komentar tidak boleh melebihi 50 kata",
  defineColumnTip: "Periksa hingga 12 item",
  pswCheckTip: "Saran adalah kombinasi 6-20 digit, huruf dan simbol",
  chooseGroup: "Silakan pilih grup",
  chooseAgain:
    "Anda hanya dapat menanyakan data selama enam bulan terakhir, silakan pilih lagi！",
  noDataTip: "Tidak ada data！",
  noMachineNameError: "Silakan pilih perangkat！",
  loginAccountError: "Akun login tidak boleh 15 digit！",
  includeExpire: "Yang kedaluwarsa",
  groupNameTip: "Nama grup tidak boleh kosong！",
  outageTips: "Apakah Anda yakin minyak terputus?",
  powerSupplyTips: "Anda yakin mengembalikan minyak?", //印度尼西亚
  centerPhoneTips: "Silakan masukkan nomornya",
  centerPhoneLenTips: "Silakan masukkan 8-20 digit",
  passworldillegal: "Ada karakter ilegal",
  // 2.0.0 POI，权限版本
  singleAdd:'Tambahkan tunggal',
  batchImport:'Impor Batch',
  name:'Nama',
  icon:'Ikon',
  defaultGroup:'Grup default',
  remark:'Komentar',
  uploadFile:'Unggah file',
  exampleDownload:'Download contoh',
  uploadFiles:'Unggah file',
  poiTips1:'Anda dapat mengimpor POI dengan mengunggah file Excel dengan informasi terkait. Silakan ikuti format contoh untuk menyiapkan file',
  poiTips2:'Name: Diperlukan, tidak lebih dari 32 karakter',
  poiTips3:'Ikon: wajib diisi, masukkan 1,2,3,4',
  poiTips4:'Latitude：Wajib',
  poiTips5:'Longitude：Wajib',
  poiTips6:'Nama grup: Opsional, tidak lebih dari 32 karakter. Jika nama grup tidak diisi, poin POI milik grup default. Jika nama grup yang diisi konsisten dengan nama grup yang dibuat, poin POI milik grup yang dibuat. Nama grup belum dibuat, sistem akan menambahkan grup',
  poiTips7:'Komentar: Opsional, tidak lebih dari 50 karakter',
  // 权限相关
  roleLimit: 'Izin Peran',
  operateLog: 'Log operasi',
  sysAccountManage: 'Akun otoritas',
  rolen: 'Peran',
  rolename: 'Nama peran',
  addRole: 'Peran baru',
  editRole: 'Edit peran',
  deleteRole: 'Hapus peran',
  delRoleTip: 'Anda yakin ingin menghapus peran ini?',
  delAccountTip: 'Anda yakin ingin menghapus akun ini?',
  limitconfig: 'Profil Hak',
  newAccountTip1: 'Akun otoritas mirip dengan akun virtual lama, dan merupakan sub-akun dari administrator. Administrator dapat membuat akun otoritas dan menetapkan peran yang berbeda ke akun otoritas, sehingga akun yang berbeda dapat melihat konten dan operasi yang berbeda di platform.',
  newAccountTip2: 'Proses membuat akun izin:',
  newAccountTip31: '1. Di halaman manajemen peran,',
  newAccountTip32: 'Peran baru',
  newAccountTip33: ', Dan mengkonfigurasi izin untuk peran tersebut;',
  newAccountTip4: '2. Pada halaman manajemen akun otoritas, buat akun otoritas baru dan tetapkan peran ke akun tersebut.',
  newRoleTip1: 'Administrator dapat membuat peran dan mengonfigurasi izin operasi yang berbeda untuk peran yang berbeda untuk memenuhi kebutuhan bisnis dalam skenario yang berbeda.',
  newRoleTip2: 'Misalnya, konfigurasikan apakah peran keuangan memiliki izin untuk mencari dan memantau, apakah memiliki izin untuk menambahkan pelanggan, apakah memiliki izin untuk mengubah informasi perangkat, dll.',
  "refuelrate": "Tingkat pengisian bahan bakar",
  "refuellimit": "Jika kenaikan oli per menit lebih besar dari xxxxL dan kurang dari xxxxL, ini dianggap sebagai pengisian bahan bakar.",
  "refueltip": "Tingkat pengisian bahan bakar maksimum tidak boleh kurang dari tingkat pengisian bahan bakar minimum!",
  viewLimitConf: 'Lihat pengaturan izin',
  viewLimit: 'Lihat izin',
  newSysAcc: 'Akun sistem baru',
  editSysAcc: 'Edit akun izin',
  virtualAcc: 'Akun virtual',
  oriVirtualAcc: 'Akun virtual asli',
  virtualTip: 'Modul akun virtual telah ditingkatkan menjadi modul akun sistem, harap buat akun sistem baru',
  operaTime: 'Waktu operasi',
  ipaddr: 'alamat IP',
  businessType: 'jenis bisnis',
  params: 'Parameter permintaan',
  operateType: 'Jenis operasi',
  uAcc: 'akun pengguna',
  uName: 'nama pengguna',
  uType: 'tipe Pengguna',
  logDetail: 'Detail log',
  delAccount: 'Hapus akun',
  modifyTime: 'Ubah Waktu',
  unbindlimit: 'Tidak dapat membuat pagar elektronik sekali klik tanpa izin perangkat terkait!',
  setSmsTip: 'Jika Anda mengatur notifikasi SMS, Anda perlu mengaktifkan notifikasi platform terlebih dahulu; jika pengiriman notifikasi platform berhasil, notifikasi SMS tidak berhasil, Anda perlu mengaktifkan notifikasi SMS lagi',
  cusSetComTip: 'Penafian: Risiko yang dibawa oleh instruksi khusus tidak ada hubungannya dengan platform',
  cusSetComPas: 'Silakan masukkan kata sandi akun login saat ini',
  cusSetComDes1: 'Instruksi kustom hanya mendukung instruksi online.',
  cusSetComDes2: 'Jika perangkat tidak merespon dalam dua menit setelah mengirim perintah, proses dihentikan dan status perintah dinilai tidak ada respon.',
  cueSetComoffline: 'Perangkat tidak merespons, dan pengiriman perintah kustom gagal!',
  fbType: 'Jenis umpan balik',
  fbType1: 'Penasihat',
  fbType2: 'Malfungsi',
  fbType3: 'pengalaman pengguna',
  fbType4: 'Saran fitur baru',
  fbType5: 'lain',
  upload: 'Unggah',
  uploadImg: 'unggah gambar',
  uploadType: 'Harap unggah file dengan jenis .jpg .png .jpeg .gif',
  uploadSize: 'File unggahan tidak boleh lebih dari 3M',
  fbManager: 'Manajemen umpan balik',
  blManager: 'Manajemen pengumuman',
  fbUploadTip: 'Pilih jenis umpan balik',
  menuPlatform: "Berita platform",
  menuFeedback: "Umpan balik",
  menuBulletin: "Pengumuman platform",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "Perilaku Mengemudi",
  BtyjdfghtwsrgGHFEEGRDAF: "Percepatan Cepat",
  BtyuwyfgrWERERRTHDAsdDF: "Perlambatan Cepat",
  Be2562h253grgsHHJDbRDAF: "Belok Tajam",
  celTemperature:'Suhu Celsius.'
};
// 权限tree
lg.limits = {
  "ACC_statistics": "Statistik ACC",
  "Account_Home": "Beranda Akun",
  "Add": "Baru",
  "Add_POI": "Tambahkan POI",
  "Add_customer": "Tambahkan pelanggan baru",
  "Add_device_group": "Tambahkan grup perangkat",
  "Add_fence": "Tambahkan pagar",
  "Add_sharing_track": "Tambahkan trek berbagi",
  "Add_system_account": "Akun izin baru",
  "Alarm_details": "Ikhtisar alarm",
  "Alarm_message": "Pesan alarm",
  "Alarm_overview": "Ikhtisar alarm",
  "Alarm_statistics": "Statistik alarm",
  "All_news": "Semua berita",
  "Associated_equipment": "Perangkat terkait",
  "Available_points": "Poin yang tersedia",
  "Barcode_statistics": "Statistik Kode Batang",
  "Batch_Import": "Impor Batch",
  "Batch_renewal": "Pembaruan batch",
  "Batch_reset": "Reset massal",
  "Bulk_sales": "Penjualan massal",
  "Call_the_police": "Alarm",
  "Customer_details": "Detil pelanggan",
  "Customer_transfer": "Transfer pelanggan",
  "Delete_POI": "Hapus POI",
  "Delete_account": "Hapus akun",
  "Delete_customer": "Hapus pelanggan",
  "Delete_device": "Hapus perangkat",
  "Delete_device_group": "Hapus grup perangkat",
  "Delete_fence": "Hapus pagar",
  "Delete_role": "Hapus peran",
  "Device_List": "Daftar perangkat",
  "Device_grouping": "Pengelompokan",
  "Device_transfer": "Transfer perangkat",
  "Due_reminder": "Pengingat kedaluwarsa",
  "Edit_details": "Edit detail",
  "Equipment_management": "Manajemen peralatan",
  "My_clinet": "Manajemen peralatan",
  "Fence": "Pagar",
  "Fence_management": "Manajemen Pagar",
  "Generate": "menghasilkan",
  "Generate_lead-in_points": "Hasilkan poin impor",
  "Generate_renewal_points": "Hasilkan titik pembaruan",
  "Have_read": "Kosong",
  "Idle_speed_statistics": "Statistik kecepatan siaga",
  "Import": "Impor",
  "Import_Device": "Impor perangkat",
  "Industry_Statistics": "Statistik industri",
  "Location_monitoring": "Pemantauan lokasi",
  "Log_management": "Pemantauan lokasi",
  "Mark_read": "Ditandai baca",
  "Menu_management": "Manajemen menu",
  "Message_Center": "Isi dokumen masalah",
  "Mileage_statistics": "Statistik jarak tempuh",
  "Modify_POI": "Ubah POI",
  "Modify_device_details": "Ubah detail perangkat",
  "Modify_device_group": "Ubah grup perangkat",
  "Modify_role": "Ubah peran",
  "Modify_sharing_track": "Ubah trek berbagi",
  "Modify_user_expiration": "Ubah kedaluwarsa pengguna",
  "More": "Lebih banyak",
  "My_client": "Klien saya",
  "New_role": "Peran baru",
  "New_users": "Tambahkan pengguna",
  "Oil_statistics": "Statistik kuantitas minyak",
  "POI_management": "Manajemen POI",
  "Points_record": "Catatan titik",
  "Push": "Dorong",
  "Quick_sale": "Penjualan cepat",
  "Renew": "Biaya perpanjangan",
  "Replay": "Pemutaran",
  "Role_management": "Manajemen peran",
  "Run_overview": "Tinjauan operasi",
  "Running_statistics": "Statistik operasional",
  "Sales_equipment": "Peralatan penjualan",
  "Set_expiration_reminder": "Setel pengingat kedaluwarsa",
  "Share_track": "Bagikan trek",
  "Sharing_management": "Bagikan",
  "Speeding_detailed_list": "Daftar ngebut",
  "Statistical_report": "Laporan statistik",
  "Stay_detailed_list": "Daftar tetap",
  "System_account_management": "Akun otoritas",
  "Temperature_statistics": "Statistik suhu",
  "Transfer": "Transfer",
  "Transfer_group": "Transfer grup",
  "Transfer_point": "Transfer titik impor",
  "Transfer_renewal_point": "Transfer titik pembaruan",
  "Trip_statistics": "Statistik perjalanan",
  "Unlink": "Batalkan tautan",
  "View": "Lihat",
  "View_POI": "Lihat POI",
  "View_device_group": "Lihat grup perangkat",
  "View_fence": "Lihat pagar",
  "View_role": "Lihat peran",
  "View_sharing_track": "Lihat trek berbagi",
  "Virtual_account": "Akun virtual",
  "Voltage_analysis": "Analisis tegangan",
  "Voltage_statistics": "Statistik tegangan",
  "batch_deletion": "Hapus secara massal",
  "change_Password": "Kata sandi",
  "delete": "Hapus",
  "edit": "Edit",
  "instruction": "Instruksi",
  "modify": "Ubah",
  "monitor": "Pemantauan",
  "my_account": "akun saya",
  "reset_Password": "Setel ulang kata sandi",
  "share_it": "Bagikan",
  "sub_user": "Pengguna bawahan",
  "track": "Pelacakan",
  "Custom_Order": "Instruksi khusus",
  "GeoKey_Manager": "Manajemen GeoKey",
  "GeoKey_Update": "memodifikasi",
  "GeoKey_Delete": "menghapus",
  "GeoKey_Add": "Tambahkan",
  "GeoKey_View": "Melihat",
  "feedback_manager": "Manajemen umpan balik",
  "feedback_list": "Melihat",
  "feedback_handle": "Memproses umpan balik",
  "proclamat_manager": "Manajemen pengumuman",
  "proclamat_manager_list": "Lihat pengumuman",
  "proclamat_manager_update": "Pengumuman modifikasi",
  "proclamat_manager_delete": "Hapus pengumuman",
  "proclamat_manager_save": "Pengumuman baru",
  "device_update_batch_model": "Model perangkat modifikasi batch"
}

// Isi dokumen masalah
lg.questionDocumentArr = [
  [
    "A: Silakan matikan",
    "A: Setelah Anda mematikan mobil, gunakan pena listrik dan meter universal untuk mengukur apakah tegangan saluran mobil yang terhubung sesuai dengan pelacak GPS. Kisaran tegangan umumnya 9-36V. Tindakan pencegahan pemasangan kabel: Personel pemasangan dan pemasangan kabel harus memiliki pemahaman tentang saluran mobil dan memiliki kemampuan tertentu untuk menghindari kerusakan pada mobil Anda yang disebabkan oleh pemasangan kabel yang tidak benar. ",
  ],
  [
    "T: Perangkat kabel atau perangkat pelacakan real-time nirkabel, panggilan telepon atau perangkat status boot latar belakang IoT offline ",
    "A: 1. Kirim pesan teks untuk memulai kembali, amati beberapa menit untuk melihat apakah itu online. Umumnya mengirim RESET # silakan hubungi dealer untuk menentukan.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Koneksi jaringan tidak stabil. Harap pindahkan mobil ke area sinyal yang bagus.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Setelah langkah-langkah di atas tidak tersedia, Anda harus menghubungi operator seluler untuk memeriksa apakah kartu tidak normal.",
  ],
  [
    "T: Perangkat offline dalam batch pada awal dan akhir bulan.",
    "A: Silakan periksa apakah kartu menunggak. Jika tunggakan, harap isi ulang tepat waktu dan lanjutkan menggunakannya.",
  ],
  [
    "T: Mobil sedang mengemudi, posisi GPS online tidak diperbarui.",
    "<br/>&nbsp;&nbsp;&nbsp;&nbsp;A: 1. Perangkat kabel dapat mengirim SMS STATUS # untuk memeriksa status penerimaan sinyal satelit, lihat GPS: pencarian satelit adalah sinyal satelit telah dalam pencarian, situasi ini perlu memeriksa lokasi instalasi, apakah itu dipasang sesuai dengan instruksi. Menghadap ke atas, tidak ada penutup logam di bagian atas.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Kirim SMS STATUS #, status pengembalian adalah GPS: MATI, silakan kirim PABRIK # lagi, setelah menerima balasan OK, amati 5 menit untuk melihat apakah ada pembaruan.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Menurut dua metode di atas, kesalahan tidak dapat dihilangkan. Hubungi penjual untuk perbaikan.",
  ],
  [
    "T: Mengapa platform pengisian daya mengisi waktu lama masih menunjukkan bahwa itu tidak penuh?",
    "A: Tampilan daya platform didasarkan pada informasi yang dikembalikan oleh perangkat untuk membuat analisis data untuk menentukan daya perangkat saat ini. Dalam beberapa kasus khusus, solusi kesalahan tampilan daya akan muncul:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Data daya perangkat dan data posisi perangkat diunggah bersama-sama. Jika baterai tidak berubah dalam waktu lama, silakan: 1 Bawa perangkat Anda bergerak 100-300 meter untuk memperbarui informasi lokasi perangkat. Data dan data lokasi dapat diumpankan kembali bersama ke platform untuk menjadi refresh tampilan daya.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Menurut perubahan indikator daya, tentukan apakah sudah terisi penuh (ambil S15 sebagai contoh). Langkah-langkah operasi adalah sebagai berikut: 1 mengisi daya 8-10 jam, kemudian indikator daya berubah menjadi kuning hijau, setelah mencabut kabel pengisi daya, masukkan kabel pengisian. Indikator daya akan berubah menjadi hijau kuning dalam 15 menit hingga terisi penuh, silakan lihat manual untuk model lain.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, Pengisian untuk waktu yang lama juga penuh dengan listrik, situasi ini mungkin tegangan plug pengisian lebih rendah dari 1A, silakan gunakan tegangan 5V, kepala pengisian 1A untuk mengisi daya 8-10 jam",
  ],
  [
    "T: Perintah cutoff daya GPS telah berhasil dikeluarkan. Mengapa mobil masih tidak rusak?",
    "A: Setelah perintah mematikan berhasil dikeluarkan, peralatan harus dieksekusi dalam kondisi berikut:: <br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Pastikan pengkabelan peralatan sudah benar dan ikuti diagram pengkabelan manual.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, peralatan bekerja secara normal, dalam keadaan statis atau mengemudi, memiliki posisi, tidak offline, dan kecepatan kendaraan tidak melebihi 20 kilometer, jika kendaraan offline, tidak diposisikan atau kecepatan kendaraan melebihi 20 kilometer, yaitu, Jika perintah pemutusan daya berhasil dikeluarkan, terminal tidak akan menjalankan Q: Produk nirkabel dipasang untuk pertama kalinya dalam tiga tahun, dan perangkat layar tidak diposisikan atau online.",
  ],
  [
    "T: Tiga tahun produk nirkabel dipasang untuk pertama kalinya, perangkat layar tidak diposisikan atau online.",
    "答：<br/>&nbsp;&nbsp;&nbsp;&nbsp;A: 1. Nyalakan sakelar untuk mengamati apakah lampu indikator berkedip. Indikator kuning dan hijau berkedip pada waktu yang sama seperti normal, berkedip lambat pada sinyal pencarian, dan perangkat tidak menyala. (Status model yang berbeda akan berbeda. Silakan lihat manual untuk model lain)<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, lampu indikator tidak berkedip pada saluran, jika sinyal dibandingkan Jika perbedaan dihidupkan, harap nyalakan sinyal di area yang baik. Area sinyal bagus tidak online, bisa dimatikan",
  ],
  [
    "T: Produk kabel dipasang untuk pertama kali, dan perangkat layar tidak diposisikan.",
    "答：<br/>&nbsp;&nbsp;&nbsp;&nbsp;A: 1. Amati apakah indikator status GPS terminal normal, periksa status indikator sesuai dengan instruksi dari model yang berbeda.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Jika lampu indikator mati, perangkat tidak dapat dihidupkan secara normal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, (hijau kuning) indikator kartu tidak menyala, matikan dan pasang kembali kartu, dan kemudian hidupkan untuk melihat lampu normal normal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;4. Tentukan apakah nomor kartu SIM di perangkat tidak menunggak, dan apakah fungsi akses Internet GPRS normal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;5. Tidak ada jaringan GSM di tempat peralatan berada, seperti ruang bawah, terowongan, dll., Di mana sinyal lemah, silakan buka Mobil ini diuji di tempat yang baik yang dicakup oleh GPRS.<br/>&nbsp;&nbsp;&nbsp;&nbsp;6, posisi positioner tidak boleh terlalu tertutup, tidak memiliki benda logam, sejauh mungkin dalam posisi pemasangan mobil. Kalau tidak, itu mempengaruhi penerimaan sinyal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;7, boot normal, berhenti di daerah sinyal baik tidak online, Anda dapat menerbitkan kembali perintah garis untuk memeriksa apakah antarmuka IP dan jaringan kartu link normal.",
  ],
  [
    "T: Setelah peralatan kabel dipasang, lampu indikator mati, offline, solusinya; harap matikan mobil, gunakan pena listrik dan meter universal untuk mengukur apakah tegangan saluran kendaraan yang terhubung sesuai dengan rentang tegangan pelacak GPS umumnya 9-36V.<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tindakan pencegahan kabel:：<br/>&nbsp;&nbsp;&nbsp;&nbsp;Personel pemasangan dan pengkabelan perlu memiliki pemahaman tentang saluran mobil dan memiliki kemampuan untuk menghindari kerusakan pada mobil Anda yang disebabkan oleh pemasangan kabel yang tidak benar.",
  ],
  [
    "Perangkat kabel atau perangkat pelacakan real-time nirkabel, telepon terhubung atau latar belakang IoT dihidupkan, dan perangkat offline.",
    "<br/>&nbsp;&nbsp;&nbsp;&nbsp;A: 1. Kirim pesan teks untuk memulai kembali, amati beberapa menit untuk melihat apakah itu online. Umumnya mengirim RESET # silakan hubungi dealer untuk menentukan.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.2. Koneksi jaringan tidak stabil. Harap pindahkan mobil ke area sinyal yang bagus.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Setelah langkah-langkah di atas, Anda belum dapat online. Anda perlu menghubungi operator seluler untuk memeriksa apakah kartu tidak normal.",
  ],
  [
    "T: Peralatan offline offline di awal dan akhir bulan ",
    " Solusi: Silakan periksa apakah kartunya Tunggakan jatuh. Jika tunggakan, harap isi kembali tepat waktu dan lanjutkan menggunakannya.",
  ],
  [
    "T: Mobil sedang mengemudi, posisi online GPS tidak diperbarui ",
    " Solusi: 1. Perangkat pengkabelan dapat mengirim SMS STATUS # untuk memeriksa status penerimaan sinyal satelit, lihat GPS: mencari satelit adalah sinyal satelit telah dalam pencarian, situasi ini perlu diperiksa Lokasi pemasangan, apakah sudah dipasang sesuai petunjuk. Menghadap ke atas, tidak ada penutup logam di bagian atas.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Kirim SMS STATUS #, status kembali adalah GPS: MATI, silakan kirim PABRIK # lagi, setelah menerima balasan OK, amati 5 menit untuk melihat apakah posisi diperbarui.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Menurut dua metode di atas, kesalahan tidak dapat dihilangkan. Hubungi penjual untuk perbaikan.",
  ],
  [
    "T: Mengapa platform pengisian daya mengisi waktu lama masih menunjukkan bahwa itu tidak penuh? Tampilan daya platform didasarkan pada informasi umpan balik dari perangkat untuk membuat analisis data untuk menentukan daya perangkat saat ini. Dalam beberapa kasus khusus, kesalahan tampilan daya akan terjadi. Solusi: 1. Data daya perangkat diunggah bersama dengan data posisi perangkat. Jika baterai tidak berubah sejak pengisian untuk waktu yang lama, harap: 1 Bawa perangkat Anda bergerak 100-300 meter untuk memperbarui informasi lokasi perangkat. Buat data baterai dan lokasinya Data dapat diumpankan kembali ke platform bersama untuk menjadi refresh tampilan daya;<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Menurut perubahan indikator daya, tentukan apakah sudah terisi penuh (ambil S15 sebagai contoh). Langkah-langkah operasi adalah sebagai berikut: 1 mengisi daya 8-10 jam, kemudian indikator daya berubah menjadi kuning hijau, setelah mencabut kabel pengisi daya, masukkan kabel pengisian. Dalam 15 menit, indikator daya akan berubah menjadi kuning dan hijau menjadi daya penuh, silakan lihat manual untuk model lain.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, pengisian untuk waktu yang lama juga penuh dengan listrik, situasi ini mungkin tegangan plug pengisian lebih rendah dari 1A, silakan mengisi daya 5V, 1A pengisian kepala selama 8-10 jam.",
  ],
];
lg.webOptDoc =
  "<h1>一、新增用户帐号步骤</h1>" +
  "<ol>" +
  "<li>" +
  "1. 在浏览器输入我们的平台地https://www.gpsnow.net，输入经销商帐号进入到平台的登录界面。" +
  "</li>" +
  "<li>" +
  "2. 登录成功后您会看见平台的首页，在顶部的导航栏（如下图）点击“我的客户”。" +
  "</li>" +
  "<li>" +
  "3. 在全部客户列表选中一个客户,然后选着添加，弹出新增下级用户界面（如下图）。" +
  '<img src="../../lib/document/cn/img/1.png"/>' +
  "</li>" +
  "<li>" +
  "4. 根据用户需求分配用户类型（勾选专业权限用途是为S208油感温感项目设备显示更多信息）。" +
  "</li>" +
  "<li>" +
  "5. 客户名称可与登陆帐号不一致，也可一致。" +
  "</li>" +
  "<li>" +
  "6. 输入完信息点确定，显示添加成功完成。" +
  "</li>" +
  "<li>" +
  "7. 登录成功后您会看见平台的首页，在顶部的导航栏（如下图）点击“我的客户”" +
  "</li>" +
  "<li>" +
  "8. 登录成功后您会看见平台的首页，在顶部的导航栏（如下图）点击“我的客户”" +
  "</li>" +
  "</ol>" +
  "<h1>二、销售设备步骤</h1>" +
  "<ol>" +
  "<li>" +
  "1. 在搜索框输入下级用户帐号（如下图）" +
  "</li>" +
  "<li>" +
  "2. 在左边选中帐号，鼠标右键出现销售设备点击。" +
  '<img src="../../lib/document/cn/img/2.png"/>' +
  "</li>" +
  "<li>" +
  "3. 输入单个或者批量IMEI号，输入批量IMEI号。" +
  '<img src="../../lib/document/cn/img/3.png"/>' +
  "</li>" +
  "<li>" +
  "4. 输入完点确定，再点提交，系统提示销售成功完成。" +
  "</li>" +
  "</ol>" +
  "<h1>三、Kirim akun baru ke pengguna untuk login.</h1>" +
  "<ol>" +
  "<li>" +
  "1.Browser membuka https://www.gpsnow.net untuk memasuki antarmuka login dan memasukkan kata sandi akun (seperti yang ditunjukkan di bawah).。" +
  "</li>" +
  "<li>" +
  "2. Bimbing pelanggan untuk menggunakan kode QR di bawah, minggu depan aplikasi Android atau iOS." +
  "</li>" +
  "<li>" +
  "3. Buat akun dan kata sandi pada langkah pertama dan kirimkan ke pengguna bawahan untuk masuk." +
  "</li>" +
  '<img src="../../lib/document/cn/img/4.png"/>' +
  '<img src="../../lib/document/cn/img/5.png"/>' +
  "<li>" +
  "Tampilan halaman pemantauan halaman web setelah login" +
  "</li>" +
  "<li>" +
  "Pengguna memonitor perangkat di sini, memeriksa statistik perangkat dan informasi alarm, memeriksa detail perangkat, dan memodifikasi data dan kata sandi loginnya sendiri." +
  "</li>" +
  "</ol>";
lg.appOptDoc = "Tetap disini ....";
// Bantuan parameter kueri
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Kata sandi terminal permintaan</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>Permintaan nomor kartu SIM built-in terminal</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Periksa nomor ponsel pemiliknya</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push("<td>Nilai batas kecepatan alarm kueri melebihi kecepatan</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Pertanyaan frekuensi pelaporan setelah pelacakan dimulai, dalam detik</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push(
  "<td>Kueri apakah akan mengaktifkan pelacakan, 1 untuk mengaktifkan pelacakan, 0 untuk mematikan pelacakan</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push(
  "<td>Meminta rentang penilaian alarm shift ilegal, unit ini meter</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>Menanyakan apakah akan mengaktifkan alarm SMS getaran, 1 diaktifkan untuk mematikan 0"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Sensitivitas getaran kueri 0 ~ 15, 0 adalah sensitivitas tertinggi, terlalu tinggi mungkin salah positif, 15 adalah sensitivitas terendah</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>Menanyakan apakah akan mengaktifkan alarm telepon getaran, 1 diaktifkan untuk mematikan 0"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Periksa apakah fungsi drift filter GPS diaktifkan. 1 adalah untuk menghidupkan 0 untuk mematikan. Jika aktif, perangkat anti-pencurian tidak akan bergetar dalam waktu 5 menit, kemudian masukkan keadaan statis dan filter semua titik drift GPS.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Periksa apakah fungsi tidur diaktifkan. Jika alarm menyala, alarm dimatikan. Jika alarm tidak bergetar dalam 30 menit, ia akan memasuki kondisi tidur dan mematikan pemutusan GPS.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Pertanyaan apakah akan mematikan fungsi alarm kegagalan daya, 1 adalah untuk menghidupkan 0 untuk mematikan</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">GPS:</td>');
html.push(
  "<td>Permintaan nomor satelit dan intensitas yang diterima oleh GPS, misalnya: 2300 1223 3431. . . Sebanyak 12 grup dengan empat digit, 2300 menunjukkan bahwa kekuatan sinyal satelit dari penerima nomor 23 adalah 00, dan 1223 menunjukkan bahwa kekuatan sinyal satelit dari nomor 12 yang diterima adalah 23</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VBAT:</td>');
html.push(
  "<td>Permintaan tegangan baterai, pengisian tegangan antarmuka, pengisian arus, misalnya: VBAT = 3713300: 4960750: 303500 berarti tegangan baterai 3713300uV, yaitu 3,71v, tegangan pengisian 4,96V, pengisian arus 303mA</td>"
);
html.push("</tr>");
html.push("</table>");
lg.queryparamhelp = html.join("");

// Bantuan dengan pengaturan parameter
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Atur kata sandi terminal, kata sandi hanya bisa 6 digit</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>Atur nomor kartu SIM di terminal</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Tetapkan nomor ponsel pemilik</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push(
  "<td>Setel nilai batas kecepatan alarm melebihi kecepatan, kisarannya harus antara 0 ~ 300</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Atur frekuensi pelaporan setelah pelacakan dihidupkan, dalam detik.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push(
  "<td>Tetapkan apakah akan mengaktifkan pelacakan, 1 untuk mengaktifkan pelacakan, 0 untuk mematikan pelacakan</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push("<td>Atur rentang penilaian alarm shift ilegal, unit ini meter</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>Setel apakah akan mengaktifkan alarm SMS getaran, 1 diaktifkan untuk mematikan 0"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Atur sensitivitas getaran 0 ~ 15, 0 adalah sensitivitas tertinggi, terlalu tinggi mungkin salah positif, 15 adalah sensitivitas terendah</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>Setel apakah akan mengaktifkan fungsi alarm kegagalan daya, 1 diaktifkan untuk mematikan 0"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Setel apakah akan mengaktifkan fungsi filter GPS, 1 adalah untuk menghidupkan 0 mati, jika aktif, alarm tidak akan bergetar dalam waktu 5 menit, kemudian masukkan keadaan statis, filter semua titik GPS drift</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Setel apakah akan mengaktifkan fungsi tidur, 1 adalah untuk menghidupkan 0 mati, jika menyala, alarm tidak akan bergetar dalam 30 menit, kemudian memasuki kondisi tidur, matikan rantai GPS, sehingga menghemat daya</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Setel apakah akan mengaktifkan fungsi alarm kegagalan daya, 1 diaktifkan untuk mematikan 0</td>"
);
html.push("</tr>");
html.push("</table>");
lg.setparamhelp = html.join("");

//Tambahkan dalam batch
html = [];
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox" ' +
    'style="z-index: 999;position:absolute;left:125px;top:88px;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Tambahkan:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkAdds_treeDiv" +
    "," +
    "bulkAdds_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Platform kedaluwarsa:</td>'
);
html.push("<td>");
html.push(
  '<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Model peralatan:</td>'
);
html.push("<td>");
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    "</div>" +
    '<div id="deviceList"></div>' +
    "</div>" +
    "</span>"
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Tambahkan perangkat:</td>'
);
html.push("<td>");
html.push(
  '<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push(
  '<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>'
);
lg.bulkAdds = html.join("");

//Pembaruan batch
html = [];
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Tambahkan perangkat:</td>'
);
html.push("<td>");
html.push(
  '<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="re_addNumBox">arus：<span id="account_re_addNum">0</span>'
);
html.push("</span>");
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<tr>");
html.push(
  '<td style="text-align:right;"><span style="color:red">*</span>Jenis kartu</td>'
);
html.push("<td>");
html.push('<input  type="radio" name="red_cardType"');
html.push(
  'class="easyui-validatebox"  value="3" checked><label>一年</label></input>'
);
html.push(
  '<input  type="radio" name="red_cardType" style="margin-left:15px;" '
);
html.push(
  'class="easyui-validatebox" value="4"><label>Seumur hidup</label></input>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td style="text-align: right">Poin pengurangan</td>');
html.push("<td>");
html.push(
  '<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Pengguna kedaluwarsa:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Komentar</td>'
);
html.push("<td>");
html.push(
  '<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkRenew = html.join("");

// //Penjualan massal，myAccount
html = [];
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel treePulldownBox"  style="z-index: 999;position:absolute;left:215px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Dijual ke:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkSales_treeDiv" +
    "," +
    "bulkSales_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push(
  '<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Pengguna kedaluwarsa:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Tambahkan perangkat:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="bs_addNumBox">arus：<span id="account_bs_addNum">0</span>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="bs_reset" class="swd-gray-btn" title="' +
    lg.reset +
    '"  style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");

lg.bulkSales = html.join("");

//Transfer batch1，弹出框
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:127px;top:172px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Targetkan pelanggan:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Tambahkan perangkat:</td>'
);
html.push("<td>");
html.push(
  '<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" ></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Transfer</a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)">Batalkan</a>'
);

lg.bulkTransfer = html.join("");

//Transfer batch 2,myClient
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:117px;top:84px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Targetkan pelanggan:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Tambahkan perangkat:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bt_addMachines" style="cursor:pointer" title="' +
    lg.addTo +
    '" src="../../images/main/myAccount/add3.png" />'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);

lg.bulkTransfer2 = html.join("");

//Transfer pengguna secara massal
html = [];
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:203px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Targetkan pelanggan:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">'
);
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("<td></td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");

lg.bulkTransferUser = html.join("");
window.lg = lg