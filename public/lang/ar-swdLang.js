var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
var lg = {
  //阿拉伯
  //common common
  user_guide: 'دليل المستخدم',
  remoteSwitch: "التبديل عن بعد",
  pageTitle:
    "WhatsGPS Global Tracking System|Vehicle GPS Tracker|3G Tracker|mini 4G Tracker|GPSNow|Car Locator",
  description:
    site+" is dedicated to providing users with intelligent cloud location services. It is the world's leading location service platform.",
  pageLang: "عربي",
  inputCountTips: "الرجاء إدخال الحساب / IMEI",
  inputPasswordTips: "من فضلك أدخل رقمك السري",
  appDownload: "تحميل التطبيق",
  rememberPassword: "تذكرنى",
  forgetPassword: 'نسيت كلمة السر',
  siteName: "WhatsGPS",
  noToken: " الرجاء إرسال الرمز",
  loginFirst: "سجل الدخول أولا",
  move: "يتحرك",
  stop: "ساكن",
  query: "تحقق",
  imeiQuery: "IMEI",
  delete: "حذف",
  update: "تحديث",
  cancel: "الغاء",
  soft: "رقم",
  more: "أكثر",
  useful:'مفيد',
  useless:'غير مفيد',
  about:'About',
  replyFeedback:'تعليقات على "$"',
  edit: "تعديل",
  add: "إضافة",
  addTo: "إضافة",
  addDevice: "اضف جهاز",
  machineName: "الجهاز",
  searchDevice: "جهاز",
  date: "التاريخ",
  LatestUpdate: "أخر تحديث",
  engine: "ACC",
  locTime: "وقت الموقع",
  locType: "نوع الموقع",
  startLoc: "ابدأ الموقع",
  endLoc: "انهى الموقع",
  address: "العنوان",
  noAddressTips: "غير قادر على الحصول على معلومات العنوان",
  lonlat: "خط طول وعرض",
  carNO: "لوحه لا",
  imei: "رقم المعدات (IMEI)",
  IMEI: "IMEI",
  simNO: "رقم البطاقة SIM",
  activeTime: "وقت التفعيل",
  expireTime: "وقت الانتهاء",
  acceptSubordinateAlarm: "قبول التنبيه ثانوي",
  acceptAlarmTips1: "بعد الاختيار",
  acceptAlarmTips2: "سوف تتلقى معلومات إنذار الجهاز من جميع العملاء المرؤوسين",
  speed: "السرعه",
  y: "سنه",
  M: "شهر",
  d: "يوم",
  h: "ساعه",
  min: "دقيقه",
  s: "ثانيه",
  _year: "y",
  _month: "m",
  _day: "d",
  _hour: "h",
  _minute: "m",
  _second: "s",
  confirm: "تأكيد",
  yes: "نعم",
  car: "السياره",
  not: "لا",
  m: "متر",
  account: "الحساب",
  psw: "كلمه السر",
  save: "حفظ",
  operator: "العمل",
  queryNoData: "لا توجد بيانات",
  name: "اسم المستخدم",
  type: "نموذج ",
  open: "فتح",
  close: "قفل",
  send: "ارسال",
  alarm: "إنذار",
  alarmSetting: "اعدادت التنبيه",
  look: "انضر",
  tailAfter: "تتبع",
  history: "استرجاع",
  dir: "اتجاه",
  locStatus: "حاله الموقع",
  machineTypeText: "نوع الجهاز",
  carUser: "مستخدم السياره",
  machine: "المركبات",
  unknowMachineType: "اله غير معروفه",
  noCommandRecord: "لا تعمل الأوامر لهذا الجهاز",
  type1: "النوع",
  role: "Role",
  roles: "الأدوار",
  timeType: "نوع الوقت",
  moveSpeed: "تحريك السرعه",
  signal: "الاشاره",
  loc: "المكان",
  wiretype: "النوع",
  wire: "سلكي",
  wireless: "لا سلكي",
  expire: "منتهية الصلاحية",
  hour: "ساعه",
  hourTo: "ساعه الي",
  remark: "تعليقتعليقتعليق",
  remarkInfo: "تعليق",
  noPriviledges: "الحساب ليس به امتيازات تشغيل",
  commandNoOpen: "اوامر الجهاز الحالي لا تصلح للاستخدام بعد",
  choseDelelePhone: "من فضلك اختار الرقم لتمسحه اولا",
  streetView: "رؤيه الشارع",
  wrongFormat: "خطا في تنسيق الادخال",
  inputFiexd: "من فضلك ادخل رقم ثابت",
  serialNumberStart: "من فضلك ادخل بدايه رقم المسلسل",
  serialNumberEnd: "من فضلك ادخل نهايه رقم المسلسل",
  clickSearchFirst: "من فضلك اضغط بحث الجهاز مقدما!",
  isDeleteDevice: "لا يمكن استعادة الجهاز بعد حذفه. هل تم حذفه؟",
  //平台错误代码提示
  errorTips: "فشل العمليه مع رمز خطأ：",
  error10003: 'كلمة مرور خاطئة',
  error90010: 'الجهاز غير متصل بالإنترنت ، وفشل إرسال الأمر المخصص!',
  error70003: 'لا يمكن أن تكون قيمة جهاز التحكم عن بعد فارغة',
  error70006: 'لا يدعم أو ليس له الحق في إصدار التعليمات',
  error20001: 'لا يمكن ترك رقم تعريف المركبة فارغًا',
  error20012: 'المركبة غير مفعلة',
  error10012: "خطأ في الرقم السري القديم",
  error10017: "فشل الازاله يرجي مسح المستخدم الفرعي",
  error10023: "فشل الازاله المستخدم لديه جهاز",
  error20008: "فشل الاضافه رقم الجهاز بالفعل موجود",
  error20006: "يرجي ادخال رقم الجهاز المكون من 15 رقم",
  error10019: "طريقه ادخال هاتف خاطئه",
  error10024: "لا تكرر المبيعات",
  error120003: "مشاركة الروابط معطلة",
  error10025: "لا يمكن أن تكون معلومات الجهاز المعدلة فارغة",
  error2010: "يرجى تحميل الملف",
  error20002: "لا يوجد رقم IMEI",
  error10081: "عدد غير كاف من بطاقات التجديد",
  error10082: 'لا حاجة لإعادة الشحن لجهاز مدى الحياة',
  error3000: 'تم تعيين الدور لحساب النظام ولا يمكن حذفه',
  error103: 'تم تعطيل الحساب ، يرجى الاتصال بمزود الخدمة الخاص بك',
  error124: 'لا يمكن أن تعمل على نفسها',
  // 登陆相关 login.js
  logining: "جاري تسجيل الدخول",
  login: "دخول",
  userEmpty: "المستخدم فارغ",
  pswEmpty: "الرقم السري فارغ",
  prompt: "تذكير",
  accountOrPswError: "خطأ في الرقم السري أو الحساب",
  UserNameAlreadyExist: "اسم المستخدم موجود بالفعل",
  noQualified: "لا توجد معلومات مؤهلة",
  //main.js
  systemName: "WhatsGPS",
  navTitle_user: ["رصد الموقع", "تقرير احصائي", "إدارة الجهاز"],
  navTitle_dealer: [
    "الصفحه الرئيسيه",
    "الاجهزة",
    "عرض الخريطة",
    "المزيد من العمليات",
  ],
  exitStytem: "خروج",
  user: "مستخدم",
  UserCenter: "رقم اساسي للمستخدم",
  alarmInfo: "تنبيه",
  confirmExit: "تطبيق الخروج",
  errorMsg: "خطأ الارسال: ",
  logintimeout: "انتهي الوقت المسموح للدخول",
  clearAlarm: "ازاله",
  clear: "إزالة",
  searchbtn: "المستخدم",
  print: "طباعه",
  export: "تصدير",
  //feedback
  feedback: "نتائج",
  feedback_sublime: "تنفيذ/ تطبيق",
  alerttitle: "لايمكن ان يكون العنوان فارغ",
  alertcontent: "لايمكن ان تكون النتائج فارغه",
  submitfail: "فشل التطبيق",
  saveSuccess: "تم التنفيذ بنجاح",
  submitsuccess: "تم التنفيذ بنجاح سوف نعالج نتائجك",
  adviceTitle: "عنوان",
  adviceTitle_p: "عنوان السؤال والرأي",
  adviceContent: "اسأله و أراء",
  adviceContent_p: "صف باختصار الاسئله والتعليقات التي تود تقديمها",
  contact: "معلومات الاتصال",
  contact_p: "ادخل رقمك او البريد الالكتروني",
  //monitor.js
  myMachine: "جهازي",
  all: "جميع",
  online: "على الانترنت",
  offline: "غير متصل",
  unUse: "غير مستخدمة",
  group: "مجموعه",
  moveGruop: "الانتقال إلى المجموعة",
  arrearage: "تاخر عن دفع",
  noStatus: "لا يوجد حاله",
  inputMachineName: "رقم الجهاز",
  defaultGroup: "المجموعة الافتراضية",
  offlineLessOneDay: "غير نشط<يوم واحد",
  demoUserForbid: "تجربه المستخدمين لايمكن استخدام هذه الميزه",
  shareTrack: "مشاركه الموقع",
  shareName: "الاسم",
  liveShare: "تقاسم المسار في الوقت الحقيقي",
  expiration: "تاريخ انتهاء الصلاحية",
  getShareLink: "انشاء رابط",
  copy: "انسخ",
  copySuccess: "نسخ ناجح!",
  enlarge: "تكبير",
  shareExpired: "مشاركه الرابط منتهيه الصلاحيه",
  LinkFailure: "فشل فتح الرابط",
  inputShareName: "ضع اسم مشاركه",
  inputValid: "يرجى إدخال وقت الفشل الصحيح",
  //statistics.js
  runOverview: "نظره عامه",
  runSta: "نظره عامه",
  mileageSta: "تقارير الاميال",
  tripSta: "إحصائيات الرحلة",
  overSpeedDetail: "تفاصيل تجاوز السرعه",
  stopDetail: "كن مفصلا",
  alarmSta: "تقرير التنبيه",
  alarmOverview: "عرض التنبيهات",
  alarmDetail: "تفاصيل التنبيهات",
  shortcutQuery: "فحص سريع",
  today: "اليوم",
  yesterday: "بالامس",
  lastWeek: "الاسبوع الماضي",
  thisWeek: "هذا الاسبوع",
  thisMonth: "هذا الشهر",
  lastMonth: "الشهر الماضي",
  mileageNum: "كيلومتر/ ميل",
  overSpeedNum: "سرعه زائده كم/ساعه",
  overSpeed: "سرعه زائده",
  stopTimes: "وقت الاتظار",
  searchMachine: "بحث",
  speedNum: "سرعه كم/ساعه",
  querying: "الاستعلام",
  stopTime: "وقت ثابت",
  HisToryStopTime: "وقت ثابت",
  clickLookLoc: "انقر لعرض عنوان",
  lookLoc: "استعلام عن موقع",
  noData: "لا توجد بيانات",
  alarmTime: "وقت التنبيه",
  vibrationLevel: "مستوى الاهتزاز",
  vibrationWay: "نوع التنبيه",
  acc: "ACC",
  accStatistics: "احصائيات",
  accType: ["كل", "المحرك متصل", "المحرك غير متصل"],
  accstatus: ["متصل", "غير متصل"],
  openAccQuery: "افتح استعلام ACC",
  runtime: "وقت العمل",
  //监控页面修改
  run: "سفر",
  speed: "سرعة",
  //设备管理
  machineManage: "اجهزتي",
  deviceTable: "مركباتي",
  status: "الحاله",
  havaExpired: "انتهت الصلاحيه",
  expiredIn60: "ينتهي خلال 60يوم",
  expiredIn7: "ينتهي خلال 7 ايام",
  normal: "عادي",
  allMachine: "كل",
  allMachine1: "كل الاجهزه",
  expiredIn7Machine: "ينتهي خلال 7 ايام",
  expiredIn60Machine: "ينتهي خلال 60يوم",
  havaExpiredMachine: "مركبات انتهت صلاحيتها",

  //history.js
  replay: "تشغيل",
  replaytitle: "  اعاده",
  choseDate: "اختيار تاريخ",
  from: "من",
  to: "إلى",
  startTime: "وقت البدء",
  endTime: "انتهى الوقت",
  pause: "فاصل",
  slow: "بطئ",
  mid: "متوسط",
  fast: "سريع",
  startTimeMsg: "وقت بدايه الرساله",
  endTimeMsg: "وقت نهايه الرساله",
  smallEnd: "نهايه الوقت اقل من بديه الوقت يرجي الاختيار مره اخري",
  bigInterval: "الوقت الزمني اقل من 31يوم",
  trackisempty: "التتبع فارغ",
  longitude: "خط طول",
  latitude: "خط عرض",
  direction: "اتجاه",
  stopMark: "وقف علامه",
  setStopTimes: [
    {
      text: "1 دقيقه",
      value: "1",
    },
    {
      text: "2 دقيقه",
      value: "2",
    },
    {
      text: "3 دقيقه",
      value: "3",
    },
    {
      text: "5 دقيقه",
      value: "5",
    },
    {
      text: "10 دقيقه",
      value: "10",
    },
    {
      text: "15 دقيقه",
      value: "15",
    },
    {
      text: "20 دقيقه",
      value: "20",
    },
    {
      text: "30 دقيقه",
      value: "30",
    },
    {
      text: "45 دقيقه",
      value: "45",
    },
    {
      text: "1 ساعه",
      value: "60",
    },
    {
      text: "6 ساعه",
      value: "360",
    },
    {
      text: "12 ساعه",
      value: "720",
    },
  ],
  filterDrift: "ازاله موقع",
  userType: [
    "مشرف",
    "تاجر",
    "مستخدم فقط",
    "مستخدم فقط",
    "تأجير",
    "مستخدم السياره",
    "السيطرة على المخاطر",
    "تخصص",
  ],
  userTypeArr: [
    "مشرف",
    "تاجر",
    "مستخدم فقط",
    "مستخدم فقط",
    "تأجير",
    "مستخدم السياره",
    "تخصص",
  ],
  machineType: {
    '0': 'نوع الاله',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '83':'S03A',
    '84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: [
    "نوع التنبيه",
    "اهتزاز التنبيه",
    "تنبيه فصل الكهرباء",
    "تنبيه البطاريه ضعيفه",
    "تنبيه رساله الاستغاثه",
    "تنبيه تجلوز السرعه",
    "تنبيه المنطقه الجغرافيه",
    "تنبيه الخروج عن المسار",
    "تنبيه خارجي البطاريه ضعيفه",
    "تنبيه الخروج عن المنطقه",
    "تنبيه فك الجهاز",
    "تنبيه استشعار الضوء",
    "تنبيه استشعار مغناطيسي",
    "تفكيك التنبيه",
    "تنبيه بلوتوث",
    "تنبيه الاشاره",
    "محطه التنبيه",
    "تنبيه بدخول منطقه جغرافيه",
    "تنبيه بدخول منطقه جغرافيه",
    "تنبيه بخروج منطقه جغرافيه",
    "الباب-تنبيه فتح",
    "قياده متعبه",
    "نقطه الدخول",
    "الخروج من النقطه",
    "بقاء النقطه",
    "محطه غير نشطه",
    "تنبيه بدخول منطقه جغرافيه",
    "تنبيه بخروج منطقه جغرافيه",
    "تنبيه بدخول منطقه جغرافيه",
    "تنبيه بخروج منطقه جغرافيه",
    "إنذار الوقود",
    "ACC ON",
    "ACC OFF",
    "إنذار الاصطدام",
  ],
  alarmTypeNew:  {
    '40': 'ارتفاع درجة الحرارة التنبيه',
    '45': 'انخفاض درجة الحرارة التنبيه',
    '50': 'إنذار الجهد الزائد',
    '55': 'إنذار انخفاض الجهد',
    '60': 'إنذار وقوف السيارات'
  },
  alarmNotificationType: [
    { type: "اهتزاز التنبيه", value: 1 },
    { type: "تنبيه فصل الكهرباء", value: 2 },
    { type: "تنبيه البطاريه ضعيفه", value: 3 },
    { type: "تنبيه رساله الاستغاثه", value: 4 },
    { type: "تنبيه تجلوز السرعه", value: 5 },
    // {type:'تنبيه المنطقه الجغرافيه',value:6},
    { type: "تنبيه الخروج عن المسار", value: 7 },
    { type: "إنذار الطاقة المنخفضة", value: 8 },
    { type: "تنبيه الخروج عن المنطقه", value: 9 },
    { type: "تنبيه فك الجهاز", value: 10 },
    { type: "تنبيه استشعار الضوء", value: 11 },
    { type: "تفكيك التنبيه", value: 13 },
    { type: "تنبيه الاشاره", value: 15 },
    { type: "محطه التنبيه", value: 16 },
    // {type:'تنبيه بدخول منطقه جغرافيه',value:17},
    // {type:'تنبيه بدخول منطقه جغرافيه',value:18},
    // {type:'تنبيه بخروج منطقه جغرافيه',value:19},
    { type: "قياده متعبه", value: 21 },
    { type: "دخول النقطه", value: 22 },
    { type: "خروج النقطه", value: 23 },
    { type: "بقاء النقطه", value: 24 },
    { type: "محطه غير نشطه", value: 25 },
    // {type:'تنبيه بدخول منطقه جغرافيه (السيطرة على المخاطر)',value:26},
    // {type:'تنبيه بخروج منطقه جغرافيه(السيطرة على المخاطر)',value:27},
    { type: "تنبيه بدخول منطقه جغرافيه", value: 26 },
    { type: "تنبيه بخروج منطقه جغرافيه", value: 27 },
    { type: "إنذار الوقود", value: 30 },
    { type: "ACC ON", value: 31 },
    { type: "ACC OFF", value: 32 },
    { type: "إنذار الاصطدام", value: 33 },
  ],
  alarmTypeText: "نوع التنبيه",
  alarmNotification: "التنبيه",
  pointType: [
    "نوع نقطه",
    "موقع القمر الصناعي",
    "موقع البوصله",
    "LBSموقع",
    "موقع شبكه النت",
  ],

  cardType: [
    "نوع غير معروف",
    "بطاقه جديد",
    "بطاقه مدي الحياه",
    "كارد جديد",
    "كارد مدى الحياه",
  ],
  // 东南西北
  directarray: ["شرق", "جنوب", "غرب", "شمال"],
  // 方向字段
  directionarray: [
    "اتجاه الشمال",
    "شمال شرق",
    "اتجاه الشرق",
    "جنوب شرق",
    "اتجاه الجنوب",
    "جنوب غرب",
    "اتجاه الغرب",
    "شمال غرب",
  ],
  // 定位方式
  pointedarray: [
    "غير محدد",
    "موقع رمز المنطقة",
    "موقع رمز المنطقة",
    "موقع لاك",
    "موقع شبكه النت",
  ],

  //map Relevant
  ruler: "قاعده",
  distance: "المرور",
  baidumap: "Baidu",
  map: "خريطة",
  satellite: "الأقمار الصناعية",
  ThreeDimensional: "3D",
  baidusatellite: "قمر صناعي بايد",
  googlemap: "Google",
  googlesatellite: "قمر صناعي جوجل",
  fullscreen: "شاشه كامله",
  noBaidumapStreetView: "موقع حالي علي الخرائط بدون عرض شوارع",
  noGooglemapStreetView: "الموقع الحالى على خرائط جوجل بدون عرض واقعى للشارع",
  exitStreetView: "خروج من عرض الشوارع",
  draw: "رسم",
  finish: "انهاء",
  unknown: "غير معروف",
  realTimeTailAfter: "وقت تتبع فعلي",
  trackReply: "استرجاع زاكره الجهاز",
  afterRefresh: "تحديث",
  rightClickEnd: "انقر فوق انهاء كليك يمين,نصف القطر：",
  rightClickEndGoogle: "كليك يمين انهى------------------------نصف القطر：",

  //tree Relevant
  currentUserMachineCount: "عدد الاجهزه المستخدمه الحاليه",
  childUserMachineCount: "عدد اجهزه الاطفال المستخدمه",

  //Window relevant

  electronicFence: "منطقه جغرافيه",
  drawTrack: "تجهيز السياج",
  showOrHide: "عرض او اخفاء",
  showDeviceName: "اعرض اسم الجهاز",
  circleCustom: "تعريف المستخدم",
  circle200m: "دائره 200 متر",
  polygonCustom: "رسم مضلع",
  drawPolygon: "ارسم مضلع",
  drawCircle: "أرسم دائرة",
  radiusMin100:
    "يبلغ قطر دائرة السياج المسحوق 20 متر على الأقل. يرجى إعادة رسمها. دائرة السياج الحالي:",
  showAllFences: "عرض كل الأسوار",
  lookEF: "فحص المنطقه الجغرافيه",
  noEF: "لا بيانات للسياج ",
  hideEF: "اخفاء السياج",
  blockUpEF: "غلق السياج",
  deleteEF: "حذف السياج",
  isStartUsing: "سواء للتمكين",
  startUsing: "تمكين",
  stopUsing: "وقف التمكين",
  nowEFrange: "مستوى السياج الحالى",
  enableSucess: "تمكين ناجح",
  unableSucess: "غير ممكن ناجح",
  sureDeleteMorgage: "مسح النقطه",
  enterMorgageName: "ادخل النقطه",
  openMorgagelongStayAlarm: "ابدأ النقطه",
  openMorgageinOutAlarm: "ابدأ الدخول وانهى النقطه",
  setEFSuccess: "تمكين سياج ناجح",
  setElectronicFence: "تمكين السياج الالكترونى",
  drawFence: "ارسم سياج",
  drawMorgagePoint: "ارسم نقطه",
  customFence: "اختيار سياج",
  enterFenceTips: "دخول المنبه",
  leaveFenceTips: "اترك المنبه",
  inputFenceName: "ادخل اسم سياج",
  relation: "حزمه",
  relationDevice: "جهاز مقترن",
  unRelation: "غير مقترن",
  hadRelation: "مقترن بالفعل",
  quickRelation: "حزمه بنقره واحده",
  cancelRelation: "الغاء الحزمه",
  relationSuccess: "نجاح الحزمه",
  cancelRelationSuccess: "لاغى بنجاح",
  relationFail: "فشل الالغاء",
  deviceList: "قائمه الاجهزه",
  isDeleteFence: "لمسح السياج",
  choseRelationDeviceFirst: "حدد الجهاز اولا ليتم اقترانه!",
  choseCancelRelationDeviceFirst: "حدد الجهاز اولا ليتم عدم اقترانه!",
  selectOneTips: "اختر على الاقل طريقه واحده للتنبيه",
  radius: "نصف القطر",
  //设置二押点页面
  setMortgagePoint: "تعيين نقطه",

  circleMortage: "نقطه دائره",
  polygonMorgage: "نقطه مضلعه",
  morgageSet: "تعين النقطه ممكن بالفعل",
  operatePrompt: "تشغيل سريع",
  startDrawing: "اضغط لبدايه الرسم",
  drawingtip1: "كليك شمال لبدايه الرسم, ضغطتين لانهاء الرسم",
  drawingtip2: "اضغط كليك شمال واسحب لتبدأ الرسم",

  /************************************************/
  endTrace: "نهايه التتبع",
  travelMileage: "السفر بالميل",
  /************************************************/
  myAccount: "حساب",
  serviceProvide: "مزود الخدمه",
  completeInfo: "اكتملت المعلومات(متصلين-رقم تليفون)",
  clientName: "اسم العميل",
  loginAccount: "المستعمل",
  linkMan: "اتصال",
  linkPhone: "تليفون",
  clientNameEmpty: "اسم العميل فارغ",
  updateSuccess: "تم التحديث بنجاح",
  /************************************************/
  oldPsw: "خطأ في الرقم السري القديم",
  newPsw: "رقم سري جديد",
  confirmPsw: "تطبيق الرقم السري",
  pswNoSame: "خطأ في كلمه السر",
  pswUpdateSuccess: "تحديث الرقم السرى ناجح!",
  email: " بريد الكتروني",
  oldPwdWarn: "يرجي ادخال الرقم السري القديم",
  newPwdWarn: "من فضلك ادخل رقم سرى جديد",
  pwdConfirmWarn: "يرجي تطبيق الرقم السري الجديد",
  /************************************************/
  //Custom popup components
  resetPswFailure: "فشل اعاده ضبط الرقم السري",
  notification: "اشعارات",
  isResetPsw_a: "متأكد من اعاده تعيين  ",
  isResetPsw_b: "`s الرقم السرى?",
  pwsResetSuccess_a: "تم اعاده تعين ",
  pwsResetSuccess_b: "`s الرقم السرى الى 123456",
  /************************************************/
  machineSearch: "معلومات الجهاز",
  search: "بحث",
  clientRelation: "علاقه العميل",
  machineDetail: "التفاصيل",
  machineDetail2: "تفاصيل الجهاز",
  machineCtrl: "تعليمات",
  transfer: "نقل",
  belongCustom: "ينتمي للعميل",
  addImeiFirst: "ادخل رقم الجهاز اولا",
  addUserFirst: "اضافه العميل اولا",
  transferSuccess: "تم النقل بنجاح",
  multiAdd: "اضافه مجموعه",
  multiImport: "استيراد دفعة واحدة",
  multiRenew: "دفعة التجديد",
  //批量修改设备begin
  editDevice:'تعديل طراز الجهاز',
  deviceAfter: 'طراز الجهاز (بعد التعديل)',
  editDeviceTips:'يرجى التأكد من أن الجهاز المراد تعديله هو نفس الطراز وغير نشط!',
  pleaseChoseDevice: 'الرجاء تحديد الجهاز المراد تعديله أولاً!',
  editResult:'تحرير النتيجة',
  successCount:'تم تعديل الجهاز بنجاح:',
  failCount:'الأجهزة الفاشلة:',
  //批量修改设备end
  multiDelete: "دفعة حذف",
  canNotAddImei: "رقم الجهاز غير موجود لا تستطيع اضافه رقم الجهاز",
  importTime: "تاريخ التصنيع",
  loginName: "Account",
  platformDue: "تاريخ الاستحقاق / تجديد المنصة",
  machinePhone: "شريحة جوال",
  userDue: "تنتهي صلاحية المستخدم ",
  overSpeedAlarm: "تنبيه تجلوز السرعه",
  changeIcon: "ايقونه",
  dealerNote: "علم",
  noUserDue: "لا يوجد مستحق من المستخدم",
  phoneLengththan3: "الهاتف يجب ان يكون اكثر من ثلاثه ارقام",
  serialNumberInput: "قم بوضع السريال ",

  /************************************************/
  sending: "جار الارسالانتظر من فضلك",
  sendFailure: "إرسال الفشل",
  ctrlName: "الاسم",
  interval: "الوقت الفعلي",
  intervalError: "شكل الفاصلة خطأ",
  currectInterval: "الرجاء إدخال الفاصل الزمني الصحيح!",
  intervalLimit: "الفاصل الزمني 10-720 المدى,(دقيقه)",
  intervalLimit2: "الفاصل الزمني 10-5400 المدى,(ثانيه)",
  intervalLimit3: "الفاصل الزمني 5-1440 المدى,(دقيقه)",
  intervalLimit4: "الفاصل الزمني 3-999 المدى,(ثانيه)",
  intervalLimit5: "الفاصل الزمني 10-10800 المدى,(ثانيه)",
  intervalLimit1:
    "الفاصل الزمني 1-999 المدى,(Minute).",
  intervalLimit6: "الفاصل الزمني 10-65535 المدى,(ثانيا)",
  intervalLimit7: "الفاصل الزمني 1-999999 المدى,(ثانيا)",
  intervalLimit8: "قم بتعيين نطاق الفاصل الزمني 0-255 ، 0 يعني للإغلاق",
  intervalLimit9: "الفاصل الزمني 3-10800 المدى,(ثانيه)",
  intervalLimit10: "الفاصل الزمني 3-86400 المدى,(ثانيه)",
  intervalLimit11: "الفاصل الزمني 180-86400 المدى,(ثانيه)",
  intervalLimit22: "الفاصل الزمني 60-86400 المدى,(ثانيه)",
  intervalLimit23: "نطاق الفاصل الزمني 5-60 ، (ثانية)",
  intervalLimit24: "نطاق الفاصل الزمني 10-86400 ، (ثانية)",
  intervalLimit25: "الفاصل الزمني 5-43200 المدى,(دقيقه)",
  intervalLimit12: "الفاصل الزمني 10-60 المدى,(ثانيه)",
  intervalLimit13:
    "اضبط نطاق الوقت الفاصل 1-24 (يعني 1-24 ساعة) أو 101-107 (يعني 1-7 أيام)",
  intervalLimit14:
    "ضبط نطاق الوقت الفاصل 10-3600 ، وحدة (الثانية) ؛ الافتراضي: 10s",
  intervalLimit15:
    "ضبط نطاق الوقت الفاصل 180-86400 ، الوحدة (الثانية) ؛ الافتراضي: 3600s",
  intervalLimit16:
    "ضبط نطاق الوقت الفاصل 1-72 ، وحدة (ساعة) ؛ الافتراضي: 24 ساعة",
  intervalLimit17: "تحديد نطاق درجة الحرارة -127-127 ، وحدة (درجة مئوية)",
  intervalLimit18: "تعيين النطاق الزمني الفاصل 5-18000 ، الوحدة (الثانية)",
  intervalLimit19: "ضبط الفاصل الزمني 10-300 وحدة (ثانية)",
  intervalLimit20: "تعيين الفترة الزمنية الفاصلة 5-399 ، الوحدة (بالثواني)",
  intervalLimit21: "تعيين الفترة الزمنية الفاصلة 5-300 ، الوحدة (بالثواني)",
  noInterval: "ادخل الفاصل الزمني",
  intervalTips: "قم بإيقاف تشغيل وضع التعقب عن طريق ضبط وقت التنبيه للتنبيه",
  phoneMonitorTips:
    "بعد إرسال الأمر ، سيقوم الجهاز بطلب رقم رد الاتصال للمراقبة.",
  time1: "1وقت",
  time2: "وقت2",
  time3: "وقت3",
  time4: "وقت4",
  time5: "وقت5",
  intervalNum: "الفاصل الزمنى(دقائق)",
  sun: "الاحد",
  mon: "الاثنين",
  tue: "الثلاثاء",
  wed: "الاربعاء",
  thu: "الخميس",
  fri: "الجمعه",
  sat: "Sat.",
  awakenTime: "قفعيل الوقت",
  centerPhone: "تليفون تحكم",
  inputCenterPhone: "مركز ادخل هاتف!",
  phone1: "رقم 1",
  phone2: "رقم 2",
  phone3: "رقم 3",
  phone4: "رقم 4",
  phone5: "رقم 5",
  inputPhone: "ادخل التليفون",
  offlineCtrl: "سيتم ارسال التعليمات للجهاز تلقائيا بعد الاتصال بالنت",
  terNotSupport: "غير مفعل",
  terReplyFail: "فشل الاستجابه",
  machineInfo: "معلومات الجهاز",

  /************************************************/
  alarmTypeScreen: "شاشه نوع التنبيه",
  allRead: "الكل مقروء",
  read: "يقرأ",
  noAlarmInfo: "لايوجد معلومات انذار",
  alarmTip: "تصفيه هذا النوع من معلومات التنبيه بعدم الفحص",

  /************************************************/
  updatePsw: "كلمه السر",
  resetPsw: "إعادة ضبط كلمة المرور",

  /************************************************/
  multiSell: "بيع مجموعه",
  sell: "مبيعات",
  sellSuccess: "نتم البيع بنجاح",
  modifySuccess: "تعديل النجاح",
  modifyFail: "تعديل الفشل",
  multiTransfer: "نقل مجموعه",
  multiUserExpires: "ينتهي تعديل دفعة المستخدمين",
  batchModifying: "دفعة تعديل",
  userTransfer: "نقل مستخدم",
  machineRemark: "اعاده تقييم",
  sendCtrl: "ارسال امر",
  ctrl: "امر",
  ctrlLog: " سجل  الأوامر",
  ctrlLogTips: "تاريخ الاوامر",
  s06Ctrls: ["ايقاف المحرك", "تشغيل المحرك", "استعلام الموقع"],
  ctrlType: "اسم الأمر",
  resInfo: "نتيجة",
  resTime: "اعاده ضبط الوقت",
  ctrlSendTime: "ارسل الوقت",
  // csv文件导入上传
  choseCsv: "اختر ملف CSV",
  choseFile: "حدد الملف",
  submit: "تقدم",
  targeDevice: "الجهاز الهدف",
  csvTips_1: "1.حفظ ملف Excel في شكل CSV",
  csvTips_2: "2.استيراد ملف CSV في النظام",
  importExplain: "استيراد التعليمات:",
  fileDemo: "صيغة الملف مثلا",
  // add
  sendType: "النوع",
  onlineCtrl: "امر نشط",
  offCtrl: "امر غير نشط",
  resStatus: [
    "غير مرسل",
    "غير صحيح",
    "تم العلاج",
    "نفذ بنجاح",
    "فشل التنفيذ",
    "لا اجابه",
  ],
  /************************************************/
  addSubordinateClient: "اضافه حساب فرعي",
  noSubordinateClient: "لا يوجد حساب فرعي",
  superiorCustomerEmpty: "اختيار عميل مميز",
  noCustomerName: "اسم العميل",
  noLoginAccount: "مستخدم",
  noPsw: "كلمه السر",
  noConfirmPsw: "تطبيق الرقم السري",
  pswNotAtypism: "كلمه المرور غير فعاله",
  addSuccess: "تم الاضافه بنجاح",
  superiorCustomer: "عميل مميز",
  addVerticalImei: "اضافه رقم الجهاز",
  noImei: "No IMEI",
  addImei_curr: "ادخل رقم IMEI",
  no: "وحده",
  aRowAImei: "رقم IMEI لكل سطر",

  /*
   * dealer  Beginning of interface translation
   *
   * */
  //main.js
  imeiOrUserEmpty: "IMEI او اسم مستخدم فارغ!",
  accountEmpty: "IMEI/اسم/الحساب مطلوب",
  queryNoUser: "لا يوجد مستخدم للاستعلام",
  queryNoIMEI: "استفسار لا يمي",
  imeiOrClientOrAccount: "IMEI/اسم/حساب",
  dueSoon: "ل ينتهي",
  recentlyOffline: "غير متصل",
  choseSellDeviceFirst: "اختار الجهاز الذي تبيعه اولا",
  choseDeviceFirst: "اختار الجهاز الذي تنقله اولا",
  choseDeviceExpiresFirst: "يرجى اختيار جهاز تعديل أولا!",
  choseRenewDeviceFirst: "يرجى اختيار جهاز تجديد أولا!",
  choseDeleteDeviceFirst: "اختار الجهاز اولا للحذف!",
  choseClientFirst: "اختار العميل الذي تنقله اولا",

  //myClient.js
  clientList: "العملاء",
  accountInfo: "معلومات الحساب",
  machineCount: "كمية المعدات",
  stock: "Total",
  inventory: "Stock",
  subordinateClient: "الحسابات الفرعيه",
  datum: "معلومات",
  monitor: "مراقب",
  dueMachineInfo: "الاستحقاق",
  haveExpired: "المستحق",
  timeRange: [
    "بسبعه ايام",
    "بثلاثون يوم",
    "بستون يوم",
    "بسبعه ايام",
    "بثلاثون الى ستون يوم",
  ],
  offlineMachineInfo: "معلومات الاجهزه غير المتصله",
  timeRange1: [
    "بساعه",
    "بيوم",
    "بسبعه ايام",
    "بشهر",
    "بشهرين",
    "فوق شهرين",
    "بين ساعه لسبعه ايام",
    "بين يوم لسبعه ايام",
    "بين سبعه ايام الى ثلاثون",
    "بين ثلاثون يوم الى ستون ",
  ],
  offlineTime: "متوسط",
  includeSubordinateClient: "الحساب الفرعي",

  stopMachineInfo: "معلومات ثابته",
  stopTime1: "وقت ثابت",
  unUseMachineInfo: "معلومات غير نشطه",
  unUseMachineCount: "كميه غير نشطه",

  sellTime: "وقت البيع",
  detail: "تفاصيل الجهاز",
  manageDevice: "إدارة الجهاز",
  details: "تفاصيل الجهاز",
  deleteSuccess: "تم المسح",
  deleteFail: "فشل المسح",
  renewalLink: "تجديد اشتراك",
  deleteGroupTips: "سواء لحذف مجموعة",
  addGroup: "إضافة مجموعة",
  jurisdictionRange: "تعديل الوظائف",
  machineSellTransfer: "بيع جهاز",
  monitorMachineGroup: ".عرض الخريطة",
  jurisdictionArr: [
    "اداره العميل",
    "اداره الرسائل",
    "GEO اداره",
    "اداره التنبيه",
    "اداره حساي افتراضي",
    "ايفاد التعليمات",
  ],
  confrimDelSim: "تطبيق مسح الشريحه:",

  // 右键菜单
  sellDevice: "بيع جهاز",
  addClient: "أضف الزبون",
  deleteClient: "مسح عميل",
  resetPassword: "تغيير الرقم السري",
  transferClient: "نقل عميل",
  ifDeleteClient: "مسح عميل",

  //myAccount
  myWorkPlace: "حسابي",
  availablePoints: "توازن",
  yearCard: "تجديد سنه",
  lifetimeOfCard: "تجديد مدي الحياه",
  oneyear: "Annual",
  lifetime: "Lifelong",
  commonImportPoint: "بطاقه جديده",
  lifetimeImportPoint: "تجديد مدي الحياه",
  myServiceProvide: "المورد",
  moreOperator: "عمليات اكثر",
  dueMachine: "جهاز منتهى الصلاحيه",
  offlineMachine: "جهاز غير متصل",
  quickSell: "بيع اسرع",
  sellTo: "مركبات",
  machineBelong: "ينتمي الي",
  reset: "اعاده ضبط",
  targetCustomer: "مركبه العميل",
  common_lifetimeImport: "كارد سنوى(0),كارد مدى الحياه(0)",
  cardType1: "النوع",
  credit: "كميه",
  generateImportPoint: "اضافه بطاقه جديده",
  generateImportPointSuc: "تم اارسال بطاقه",
  generateImportPointFail: "فشل ارسال بطاقه",
  year_lifeTimeCard: "كارد جديد(0),كارد مدى الحياه جديد(0)",
  generateRenewPoint: "انشاء بطاقه تجديد",
  transferTo: "نقل",
  transferPoint: "كميه",
  transferRenewPoint: "نقل بطاقه تجديد",
  pointHistoryRecord: "ذاكره البطاقه",
  newGeneration: "جديد",
  operatorType: "عمليه",
  consume: "استهلاك",
  give: "يعطي",
  income: "دخل",
  pay: "مصروفات or استهلاك",
  imeiErr: "ادخل اخر 6 ارقام من رقم الجهاز",
  accountFirstPage: "صفحه رئيسيه",

  /*
   * dealer  End of interface translation
   *
   * */
  // 1.4.8 السيطرة على المخاطر
  finrisk: "المالية السيطرة على المخاطر",
  attention: "Sالاشتراك",
  cancelattention: "الغاء الاشتراك",
  poweroff: "خارج السلطة",
  inout: "داخل وخارج",
  inoutEF: "داخل وخارج السياج",
  longstay: "بقاء النقطه",
  secsetting: "اعدادت النقطه ",
  EFsetting: "اعدادت السياج",
  polygonFence: "سياج مضلع",
  cycleFence: "دوره السياج",
  haveBeenSetFence: "تم تعيين السياج",
  haveBeenSetPoint: "تم تحديد النقطه",
  drawingFailed: "فشل الرسم من فضلك ارسم!",
  inoutdot: "داخل وخارج",
  eleStatistics: "احصائيات الكهرباء",
  noData: "لا يوجد بيانات",
  // 进出二押点表格
  accountbe: "الحساب",
  SMtype: "نوع النقطه",
  SMname: "اسم النقطه",
  time: "الوقت",
  position: "الموقع",
  lastele: "البطاريه المتبقيه",
  statisticTime: "احصائيات البيانات",
  searchalarmType: [
    "الكل",
    "غير نشط",
    "مغلق",
    "داخل وخارج السياج",
    "داخل وخارج",
    "نقطه البقاء",
  ],
  remarks: ["نقطه البقاء", "ضمان الشركه", "نقطه تفكيك", "تسويق"],
  focusOnly: "Focus only",

  autoRecord: "التسجيل التلقائي",
  /******************************************************set command start**********************************8*/
  setCtrl: {
    text: "ضبط الاوامر",
    value: "",
  },
  moreCtrl: {
    text: 'المزيد من التعليمات',
    value: ''
},
  sc_openTraceModel: {
    text: "ضبط نظام التتبع",
    value: "0",
  },
  sc_closeTraceModel: {
    text: "قفل نظام التتبع",
    value: "1",
  },
  sc_setSleepTime: {
    text: "ضبط وقت القفل",
    value: "2",
  },
  sc_setAwakenTime: {
    text: "ضبط وقت التشغيل",
    value: "3",
  },
  sc_setDismantleAlarm: {
    text: "تجهيز تنبيه التفكك",
    value: "4",
  },
  sc_setSMSC: {
    text: "رقم سنتر",
    value: "5",
  },
  sc_delSMSC: {
    text: "حذف SMSC",
    value: "6",
  },
  sc_setSOS: {
    text: "اضافه رقم استغاثه",
    value: "7",
  },
  sc_delSOS: {
    text: "مسح رقم استغاثه",
    value: "8",
  },
  sc_restartTheInstruction: {
    text: "اعاده تعيين",
    value: "9",
  },
  sc_uploadTime: {
    text: "قم بضبط الفاصل الزمني للتحميل",
    value: "10",
  },
  /*Alarm clock time setting
     Timing reback time setting
     Dismantle alarm setting
     Week mode open close*/
  sc_setAlarmClock: {
    text: "ضبط ساعه التنبيه",
    value: "11",
  },
  sc_setTimingRebackTime: {
    text: "ضبط اعاده تعيين الوقت",
    value: "12",
  },
  sc_openWeekMode: {
    text: "وضع الاسبوع مفتوح",
    value: "13",
  },
  sc_closeWeekMode: {
    text: "وضع الاسبوع مغلق",
    value: "14",
  },

  sc_powerSaverMode: {
    text: "تحميل الفتره",
    value: "15",
  },
  sc_carCatchingMode: {
    text: "وضع التتبع",
    value: "16",
  },
  sc_closeDismantlingAlarm: {
    text: "غلق تنبيه التفكك",
    value: "17",
  },
  sc_openDismantlingAlarm: {
    text: "فتح تنبيه التفكك",
    value: "18",
  },
  sc_VibrationAlarm: {
    text: "تجهيز تنبيه الاهتزاز",
    value: "19",
  },
  sc_timeZone: {
    text: "إعداد المنطقة الزمنية",
    value: "20",
  },
  sc_phoneMonitor: {
    text: "الاستماع عبر الهاتف",
    value: "21",
  },
  sc_stopCarSetting: {
    text: "إعدادات وقوف السيارات",
    value: "22",
  },
  sc_bindAlarmNumber: {
    text: "ربط رقم التنبيه",
    value: "23",
  },
  sc_bindPowerAlarm: {
    text: "إنذار انقطاع التيار الكهربائي",
    value: "24",
  },
  sc_fatigueDrivingSetting: {
    text: "إعداد القيادة التعب",
    value: "25",
  },
  sc_peripheralSetting: {
    text: "الإعدادات الطرفية",
    value: "26",
  },
  sc_SMSAlarmSetting: {
    text: "تعيين رسالة نصية قصيرة",
    value: "27",
  },
  sc_autoRecordSetting: {
    text: "إعدادات التسجيل التلقائي",
    value: "28",
  },
  sc_monitorCallback: {
    text: "رد الاتصال",
    value: "29",
  },
  sc_recordCtrl: {
    text: "تعليمات التسجيل",
    value: "30",
  },
  sc_unbindAlarmNumber: {
    text: "رقم إنذار إلغاء التوثيق",
    value: "31",
  },
  sc_alarmSensitivitySetting: {
    text: "إعداد حساسية الإنذار بالاهتزاز",
    value: "32",
  },
  sc_alarmSMSsettings: {
    text: "إعدادات رسائل التنبيه للاهتزاز",
    value: "33",
  },
  sc_alarmCallSettings: {
    text: "إعدادات مكالمة التنبيه بالاهتزاز",
    value: "34",
  },
  sc_openFailureAlarmSetting: {
    text: "إنذار انقطاع التيار الكهربائي قيد التشغيل",
    value: "35",
  },
  sc_restoreFactory: {
    text: "استعادة المصنع",
    value: "36",
  },
  sc_openVibrationAlarm: {
    text: 'النص: "شغّل إنذار الاهتزاز',
    value: "37",
  },
  sc_closeVibrationAlarm: {
    text: 'النص: "أوقف تشغيل منبه الاهتزاز',
    value: "38",
  },
  sc_closeFailureAlarmSetting: {
    text: "إنذار انقطاع التيار الكهربائي",
    value: "39",
  },
  sc_feulAlarm: {
    text: "إعداد إنذار الوقود",
    value: "40",
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: "وضع توفير الطاقة",
    value: "41",
  },
  sc_sleepMode: {
    text: "وضع السكون",
    value: "42",
  },
  sc_alarmMode: {
    text: "وضع التنبيه",
    value: "43",
  },
  sc_weekMode: {
    text: "وضع الأسبوع",
    value: "44",
  },
  sc_monitorNumberSetting: {
    text: "رصد عدد الإعداد",
    value: "45",
  },
  sc_singlePositionSetting: {
    text: " طريقة واحدة لتحديد المواقع",
    value: "46",
  },
  sc_timingworkSetting: {
    text: "توقيت وضع التشغيل",
    value: "47",
  },
  sc_openLightAlarm: {
    text: "فتح جهاز استشعار الضوء إنذار",
    value: "48",
  },
  sc_closeLightAlarm: {
    text: "إغلاق ضوء استشعار إنذار",
    value: "49",
  },
  sc_workModeSetting: {
    text: "إعداد وضع العمل",
    value: "50",
  },
  sc_timingOnAndOffMachine: {
    text: "توقيت التبديل الإعداد",
    value: "51",
  },
  sc_setRealTimeTrackMode: {
    text: "ضبط وضع مطاردة في الوقت الحقيقي",
    value: "52",
  },
  sc_setClockMode: {
    text: "ضبط وضع المنبه",
    value: "53",
  },
  sc_openTemperatureAlarm: {
    text: "بدوره على درجة الحرارة التنبيه",
    value: "54",
  },
  sc_closeTemperatureAlarm: {
    text: "قم بإيقاف تشغيل إنذار درجة الحرارة",
    value: "55",
  },
  sc_timingPostbackSetting: {
    text: "توقيت إعدادات إعادة النشر",
    value: "56",
  },
  sc_remoteBoot: {
    text: "التمهيد عن بعد",
    value: "57",
  },
  sc_smartTrack: {
    text: "تتبع الذكية",
    value: "58",
  },
  sc_cancelSmartTrack: {
    text: "إلغاء التتبع الذكي",
    value: "59",
  },
  sc_cancelAlarm: {
    text: "إلغاء التنبيه",
    value: "60",
  },
  sc_smartPowerSavingMode: {
    text: "تعيين وضع توفير الطاقة الذكي",
    value: "61",
  },
  sc_monitorSetting: {
    text: "مراقب",
    value: '62'
  },
  sc_timedReturnMode: {
    text: 'وضع العودة الموقوت',
    value: '100'
  },
  sc_operatingMode: {
      text: ' وضع التشغيل',
      value: '101'
  },
  sc_realTimeMode : {
      text: ' وضع تحديد المواقع في الوقت الحقيقي',
      value: '102'
  },
  sc_alarmMode : {
      text: 'وضع التنبيه',
      value: '103'
  },
  sc_weekMode : {
      text: 'وضع الأسبوع',
      value: '104'
  },
  sc_antidemolitionAlarm : {
      text: ' إنذار مضاد للهدم',
      value: '105'
  },
  sc_vibrationAlarm : {
      text: 'إنذار الاهتزاز',
      value: '106'
  },
  sc_monitoringNumber : {
      text: 'مراقبة إدارة الأرقام',
      value: '107'
  },
  sc_queryMonitoring : {
      text: 'رقم مراقبة الاستعلام',
      value: '108'
  },
  sc_electricityControl : {
      text: 'التحكم في النفط والكهرباء',
      value: '109'
  },
  sc_SOSnumber : {
      text: 'إدارة رقم SOS',
      value: '110'
  },
  sc_SleepCommand : {
    text: 'أمر النوم',
    value: '201'
  },
  sc_RadiusCommand : {
      text: 'نصف قطر الإزاحة',
      value: '202'
  },
  sc_punchTimeMode:{
      text:'打卡模式',
      value:'203'  
  },
  sc_intervelMode:{
      text:'时间段模式',
      value:'204'  
  },
  sc_activeGPS:{
      text:'激活GPS',
      value:'205'  
  },
  sc_lowPowerAlert: {
    text: "تذكير البطارية المنخفضة" ,
    value: '206'
  },
  sc_SOSAlert: {
      text: 'SOS报警',
      value: '207'
  },
  mc_cuscom : {
    text: 'Hướng dẫn tùy chỉnh',
    value: '1'
  },
  NormalTrack: 'وضع التعقب الطبيعي',
  listeningToNumber:'هل أنت متأكد أنك تريد التحقق من رقم المراقبة لـ ؟',
  versionNumber:'هل أنت متأكد أنك تريد التحقق من رقم إصدار ؟',
  longitudeAndLatitudeInformation:'هل أنت متأكد أنك تريد التحقق من معلومات خطوط الطول والعرض الخاصة بـ',
  equipmentStatus:'هل أنت متأكد أنك تريد التحقق من حالة',
  public_parameter:' هل أنت متأكد أنك تريد التحقق من معلمات  لـ ',
  GPRS_parameter:' هل أنت متأكد أنك تريد التحقق من معلمات GPRS لـ ؟',
  deviceName: 'هل أنت متأكد أنك تريد تشغيل جهاز',
  SMS_alert:'هل تريد بالتأكيد التحقق من تنبيه التذكير عبر الرسائل القصيرة SMS لـ ؟',
  theBindingNumber:'هل أنت متأكد أنك تريد التحقق من الرقم الملزم لـ ؟',
  intervalTimeRange:'ضبط النطاق الزمني للفاصل الزمني هو 001-999 ، وحدة (دقيقة)',
  pleaseChoose:'اختر من فضلك',
  RealTimeCarChase: 'هل أنت متأكد أنك تريد ضبط هذا الجهاز على وضع مطاردة السيارة في الوقت الفعلي؟',
  inputPhoneNumber: "الرجاء إدخال رقم الهاتف",
  inputCorPhoneNumber: "الرجاء إدخال رقم الهاتف الصحيح",
  autoCallPhone: "نصيحة: بعد تنفيذ الأمر بنجاح ، ستطلب المحطة الطرفية الرقم المحدد تلقائيًا",
  limitTheNumberOfCellPhoneNumbers1:'يدعم هذا الأمر ما يصل إلى 5 أرقام هواتف محمولة',
  limitTheNumberOfCellPhoneNumbers2:'يدعم هذا الأمر ما يصل إلى 3 أرقام هواتف محمولة',
  equipmentTorestart:'هل أنت متأكد أنك تريد إعادة تشغيل هذا الجهاز',
  remindTheWay:'طريقة للتذكير',
  alarmWakeUpTime:'وقت تنبيه التنبيه',
  alarmWakeUpTime1:'وقت تنبيه التنبيه 1',
  alarmWakeUpTime2:'وقت تنبيه التنبيه 2',
  alarmWakeUpTime3:'وقت تنبيه التنبيه 3',
  alarmWakeUpTime4:'وقت إيقاظ التنبيه 4',
  sensitivityLevel:' الرجاء تحديد مستوى الحساسية',
  parking_time:'وقت وقوف السيارات',
  selectWorkingMode:'الرجاء تحديد وضع العمل',
  Alarm_value:' قيمة الإنذار',
  Buffer_value:'قيمة العازلة',
  gqg_disconnect:'قطع الاتصال',
  gqg_turnOn:'شغله',
  Return_interval:'الفاصل الزمني للعودة',
  gq_startTime:'وقت البدء',
  gq_restingTime:'وقت الراحة',
  gq_Eastern:'المنطقة الزمنية الشرقية',
  gq_Western:' المنطقة الزمنية الغربية',

  gq_driver:'إنذار القيادة التعب',
  gq_deviceName:'هل أنت متأكد أنك تريد تشغيل هذا الجهاز؟',
  gq_noteAlarm:'هل أنت متأكد أنك تريد التحقق من تنبيه التذكير عبر الرسائل القصيرة؟',
  gq_restoreOriginal:'هل أنت متأكد أنك تريد استعادة هذه المعدات إلى المصنع الأصلي؟',
  gq_normalMode:'لوضع العادي',
  gq_IntelligentsleepMode:'وضع النوم الذكي',
  gq_DeepsleepMode:'وضع السكون العميق',
  gq_RemotebootMode:'وضع التمهيد البعيد',
  gq_IntelligentsleepModeTips:'هل أنت متأكد أنك تريد الضبط على وضع السكون الذكي',
  gq_DeepsleepModeTips:'هل أنت متأكد أنك تريد التعيين على وضع السكون العميق',
  gq_RemotebootModeTips:'هل أنت متأكد أنك تريد التعيين على وضع التمهيد البعيد',
  gq_normalModeTips:'هل أنت متأكد أنك تريد التعيين إلى الوضع العادي',
  gq_sleepModeTips:'هل أنت متأكد أنك تريد ضبط هذا الجهاز على وضع السكون؟',
  gq_Locatethereturnmode:'تحديد وضع العودة',
  gq_regularWorkingHours:'فترة العمل المحددة بوقت',
  gq_AlarmType:{
      text:'نوع التنبيه',
      value: '111'
  },
  IssuedbyThePrompt:'تم إصدار الأمر ، برجاء انتظار استجابة الجهاز',
  platformToinform:'خطار المنصة',
  gq_shortNote:'إشعار SMS', 
  /************指令白话文**********************/
  closeDismantlingAlarm: "إغلاق التنبيه التفكيك",
  openDismantlingAlarm: "فتح تفكيك التنبيه",
  closeTimingRebackMode: "إغلاق وضع إعادة التشغيل التوقيت",
  minute: "الدقائق",
  timingrebackModeSetting: "تحميل الفتره:",
  setWakeupTime: "تجهيز وقت اليقظه:",
  weekModeSetting: "اعاده وضع الاسبوع:",
  closeWeekMode: "غلق وضع الاسبوع",
  setRealtimeTrackMode: "تعيين وضع المسار",
  fortification: "تحصين",
  disarming: "نزع",
  settimingrebackmodeinterval: "تعيين فاصل زمني لاعاده وضع الوقت:",
  oilCutCommand: "امر فصل البنزين",
  restoreOilCommand: "اعاده توصيل البنزين",
  turnNnTheVehiclesPower: "توصيل كهربا العربيه",
  turnOffTehVehiclesPower: "فصل كهربا العربيه",
  implementBrakes: "تطبيق الفرامل",
  dissolveBrakes: "حل الفرامل",
  openVoiceMonitorSlarm: "تشغيل تنبيه تتبع الصوت",
  closeVoiceMonitorAlarm: "فصل تنبيه تتبع الصوت",
  openCarSearchingMode: "وضع البحث  ف السياره المفتوحه",
  closeCarSearchingMode: "وضع البحث  ف السياره المقفوله",
  unrecognizedCommand: "امر غير معروف",
  commandSendSuccess: "مبروك!الجهاز نفذ الأمر بنجاح!",

  /********************************************设置指令结束**************************************************/

  /********************************************查询指令开始**************************************************/
  queryCtrl: {
    text: "استعلام الاوامر",
    value: "",
  },
  /*参数设置查询*/
  qc_softwareVersion: {
    text: "اصدار برنامج الاستعلام",
    value: "1",
  },
  qc_latlngInfo: {
    text: "الاستعلام عن خط الطول والعرض",
    value: "2",
  },
  qc_locationHref: {
    text: "تكوين معامل الاستعلام",
    value: "3",
  },
  qc_status: {
    text: "استعلام الحاله",
    value: "4",
  },
  qc_gprs_param: {
    text: "استعلام GPRS",
    value: "5",
  },
  qc_name_param: {
    text: "الاسم",
    value: "6",
  },
  qc_SMSReminderAlarm_param: {
    text: "تذكير تذكير الرسائل القصيرة الاستعلام",
    value: "7",
  },
  qc_bindNumber_param: {
    text: "رقم طلب البحث",
    value: "8",
  },

  /********************************************查询指令结束**************************************************/

  /*******************************************控制指令开始***************************************************/

  controlCtrl: {
    text: "تحكم الامر",
    value: "",
  },
  cc_offOilElectric: {
    text: "فصل البنزين الكتروني",
    value: "1",
  },
  cc_recoveryOilElectricity: {
    text: "تشغيل البنزين الكتروني",
    value: "2",
  },
  cc_factorySettings: {
    text: "ضبط المصنع",
    value: "4",
  },
  cc_fortify: {
    text: "مانع",
    value: "75",
  },
  cc_disarming: {
    text: "نزع",
    value: "76",
  },
  cc_brokenOil: {
    text: "تعليمات قطع النفط",
    value: "7",
  },
  cc_RecoveryOil: {
    text: "دائرة زيت الانتعاش",
    value: "8",
  },

  /*******************************************控制指令结束***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: "KM",
  mileage: "عدد الأميال",
  importMachine: "اضف جديد IMEI",
  transferImportPoint: "نقل بطاقة جديدة",
  machineType1: "نموذج",
  confirmIMEI:
    "يرجى التأكد من صلاحية رقم IMEI واسم الطراز قبل الاستيراد ، لأن العملية غير قابلة للإلغاء.",
  renew: "جدد",
  deductPointNum: "كمية",
  renewSuccess: "تجديد النجاح!",
  wireType: [
    {
      text: "سلكى",
      value: false,
    },
    {
      text: "لاسلكى",
      value: true,
    },
  ],
  vibrationWays: [
    {
      text: "منصه",
      value: 0,
    },
    {
      text: "منصه+رسائل",
      value: 1,
    },
    {
      text: "منصه+رسائل+هاتف",
      value: 2,
    },
  ],
  addMachineType: [
    {
      text: "S06",
      value: "3",
    },
    // SO6子级-----start-----------
    {
      text: "GT06",
      value: "8",
    },
    {
      text: "S08V",
      value: "9",
    },
    {
      text: "S01",
      value: "10",
    },
    {
      text: "S01T",
      value: "11",
    },
    {
      text: "S116",
      value: "12",
    },
    {
      text: "S119",
      value: "13",
    },
    {
      text: "TR06",
      value: "14",
    },
    {
      text: "GT06N",
      value: "15",
    },
    {
      text: "S101",
      value: "16",
    },
    {
      text: "S101T",
      value: "17",
    },
    {
      text: "S06U",
      value: "18",
    },
    {
      text: "S112U",
      value: "19",
    },
    {
      text: "S112B",
      value: "20",
    },
    // SO6子级-----end-----------
    {
      text: "S15/S02F",
      value: "1",
    },
    {
      text: "S05",
      value: "2",
    },
    {
      text: "SW06",
      value: "4",
    },
    {
      text: "S001",
      value: "5",
    },
    {
      text: "S08",
      value: "6",
    },
    {
      text: "S09",
      value: "7",
    },
  ],

  /*
   * 2018-02-02
   * */
  maploadfail: "تريد الانتقال لخريطه اخري اسف,فشل تحميل الخريطه",

  /*
    2018-03-06新增 控制指令
    * */
  cc_openPower: {
    text: "تشغيل كهربا العربيه",
    value: "7",
  },
  cc_closePower: {
    text: "قفلكهربا العربيه",
    value: "8",
  },
  cc_openBrake: {
    text: "فتح الفرامل",
    value: "9",
  },
  cc_closeBrake: {
    text: "غلق الفرامل",
    value: "10",
  },
  cc_openAlmrmvoice: {
    text: "تشغيل التنبيه",
    value: "11",
  },
  cc_closeAlmrmvoice: {
    text: "قفل التنبيه",
    value: "12",
  },
  /*2018-03-06新增 控制指令
   * */
  cc_openFindCar: {
    text: "البحث عن عربيه",
    value: "13",
  },
  cc_closeFindCar: {
    text: "قفل البحث عن عربيه",
    value: "14",
  },
  /*2018-03-19
   * */
  EF: "منطقه جغرافيه",

  /*
    2018-03-29，扩展字段
    * */
  exData: ["مقاومة","كهرباء", "الجهد", "وقود", "درجة الحرارة"],

  /*
    2018-04-10
    * */
  notSta: "غير موجوده  بالاحصائيات",

  // 油量统计
  fuelSetting: "وضع الوقود",
  mianFuelTank: "خزان الوقود الرئيسي",
  auxiliaryTank: "خزان الوقود الثانوي",
  maximum: " أقصى",
  minimum: "الحد الأدنى",
  FullTankFuel: "ملئ خزان وقود ",
  fuelMinValue: 'لا يمكن أن يكون حجم الوقود أقل من 10 لتر',
  standardSetting: "وضع المعايير",
  emptyBoxMax: "علبة  قيمة أكبر",
  fullBoxMax: "أقصى قيمة  فارغة",
  fuelStatistics: "من وقود إحصاءات",
  settingSuccess: "النجاح",
  settingFail: "فشل",
  pleaseInput: "الرجاء إدخال",
  fuelTimes: "الوقود مرة",
  fuelTotal: "وقود توتال",
  refuelingTime: 'الوقت للتزود بالوقود',
  fuelDate: "الوقود الآن",
  fuel: "وقود",
  fuelChange: "وقود التغيير",
  feulTable: "جدول تحليل الوقود",
  addFullFilter: "الرجاء إضافة الفلتر الكامل",
  enterIntNum: "يرجى إدخال عدد صحيح موجب",
  // 温度统计
  tempSta: "إحصاءات درجة الحرارة",
  tempTable: "جدول تحليل درجة الحرارة",
  industrySta: "إحصائيات الصناعة",
  temperature: "درجة الحرارة",
  temperature1: "1درجة الحرارة",
  temperature2: "2درجة الحرارة",
  temperature3: "3درجة الحرارة",
  tempRange: "نطاق درجة الحرارة",
  tempSetting:'ضبط درجة الحرارة',
  tempSensor:'مستشعر درجة الحرارة',
  tempAlert:'بعد إيقاف تشغيل مستشعر درجة الحرارة ، لن يتلقى بيانات درجة الحرارة!',
  phoneNumber: "رقم الهاتف المحمول",
  sosAlarm: "إنذار SOS",
  undervoltageAlarm: "إنذار انخفاض الجهد",
  overvoltageAlarm: "إنذار الجهد الزائد",
  OilChangeAlarm: "إنذار تغيير الزيت",
  accDetection: "كشف ACC",
  PositiveAndNegativeDetection: "الاكتشاف الإيجابي والسلبي",
  alermValue: "قيمة التنبيه",
  bufferValue: "قيمة المخزن المؤقت",
  timeZoneDifference: "فرق التوقيت الزمني",
  meridianEast: "ميريديان الشرق",
  meridianWest: "غرب خط الطول",
  max12hour: "لا يمكن أن يكون أكثر من 12 ساعة",
  trackDownload: "تتبع التحميل",
  download: "تحميل",
  multiReset: "إعادة تعيين السائبة",
  resetSuccess: "إعادة النجاح",
  multiResetTips: "نصائح إعادة تعيين متعددة",
  point: "نقطة",
  myplace: "مكاني",
  addPoint: "أضف نقطة",
  error10018: "التوازن لا يكفي",
  error110:'الكائن غير موجود',
  error109:'تم تجاوز الحد الأقصى',
  error20013:'نوع الجهاز غير موجود',
  error90001:'لا يمكن أن يكون الرقم التسلسلي لنوع الجهاز فارغًا',
  error20003:'لا يمكن أن يكون Imei فارغًا',
  inputName: "اسم الإدخال",
  virtualAccount: "حساب افتراضي",
  createTime: "خلق الوقت",
  permission: "الإذن",
  permissionRange: "نطاق الإذن",
  canChange: "يمكن أن تتغير",
  fotbidPassword: "مراجعة كلمة المرور",
  virtualAccountTipsText: "نصائح الحساب الافتراضية النص",
  noOperationPermission: "لا يوجد إذن",
  number: "رقم",
  rangeSetting: "ضبط النطاق",
  setting: "ضبط",
  // 1.6.1
  duration: "المدة؟",
  voltageSta: "الفولطية",
  voltageAnalysis: " الفولطية",
  voltageEchart: " تحليل الفولطية",
  platformAlarm: " منصة",
  platformAlarm1:' الهاتف',
  platformAndPhone: 'الهاتف+المنصات',
  smsAndplatformAlarm: "SMS+ المنصات",
  smsAndplatformAlarm1: "SMS",
  smsAndplatformAlarmandPhone: " المنصة + SMS+ الهاتف",
  smsAndplatformAlarmandPhone1: " SMS+ الهاتف",
  more_speed: "Km",
  attribute: "السمة",
  profession: "محترفة",
  locationPoint: "مرساة نقطة",
  openPlatform: "فتح API",
  experience: "عرض",
  onlyViewMonitor: "يمكن فقط عرض الرصد",
  inputAccountOrUserName: "الرجاء إدخال رقم الحساب أو اسم المستخدم",
  noDeviceTips: "لم تجد معلومات المعدات ذات الصلة ، والتحقق من العملاء",
  noUserTips: "لم تجد معلومات المستخدم ذات الصلة ، والتحقق من المعدات",
  clickHere: "اضغط هنا",
  pointIntervalSelect: "نقطة المسار الفاصل",
  payment: "دفع",
  pleaceClick: "انقر على",
  paymentSaveTips: "نصيحة الأمان: يرجى تأكيد مع مزود الخدمة صحة الرابط",
  fuelAlarmValue: "قيمة التنبيه كمية النفط",
  fuelConsumption: "استهلاك الوقود",
  client: "زبون",
  create: "جديد",
  importPoint: "نقطة البداية",
  general: "عادي",
  lifelong: "مدى الحياة",
  renewalCard: "تجديد البطاقة",
  settingFuelFirst: "يرجى ضبط قيمة إنذار كمية الزيت قبل إرسال الأمر.",
  overSpeedSetting: "إنذار السرعة",
  kmPerHour: "km/h",
  times: "مرات",
  total: "مجموع",
  primary: "ابتدائي",
  minor: "النائب",
  unActiveTips: "الجهاز غير مفعل ولا يمكن استخدامه.",
  arrearsTips: "الجهاز في متأخرات ولا يمكنه استخدام هذه الميزة",
  loading: "جار التحميل",
  expirationReminder: "تذكير انتهاء",
  projectName: "اسم المشروع",
  expireDate: "تاريخ انقضاء",
  changePwdTips:
    "كلمة السر الخاصة بك هو بسيط جدا  ، هناك مخاطر أمنية  ، يرجى تعديل كلمة السر الخاصة بك على الفور",
  pwdCheckTips1: "الاقتراحات هي 6-20 حرفًا أو أرقامًا أو رموزًا",
  pwdCheckTips2: "كلمة السر الخاصة بك قوة ضعيفة جد",
  pwdCheckTips3: "كلمة السر الخاصة بك يمكن أن تكون أكثر تعقيد",
  pwdCheckTips4: "كلمة السر الخاصة بك آمنة جدا",
  pwdLevel1: "أضعف",
  pwdLevel2: "وسط",
  pwdLevel3: "قوي",
  comfirmChangePwd: "تأكيد تغيير كلمة المرور",
  notSetYet: "لم تحدد بعد",
  liter: "لتر",
  arrearageDayTips: "تنتهي بعد أيام",
  todayExpire: "بسبب اليوم",
  forgotPwd: "هل نسيت كلمة مرورك؟",
  forgotPwdTips: "يرجى الاتصال البائع لتغيير كلمة المرور الخاصة بك.",
  //1.6.7
  commonProblem: "مشكلة شائعة",
  instructions: "تعليمات",
  webInstructions: "تعليمات الويب",
  appInstructions: "تعليمات التطبيق",
  acceptAlarmNtification: "إنذار إنذار",
  alarmPeriod: "فترة الإنذار",
  whiteDay: "يوم",
  blackNight: "ليل",
  allDay: "كل اليوم",
  alarmEmail: "بريد الانذار",
  muchEmailTips: 'يمكن إدخال صناديق بريد متعددة ، مفصولة بالرمز "؛".',
  newsCenter: "مركز إعلام",
  allNews: "الكل",
  unReadNews: "غير مقروء",
  readNews: "اقرأ",
  allTypeNews: "أنواع الإخطار",
  alarmInformation: "معلومات الإنذار",
  titleContent: "عنوان",
  markRead: "علامة القراءة",
  allRead: "الكل يقرأ",
  allDelete: "حذف الكل",
  selectFirst: "اختر الأول!",
  updateFail: "فشل التحديث!",
  ifAllReadTips: "الكل يقرأ؟",
  ifAllDeleteTips: "حذف الكل؟",
  stationInfo: "محطة المعلومات",
  phone: "الهاتف المحمول",
  //1.6.72
  plsSelectTime: "يرجى اختيار الوقت!",
  customerNotFound: "لا يمكن العثور على العميل",
  Postalcode: "موقع",
  accWarning: "إنذار ACC",
  canInputMultiPhone: "يمكنك إدخال أرقام جوال متعددة ، يرجى استخدام ؛ منفصلة",
  noLocationInfo: "الجهاز ليس لديه معلومات الموقع",
  //1.6.9
  fenceName: "اسم السياج",
  fenceManage: "إدارة السياج",
  circular: "جولة",
  polygon: "المضلع",
  allFence: "كل السياج",
  shape: "شكل",
  stationNews: " رسالة المحطة",
  phonePlaceholder: 'يمكن إدخال أرقام متعددة ، مفصولة بـ "،"',
  addressPlaceholder:
    'الرجاء إدخال العنوان والرمز البريدي بالترتيب مفصولة بـ "،"',
  isUnbind: "سواء لإلغاء الربط",
  alarmCar: "مركبة الانذار",
  alarmAddress: "موقع المنبه",
  chooseAtLeastOneTime: "اختر مرة واحدة على الأقل",
  alarmMessage: "لا توجد تفاصيل حول معلومات الإنذار هذه.",
  navigatorBack: "العودة إلى الرئيس",
  timeOverMessage:
    "لا يمكن أن يكون وقت انتهاء صلاحية المستخدم أكبر من وقت انتهاء صلاحية النظام الأساسي",
  //1.7.0
  userTypeStr: "نوع المستخدم",
  newAdd: "جديد",
  findAll: "مجموع",
  findStr: " مطابقة البيانات",
  customColumn: "تخصيص",
  updatePswErr: "تم تحديث كلمة المرور الفاشلة",
  professionalUser: "مستخدم محترف أم لا؟",
  confirmStr: "أكد",
  inputTargetCustomer: "العميل المستهدف",
  superiorUser: "مستخدم متفوق",
  speedReport: "تقرير السرعة",
  createAccount: "انشاء حساب",
  push: "تنبيه",
  searchCreateStr: "سيعمل على تشغيل حساب ونقل الجهاز إلى هذا الحساب.",
  allowIMEI: "السماح بتسجيل الدخول إلى IMEI",
  defaultPswTip: "كلمة المرور الافتراضية هي آخر 6 أرقام من IMEI",
  createAccountTip: "تم إنشاء الحساب ونقل الجهاز بنجاح",
  showAll: "إظهار الكل",
  bingmap: "بنج خريطة",
  areaZoom: "زوم",
  areaZoomReduction: "استعادة التكبير",
  reduction: "انخفاض",
  saveImg: "حفظ كصورة",
  fleetFence: "سياج الأسطول",
  alarmToSub: "إشعار إنذار",
  bikeFence: "سياج الدراجة",
  delGroupTip: "أخفق الحذف ، يرجى حذف نقاط الاهتمام أولاً!",
  isExporting: "تصدير ...",
  addressResolution: "دقة العنوان ...",
  simNOTip: "يمكن أن يكون رقم بطاقة SIM مجرد رقم",
  unArrowServiceTip:
    "وقت انتهاء صلاحية المستخدم للأجهزة التالية أكبر من وقت انتهاء صلاحية النظام الأساسي ، يرجى إعادة التحديد. رقم الجهاز هو:",
  platformAlarmandPhone: "منصة إنذار + هاتف",
  openLightAlarm: "فتح جهاز استشعار الضوء إنذار",
  closeLightAlarm: "إغلاق ضوء استشعار إنذار",
  ACCAlarm: "إنذار ACC",
  translateError: "فشل النقل ، المستخدم المستهدف ليس لديه إذن",
  distanceTip: 'انقر فوق "موافق" ، انقر نقرًا مزدوجًا للانتهاء',
  workMode: "نمط العمل",
  workModeType: "0: الوضع العادي ؛ 1 وضع السكون الذكي ؛ 2 وضع السكون العميق",
  clickToStreetMap: "انقر فوق فتح خريطة الشارع",
  current: "تيار",
  remarkTip: "ملاحظة: لا تبيع بطاقات من أنواع مختلفة من الحزم في نفس الوقت",
  searchRes: "نتيجة البحث",
  updateIcon: "تغيير الأيقونة",
  youHaveALarmInfo: "لديك رسالة تنبيه",
  moveInterval: "فترة التمرين",
  staticInterval: "الفاصل الزمني الفاصل",
  notSupportTraffic: "Coming soon.",
  ignite: "لجنة التنسيق الإدارية على",
  flameout: "لجنة التنسيق الإدارية قبالة",
  generateRenewalPointSuc: "توليد نقاط التجديد بنجاح",
  noGPSsignal: "لا وضعه",
  imeiErr2: "الرجاء إدخال آخر 6 أرقام على الأقل من رقم imei",
  searchCreateStr2: "سيؤدي هذا إلى إنشاء حساب ونقل هذا الجهاز إلى اسم الحساب",
  addUser: "إضافة مستخدم",
  alarmTemperature: "قيمة درجة حرارة التنبيه",
  highTemperatureAlarm: "ارتفاع درجة الحرارة التنبيه",
  lowTemperatureAlarm: "انخفاض درجة الحرارة التنبيه",
  temperatureTip: "الرجاء إدخال قيمة درجة الحرارة！",
  locMode: "وضع تحديد المواقع",
  imeiInput: "：الرجاء إدخال رقم IMEI",
  noResult: "لا توجد نتائج مطابقة",
  noAddressKey: "يتعذر مؤقتًا الحصول على معلومات العنوان",
  deviceGroup: "إدارة المجموعة",
  shareManage: "مشاركة الإدارة",
  lastPosition: "الموضع الأخير",
  defaultGroup: "مجموعة Defaul",
  tankShape: "شكل خزان الوقود",
  standard: "قياسي",
  oval: "البيضاوي",
  irregular: "غير منتظم",
  //1.8.4
  inputAddressOrLoc: "الرجاء إدخال العنوان / خطوط الطول",
  inputGroupName: "اسم مجموعة الإدخال",
  lock: "قفل",
  shareHistory: "مشاركة سجل التشغيل",
  tomorrow: "  غدا غدا",
  threeDay: "3 أيام",
  shareSuccess: "مشاركة رابط النجاح",
  effective: "فعال",
  lapse: "غير صالح",
  copyShareLink: "نسخ نجاح الرابط",
  openStr: "ON",
  closeStr: 'OFF',
  linkError: "خطأ في الرابط",
  inputUserName: "اسم مستخدم الإدخال",
  barCodeStatistics: "إحصائيات ",
  barCode: "bar code",
  sweepCodeTime: "وقت المسح",
  workModeType2:
    "1 وضع السكون الذكي ；2 وضع النوم العميق ；3 وضع التبديل عن بعد",
  remoteSwitchMode: "وضع التبديل عن بعد",
  saleTime: "تاريخ البيع",
  onlineTime: "تاريخ الاطلاق",
  dayMileage: "الأميال اليوم",
  imeiNum: "IMEI ",
  overSpeedValue: "السرعة الزائدة عتبة",
  shareNoOpen: "لم يتم تمكين رابط المشاركة",
  addTo2: "Customer",
  overDue: "منتهية الصلاحية",
  openInterface: "واجهة مفتوحة",
  privacyPolicy: "سياسة الخصوصية",
  serviceTerm: "شروط الخدمة",
  importError:
    "تعذرت الإضافة ، يجب أن يكون رقم الجهاز (IMEI) مكونًا من 15 رقمًا",
  importResult: "نتائج الاستيراد",
  totalNum: "مجموع",
  successInfo: "نجاح",
  errorInfo: "فشل",
  repeatImei: "كرر IMEI",
  includeAccount: "حساب فرعي",
  formatError: "تالف",
  importErrorInfo: "الرجاء إدخال رقم IMEI المكون من 15 رقمًا",
  totalMileage: "إجمالي الأميال",
  totalOverSpeed: "إجمالي السرعة الزائدة (مرات)",
  totalStop: "إجمالي التوقف (مرات)",
  totalOil: "Total oil",
  timeChoose: "اختيار الوقت",
  intervalTime: "وقت الفاصل الزمني",
  default: "افتراضي",
  idleSpeedStatics: "إحصائيات السرعة الخاملة",
  offlineStatistics: 'إحصائيات غير متصل',
  idleSpeed: "سرعة الخمول",
  idleSpeedTimeTip1: "وقت الخمول لا يمكن أن يكون فارغًا",
  idleSpeedTimeTip2: "يجب أن يكون وقت الخمول عددًا صحيحًا موجبًا",
  averageSpeed: "متوسط السرعة",
  averageOil: "متوسط استهلاك الوقود",
  oilImgTitle: "مخطط تحليل النفط",
  oilChangeDetail: "تفاصيل تغيير الوقود",
  machineNameError: "لا يمكن أن يحتوي اسم الجهاز على رموز خاصة (/ ')",
  remarqueError: "معلومات الملاحظة لا يمكن أن تتجاوز 50 كلمة",
  defineColumnTip: "تحقق من ما يصل إلى 12 عنصرًا",
  pswCheckTip: "الاقتراح عبارة عن مزيج من 6-20 رقمًا وحروفًا ورموزًا",
  chooseGroup: "الرجاء تحديد مجموعة",
  chooseAgain:
    "يمكنك فقط الاستعلام عن البيانات للأشهر الستة الماضية ، يرجى التحديد مرة أخرى！",
  noDataTip: "لا توجد بيانات！",
  noMachineNameError: "يرجى تحديد جهاز！",
  loginAccountError: "لا يمكن أن يتكون حساب تسجيل الدخول من 15 رقمًا！",
  includeExpire: "التي تنتهي صلاحيتها",
  groupNameTip: "اسم المجموعة لا يمكن أن يكون فارغًا！",
  outageTips: "هل أنت متأكد من قطع النفط؟",
  powerSupplyTips: "هل أنت متأكد من استعادة النفط؟",
  centerPhoneTips: "يرجى إدخال الرقم",
  centerPhoneLenTips: "رجى إدخال 8-20 رقمًا",
  passworldillegal: "هناك أحرف غير قانونية",
  // 2.0.0 POI，权限版本
  singleAdd:'إضافة واحدة',
  batchImport:'استيراد دفعة',
  name:'الاسم',
  icon:'رمز',
  defaultGroup:'المجموعة الافتراضية',
  remark:'ملاحظة',
  uploadFile:'تحميل الملف',
  exampleDownload:' تنزيل مثال',
  uploadFiles:'تحميل الملف',
  poiTips1:' يمكنك استيراد POI عن طريق تحميل ملف Excel مع المعلومات ذات الصلة. يرجى اتباع شكل المثال لإعداد الملف.',
  poiTips2:'الاسم: مطلوب ، لا يزيد عن 32 حرفًا ؛',
  poiTips3:'أيقونة: مطلوب ، أدخل 1،2،3،4',
  poiTips4:'.خط العرض:- مطلوب',
  poiTips5:'خط الطول: مطلوب',
  poiTips6:'.اسم المجموعة: اختياري ، لا يزيد عن 32 حرفًا. إذا لم يتم ملء اسم المجموعة ، فإن نقطة POI تنتمي إلى المجموعة الافتراضية. إذا كان اسم المجموعة المملوء متسقًا مع اسم المجموعة التي تم إنشاؤها ، فإن نقطة POI تنتمي إلى المجموعة التي تم إنشاؤها. لم يتم إنشاء اسم المجموعة ، سيقوم النظام بإضافة المجموعة ؛',
  poiTips7:'ملاحظات: اختيارية ، لا تزيد عن 50 حرفًا',
  // 权限相关
  roleLimit: 'أذونات الدور',
  operateLog: 'سجل العمليات',
  sysAccountManage: 'حساب الهيئة',
  rolen: 'الأدوار',
  rolename: 'اسم الدور',
  addRole: 'دور جديد',
  editRole: 'تحرير الدور',
  deleteRole: 'حذف الدور',
  delRoleTip: 'هل أنت متأكد أنك تريد حذف هذا الدور؟',
  delAccountTip: 'هل أنت متأكد أنك تريد حذف هذا الحساب؟',
  limitconfig: 'ملف تعريف الحقوق',
  newAccountTip1: 'حساب السلطة مشابه للحساب الافتراضي القديم ، وهو الحساب الفرعي للمسؤول. يمكن للمسؤولين إنشاء حسابات الاستناد وتعيين أدوار مختلفة لحسابات الاستناد ، بحيث يمكن للحسابات المختلفة رؤية محتوى وعمليات مختلفة على النظام الأساسي.',
  newAccountTip2: 'عملية إنشاء حساب إذن:',
  newAccountTip31: '1. في صفحة إدارة الدور ،',
  newAccountTip32: 'دور جديد',
  newAccountTip33: '، وتكوين أذونات الدور ؛',
  newAccountTip4: '2. في صفحة إدارة حساب الاستناد ، قم بإنشاء حساب سلطة جديد وقم بتعيين الأدوار للحساب.',
  newRoleTip1: 'يمكن للمسؤولين إنشاء أدوار وتكوين أذونات تشغيل مختلفة لأدوار مختلفة لتلبية احتياجات العمل في سيناريوهات مختلفة.',
  newRoleTip2: 'على سبيل المثال ، قم بتكوين ما إذا كان الدور المالي لديه الإذن لتحديد موقع ومراقبته ، وما إذا كان لديه الإذن بإضافة عملاء ، وما إذا كان لديه الإذن بتعديل معلومات الجهاز ، وما إلى ذلك.',
  "refuelrate": "معدل التزود بالوقود",
  "refuellimit": "عندما تكون الزيادة في الزيت في الدقيقة أكبر من xxxxL وأقل من xxxxL ، فإنها تعتبر إعادة تعبئة للوقود",
  "refueltip": "يجب ألا يقل الحد الأقصى لمعدل إعادة التزود بالوقود عن الحد الأدنى لمعدل إعادة التزود بالوقود!",
  viewLimitConf: 'عرض إعدادات الأذونات',
  viewLimit: 'عرض الأذونات',
  newSysAcc: 'حساب نظام جديد',
  editSysAcc: 'تحرير حساب إذن',
  virtualAcc: 'حساب افتراضي',
  oriVirtualAcc: 'الحساب الافتراضي الأصلي',
  virtualTip: 'تمت ترقية وحدة الحساب الافتراضي إلى وحدة حساب النظام ، يرجى إنشاء حساب نظام جديد',
  operaTime: 'وقت التشغيل',
  ipaddr: 'عنوان IP',
  businessType: 'نوع العمل',
  params: 'طلب المعلمة',
  operateType: 'نوع العملية',
  uAcc: 'حساب المستخدم',
  uName: 'اسم المستخدم',
  uType: 'نوع المستخدم',
  logDetail: 'تفاصيل السجل',
  delAccount: 'حذف الحساب',
  modifyTime: 'تعديل الوقت',
  unbindlimit: 'لا يمكن إنشاء سياج إلكتروني بنقرة واحدة بدون أذونات الجهاز المرتبطة!',
  setSmsTip: 'إذا قمت بإعداد إشعار SMS ، فأنت بحاجة إلى فتح إشعار النظام الأساسي أولاً ؛ إذا تم إرسال إشعار النظام الأساسي بنجاح ، فلن ينجح إشعار SMS ، فأنت بحاجة إلى تشغيل إشعار SMS مرة أخرى',
  cusSetComTip: 'إخلاء المسؤولية: لا علاقة للمخاطر الناتجة عن الإرشادات المخصصة بالمنصة',
  cusSetComPas: 'الرجاء إدخال كلمة المرور لحساب تسجيل الدخول الحالي',
  cusSetComDes1: 'التعليمات المخصصة تدعم التعليمات عبر الإنترنت فقط.',
  cusSetComDes2: 'إذا لم يستجب الجهاز خلال دقيقتين من إرسال الأمر ، يتم إنهاء العملية ويتم الحكم على حالة الأمر على أنها لا توجد استجابة.',
  cueSetComoffline: 'الجهاز لا يستجيب وفشل إرسال الأمر المخصص!',
  fbType: 'نوع الأنطباع',
  fbType1: 'استشاري',
  fbType2: 'عطل',
  fbType3: 'تجربة المستخدم',
  fbType4: 'اقتراحات الميزات الجديدة',
  fbType5: 'آخر',
  upload: 'رفع',
  uploadImg: 'تحميل الصور',
  uploadType: 'يرجى تحميل ملفات من النوع jpg .png .jpeg .gif',
  uploadSize: 'لا يمكن أن يكون ملف التحميل أكبر من 3 ميجا',
  fbManager: 'إدارة الملاحظات',
  blManager: 'إدارة الإعلان',
  fbUploadTip: 'الرجاء تحديد نوع التعليقات',
  menuPlatform: "منصة الأخبار",
  menuFeedback: "ردود الفعل",
  menuBulletin: "إعلان المنصة",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "سلوك القيادة",
  BtyjdfghtwsrgGHFEEGRDAF: "التسريع السريع",
  BtyuwyfgrWERERRTHDAsdDF: "التباطؤ السريع",
  Be2562h253grgsHHJDbRDAF: "منعطف حاد",
  celTemperature:'درجة حرارة مئوية'
};
// 权限tree
lg.limits = {
  "ACC_statistics": "احصائيات",
  "Account_Home": "صفحه رئيسيه",
  "Add": "جديد",
  "Add_POI": "أضف POI",
  "Add_customer": "أضف الزبون",
  "Add_device_group": "أضف مجموعة الأجهزة",
  "Add_fence": "أضف السياج",
  "Add_sharing_track": "إضافة مسار المشاركة",
  "Add_system_account": "حساب إذن جديد",
  "Alarm_details": "تفاصيل التنبيهات",
  "Alarm_message": "رسالة إنذار",
  "Alarm_overview": "عرض التنبيهات",
  "Alarm_statistics": "تقرير التنبيه",
  "All_news": "الكل",
  "Associated_equipment": "جهاز مقترن",
  "Available_points": "توازن",
  "Barcode_statistics": "إحصائيات ",
  "Batch_Import": "استيراد دفعة",
  "Batch_renewal": "دفعة التجديد",
  "Batch_reset": "إعادة تعيين السائبة",
  "Bulk_sales": "بيع مجموعه",
  "Call_the_police": "إنذار",
  "Customer_details": "تفاصيل العميل",
  "Customer_transfer": "نقل عميل",
  "Delete_POI": "حذف POI",
  "Delete_account": "حذف الحساب",
  "Delete_customer": "مسح عميل",
  "Delete_device": "حذف الجهاز",
  "Delete_device_group": "حذف مجموعة الجهاز",
  "Delete_fence": "حذف السياج",
  "Delete_role": "حذف الدور",
  "Device_List": "قائمه الاجهزه",
  "Device_grouping": "إدارة المجموعة",
  "Device_transfer": "نقل الجهاز",
  "Due_reminder": "تذكير انتهاء",
  "Edit_details": "عدل التفاصيل",
  "Equipment_management": "اجهزتي",
  "My_clinet": "اجهزتي",
  "Fence": "منطقه جغرافيه",
  "Fence_management": "إدارة السياج",
  "Generate": "انشاء",
  "Generate_lead-in_points": "اضافه بطاقه جديده",
  "Generate_renewal_points": "انشاء بطاقه تجديد",
  "Have_read": "ازاله",
  "Idle_speed_statistics": "إحصائيات السرعة الخاملة",
  "Import": "استيراد",
  "Import_Device": "اضف جديد IMEI",
  "Industry_Statistics": "إحصائيات الصناعة",
  "Location_monitoring": "مراقبة الموقع",
  "Log_management": "إدارة السجل",
  "Mark_read": "علامة القراءة",
  "Menu_management": "إدارة القائمة",
  "Message_Center": "مركز إعلام",
  "Mileage_statistics": "تقارير الاميال",
  "Modify_POI": "تعديل POI",
  "Modify_device_details": "عديل تفاصيل الجهاز",
  "Modify_device_group": "تعديل مجموعة الجهاز",
  "Modify_role": "تعديل الدور",
  "Modify_sharing_track": "تعديل مشاركة المسار",
  "Modify_user_expiration": "ينتهي تعديل دفعة المستخدمين",
  "More": "أكثر",
  "My_client": "عميلي",
  "New_role": "دور جديد",
  "New_users": "إضافة مستخدم",
  "Oil_statistics": "من وقود إحصاءات",
  "POI_management": "إدارة POI",
  "Points_record": "ذاكره البطاقه",
  "Push": "تنبيه",
  "Quick_sale": "بيع اسرع",
  "Renew": "جدد",
  "Replay": " اعاده",
  "Role_management": "إدارة الدور",
  "Run_overview": "نظره عامه",
  "Running_statistics": "نظره عامه",
  "Sales_equipment": "بيع جهاز",
  "Set_expiration_reminder": "تعيين تذكير انتهاء الصلاحية",
  "Share_track": "مشاركة سجل التشغيل",
  "Sharing_management": "مشاركة الإدارة",
  "Speeding_detailed_list": "تفاصيل تجاوز السرعه",
  "Statistical_report": "تقرير احصائي",
  "Stay_detailed_list": "كن مفصلا",
  "System_account_management": "حساب الهيئة",
  "Temperature_statistics": "إحصاءات درجة الحرارة",
  "Transfer": "نقل مستخدم",
  "Transfer_group": "مجموعة التحويل",
  "Transfer_point": "نقل بطاقة جديدة",
  "Transfer_renewal_point": "نقل بطاقه تجديد",
  "Trip_statistics": "إحصائيات الرحلة",
  "Unlink": "فك الارتباط",
  "View": "انضر",
  "View_POI": "مشاهدة ملف POI",
  "View_device_group": "عرض مجموعة الجهاز",
  "View_fence": "فحص المنطقه الجغرافيه",
  "View_role": "مشاهدة الدور",
  "View_sharing_track": "عرض مسار المشاركة",
  "Virtual_account": "حساب افتراضي",
  "Voltage_analysis": " الفولطية",
  "Voltage_statistics": "الفولطية",
  "batch_deletion": "دفعة حذف",
  "change_Password": "مراجعة كلمة المرور",
  "delete": "حذف",
  "edit": "تعديل",
  "instruction": "تعليمات",
  "modify": "تحديث",
  "monitor": "مراقب",
  "my_account": "حسابي",
  "reset_Password": "تغيير الرقم السري",
  "share_it": "مشاركه الموقع",
  "sub_user": "الحسابات الفرعيه",
  "track": "تتبع",
  "Custom_Order": "تعليمات مخصصة",
  "GeoKey_Manager": "إدارة GeoKey",
  "GeoKey_Update": "تعديل",
  "GeoKey_Delete": "حذف",
  "GeoKey_Add": "اضف إليه",
  "GeoKey_View": "رأي",
  "feedback_manager": "إدارة الملاحظات",
  "feedback_list": "رأي",
  "feedback_handle": "معالجة الملاحظات",
  "proclamat_manager": "إدارة الإعلان",
  "proclamat_manager_list": "مشاهدة الإعلان",
  "proclamat_manager_update": "إعلان تعديل",
  "proclamat_manager_delete": "حذف الإعلان",
  "proclamat_manager_save": "إعلان جديد",
  "device_update_batch_model": "تعديل طراز الجهاز دفعة واحدة"
}

// 问题文档的内容
lg.questionDocumentArr = [
  [
    "س: يتم إيقاف تشغيل مؤشر المؤشر بعد تثبيت جهاز الأسلاك ، وأنه غير متصل.",
    "ج: بعد إيقاف تشغيل السيارة ، استخدم القلم الكهربائي والمقياس العالمي لقياس ما إذا كان الجهد الكهربي للخط المتصل متماشياً مع نطاق جهد تعقب GPS بشكل عام من 9 إلى 36 فولت.<br/>احتياطات الأسلاك: يحتاج موظفو التركيب والأسلاك إلى فهم خط السيارة ولديهم قدرة عملية معينة على تجنب الأضرار التي لحقت سيارتك بسبب الأسلاك غير الصحيحة.",
  ],
  [
    "س: جهاز سلكي أو جهاز تتبع لاسلكي في الوقت الفعلي أو مكالمة هاتفية أو جهاز حالة تمهيد خلفية إنترنت الأشياء في وضع عدم الاتصال",
    "الجواب:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. إرسال رسالة نصية لإعادة التشغيل ، لاحظ بضع دقائق لمعرفة ما إذا كان متصلاً.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. اتصال الشبكة غير مستقر ، يرجى نقل السيارة إلى منطقة إشارة جيدة.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. بعد الخطوات المذكورة أعلاه ، لم تتمكن من الاتصال بالإنترنت ، حيث يتعين عليك الاتصال بمشغل شبكة الجوال للتحقق مما إذا كانت البطاقة غير طبيعية.",
  ],
  [
    "س: الجهاز غير متصل على دفعات في بداية ونهاية الشهر.",
    "ج: يرجى التحقق مما إذا كانت البطاقة متأخرة ، وإذا كانت متأخرات ، فيرجى إعادة شحنها في الوقت المناسب واستئناف استخدامها.",
  ],
  [
    "س: السيارة تقود ، لم يتم تحديث موقع GPS عبر الإنترنت.",
    "الجواب:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. يمكن لجهاز الأسلاك إرسال رسالة SMS # للتحقق من حالة استقبال إشارة القمر الصناعي ، راجع نظام تحديد المواقع العالمي (GPS): بحث القمر الصناعي هو أن إشارة القمر الصناعي كانت قيد البحث ، يحتاج هذا الموقف إلى التحقق من موقع التثبيت ، سواء كان مثبتًا وفقًا للتعليمات. مواجهة ، لا يوجد غطاء معدني في الأعلى.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. أرسل رسالة SMS # ، وحالة العودة هي GPS: OFF ، يرجى إرسال FACTORY # مرة أخرى ، بعد تلقي الرد OK ، لاحظ 5 دقائق لمعرفة ما إذا كان هناك أي تحديث.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. وفقًا للطريقتين المذكورتين أعلاه ، لا يمكن التخلص من الخطأ ، يرجى الاتصال بالبائع للإصلاح.",
  ],
  [
    "س: لماذا لا تزال منصة الشحن تفرض رسومًا لفترة طويلة على أنها غير ممتلئة؟",
    "ج: تعتمد شاشة طاقة المنصة على المعلومات التي أرجعها الجهاز لإجراء تحليل للبيانات لتحديد الطاقة الحالية للجهاز ، وفي بعض الحالات الخاصة ، سيظهر حل خطأ عرض الطاقة:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. يتم تحميل بيانات طاقة الجهاز وبيانات تحديد موقع الجهاز معًا.إذا لم يتم تغيير البطارية لفترة طويلة ، فيرجى: 1 إحضار جهازك لتحريك 100-300 متر لتحديث معلومات موقع الجهاز. يمكن تغذية بيانات الموقع والموقع مرة أخرى مع النظام الأساسي ليكون تحديثًا لعرض الطاقة.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. وفقًا لتغيير مؤشر الطاقة ، حدد ما إذا كان مشحونًا بالكامل (خذ S15 كمثال) .خطوات العملية هي كما يلي: 1 شحنة 8-10 ساعات ، ثم يتحول مؤشر الطاقة إلى اللون الأخضر الأصفر ، بعد فصل خط الشحن ، أدخل كابل الشحن. خلال 15 دقيقة ، سوف يتحول مؤشر الطاقة إلى اللون الأصفر والأخضر إلى الطاقة الكاملة ؛ يرجى الرجوع إلى الدليل للحصول على نماذج أخرى.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3 ، والشحن لفترة طويلة هو أيضا مليئة بالكهرباء ، قد يكون هذا الموقف هو الجهد قابس الشحن أقل من 1A ، يرجى شحن 5V ، 1A شحن رئيس لمدة 8-10 ساعات.",
  ],
  [
    "س: تم إصدار أمر قطع التيار الكهربائي عن نظام تحديد المواقع العالمي (GPS) بنجاح.",
    "الإجابة: بعد إصدار أمر إيقاف التشغيل بنجاح ، يجب تنفيذ الجهاز وفقًا للشروط التالية: <br/>&nbsp;&nbsp;&nbsp;&nbsp;1. تأكد من أن أسلاك الجهاز صحيحة واتبع مخطط الأسلاك في الدليل.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2 ، أن الجهاز يعمل بشكل طبيعي ، يكون في حالة ثابتة أو في حالة القيادة ، وله وضع غير متصل بالشبكة ، ولا تتجاوز سرعة السيارة 20 كم ؛ إذا كانت السيارة في وضع عدم الاتصال ، أو غير موضعية ، أو تجاوزت سرعة السيارة 20 كيلومتراً ، حتى لو تم إيقاف تشغيل أمر الطاقة تم التسليم بنجاح ولن يتم تنفيذ الجهاز.",
  ],
  [
    "س: ثلاث سنوات من المنتجات اللاسلكية مثبتة لأول مرة ، لا يتم وضع جهاز العرض أو عبر الإنترنت.",
    "الجواب:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. قم بتشغيل المفتاح للتحقق مما إذا كان مصباح المؤشر يومض. على سبيل المثال ، تومض مؤشرات S18 باللونين الأصفر والأخضر في نفس الوقت كالمعتاد ، ويكون مصباح الوميض في إشارة البحث. (ستختلف حالة الطرز المختلفة. يرجى الرجوع إلى دليل الطرز الأخرى)<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. لا يضيء مصباح المؤشر على الخط ، وإذا كانت الإشارة في وضع ضعيف ، فيرجى الحصول على الإشارة في منطقة جيدة. منطقة الإشارة الجيدة ليست على الخط ، يمكنك إيقاف التشغيل لمدة دقيقة واحدة وإعادة تثبيت البطاقة ثم بدء الاختبار.",
  ],
  [
    "س: تم تثبيت منتج الكابل لأول مرة ، ولم يتم وضع جهاز العرض.",
    "الجواب:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. لاحظ ما إذا كان مؤشر حالة GPS الخاص بالجهاز طبيعيًا ، تحقق من حالة المؤشر وفقًا لتعليمات الطرازات المختلفة.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. إذا كان مصباح المؤشر مطفأ ، فلن يتمكن الجهاز من التشغيل بشكل طبيعي.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3 ، (أخضر أصفر) مؤشر بطاقة ليست مضاءة ، وإيقاف تشغيل الطاقة وإعادة تثبيت البطاقة ، ومن ثم الطاقة على رؤية الضوء الطبيعي أمر طبيعي.<br/>&nbsp;&nbsp;&nbsp;&nbsp;4. تحديد ما إذا كان رقم بطاقة SIM في الجهاز ليس في المتأخرات ، وما إذا كانت وظيفة الوصول إلى الإنترنت GPRS طبيعية.<br/>&nbsp;&nbsp;&nbsp;&nbsp;5. لا توجد شبكة GSM في المكان الذي يوجد فيه الجهاز ، مثل الطابق السفلي والنفق وما إلى ذلك ، حيث تكون الإشارة ضعيفة ، فالرجاء الانتقال إلى المكان الذي تكون فيه تغطية GPRS جيدة.<br/>&nbsp;&nbsp;&nbsp;&nbsp;6 ، لا ينبغي أن يكون موقف لتحديد المواقع مغلقة جدا ، لا تملك الأشياء المعدنية ، إلى أقصى حد ممكن في موقف تثبيت السيارة. وإلا فإنه يؤثر على استقبال إشارة.<br/>&nbsp;&nbsp;&nbsp;&nbsp;7 ، التمهيد العادي ، والتوقف في منطقة إشارة جيدة ليست على الإنترنت ، يمكنك إعادة إصدار الأمر سطر للتحقق ما إذا كانت واجهة IP وشبكة رابط البطاقة أمر طبيعي.",
  ],
  [
    "س: يتم إيقاف تشغيل مؤشر المؤشر بعد تثبيت جهاز الأسلاك ، وأنه غير متصل.",
    "الحل:<br/>&nbsp;&nbsp;&nbsp;&nbsp;بعد إيقاف تشغيل السيارة ، استخدم القلم الكهربائي والمقياس العالمي لقياس ما إذا كان الجهد الكهربي لخط السيارة المتصل متوافقًا مع نطاق جهد تعقب GPS بشكل عام هو 9-36 فولت.<br/>&nbsp;&nbsp;&nbsp;&nbsp;احتياطات الأسلاك:<br/>&nbsp;&nbsp;&nbsp;&nbsp;يحتاج موظفو التركيب والأسلاك إلى فهم خط السيارة ولديهم قدرة عملية معينة على تجنب الأضرار التي لحقت سيارتك بسبب الأسلاك غير الصحيحة.",
  ],
  [
    "س: جهاز سلكي أو جهاز تتبع لاسلكي في الوقت الفعلي أو مكالمة هاتفية أو جهاز حالة تمهيد خلفية إنترنت الأشياء في وضع عدم الاتصال",
    "الجواب:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. إرسال رسالة نصية لإعادة التشغيل ، لاحظ بضع دقائق لمعرفة ما إذا كان متصلاً. عموما إرسال إعادة تعيين # يرجى الاتصال بالموزع لتحديد.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. اتصال الشبكة غير مستقر ، يرجى نقل السيارة إلى منطقة إشارة جيدة.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. بعد الخطوات المذكورة أعلاه ، لم تتمكن من الاتصال بالإنترنت ، حيث يتعين عليك الاتصال بمشغل شبكة الجوال للتحقق مما إذا كانت البطاقة غير طبيعية.",
  ],
  [
    "س: الجهاز غير متصل على دفعات في بداية ونهاية الشهر.",
    "الحل: يرجى التحقق مما إذا كانت البطاقة متأخرة ، وإذا كانت متأخرات ، فيرجى إعادة شحنها في الوقت المناسب واستئناف استخدامها.",
  ],
  [
    "س: السيارة تقود ، لم يتم تحديث موقع GPS عبر الإنترنت.",
    "الحل:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. يمكن لجهاز الأسلاك إرسال رسالة SMS # للتحقق من حالة استقبال إشارة القمر الصناعي ، راجع نظام تحديد المواقع العالمي (GPS): بحث القمر الصناعي هو أن إشارة القمر الصناعي كانت قيد البحث ، يحتاج هذا الموقف إلى التحقق من موقع التثبيت ، سواء كان مثبتًا وفقًا للتعليمات. مواجهة ، لا يوجد غطاء معدني في الأعلى.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. أرسل SMS STATUS # ، وحالة العودة هي GPS: OFF ، يرجى إرسال FACTORY # مرة أخرى ، بعد تلقي الرد موافق ، ومراقبة 5 دقائق لمعرفة ما إذا تم تحديث الموقف.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. وفقًا للطريقتين المذكورتين أعلاه ، لا يمكن التخلص من الخطأ ، يرجى الاتصال بالبائع للإصلاح.",
  ],
  [
    "س: لماذا لا تزال منصة الشحن تفرض رسومًا لفترة طويلة على أنها غير ممتلئة؟",
    "تعتمد شاشة طاقة المنصة على المعلومات التي يتم تغذيتها من الجهاز لإجراء تحليل للبيانات لتحديد الطاقة الحالية للجهاز ، وفي بعض الحالات الخاصة ، سيحدث خطأ في عرض الطاقة.<br/>الحل:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. يتم تحميل بيانات طاقة الجهاز وبيانات تحديد موقع الجهاز معًا.إذا لم يتم تغيير البطارية لفترة طويلة ، فيرجى: 1 إحضار جهازك لتحريك 100-300 متر لتحديث معلومات موقع الجهاز. يمكن تغذية البيانات والموقع معًا مرة أخرى على المنصة لتكون تحديثًا لعرض الطاقة ؛<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. وفقًا لتغيير مؤشر الطاقة ، حدد ما إذا كان مشحونًا بالكامل (خذ S15 كمثال) .خطوات العملية هي كما يلي: 1 شحنة 8-10 ساعات ، ثم يتحول مؤشر الطاقة إلى اللون الأخضر الأصفر ، بعد فصل خط الشحن ، أدخل كابل الشحن. خلال 15 دقيقة ، سوف يتحول مؤشر الطاقة إلى اللون الأصفر والأخضر إلى الطاقة الكاملة ؛ يرجى الرجوع إلى الدليل للحصول على نماذج أخرى.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3 ، والشحن لفترة طويلة هو أيضا مليئة بالكهرباء ، قد يكون هذا الموقف هو الجهد قابس الشحن أقل من 1A ، يرجى شحن 5V ، 1A شحن رئيس لمدة 8-10 ساعات.",
  ],
];
lg.webOptDoc = "قريبا...";
lg.appOptDoc = "قريبا...";
// 查询参数的帮助
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">كلمه السر:</td>');
html.push("<td>استعلام كلمه السر المؤقته</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">تليفون:</td>');
html.push("<td>Query terminal built-in SIM card</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">المستعمل:</td>');
html.push("<td>استعلام عن صاحب رقم التليفون</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">سرعة:</td>');
html.push("<td>استعلام عن حد السرعه</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push("<td>المقياس بالثوانياستعلام عن عدد مرات التتبع</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">أثر:</td>');
html.push("<td>الاستعلام عن اذا كان التتبع متاح.1:enable,0:disable</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">نصف القطر:</td>');
html.push("<td>الاستعلام عن مدي التنبيه غير القانوني,Unit: meter</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push("<td>الاستعلام عن اذا كان تنبيه الرسايل متاح,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>حساسية الاهتزاز الاستعلام هو 0 إلى 15 ، 0 هي أعلى حساسية ، عالية جدا قد يكون الإنذار الكاذب ، 15 هو أدنى حساسية</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push("<td>الاستعلام عن اذا كان تنبيه المكالمات متاح,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>الاستعلام عما إذا كان الانجراف مرشح نظام تحديد المواقع العالمي ممكّنًا ، 1: التمكين ، 0: التعطيل ، إذا قمت بتشغيل جهاز الحماية ضد السرقة ستدخل الحالة الثابتة دون حدوث اهتزاز خلال 5 دقائق ، وتصفية كل انجراف GPS</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>الاستعلام ما إذا تم تمكين وظيفة النوم ، 1: تمكين ، 0: تعطيل ، إذا قمت بتشغيل جهاز الحماية ضد السرقة سوف تدخل وضع النوم مع عدم حدوث اهتزاز خلال 30 دقيقة ، فإنه سيتم إغلاق وظيفة GPS وتوفير الطاقة</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>الاستعلام عما إذا تم تمكين إنذار إيقاف التشغيل,1:ممكن,0:غير ممكن</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">GPS:</td>');
html.push(
  "<td>قم بالاستفسار عن قوة إشارة القمر الصناعي ، على سبيل المثال: 2300 1223 3431. . . ما مجموعه 12 مجموعة من أربعة أرقام ، 2300 يعني: قوة الإشارة من القمر الصناعي رقم 23 هو 0،1223 يعني: قوة الإشارة من القمر الصناعي رقم 12 هو 23</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VBAT:</td>');
html.push(
  "<td>البطارية الجهد الاستعلام ، شحن منفذ الجهد تهمة الحالية على سبيل المثال: VBAT = 3713300: 4960750: 303500 يشير إلى أن الجهد البطارية هو 3713300uV 3.71v المطبقة على الجهد شحن على رقاقة 4.96V ، والشحن الحالية 303mA</td>"
);
html.push("</tr>");
html.push("</table>");
lg.queryparamhelp = html.join("");

// 设置参数的帮助
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">كلمه السر:</td>');
html.push("<td>قم بتعيين كلمة مرور طرفية ، وهي عبارة عن 6 أرقام فقط</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>اضبط رقم SIM الخاص  بالمحطه</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">مستخدم:</td>');
html.push("<td>حدد عدد مالكي الهواتف المحمولة</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">السرعه:</td>');
html.push("<td>Set the speed limit value,0-300</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">تردد:</td>');
html.push("<td>قم بإعداد التردد المبلغ عنه عند تشغيل التعقب,وحده:ثانيه</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">مسار:</td>');
html.push("<td>انشاء ما اذا كان فتح المسار,1:افتح,0:اغلق</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">نصف القطر:</td>');
html.push("<td>قم بإعداد نطاق الإنذار غير القانوني,وحده: متر</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">اهتزاز:</td>');
html.push("<td>إعداد ما إذا كان يتم تمكين التنبيه SMS,1:تمكين,0:غير ممكن");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>تعيين حساسية الاهتزاز من 0 إلى 15 ، 0 هو أعلى حساسية ، عالية جدا قد يكون الإنذار الكاذب ، 15 هو أدنى حساسية</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">اهتزاز المكالمات:</td>');
html.push(
  "<td>قم بإعداد ما إذا كان إنذار المكالمات ممكّنًا أم لا,1:تمكين,0:غير ممكن"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">اهتزازGPS:</td>');
html.push(
  "<td>قم بإعداد ما إذا كان يتم تمكين انحراف مرشح GPS ، 1: التمكين ، 0: التعطيل ، إذا قمت بتشغيل جهاز الحماية ضد السرقة ستدخل الحالة الثابتة دون حدوث اهتزاز خلال 5 دقائق ، وتصفية كل انجراف GPS</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">نوم:</td>');
html.push(
  "<td>قم بإعداد ما إذا كانت وظيفة النوم ممكّنة ، 1: التمكين ، 0: تعطيل ، إذا قمت بتشغيل جهاز الحماية ضد السرقة ، فسيتم إدخال وضع النوم دون حدوث اهتزاز خلال 30 دقيقة ، سيغلق وظيفة GPS ويوفر الطاقة</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>قم بإعداد ما إذا كان إنذار إيقاف التشغيل ممكّنًا أم لا,1:تمكين,0:غير ممكن</td>"
);
html.push("</tr>");
html.push("</table>");
lg.setparamhelp = html.join("");

//批量添加
html = [];
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox" ' +
    'style="z-index: 999;position:absolute;left:140px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:150px;" class="ztree" id="bulkAdds_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Customer:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkAdds_treeDiv" +
    "," +
    "bulkAdds_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>منهاج الواجب:</td>'
);
html.push("<td>");
html.push(
  '<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>نموذج:</td>'
);
html.push("<td>");
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    "</div>" +
    '<div id="deviceList"></div>' +
    "</div>" +
    "</span>"
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافى:</td>'
);
html.push("<td>");
html.push(
  '<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push(
  '<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>'
);
lg.bulkAdds = html.join("");

//批量续费
html = [];
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:92px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Add device:</td>'
);
html.push("<td>");
html.push(
  '<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="re_addNumBox">تيار：<span id="account_re_addNum">0</span>'
);
html.push("</span>");
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<tr>");
html.push(
  '<td style="text-align:right;"><span style="color:red">*</span>Type</td>'
);
html.push("<td>");
html.push('<input  type="radio" name="red_cardType"');
html.push(
  'class="easyui-validatebox"  value="3" checked><label>Annual card</label></input>'
);
html.push(
  '<input  type="radio" name="red_cardType" style="margin-left:15px;" '
);
html.push(
  'class="easyui-validatebox" value="4"><label>Lifelong card</label></input>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td style="text-align: right">Quantity</td>');
html.push("<td>");
html.push(
  '<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>User Due:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Remark</td>'
);
html.push("<td>");
html.push(
  '<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkRenew = html.join("");

//批量销售，myAccount
html = [];
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:130px;top:87px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Customer:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkSales_treeDiv" +
    "," +
    "bulkSales_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push(
  '<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>مستخدم حتى:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافى:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="bs_addNumBox">تيار：<span id="account_bs_addNum">0</span>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="bs_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");

lg.bulkSales = html.join("");

//批量转移1，弹出框
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:152px;top:171px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافى:</td>'
);
html.push("<td>");
html.push(
  '<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >اضافه مجموعه</a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)">يتحرك</a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)" >الغاء</a>'
);

lg.bulkTransfer = html.join("");

//批量转移2,myClient
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:142px;top:83px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافى:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bt_addMachines" style="cursor:pointer" title="' +
    lg.addTo +
    '" src="../../images/main/myAccount/add3.png" />'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);

lg.bulkTransfer2 = html.join("");

//批量转移用户
html = [];
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:141px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">'
);
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("<td></td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");

lg.bulkTransferUser = html.join("");
window.lg = lg