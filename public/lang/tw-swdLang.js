var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
    site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
    site = 'Forcegps'
  }
var lg = {
    //common
    user_guide: '用戶指南',
    remoteSwitch: "遠程開關機",
    pageTitle:'立即定位全球位置服務平臺|GPS定位器|GPS車輛管理|車載追蹤器',
    description:'立即定位（'+site+'）以行業最新科學技術為基礎，依託大數據分佈式處理，致力於為用戶提供智能雲定位服務，是全球領先的位置服務平台。',
    pageLang:'繁體中文',
    inputCountTips:'請輸入帳號/IMEI',
    inputPasswordTips:'請輸入密碼',
    appDownload:'客戶端下載',
    siteName:'WhatsGPS',
    rememberPassword:'記住密碼',
    forgetPassword: '忘記密碼',
    noToken: "請傳token",
    loginFirst: '請先登錄',
    move: '運動',
    stop: '靜止',
    query: '查詢',
    imeiQuery:'IMEI',
    delete: '刪除',
    update: '修改',
    cancel:'取消',
    soft:'序號',
    more:'更多',
    useful:'有幫助',
    useless:'無幫助',
    replyFeedback:'關於“$”的反饋回复',
    edit: '編輯',
    add: '增加',
    addTo:'添加',
    addDevice:'添加設備',
    machineName: '設備名稱',
    searchDevice:'搜設備',
    date: '日期',
    LatestUpdate:'信號',
    engine: 'ACC',
    locTime: '定位時間',
    locType: '定位類型',
    startLoc:'開始位置',
    endLoc:'結束位置',
    address: '地址',
    noAddressTips:'無法獲取地址信息',
    lonlat: '經緯度',
    carNO: '車牌號',
    imei: 'IMEI',
    IMEI: "imei",
    simNO: 'SIM卡號',
    activeTime: '激活時間',
    expireTime: '到期時間',
    acceptSubordinateAlarm:'接受下級報警',
    acceptAlarmTips1:'勾選後',
    acceptAlarmTips2:'您會收到所有下級客戶的設備報警信息',
    speed: '速度',
    y: "年",
    M: "月",
    d: "天",
    h: "時",
    min: "分",
    s: "秒",
    _year:'年',
    _month:'月',
    _day:'天',
    _hour:'時',
    _minute:'分',
    _second:'秒',
    confirm: '確定',
    yes: "是",
    car: '車輛',
    not: "否",
    m: '米',
    account: '帳號',
    psw: '密碼',
    save: '保存',
    operator: '操作',
    queryNoData: '未查詢到任何數據',
    name: '名稱',
    type: '型號',
    open: '開',
    close: '關',
    send: '發送',
    alarm: '報警',
    alarmSetting:'報警設置',
    look: '查看',
    tailAfter: '跟蹤',
    history: '回放',
    dir: '航向',
    locStatus: "定位狀態",
    machineTypeText: '型號',
    carUser: '車主',
    machine: '設備',
    unknowMachineType: '未知型號',
    noCommandRecord:'該設備無指令',
    type1: '類型',
    role:'類型',
    roles:'類型',
    timeType:'時間類型',
    moveSpeed: '運行速度',
    signal: '信號',
    loc: '定位',
    wiretype:'類型',
    wire:'有線',
    wireless: '無線',
    expire: '到期',
    hour: '小時',
    hourTo: '小時 至',
    remark: '備註',
    remarkInfo: '備註信息',
    noPriviledges:'該帳號無操作權限',
    commandNoOpen:'當前設備指令功能暫未開放使用',
    choseDelelePhone:'請先選擇要刪除的號碼',
    streetView:'街景',
    wrongFormat:'輸入格式錯誤',
    inputFiexd:'請輸入固定數字',
    serialNumberStart:'請輸入要連號的開始數字',
    serialNumberEnd:'請輸入要連號的結束數字',      
    clickSearchFirst:'請先點擊搜索設備號！',
    isDeleteDevice:'設備刪除後不可恢復，是否刪除？',
    //平臺錯誤代碼提示
    errorTips:'操作失敗，錯誤代碼為：',
    error10003: '密碼錯誤',
    error90010: '設備不在線，自定義指令發送失敗！',
    error70003: '遠程控制值不能為空',
    error70006: '不支持或無權下發該指令',
    error20001: '車輛ID不能為空',
    error20012: '車輛未激活',
    error10012:'舊密碼錯誤',
    error10017:'刪除失敗，請先刪除子用戶!',
    error10023:'刪除失敗，該用戶有設備',
    error20008:'添加失敗，IMEI號已經存在',
    error20006:'請輸入15位的設備號',
    error10019:'電話格式錯誤',
    error10024:'請勿重復銷售',
    error120003:'分享鏈接已過期失效',
    error10025:'修改的設備信息不能為空',
    error2010:'請上傳文件',
    error20002:'IMEI號不存在',
    error10081:'續費卡數量不足',
    error10082: '終身設備無需續費！',
    error3000: '該角色已被分配給系統帳號，無法刪除',
    error103: '該帳號已停用，請聯繫您的服務商',
    error124: '無法對自身操作',
    // 登陸相關 login.js
    logining: '登錄中...',
    login: '登 錄',
    userEmpty: '用戶名不能為空',
    pswEmpty: '密碼不能為空',
    prompt: '溫馨提示',
    accountOrPswError: '帳號或者密碼錯誤',
    UserNameAlreadyExist:'該登錄帳號已存在',
    noQualified:'沒有符合條件的信息',
    //main.js
    systemName: '立即定位監控系統',
    navTitle_user: ['定位監控', '統計報表', '設備管理'],
    navTitle_dealer: ['我的帳戶', '我的客戶', '監控平臺','更多操作'],
    exitStytem: '退出',
    user: '用戶',
    UserCenter:'用戶',
    alarmInfo: '報警',
    confirmExit: '確定退出系統?',
    errorMsg: '錯誤原因: ',
    logintimeout: '登錄超時,請重新登錄!',
    clearAlarm:'清除',
    clear:'清空',
    searchbtn: '搜用戶',
    print:'列印',
    export:'導出',
    // 意見反饋部分
    feedback: '反饋',
    feedback_sublime: '提交',
    alerttitle:'標題不能為空！',
    alertcontent:'反饋內容不能為空！',
    submitfail:'提交失敗！',
    saveSuccess:'保存成功！',
    submitsuccess: '提交成功！我們會盡快處理您的反饋~',
    adviceTitle: '標題',
    adviceTitle_p: '問題和意見標題',
    adviceContent: '問題與意見',
    adviceContent_p: '簡要描述您要反饋的問題和意見，我們將為您不斷改進。',
    contact: '聯系方式',
    contact_p: '填寫您的手機或郵箱',
    //monitor.js

    myMachine: '設備',
    all: '全部',
    online: '在線',
    offline: '離線',
    unUse: '未用',
    group: '分組',
    moveGruop:'移至分組',
    arrearage: '欠費',
    noStatus: '無狀態',
    inputMachineName: '請輸入設備名/IMEI',
    defaultGroup: '默認分組',
    offlineLessOneDay: '離線<1天',
    demoUserForbid:'體驗用戶不能使用此功能',
    shareTrack:'分享',
    shareName:'分享名稱',
    liveShare:'實時軌跡分享',
    expiration:'有效時間',
    getShareLink:'生成分享鏈接',
    copy:'復制',
    copySuccess:'復制成功!',
    enlarge:'放大',
    shareExpired:'分享鏈接已過期',
    LinkFailure:'分享鏈接打開失敗',
    inputShareName:'請輸入分享名稱',
    inputValid:'請輸入正確的有效時間',
    //statistics.js
    runOverview: "運行總覽",
    runSta: '運行統計',
    mileageSta: '里程統計',
    tripSta:'行程統計',
    overSpeedDetail: '超速詳單',
    stopDetail: '停留詳單',
    alarmSta: '報警統計',
    alarmOverview: '報警總覽',
    alarmDetail: '報警詳單',
    shortcutQuery: '快捷查詢',
    today: '今天',
    yesterday: '昨天',
    lastWeek: '上週',
    thisWeek: '本週',
    thisMonth: '本月',
    lastMonth: '上月',
    mileageNum: '里程（公里）',
    overSpeedNum: '超速(km/h)',
    overSpeed: '超速',
    stopTimes: '停留（次）',
    searchMachine: '設備',
    speedNum: '速度(km/h)',
    querying: '正在查詢',
    stopTime: '停留時間',
    HisToryStopTime:'停留',
    clickLookLoc:'點擊查看地址',
    lookLoc: '查看位置',
    noData: '無數據',
    alarmTime: '報警時間',
    vibrationLevel:'震動等級',
    vibrationWay:'報警方式',
    acc: 'ACC',
    accStatistics:'ACC統計',
    accType: ['全部狀態','ACC 打火','ACC 熄火'],
    accstatus: ['開','關'],
    openAccQuery:'ACC查詢',
    runtime: '運行時長',
    //監控頁面修改
    run:'行駛',
    speed: '速度',
    //設備管理
    machineManage: '設備管理',
    deviceTable:'我的目標',
    status: '狀態',
    havaExpired: '已過期',
    expiredIn60: '60天內到期',
    expiredIn7: '7天內到期',
    normal: '正常',
    allMachine: '全部設備',
    allMachine1: '全部設備',
    expiredIn7Machine: '7天內過期設備',
    expiredIn60Machine: '60天內過期設備',
    havaExpiredMachine: '過期設備',

    //history.js
    replay: '播放',
    replaytitle:'  回放',
    choseDate:'選擇時間',
    from: '從',
    to: '到',
    startTime:'開始時間',
    endTime:'結束時間',
    pause: '暫停',
    slow: '慢',
    mid: '中',
    fast: '快',
    startTimeMsg: '您還未選擇開始時間',
    endTimeMsg: '您還未選擇結束時間',
    smallEnd: "您輸入的結束時間小於開始時間，請重新選擇!",
    bigInterval: '您輸入的時間間隔不能超過31天!',
    trackisempty: "該時段內軌跡為空",
    longitude:'經度',
    latitude:'緯度',
    direction:'方向',
    stopMark:'停留標識',
    setStopTimes:[
        {
            text:'1分鐘',
            value:'1'
        },
        {
            text:'2分鐘',
            value:'2'
        },
        {
            text:'3分鐘',
            value:'3'
        },
        {
            text:'5分鐘',
            value:'5'
        },
        {
            text:'10分鐘',
            value:'10'
        },
        {
            text:'15分鐘',
            value:'15'
        },
        {
            text:'20分鐘',
            value:'20'
        },
        {
            text:'30分鐘',
            value:'30'
        },
        {
            text:'45分鐘',
            value:'45'
        },
        {
            text:'1小時',
            value:'60'
        },
        {
            text:'6小時',
            value:'360'
        },
        {
            text:'12小時',
            value:'720'
        },
    ],
    filterDrift:'過濾漂移',
    userType: ['管理員', '經銷商', '用戶', '物流', '租賃', '車輛用戶','風控','專業'],
    userTypeArr: ["管理員", "經銷商", "用戶", '物流', "租賃", '車輛用戶','風控','專業'],
    machineType: {
        '0':'未知類型',
        '1':'S15',
        '2':'S05',
        '93':'S05L',
        '94': 'S309',
        '95': 'S15L',
        '96':'S16L',
        '97':'S16LA',
        '98':'S16LB',
        '3':'S06',
        '4':'SW06',
        '5':'S001',
        '6':'S08',
        '7':'S09',
        '8':'GT06',
        '9':'S08V',
        '10':'S01',
        '11':'S01T',
        '12':'S116',
        '13':'S119',
        '14':'TR06',
        '15':'GT06N',
        '16':'S101',
        '17':'S101T',
        '18':'S06U',
        '19':'S112U',
        '20':'S112B',
        '21':'SA4',
        '22':'SA5',
        '23':'S208',
        '24':'S10',
        '25':'S101E',
        '26':'S709',
        '99':'S709L',
        '27':'S1028',
        '28':'S102T1',
        '29':'S288',
        '30':'S18',
        '31':'S03',
        '32':'S08S',
        '33':'S06E',
        '34':'S20',
        '35':'S100',
        '36':'S003',
        '37':'S003T',
        '38':'S701',
        '39':'S005',
        '40':'S11',
        '41':'T2A',
        '42':'S06L',
        '43':'S13',
        '86':'S13-B',
        '44':'GT800',
        '45':'S116M',
        '46':'S288G',
        '47':'S09L',
        '48':'S06A',
        '49':'S300',
        '50':'',
        '51':'GS03A',
        '52':'GS03B',
        '53':'GS05A',
        '54':'GS05B',
        '55':'S005T',
        '56':'AT6',
        '57':'GT02A',
        '58':'GT03C',
        '59':'S5E',
        '60':'S5L',
        '61':'S102L',
        '85':'S105L',
        '62':'TK103',
        '63':'TK303',
        '64':'ET300',
        '65':'S102A',
        '91':'S102A-D',
        '66':'S708',
        '67':'MT05A',
        '68':'S709N',
        '69':'',
        '70':'GS03C',
        '71':'GS03D',
        '72':'GS05C',
        '73':'GS05D',
        '74':'S116L',
        '75':'S102',
        '76':'S102T',
        '77':'S718',
        '78':'S19',
        '79':'S101A',
        '80':'VT03D',
        '81':'S5L-C',
        '82':'S710',
        '83':'S03A',
        '84':'C26',
        '87':'S102M',
        '88':'S101-B',
        '92':'LK720',
        '89':'S116-B',
        '90':'X3'
      },
    alarmType: ['未知告警', '震動報警', '斷電報警', '低電報警', 'SOS求救', '超速報警', '出圍欄報警', '位移報警', '外電低電報警',
        '出區域報警', '拆機報警', '光感報警', '磁感報警', '防拆報警', '藍牙報警', '信號屏蔽報警', '偽基站報警','入圍欄報警','入圍欄報警',
        '出圍欄報警','車門打開報警','疲勞駕駛','入二押點','出二押點','二押點久留','終端離線','入圍欄報警','出圍欄報警','入圍欄報警','出圍欄報警','油量報警','ACC ON 報警','ACC OFF 報警','碰撞報警','上班遲到','下班早退','打卡報警'],
    alarmTypeNew:  {
        '40': '高溫報警',
        '45': '低溫報警',
        '50': '過壓報警',
        '55': '欠壓報警',
        '60': '停車報警'
    },
    alarmNotificationType:[
        {type:'震動報警',value:1},
        {type:'斷電報警',value:2},
        {type:'低電報警',value:3},
        {type:'SOS求救',value:4},
        {type:'超速報警',value:5},
        // {type:'出圍欄報警',value:6},
        {type:'位移報警',value:7},
        { type: '外電低電報警', value: 8 },
        {type:'出區域報警',value:9},
        {type:'拆機報警',value:10},
        {type:'光感報警',value:11},

        {type:'防拆報警',value:13},

        {type:'信號屏蔽報警',value:15},
        {type:'偽基站報警',value:16},
        // {type:'入圍欄報警(平臺判定)',value:17},
        // {type:'入圍欄報警(終端判定)',value:18},
        // {type:'出圍欄報警',value:19},
        
        {type:'疲勞駕駛',value:21},
        {type:'入二押點',value:22},
        {type:'出二押點',value:23},
        {type:'二押點久留',value:24},
        {type:'終端離線',value:25},
        // {type:'入圍欄報警（風控）',value:26},
        // {type:'出圍欄報警（風控)',value:27}
        {type:'入圍欄報警',value:26},
        {type:'出圍欄報警',value:27},
        { type: '油量報警', value: 30 },
        { type: 'ACC ON 報警', value: 31 },
        { type: 'ACC OFF 報警', value: 32 },
        { type: '碰撞報警', value: 33 },
        { type: '上班遲到', value: 34 },
        { type: '下班早退', value: 35 },
],
    alarmTypeText: '報警類型',
    alarmNotification:'推送設置',
    pointType: ['未定位', '衛星定位', '北斗定位', '基站定位', 'WIFI定位'],

    cardType: ['未知類型', '一年導入點', '終身導入點', '年卡', '終生卡'],
    // 東南西北
    directarray: ["東", "南", "西", "北"],
    // 方向字段
    directionarray: ["正北", "東北", "正東", "東南", "正南", "西南", "正西", "西北"],
    // 定位方式
    pointedarray: ["未定位", "GPS", "基站", "基站定位", "WIFI定位"],


    //map相關
    ruler: '測距',
    distance:'路況信息',
    baidumap: "百度地圖",
    map:'地圖',
    satellite:'衛星',
    ThreeDimensional:'3D',
    baidusatellite: "百度衛星",
    googlemap: "谷歌地圖",
    googlesatellite: "谷歌衛星",
    fullscreen: "全屏",
    noBaidumapStreetView:'當前位置在百度地圖沒有街景',
    noGooglemapStreetView:'當前位置在谷歌地圖沒有街景',
    exitStreetView:'退出街景',
    draw: "繪制",
    finish: "完成",
    unknown: '未知',
    realTimeTailAfter: '實時跟蹤',
    trackReply: '軌跡回放',
    afterRefresh: "後刷新",
    rightClickEnd: "右鍵結束,半徑：",
    rightClickEndGoogle: "右鍵結束------------------------半徑：",


    //tree相關
    currentUserMachineCount: '當前用戶設備數',
    childUserMachineCount: '包含子用戶總設備數',

    //窗口相關

    electronicFence: '電子圍欄',
    drawTrack: '繪制軌跡',
    showOrHide: '顯示/隱藏',
    showDeviceName:'顯示設備名',
    circleCustom: '圓形自定義',
    circle200m: '圓形200米',
    polygonCustom: '多邊形自定義',
    drawPolygon:'繪制多邊形',
    drawCircle:'繪制圓形',
    radiusMin100:'繪制的圍欄半徑最小為20米，請重新繪制。當前圍欄半徑：',
    showAllFences:'顯示全部圍欄',
    lookEF: '查看圍欄',
    noEF: '未查詢到電子圍欄!',
    hideEF: '隱藏圍欄',
    blockUpEF: '停用圍欄',
    deleteEF:'刪除圍欄',
    isStartUsing: '是否啟用',
    startUsing: '啟用',
    stopUsing: '停用',
    nowEFrange:'當前圍欄範圍',
    enableSucess:'啟用成功',
    unableSucess:'禁用成功',
    sureDeleteMorgage:'確定刪除二押點',
    enterMorgageName:'請輸入二押點名稱',
    openMorgagelongStayAlarm:'開啟二押久留報警',
    openMorgageinOutAlarm:'開啟進出二押報警',
    setEFSuccess: '設置圍欄成功，並啟用圍欄範圍',
    setElectronicFence:'設置電子圍欄',
    drawFence:'繪制電子圍欄',
    drawMorgagePoint:'繪制二押點',
    customFence:'自定義圍欄',
    enterFenceTips:'進入報警',
    leaveFenceTips:'離開報警',
    inputFenceName:'請輸入圍欄名稱',
    relation:'關聯',
    relationDevice:'關聯設備',
    unRelation:'未關聯',
    hadRelation:'已關聯',
    quickRelation:'壹鍵關聯',
    cancelRelation:'壹鍵取消',
    relationSuccess:'關聯成功',
    cancelRelationSuccess:'取消關聯成功',
    relationFail:'關聯失敗',
    deviceList:'設備列表',
    isDeleteFence:'是否刪除圍欄',
    choseRelationDeviceFirst:'請先選中要關聯的設備!',
    choseCancelRelationDeviceFirst:'請先選中要取消關聯的設備!',
    selectOneTips:'請至少選擇壹項報警方式',
    radius:'半徑',
    //設置二押點頁面
    setMortgagePoint:'設置二押點',

    circleMortage:'圓形二押點',
    polygonMorgage: '多邊形二押點',
    morgageSet: '已設置二押點',
    operatePrompt: '操作提示',
    startDrawing: '點擊開始繪制',
    drawingtip1: '鼠標左鍵單擊開始繪制，雙擊結束繪制',
    drawingtip2: '鼠標左鍵點擊拖動開始繪制',

    /************************************************/
    endTrace: '軌跡回放完畢',
    travelMileage: '行駛里程',
    /************************************************/
    myAccount: '我的帳號',
    serviceProvide: '服務商',
    completeInfo: '請完善以下信息，比如聯繫人、電話。',
    clientName: '客戶名稱',
    loginAccount: '登錄帳號',
    linkMan: '聯繫人',
    linkPhone: '電話',
    clientNameEmpty: "客戶名稱不能為空!",
    updateSuccess: '數據更新成功!',
    /************************************************/
    oldPsw: '舊密碼',
    newPsw: '新密碼',
    confirmPsw: '密碼確認',
    pswNoSame: '密碼輸入不壹致',
    pswUpdateSuccess: "密碼修改成功!",
    email: '郵箱',
    oldPwdWarn:'請輸入舊密碼',
    newPwdWarn:'請輸入新密碼',
    pwdConfirmWarn:'請確認新密碼',
    /************************************************/
    //自定義彈窗組件
    resetPswFailure:'重置密碼失敗',
    notification:'提示',
    isResetPsw_a:'是否要將‘',
    isResetPsw_b:'’重置密碼？',
    pwsResetSuccess_a:'已將‘',
    pwsResetSuccess_b:'’密碼重置為123456',
    /************************************************/
    machineSearch: '設備搜索',
    search: '搜索',
    clientRelation: '客戶關系',
    machineDetail: '詳情',
    machineDetail2: '設備詳情',
    machineCtrl: '指令',
    transfer: '轉移',
    belongCustom: '所屬客戶',
    addImeiFirst: '請先添加IMEI號!',
    addUserFirst: '請先添加客戶!',
    transferSuccess: '轉移成功!',
    multiAdd: '批量添加',
    multiImport:'批量導入',
    multiRenew:'批量續費',
    //批量修改设备begin
    editDevice:'修改設備型號',
    deviceAfter: '設備型號（修改後）',
    editDeviceTips:'請確認需修改的設備為同一型號，且為未激活狀態！',
    pleaseChoseDevice: '請先選擇要修改的設備！',
    editResult:'修改結果',
    successCount:'修改成功的設備：',
    failCount:'修改失敗的設備：',
    //批量修改设备end
    multiDelete: '批量刪除',
    canNotAddImei: 'IMEI不存在,無法添加到列表中',
    importTime: '導入時間',
    loginName: '登錄名',
    platformDue: '平臺到期',
    machinePhone: 'SIM號',
    userDue: '用戶到期',
    overSpeedAlarm: '超速報警',
    changeIcon: '圖標',
    dealerNote: '經銷商備註',
    noUserDue: '請輸入用戶到期時間',
    phoneLengththan3:'電話長度必須大於3',
    serialNumberInput:'連號輸入',

    /************************************************/
    sending: '正在發送指令.....請稍候...',
    sendFailure:'發送失敗!',
    ctrlName: '指令名稱',
    interval: '時間間隔',
    intervalError: '時間間隔格式錯誤',
    currectInterval: "請輸入正確的時間間隔！",
    intervalLimit: '設置間隔時間範圍10-720，單位（分鐘）',
    intervalLimit2: '設置間隔時間範圍10-5400，單位（秒）',
    intervalLimit3: '設置間隔時間範圍5-1440，單位（分鐘）',
    intervalLimit4: '設置間隔時間範圍3-999，單位（秒）',
    intervalLimit5: '設置間隔時間範圍10-10800，單位（秒）',
    intervalLimit1: '設置間隔時間範圍1-999，單位（分鐘）設置間隔時間為000，表示關閉定時回傳模式',
    intervalLimit6: '設置間隔時間範圍1-65535，單位（秒）',
    intervalLimit7: '設置間隔時間範圍1-999999，單位（秒）',
    intervalLimit8: '設置間隔時間範圍0-255,0表示關閉',
    intervalLimit9: '設置間隔時間範圍3-10800，單位（秒）',
    intervalLimit10: '設置間隔時間範圍3-86400，單位（秒）',
    intervalLimit11: '設置間隔時間範圍180-86400，單位（秒）',
    intervalLimit22: '設置間隔時間範圍60-86400，單位（秒）',
    intervalLimit23: '設置間隔時間範圍5-60，單位（秒）',
    intervalLimit24: '設置間隔時間範圍10-86400，單位（秒）',
    intervalLimit25: '設置間隔時間範圍5-43200，單位（分鐘）',
    intervalLimit12: '設置間隔時間範圍10-60，單位（秒）',
    intervalLimit13: '設置間隔時間範圍1-24（表示1-24小時）或101-107（表示1-7天）',
    intervalLimit14:'設置間隔時間範圍10-3600，單位（秒）；默認：10s',
    intervalLimit15:'设置间隔时间范围180-86400，单位（秒）；默认：3600s',
    intervalLimit16:'設置間隔時間範圍180-86400，單位（秒）；默認：3600s',
    intervalLimit17:'設定溫度範圍-127-127，組織（℃）',
    intervalLimit18: '設置間隔時間範圍5-18000，單位（秒）',
    intervalLimit19: '設置間隔時間範圍10-300，單位（秒）',
    intervalLimit20: '設置間隔時間範圍5-399，單位（秒）',
    intervalLimit21: '設置間隔時間範圍5-300，單位（秒）',
    noInterval: '請輸入時間間隔!',
    intervalTips:'需關閉追蹤模式請設置鬧鐘喚醒時間',
    phoneMonitorTips:'指令發出後設備會主動撥打回撥號碼，以實現監聽。',
    time1: '時間1',
    time2: '時間2',
    time3: '時間3',
    time4: '時間4',
    time5: '時間5',
    intervalNum: '間隔(分鐘)',
    sun: '周日',
    mon: '周壹',
    tue: '周二',
    wed: '周三',
    thu: '周四',
    fri: '周五',
    sat: '周六',
    awakenTime: '喚醒時間點',
    centerPhone: '中心號碼',
    inputCenterPhone: '請輸入中心號碼!',
    phone1: '號碼一',
    phone2: '號碼二',
    phone3: '號碼三',
    phone4: '號碼四',
    phone5: '號碼五',
    inputPhone: '請輸入號碼',
    offlineCtrl: '離線指令已保存，設備上線後離線指令將自動下發給設備',
    terNotSupport: '終端不支持',
    terReplyFail: '終端回復失敗',
    machineInfo: '設備信息',

    /************************************************/
    alarmTypeScreen: '報警類型篩選',
    allRead: '全部已讀',
    read: '已讀',
    noAlarmInfo:'暫無可清除的報警信息',
    alarmTip: '提示 : 取消選中即可過濾該類型報警信息',

    /************************************************/
    updatePsw: '密碼',
    resetPsw:'重置密碼',

    /************************************************/
    multiSell: '批量銷售',
    sell: '銷售',
    sellSuccess: '銷售成功!',
    modifySuccess:'修改成功',
    modifyFail:'修改失敗',
    multiTransfer: '批量轉移',
    multiUserExpires: '修改用戶到期',
    batchModifying:'批量修改',
    userTransfer:'轉移',
    machineRemark: '備註',
    sendCtrl: '發送指令',
    ctrl: "指令",
    ctrlLog: '指令記錄',
    ctrlLogTips: '指令記錄',
    s06Ctrls: ['遠程斷油電', '遠程恢復油電', '查詢定位'],
    ctrlType: '指令名稱',
    resInfo: '響應信息',
    resTime: '響應時間',
    ctrlSendTime: '發送時間',
    // csv文件導入上傳
    choseCsv:'請選擇csv文件',
    choseFile:'選擇文件',
    submit:'提交',
    targeDevice:'目標設備',
    csvTips_1:'1、將excel文件另存為csv格式',
    csvTips_2:'2、將csv文件導入系統。',
    importExplain:'導入說明：',
    fileDemo:'文件格式示例',

    // 新增
    sendType: '發送類型',
    onlineCtrl: '在線指令',
    offCtrl: '離線指令',
    resStatus: ['未發送','已失效','已下發','執行成功','執行失敗','無應答'],
    /************************************************/
    addSubordinateClient: '新增下級用戶',
    noSubordinateClient:'沒有下級用戶',
    superiorCustomerEmpty: '請選擇上級客戶',
    noCustomerName: "請輸入客戶名稱",
    noLoginAccount: '請輸入登錄帳號',
    noPsw: '請輸入密碼',
    noConfirmPsw: '請輸入確認密碼',
    pswNotAtypism: '您輸入的密碼和確認密碼不壹致!',
    addSuccess: '添加成功',
    superiorCustomer: '上級客戶',
    addVerticalImei: '請按壹豎列輸入IMEI號',
    noImei: 'IMEI均不存在,無法添加到列表中',
    addImei_curr: '請輸入IMEI號,當前',
    no: '個',
    aRowAImei: '壹行輸入壹個IMEI',

    /*
    * dealer  界面翻譯開始
    *
    * */
    //main.js
    imeiOrUserEmpty: "設備號(IMEI)/客戶名不能為空!",
    accountEmpty: '帳號不能為空!',
    queryNoUser: "未查詢到該用戶",
    queryNoIMEI: "未查詢到該IMEI號",
    imeiOrClientOrAccount: '設備號(IMEI)/客戶名/帳號',
    dueSoon: '即將到期',
    recentlyOffline: '最近離線',
    choseSellDeviceFirst:'請先選擇要銷售的設備!',
    choseDeviceFirst:'請先選擇要轉移的設備!',
    choseDeviceExpiresFirst:'請先選擇要修改的設備!',
    choseRenewDeviceFirst:'請先選擇要續費的設備!',
    choseDeleteDeviceFirst:'請先選擇要刪除的設備!',
    choseClientFirst:'請先選擇要轉移的客戶!',

    //myClient.js
    clientList: '客戶列表',
    accountInfo: '帳戶信息',
    machineCount: '設備數量',
    stock: '進貨',
    inventory: '庫存',
    subordinateClient: '下級用戶',
    datum: '資料',
    monitor: '監控',
    dueMachineInfo: '到期設備信息',
    haveExpired: '已過期',
    timeRange: ['7天內', '30天內', '60天內', '7-30天內', '30-60天內'],
    offlineMachineInfo: '離線設備信息',
    timeRange1: ['1小時內', '1天內', '7天內', '30天內', '60天內', '60天以上', '1小時-1天內', '1-7天內', '7-30天內', '30-60天內'],
    offlineTime: '離線時長',
    includeSubordinateClient: '包含下級用戶',

    stopMachineInfo: '靜止設備信息',
    stopTime1: '靜止時長',
    unUseMachineInfo: '未啟用設備信息',
    unUseMachineCount: '未啟用設備數為',

    sellTime: '銷售時間',
    detail: '詳細',
    manageDevice:'詳細',
    details:'明細',
    deleteSuccess: '刪除成功!',
    deleteFail:'刪除失敗!',
    renewalLink: '續費連結',
    deleteGroupTips:"是否刪除分組",
    addGroup:'添加分組',
    jurisdictionRange: '權限範圍:可修改功能',
    machineSellTransfer: '設備銷售轉移',
    monitorMachineGroup: '監控設備分組',
    jurisdictionArr: ['客戶管理', '消息管理', '圍欄設置', '報警信息', '虛擬帳號管理', '下發指令'],
    confrimDelSim: '確定刪除該sim卡號:',

    // 右鍵菜單
    sellDevice:'銷售設備',
    addClient:'新增客戶',
    deleteClient:'刪除客戶',
    resetPassword:'重置密碼',
    transferClient:'客戶轉移',
    ifDeleteClient:'是否刪除',


    //myAccount
    myWorkPlace: '工作臺',
    availablePoints: '可用點數',
    yearCard: '年卡',
    lifetimeOfCard: '終生卡',
    oneyear:'一年',
    lifetime:'終身',
    commonImportPoint: '普通導入點',
    lifetimeImportPoint: '終身導入點',
    myServiceProvide: '服務商',
    moreOperator: '更多操作',
    dueMachine: '到期設備',
    offlineMachine: '離線設備',
    quickSell: '快速銷售',
    sellTo: '銷售給',
    machineBelong: '設備屬於',
    reset: '重置',
    targetCustomer: '目標客戶',
    common_lifetimeImport: '普通導入點(0),終身導入點(0)',
    cardType1: '卡類型',
    credit: '充值點數',
    generateImportPoint: '生成導入點',
    generateImportPointSuc: '生成導入點成功!',
    generateImportPointFail: '生成導入點失敗!',
    year_lifeTimeCard: '年卡(0),終生卡(0)',
    generateRenewPoint: '生成續費點',
    transferTo: '轉移到',
    transferPoint: '轉移點數',
    transferRenewPoint: '轉移續費點',
    pointHistoryRecord: '點數記錄',
    newGeneration: '新生成',
    operatorType: '操作類型',
    consume: '消費',
    give: '給',
    income: '收入',
    pay: '支出',
    imeiErr: '查詢的設備,IMEI號必須至少是後6位以上的數字!',
    accountFirstPage: '帳戶首頁',


    /*
    * dealer  界面翻譯結束
    *
    * */
// 網頁1.4.8風控部分翻譯
    finrisk: '金融風控',
    attention: '關註',
    cancelattention: '取消關註',
    poweroff: '斷電',
    inout: '進出二押',
    inoutEF: '進出圍欄',
    longstay: '二押久留',
    secsetting: '二押點設置',
    EFsetting: '圍欄設置',
    polygonFence:'多邊形電子圍欄',
    cycleFence:'圓形電子圍欄',
    haveBeenSetFence:'已設置電子圍欄',
    haveBeenSetPoint:'已設置二押點',
    drawingFailed:'繪制失敗，請重新繪制',
    inoutdot: '進出二押點',
    eleStatistics: '電量統計',
    noData:'沒有數據',
// 進出二押點表格
    accountbe: '所屬帳號',
    SMtype: '二押點類型',
    SMname: '二押點名稱',
    time: '時間',
    position: '位置',
    lastele:'剩余電量',
    statisticTime: '統計時間',
    searchalarmType: ['全部','離線','斷電','進出圍欄','進出二押','二押久留'],
    remarks: ['二押點','擔保公司','拆機點','二手交易市場'],
    focusOnly: '僅關註',
// [?]描述
    interpretSignal:'信號：設備最近壹次和平臺通信時間',
    interpretPosition: '定位：設備最近壹次衛星定位時間',
    interpretAll: '在線設備靜止時不定位，但仍會與平臺通信',
    autoRecord:'自動錄音',
    /******************************************************設置指令開始**********************************8*/
    setCtrl: {
        text: '設置指令',
        value: ''
    },
    moreCtrl: {
        text: '更多指令',
        value: ''
    },
    sc_openTraceModel: {
        text: '開啟追蹤模式',
        value: '0'
    },
    sc_closeTraceModel: {
        text: '關閉追蹤模式',
        value: '1'
    },
    sc_setSleepTime: {
        text: '設置休眠時長',
        value: '2'
    },
    sc_setAwakenTime: {
        text: '設置喚醒時間點',
        value: '3'
    },
    sc_setDismantleAlarm: {
        text: '設置防拆報警',
        value: '4'
    },
    sc_setSMSC: {
        text: '增加中心號碼',
        value: '5'
    },
    sc_delSMSC: {
        text: '刪除中心號碼',
        value: '6'
    },
    sc_setSOS: {
        text: '添加SOS',
        value: '7'
    },
    sc_delSOS: {
        text: '刪除SOS',
        value: '8'
    },
    sc_restartTheInstruction: {
        text: '重啟指令',
        value: '9'
    },
    sc_uploadTime: {
        text: '設置上傳間隔',
        value: '10'
    },
    /*鬧鐘喚醒時間設置
     定時回傳時間設置
     防拆報警設置
     星期模式開啟關閉*/
    sc_setAlarmClock: {
        text: '設置鬧鐘喚醒時間',
        value: '11'
    },
    sc_setTimingRebackTime: {
        text: '設置定時回傳時間',
        value: '12'
    },
    sc_openWeekMode: {
        text: '開啟星期模式',
        value: '13'
    },
    sc_closeWeekMode: {
        text: '關閉星期模式',
        value: '14'
    },
    sc_powerSaverMode: {
        text: '設置定時回傳模式',
        value: '15'
    },
    sc_carCatchingMode: {
        text: '設置追車模式',
        value: '16'
    },
    sc_closeDismantlingAlarm: {
        text: '關閉防拆報警設置',
        value: '17'
    },
    sc_openDismantlingAlarm: {
        text: '打開防拆報警設置',
        value: '18'
    },
    sc_VibrationAlarm:{
        text:'設置震動報警',
        value:'19'
    },
    sc_timeZone:{
        text:'時區設置',
        value:'20'
    },
    sc_phoneMonitor:{
        text: '電話監聽',
        value: '21'
    },
    sc_stopCarSetting:{
        text: '停車設置',
        value: '22'
    },
    sc_bindAlarmNumber:{
        text: '綁定報警號碼',
        value: '23'
    },
    sc_bindPowerAlarm:{
        text: '斷電報警',
        value: '24'
    },
    sc_fatigueDrivingSetting:{
        text: '疲勞駕駛設置',
        value: '25'
    },
    sc_peripheralSetting:{
        text: '外設設置',
        value: '26'
    },
    sc_SMSAlarmSetting:{
        text: '設置短信報警',
        value: '27'
    },
    sc_autoRecordSetting:{
        text: '自動錄音設置',
        value: '28'
    },
    sc_monitorCallback:{
        text: '監聽回撥',
        value: '29'
    },
    sc_recordCtrl:{
        text: '錄音指令',
        value: '30'
    },
    sc_unbindAlarmNumber:{
        text: '解除綁定報警號碼',
        value: '31'
    },
    sc_alarmSensitivitySetting:{
        text: '震動報警靈敏度設置',
        value: '32'
    },
    sc_alarmSMSsettings:{
        text: '震動報警短信設置',
        value: '33'
    },
    sc_alarmCallSettings:{
        text: '震動報警電話設置',
        value: '34'
    },
    sc_openFailureAlarmSetting:{
        text: '開啟斷電報警',
        value: '35'
    },
    sc_restoreFactory:{
        text: '恢復出廠',
        value: '36'
    },
    sc_openVibrationAlarm:{
        text:'開啟震動報警',
        value:'37'
    },
    sc_closeVibrationAlarm: {
        text: '關閉震動報警',
        value: '38'
    },
    sc_closeFailureAlarmSetting:{
        text: '關閉斷電報警',
        value: '39'
    },
    sc_feulAlarm:{
        text: '油量報警設置',
        value: '40'
    },
    //1.6.72
    sc_PowerSavingMode:{
        text:'省電模式',
        value:'41'
    },
    sc_sleepMode:{
        text:'休眠模式',
        value:'42'
    },
    sc_alarmMode:{
        text:'鬧鐘模式',
        value:'43'
    },
    sc_weekMode:{
        text:'星期模式',
        value:'44'
    },
    sc_monitorNumberSetting:{
        text:'監聽號碼設置',
        value:'45'
    },
    sc_singlePositionSetting:{
        text:'單次定位模式',
        value:'46'
    },
    sc_timingworkSetting:{
        text:'定時工作模式',
        value:'47'
    },
    sc_openLightAlarm:{
        text:'開啟光感報警',
        value:'48'
    },
    sc_closeLightAlarm:{
        text:'關閉光感報警',
        value:'49'
    },
    sc_workModeSetting:{
        text:'工作模式設置',
        value:'50'
    },
    sc_timingOnAndOffMachine:{
        text:'定時開關機設置',
        value:'51'
    },
    sc_setRealTimeTrackMode:{
        text:'設置實時追車模式',
        value:'52'
    },
    sc_setClockMode:{
        text:'設置鬧鐘模式',
        value:'53'
    },
    sc_openTemperatureAlarm:{
        text:'開啟溫度報警',
        value:'54'
    },
    sc_closeTemperatureAlarm:{
        text:'關閉溫度報警',
        value:'55'
    },
    sc_timingPostbackSetting: {
        text: '定時回傳設置',
        value: '56'
    },
    sc_remoteBoot: {
        text: '遠程開機',
        value: '57'
    },
    sc_smartTrack: {
        text: '智能跟踪',
        value: '58'
    },
    sc_cancelSmartTrack: {
        text: '取消智能追踪',
        value: '59'
    },
    sc_cancelAlarm: {
        text: '取消報警',
        value: '60'
    },
    sc_smartPowerSavingMode: {
        text: '設置智能省電模式',
        value: '61'
    },
    sc_monitorSetting: {
        text: '監聽',
        value: '62'
    },
     // 指令重构新增翻译
     sc_timedReturnMode: {
        text: '定時回傳模式',
        value: '100'
    },
    sc_operatingMode: {
        text: '工作模式',
        value: '101'
    },
    sc_realTimeMode : {
        text: '實時定位模式',
        value: '102'
    },
    sc_alarmMode : {
        text: '鬧鐘模式',
        value: '103'
    },
    sc_weekMode : {
        text: '星期模式',
        value: '104'
    },
    sc_antidemolitionAlarm : {
        text: '防拆告警',
        value: '105'
    },
    sc_vibrationAlarm : {
        text: '震動告警',
        value: '106'
    },
    sc_monitoringNumber : {
        text: '監聽號碼管理',
        value: '107'
    },
    sc_queryMonitoring : {
        text: '查詢監聽號碼',
        value: '108'
    },
    sc_electricityControl : {
        text: '油電控制',
        value: '109'
    },
    sc_SOSnumber : {
        text: 'SOS號碼管理',
        value: '110'
    },
    sc_SleepCommand : {
        text: '休眠指令',
        value: '201'
    },
    sc_RadiusCommand : {
        text: '位移半徑',
        value: '202'
    },
    sc_punchTimeMode:{
        text:'打卡模式',
        value:'203'  
    },
    sc_intervelMode:{
        text:'时间段模式',
        value:'204'  
    },
    sc_activeGPS:{
        text:'激活GPS',
        value:'205'  
    },
    sc_lowPowerAlert: {
        text: '低電報警',
        value: '206'
    },
    sc_SOSAlert: {
        text: 'SOS报警',
        value: '207'
    },
    mc_cuscom : {
        text: '自定義指令',
        value: '1'
    },
    NormalTrack: '正常追踪模式',
    listeningToNumber:'確定要查詢監聽號碼嗎？',
    versionNumber:'確定要查詢版本號嗎？',
    longitudeAndLatitudeInformation:'確定要查詢經緯度信息嗎？',
    equipmentStatus:'確定要查詢狀態嗎？',
    public_parameter:'確定要查詢參數嗎？',
    GPRS_parameter:'確定要查詢GPRS參數嗎？',
    deviceName: '确定要对设备点名吗？',
    SMS_alert:'确定要查询短信提醒报警吗？',
    theBindingNumber:'確定要查詢綁定號碼嗎？',
    intervalTimeRange:'設置間隔時間範圍為001-999，單位（分鐘）',
    pleaseChoose:'請選擇',
    RealTimeCarChase: '確定要將此設備設置為實時追車模式嗎？',
    inputPhoneNumber: "請輸入手機號碼",
    inputCorPhoneNumber: "請輸入正確的手機號碼",
    autoCallPhone: "提示：指令執行成功後，終端會自動撥打設置的號碼",
    limitTheNumberOfCellPhoneNumbers1:'此指令最多支持5個手機號碼',
    limitTheNumberOfCellPhoneNumbers2:'此指令最多支持設置3個手機號',
    equipmentTorestart:'確定要重啟此設備嗎',
    remindTheWay:'提醒方式',
    alarmWakeUpTime:'鬧鐘喚醒時間',
    alarmWakeUpTime1:'鬧鐘喚醒時間1',
    alarmWakeUpTime2:'鬧鐘喚醒時間2',
    alarmWakeUpTime3:'鬧鐘喚醒時間3',
    alarmWakeUpTime4:'鬧鐘喚醒時間4',
    sensitivityLevel:'請選擇靈敏度級別',
    parking_time:'停車時長',
    selectWorkingMode:'請選擇工作模式',
    Alarm_value:'報警值',
    Buffer_value:'緩沖值',
    gqg_disconnect:'斷開',
    gqg_turnOn:'開啟',
    Return_interval:'回傳間隔',
    gq_startTime:'運動時間',
    gq_restingTime:'靜止時間',
    gq_Eastern:'東時區',
    gq_Western:'西時區',

    gq_driver:'疲勞駕駛報警',
    gq_deviceName:'確定要對此設備點名嗎？',
    gq_noteAlarm:'確定要查詢短信提醒報警嗎？',
    gq_restoreOriginal:'確定要將此設備恢復原廠嗎？',
    gq_normalMode:'正常模式',
    gq_IntelligentsleepMode:'智能休眠模式',
    gq_DeepsleepMode:'深度休眠模式',
    gq_RemotebootMode:'遠程開機模式',
    gq_IntelligentsleepModeTips:'確定要設置為智能休眠模式嗎',
    gq_DeepsleepModeTips:'確定要設置為深度休眠模式嗎',
    gq_RemotebootModeTips:'確定要設置為遠程開機模式嗎',
    gq_normalModeTips:'確定要設置為正常模式嗎',
    gq_sleepModeTips:'確定要將此設備設置為休眠模式嗎',
    gq_Locatethereturnmode:'定位回傳模式',
    gq_regularWorkingHours:'定時工作時段',
    gq_AlarmType:{
        text: '報警類型',
        value: '111'
    },
    IssuedbyThePrompt:'指令已經下發，請等待設備響應',
    platformToinform:'平台通知',
    gq_shortNote:'短信通知', 
    /************指令白話文**********************/
    closeDismantlingAlarm:'關閉防拆報警設置',
    openDismantlingAlarm:'打開防拆報警設置',
    closeTimingRebackMode:'關閉定時回傳模式設置',
    minute:'分鐘',
    timingrebackModeSetting:'設置定時回傳模式:',
    setWakeupTime:'設置喚醒時間點:',
    weekModeSetting:'設置星期模式：',
    closeWeekMode:'關閉星期模式設置',
    setRealtimeTrackMode:'設置實時追車模式',
    fortification:'設防',
    disarming:'撤防',
    settimingrebackmodeinterval:'設置定時回傳模式間隔:',
    oilCutCommand:'斷油電指令',
    restoreOilCommand:'恢復油電指令',
    turnNnTheVehiclesPower:'啟用車輛電源',
    turnOffTehVehiclesPower:'關閉車輛電源',
    implementBrakes:'執行剎車',
    dissolveBrakes:'解除剎車',
    openVoiceMonitorSlarm:'啟用報警語音',
    closeVoiceMonitorAlarm:'關閉報警語音',
    openCarSearchingMode:'開啟尋車模式',
    closeCarSearchingMode:'關閉尋車模式',
    unrecognizedCommand:'無法識別指令',
    commandSendSuccess:'恭喜您，設備執行命令成功！',
    /********************************************設置指令結束**************************************************/


    /********************************************查詢指令開始**************************************************/
    queryCtrl: {
        text: '查詢指令',
        value: ''

    },
    /*參數設置查詢*/
    qc_softwareVersion: {

        text: '查詢軟件版本',
        value: '1'
    },
    qc_latlngInfo: {
        text: '查詢經緯度信息',
        value: '2'
    },
    qc_locationHref: {
        text: '查詢參數配置',
        value: '3'
    },
    qc_status: {
        text: '查詢狀態',
        value: '4'
    },
    qc_gprs_param: {
        text: '查詢GPRS參數',
        value: '5'
    },
    qc_name_param: {
        text: '點名',
        value: '6'
    },
    qc_SMSReminderAlarm_param: {
        text: '查詢短信提醒報警',
        value: '7'
    },
    qc_bindNumber_param: {
        text: '查詢綁定號碼',
        value: '8'
    },

    /********************************************查詢指令結束**************************************************/

    /*******************************************控制指令開始***************************************************/

    controlCtrl: {
        text: '控制指令',
        value: ''
    },
    cc_offOilElectric: {
        text: '斷油電',
        value: '1'
    },
    cc_recoveryOilElectricity: {
        text: '恢復油電',
        value: '2'
    },
    cc_factorySettings: {
        text: '恢復出廠設置',
        value: '4'
    },
    cc_fortify: {
        text: '設防',
        value: '75'
    },
    cc_disarming: {
        text: '撤防',
        value: '76'
    },
    cc_brokenOil:{
        text: '斷油指令',
        value: '7'
    },
    cc_RecoveryOil:{
        text: '恢復油路',
        value: '8'
    },

    /*******************************************控制指令結束***************************************************/


    /*
* m--》min
* 2018-01-23
* */
    km: '公里', mileage: '里程', importMachine: '導入設備', transferImportPoint: '轉移導入點',
    machineType1: '設備型號', confirmIMEI: '操作前請確認IMEI號和型號',
    renew: '續費', deductPointNum: '扣除點數', renewSuccess: '續費成功!',
    wireType:[
        {
            text:'有線',
            value:false
        },
        {
            text:'無線',
            value:true
        }
    ],
    vibrationWays:[
        {
            text:'平臺',
            value:0
        },
        {
            text:'平臺+短信',
            value:1
        },
        {
            text:'平臺+短信+電話',
            value:2
        }
    ],
    addMachineType: [{
        text: 'S06',
        value: '3'
    },
    // SO6子級-----start-----------
     {
        text: 'GT06',
        value: '8'
    },{
        text: 'S08V',
        value: '9'
    },{
        text: 'S01',
        value: '10'
    },{
        text: 'S01T',
        value: '11'
    },{
        text: 'S116',
        value: '12'
    },{
        text: 'S119',
        value: '13'
    },{
        text: 'TR06',
        value: '14'
    },{
        text: 'GT06N',
        value: '15'
    },{
        text: 'S101',
        value: '16'
    },{
        text: 'S101T',
        value: '17'
    },{
        text: 'S06U',
        value: '18'
    },{
        text: 'S112U',
        value: '19'
    },{
        text: 'S112B',
        value: '20'
    }
    // SO6子級-----end-----------
    ,{
        text: 'S15',
        value: '1'
    },{
        text: 'S05',
        value: '2'
    },{
        text: 'SW06',
        value: '4'
    },{
        text: 'S001',
        value: '5'
    },{
        text: 'S08',
        value: '6'
    },{
        text: 'S09',
        value: '7'
    }],

    /*
    * 2018-02-02
    * */
    maploadfail: "當前地圖加載失敗，是否切換到其它地圖?",

    /*
    2018-03-06新增 控制指令
    * */
    cc_openPower: {
        text: '開啟車輛電源',
        value: '7'
    },
    cc_closePower: {
        text: '關閉車輛電源',
        value: '8'
    },
    cc_openBrake: {
        text: '開啟車輛剎車',
        value: '9'
    },
    cc_closeBrake: {
        text: '關閉車輛剎車',
        value: '10'
    },
    cc_openAlmrmvoice: {
        text: '開啟車輛警鈴',
        value: '11'
    },
    cc_closeAlmrmvoice: {
        text: '關閉車輛警鈴',
        value: '12'
    },
    /*2018-03-06新增 控制指令
    * */
    cc_openFindCar: {
        text: '開啟車輛尋車',
        value: '13'
    },
    cc_closeFindCar: {
        text: '關閉車輛尋車',
        value: '14'
    },

    /*2018-03-19
    * */
    EF: '圍欄',

    /*
    2018-03-29，擴展字段
    * */
    exData:['電量','電壓','油量','溫度','電阻'],

    /*
    2018-04-10
    * */
    notSta:'基站定位，不列入統計',

    // 油量统计
    fuelSetting:'油量設置',
    mianFuelTank:'主油箱',
    auxiliaryTank:'副油箱',
    maximum:'最大值',
    minimum:'最小值',
    FullTankFuel: '滿箱油量',
    fuelMinValue: '滿箱油量不能低於10L',
    standardSetting:'標准設定',
    emptyBoxMax:'空箱最大值',
    fullBoxMax:'滿箱最大值',
    fuelStatistics:'油量統計',
    settingSuccess:'設置成功',
    settingFail:'設置失敗',
    pleaseInput:'請輸入',
    fuelTimes:'加油次數',
    fuelTotal:'加油總量',
    refuelingTime: '加油時長',
    fuelDate:'加油時間',
    fuel:'油量',
    fuelChange:'油量變化',
    feulTable:'油量分析表',
    addFullFilter:'請補充完整篩選條件',
    enterIntNum:'請輸入正整數',

    tempSta:'溫度統計',
    tempTable:'溫度分析表',
    industrySta:'行業統計',
    temperature:'溫度',
    temperature1:'溫度1',
    temperature2:'溫度2',
    temperature3:'溫度3',
    tempRange:'溫度範圍',
    tempSetting:'溫度設置',
    tempSensor:'溫度感應器',
    tempAlert:'溫度感應器關閉後，將不接收溫度數據！',
    phoneNumber:'手機號碼',
    sosAlarm:'SOS報警',
    undervoltageAlarm:'欠壓報警',
    overvoltageAlarm:'過壓報警',
    OilChangeAlarm:'油量變化報警',
    accDetection:'ACC檢測',
    PositiveAndNegativeDetection:'正反轉檢測',
    alermValue:'報警值',
    bufferValue:'緩沖值',
    timeZoneDifference:'時區差值',
    meridianEast:'子午線東',
    meridianWest:'子午線西',
    max12hour:'不能大於12小時',
    trackDownload:'軌跡下載',
    download:'下载',
    multiReset:'批量重置',
    resetSuccess:'重置成功',
    multiResetTips:'重置后设备激活时间和轨迹等测试数据将清除，设备状态在线或离线重置为未启用。重置後設備激活時間和軌蹟等測試數據將清除，設備狀態在線或離線重置為未啟用',
    point:'點',
    myplace:'我的地點',
    addPoint:'添加點',
    error10018:'導入點數量不足',
    error110:'對像不存在',
    error109:'超過最大限制',
    error20013:'設備類型不存在',
    error90001:'設備類型序號不能為空',
    error20003:'Imei不能為空',
    inputName:'請輸入名稱',
    virtualAccount:'虛擬帳號',
    createTime:'創建時間',
    permission:'權限',
    permissionRange:'權限範圍',
    canChange:'可修改的功能',
    fotbidPassword:'修改密碼',
    virtualAccountTipsText:'創建虛擬帳號時，其為當前登錄的經銷商帳號的別名帳號，可以為虛擬帳號設置權限。若要為終端用戶創建虛擬帳號，可以先將終端用戶類型改為經銷商，然後用該經銷商登錄，創建虛擬帳號，再將類型改回終端用戶。',
    noOperationPermission:'虛擬帳號無操作權限',
    number:'號碼',
    rangeSetting:'範圍設置',
    setting:'設置',
    // 1.6.1
    duration:'持續時間',
    voltageSta:'電壓統計',
    voltageAnalysis:'電壓分析',
    voltageEchart:'電壓分析表',
    platformAlarm:'平台報警',
    platformAlarm1:'電話',
    platformAndPhone: '電話+平台報警',
    smsAndplatformAlarm:'SMS+平台報警',
    smsAndplatformAlarm1:'SMS',
    smsAndplatformAlarmandPhone:'平台報警+SMS+電話',
    smsAndplatformAlarmandPhone1:'SMS+電話',
    more_speed:'速度',
    attribute:'屬性',
    profession:'專業',
    locationPoint:'定位點',
    openPlatform:'開放平臺',
    experience:'我要體驗',
    onlyViewMonitor:'僅可查看監控',
    inputAccountOrUserName:'請輸入帳號或用戶名',
    noDeviceTips:'沒查到相關的設備信息，查客戶點',
    noUserTips:'沒查到相關的用戶信息，查設備',
    clickHere:'這裡',
    pointIntervalSelect:'軌跡點間隔',
    payment:'繳費',
    pleaceClick:'請點擊',
    paymentSaveTips:'安全提示:請與服務商確認鏈接的有效性',
    fuelAlarmValue:'油量報警值',
    fuelConsumption:'油耗',
    client:'客戶',
    create:'新建',
    importPoint:'導入點',
    general:'普通',
    lifelong:'終身',
    renewalCard:'續費卡',
    settingFuelFirst:'請先設置油量報警值後再發送指令!',
    overSpeedSetting:'超速設置',
    kmPerHour:'km/h',
    times:'次',
    total:'總計',
    primary:'主',
    minor:'副',
    unActiveTips:'設備未激活，無法使用該功能',
    arrearsTips:'設備已欠費，無法使用該功能',
    loading:'數據加載中...',
    expirationReminder:'到期提醒',
    projectName:'項目名稱',
    expireDate:'過期時間',
    changePwdTips:'您的密碼過於簡單，存在安全風險，請馬上修改密碼',
    pwdCheckTips1:'建議是6-20位字母、數字或符號',
    pwdCheckTips2:'您輸入的密碼强度過弱',
    pwdCheckTips3:'您的密碼還可以更複雜些',
    pwdCheckTips4:'您的密碼很安全',
    pwdLevel1:'弱',
    pwdLevel2:'中',
    pwdLevel3:'强',
    comfirmChangePwd:'確定修改密碼',
    notSetYet:'暫不設定',
    liter:'升',
    arrearageDayTips:'天后到期',
    todayExpire:'今天到期',
    forgotPwd:'忘記密碼?',
    forgotPwdTips:'請聯繫賣家修改密碼',
    //1.6.7
    commonProblem:'常見問題',
    instructions:'操作說明',
    webInstructions:'WEB操作說明',
    appInstructions:'APP操作說明',
    acceptAlarmNtification:'接收報警通知',
    alarmPeriod:'報警時段',
    whiteDay:'白天',
    blackNight:'黑夜',
    allDay:'全天',
    alarmEmail:'報警郵件',
    muchEmailTips:'可輸入多個郵箱，用符號‘;’隔開。',
    newsCenter:'消息中心',
    allNews:'全部消息',
    unReadNews:'未讀消息',
    readNews:'已讀消息',
    allTypeNews:'全部消息類型',
    alarmInformation:'告警信息',
    titleContent:'標題內容',
    markRead:'標記已讀',
    allRead:'全部已讀',
    allDelete:'全部刪除',
    selectFirst:'請先選中再進行操作!',
    updateFail:'更新失敗!',
    ifAllReadTips:'是否全部設置為已讀？',
    ifAllDeleteTips:'是否全部刪除？',
    stationInfo:'站內信息',
    phone:'手機',
    //1.6.72
    plsSelectTime:'請選擇時間!',
    customerNotFound:'找不到該客戶',
    Postalcode:'位置',
    accWarning:'ACC報警',
    canInputMultiPhone:'可以輸入多個手機號碼，請用 ; 隔開',
    noLocationInfo:'該設備尚未有定位資訊。',
    //1.6.9
    fenceName:'圍欄名稱',
    fenceManage:'圍欄管理',
    circular:'圓形',
    polygon:'多邊形',
    allFence:'全部圍欄',
    shape:'形狀',
    stationNews:'站內消息',
    phonePlaceholder:'可輸入多個號碼，以“,”隔開',
    addressPlaceholder:'請按順序輸入地址和郵政編碼，以“,”隔開',
    isUnbind:'是否取消關聯',
    alarmCar:'報警車輛',
    alarmAddress:'報警地點',
    chooseAtLeastOneTime:'至少選擇一個時間',
    alarmMessage:'該條告警信息暫無詳情',
    navigatorBack:'返回上級',
    timeOverMessage:'用戶到期時間不能大於平台到期時間',
    //1.7.0
    userTypeStr: '用戶類型',
    newAdd: '新增',
    findAll: '一共找到',
    findStr: '條匹配的數據',
    customColumn: '自定義列',
    updatePswErr: '更新密碼失敗',
    professionalUser: '是否專業用戶',
    confirmStr: '確認',
    inputTargetCustomer: '請輸入目標客戶',
    superiorUser: '上級用戶',
    speedReport: '速度報表',
    createAccount: '創建帳號',
    push: '推送',
    searchCreateStr: '此操作將操作一個帳號，同時將此設備轉移到該帳號名下',
    allowIMEI: '允許IMEI登錄',
    defaultPswTip: '默認密碼為IMEI後6位',
    createAccountTip: '創建帳號並轉移設備成功',
    showAll: '顯示全部',
    bingmap: '必應地圖',
    areaZoom:'區域縮放',
    areaZoomReduction:'區域縮放還原',
    reduction:'還原',
    saveImg:'保存為圖片',
    fleetFence: '車隊圍欄',
    alarmToSub: '報警通知下級',
    bikeFence: '單車圍欄',
    delGroupTip:'删除失敗，請先删除興趣點！',
    isExporting:'正在匯出...',
    addressResolution:'地址解析中...',
    simNOTip:'SIM卡號只能為數字',
    unArrowServiceTip:'存在以下設備的用戶到期時間大於平台到期時間，請重新選擇。設備號為：',
    platformAlarmandPhone:'平台報警+電話',
    openLightAlarm:'開啟光感報警',
    closeLightAlarm:'關閉光感報警',
    ACCAlarm:'ACC報警',
    translateError:'轉移失敗，目標用戶無權限',
    distanceTip:'單擊確定地點，雙擊結束',
    workMode:'工作模式',
    workModeType:'0: 正常模式；1 智能休眠模式；2 深度休眠模式',
    clickToStreetMap:'點擊打開街景地圖',
    current:'當前',
    remarkTip:'備註：請勿同時銷售不同類型套餐的卡',
    searchRes:'搜索結果',
    updateIcon:'更改圖標',
    youHaveALarmInfo:'您有一條報警信息',
    moveInterval:'運動時間間隔',
    staticInterval:'靜止時間間隔',
    notSupportTraffic:'Coming soon.',
    ignite:'打火',
    flameout:'熄火',
    generateRenewalPointSuc:'生成續費點成功！',
    noGPSsignal:'未定位',
    imeiErr2:'請輸入至少後6比特以上IMEI號',
    searchCreateStr2:'此操作將創建一個帳號，同時將此設備轉移到該帳號名下',
    addUser:'新增用戶',
    alarmTemperature:'報警溫度值',
    highTemperatureAlarm:'高溫報警',
    lowTemperatureAlarm:'低溫報警',
    temperatureTip:'請輸入溫度值！',
    locMode:'定位模式',
    imeiInput:'請輸入IMEI號',
    noResult:'沒有匹配的結果',
    noAddressKey:'今天的解析次數已用完',
    deviceGroup: '設備分組',
    shareManage: '分享管理',
    lastPosition: '最後位置',
    defaultGroup: '默認組',
    tankShape: '油箱形狀',
    standard: '標準',
    oval: '橢圓',
    irregular: '不規則',
    //1.8.4
    inputAddressOrLoc:'請輸入地址/經緯度',
    inputGroupName:'請輸入組名',
    lock:'鎖定',
    shareHistory:'分享軌跡',
    tomorrow:'明天',
    threeDay:'三天',
    shareSuccess:'生成分享鏈接成功',
    effective:'有效',
    lapse:'失效',
    copyShareLink:'複製分享鏈接',
    openStr:'開啟',
    closeStr:'關閉',
    linkError:'此分享鏈接已失效',
    inputUserName:'請輸入客戶名稱',
    barCodeStatistics:'條碼統計',
    barCode:'條碼統計',
    sweepCodeTime:'掃碼時間',
    workModeType2: '1 智能休眠模式；2 深度休眠模式；3 遠程開關機模式',
    remoteSwitchMode: '遠程開關機模式',
     saleTime :'銷售日期',
     onlineTime :'上線日期',
     dayMileage:'今日里程',
     imeiNum:'IMEI號',
     overSpeedValue:'超速閾值',
     shareNoOpen:'分享鏈接未啟用',
     addTo2:'添加到',
     overDue:'過期',
     openInterface:'開放接口',
     privacyPolicy:'隱私政策',
    serviceTerm:'服務條款',
     importError:'添加失敗，設備號(IMEI)必須為15位純數字!',
     importResult:'導入結果',
     totalNum:'共計',
     successInfo:'成功',
     errorInfo:'失敗',
     repeatImei:'重複IMEI號',
     includeAccount:'包含子帳號',
    formatError:'格式錯誤',
    importErrorInfo:'請輸入15位純數字的IMEI號',
    totalMileage:'總計里程',
    totalOverSpeed:'總計超速（次）',
    totalStop:'總計停留（次）',
    totalOil:'總計油量',
    timeChoose:'時間選擇',
    intervalTime:'間隔時間',
    default:'默認',
    idleSpeedStatics: '怠速統計',
    offlineStatistics: '離線統計',
    idleSpeed:'怠速',
    idleSpeedTimeTip1:'怠速時間不能為空',
    idleSpeedTimeTip2:'怠速時間必須為正整數',
    averageSpeed:'平均速度',
    averageOil:'平均油耗',
    oilImgTitle:'油量分析圖',
    oilChangeDetail:'油量變化詳情',
    machineNameError:"設備名稱不能包含特殊符號( / ' )",
    remarkError:'備註信息不能超過20個字',
    defineColumnTip:'表格最多顯示12列',
    pswCheckTip: '建議是6-20位數字、字母和符號的組合',
    chooseGroup:'請選擇分組',
    chooseAgain:'僅可查詢近半年的數據，請重新選擇！',
    noDataTip:'暫無數據！',
    noMachineNameError:'請選擇設備！',
    loginAccountError:'登錄帳號不能為15位純數字！',
    includeExpire:'其中過期',
    groupNameTip:'分組名不能為空！',
    outageTips:'確定要斷油電嗎？',
    powerSupplyTips:'確定要恢復油電嗎？',
    centerPhoneTips:'請輸入電話號碼',
    centerPhoneLenTips: '請輸入8-20位數字',
    passworldillegal: "存在非法字符",
    // 2.0.0 POI，权限版本
    singleAdd:'單個添加',
    batchImport:'批量導入',
    name:'名稱',
    icon:'圖標',
    defaultGroup:'默認分組',
    remark:'備註',
    uploadFile:'請上傳文件',
    exampleDownload:'模板下載',
    uploadFiles:'文件上傳',
    poiTips1:'您可通過上傳Excel來導入POI點數據，Excel文件字段需按照模板 以下要求格式輸入',
    poiTips2:'名稱：必填，不能超過32個字',
    poiTips3:'圖標：必填，輸入1,2,3,4',
    poiTips4:'經度：必填',
    poiTips5:'緯度：必填',
    poiTips6:'組名稱：選填，不能超過32個字，若未填寫分組名稱，該POI點歸屬默認分組，若填寫的分組名稱和已創建的分組名稱一致， 該POI點歸屬到已創建的分組中，若填寫的分組名稱未被創建過， 系統會新增該分組',
    poiTips7:'備註：選填，不超過50個字',
    // 权限相关
    roleLimit: '角色權限',
    operateLog: '操作日誌',
    sysAccountManage: '權限帳號',
    rolen: '角色',
    rolename: '角色名稱',
    addRole: '新增角色',
    editRole: '編輯角色',
    deleteRole: '删除角色',
    delRoleTip: '確定要刪除此角色嗎？',
    delAccountTip: '確定要刪除此帳戶嗎？',
    limitconfig: '權限設置',
    newAccountTip1: '權限帳號與舊虛擬帳號類似，同為管理員的子帳號。管理員可創建權限帳號，為權限帳號分配不同的角色，以實現不同帳號在平台能看到的內容和操作不相同。',
    newAccountTip2: '創建權限帳號流程：',
    newAccountTip31: '，並為角色配置權限；',
    newAccountTip32: '新建角色',
    newAccountTip33: '，並為角色配置權限；',
    newAccountTip4: '2. 在權限帳號管理頁面，新建權限帳號，為帳號分配角色。',
    newRoleTip1: '管理員可創建角色，為不同的角色配置不同的操作權限，以滿足不同場景下的業務需求。',
    newRoleTip2: '比如配置一個財務角色是否具有定位監控的權限、是否具有添加客戶的權限、是否具有修改設備信息的權限等等',
    "refuelrate": "加油速率",
    "refuellimit": "每分鐘的油量增加大於xxxxL時，小於xxxxL時，視為加油。",
    "refueltip": "最小加油速率不得大於最大加油速率！",
    viewLimitConf: '查看權限設置',
    viewLimit: '查看權限',
    newSysAcc: '新建系統帳號',
    editSysAcc: '編輯權限帳號',
    virtualAcc: '虛擬帳號',
    oriVirtualAcc: '原虛擬帳號',
    virtualTip: '虛擬帳號模塊已升級為系統帳號模塊，請新建系統帳號',
    operaTime: '操作時間',
    ipaddr: 'IP地址',
    businessType: '業務類型',
    params: '請求參數',
    operateType: '操作類型',
    uAcc: '用戶帳號',
    uName: '用戶名稱',
    uType: '用戶類型',
    logDetail: '日誌詳情',
    delAccount: '刪除帳號',
    modifyTime: '修改時間',
    unbindlimit: '無關聯設備權限，無法創建一鍵電子圍欄！',
    setSmsTip: '若設置短信通知，需先開啟平台通知；若下發平台通知成功，短信通知未成功，需再次開啟短信通知',
    cusSetComTip: '免責聲明：通過自定義指令帶來的風險與平台無關',
    cusSetComPas: '請輸入當前登錄帳號的密碼',
    cusSetComDes1: '自定義指令僅支持在線指令。',
    cusSetComDes2: '指令發送兩分鐘內設備無響應，則結束進程，判斷該指令狀態為無響應。',
    cueSetComoffline: '設備無響應，自定義指令發送失敗！',
    fbType: '反饋類型',
    fbType1: '問題諮詢',
    fbType2: '功能異常',
    fbType3: '用戶體驗',
    fbType4: '新功能建議',
    fbType5: '其他',
    upload: '上傳',
    uploadImg: '上傳圖片',
    uploadType: '請上傳.jpg .png .jpeg .gif類型的文件',
    uploadSize: '上傳文件不能大於3M',
    fbManager: '反饋管理',
    blManager: '公告管理',
    fbUploadTip: '請選擇反饋類型',
    menuPlatform: "平台消息",
    menuFeedback: "意見反饋",
    menuBulletin: "平台公告",
    // 新增驾驶行为
    BdfhrwetASDFFEGGREGRDAF: "駕駛行為",
    BtyjdfghtwsrgGHFEEGRDAF: "急加速",
    BtyuwyfgrWERERRTHDAsdDF: "急減速",
    Be2562h253grgsHHJDbRDAF: "急轉彎",
    celTemperature:'攝氏溫度'
}
// 权限tree
lg.limits = {
    "ACC_statistics": "ACC統計",
    "Account_Home": "帳戶首頁",
    "Add": "新增",
    "Add_POI": "添加POI",
    "Add_customer": "新增客戶",
    "Add_device_group": "添加設備分組",
    "Add_fence": "添加圍欄",
    "Add_sharing_track": "添加分享軌跡",
    "Add_system_account": "新增權限帳號",
    "Alarm_details": "報警詳單",
    "Alarm_message": "報警消息",
    "Alarm_overview": "報警總覽",
    "Alarm_statistics": "報警統計",
    "All_news": "全部消息",
    "Associated_equipment": "關聯設備",
    "Available_points": "可用點數",
    "Barcode_statistics": "條碼統計",
    "Batch_Import": "批量導入",
    "Batch_renewal": "批量續費",
    "Batch_reset": "批量重置",
    "Bulk_sales": "批量銷售",
    "Call_the_police": "報警",
    "Customer_details": "客戶詳情",
    "Customer_transfer": "客戶轉移",
    "Delete_POI": "刪除POI",
    "Delete_account": "刪除帳號",
    "Delete_customer": "刪除客戶",
    "Delete_device": "刪除設備",
    "Delete_device_group": "刪除設備分組",
    "Delete_fence": "刪除圍欄",
    "Delete_role": "刪除角色",
    "Device_List": "設備列表",
    "Device_grouping": "設備分組",
    "Device_transfer": "設備轉移",
    "Due_reminder": "到期提醒",
    "Edit_details": "修改詳情",
    "Equipment_management": "設備管理",
    "My_clinet": "設備管理",
    "Fence": "圍欄",
    "Fence_management": "圍欄管理",
    "Generate": "生成",
    "Generate_lead-in_points": "生成導入點",
    "Generate_renewal_points": "生成續費點",
    "Have_read": "清除",
    "Idle_speed_statistics": "怠速統計",
    "Import": "導入",
    "Import_Device": "導入設備",
    "Industry_Statistics": "行業統計",
    "Location_monitoring": "定位監控",
    "Log_management": "日誌管理",
    "Mark_read": "標記已讀",
    "Menu_management": "菜單管理",
    "Message_Center": "消息中心",
    "Mileage_statistics": "里程統計",
    "Modify_POI": "修改POI",
    "Modify_device_details": "修改設備詳情",
    "Modify_device_group": "修改設備分組",
    "Modify_role": "修改角色",
    "Modify_sharing_track": "修改分享軌跡",
    "Modify_user_expiration": "修改用戶到期",
    "More": "更多",
    "My_client": "我的客戶",
    "New_role": "新增角色",
    "New_users": "新增用戶",
    "Oil_statistics": "油量統計",
    "POI_management": "POI管理",
    "Points_record": "點數記錄",
    "Push": "推送",
    "Quick_sale": "快速銷售",
    "Renew": "續費",
    "Replay": "回放",
    "Role_management": "角色管理",
    "Run_overview": "運行總覽",
    "Running_statistics": "運行統計",
    "Sales_equipment": "銷售設備",
    "Set_expiration_reminder": "設置到期提醒",
    "Share_track": "分享軌跡",
    "Sharing_management": "分享管理",
    "Speeding_detailed_list": "超速詳單",
    "Statistical_report": "統計報表",
    "Stay_detailed_list": "停留詳單",
    "System_account_management": "權限帳號",
    "Temperature_statistics": "溫度統計",
    "Transfer": "轉移",
    "Transfer_group": "轉移分組",
    "Transfer_point": "轉移導入點",
    "Transfer_renewal_point": "轉移續費點",
    "Trip_statistics": "行程統計",
    "Unlink": "取消關聯",
    "View": "查看",
    "View_POI": "查看POI",
    "View_device_group": "查看設備分組",
    "View_fence": "查看圍欄",
    "View_role": "查看角色",
    "View_sharing_track": "查看分享軌跡",
    "Virtual_account": "虛擬帳號",
    "Voltage_analysis": "電壓分析",
    "Voltage_statistics": "電壓統計",
    "batch_deletion": "批量刪除",
    "change_Password": "修改密碼",
    "delete": "刪除",
    "edit": "編輯",
    "instruction": "指令",
    "modify": "修改",
    "monitor": "監控",
    "my_account": "我的帳戶",
    "reset_Password": "重置密碼",
    "share_it": "分享",
    "sub_user": "下級用戶",
    "track": "跟踪",
    "Custom_Order": "自定義指令",
    "GeoKey_Manager": "GeoKey管理",
    "GeoKey_Update": "修改",
    "GeoKey_Delete": "刪除",
    "GeoKey_Add": "添加",
    "GeoKey_View": "查看",
    "feedback_manager": "反饋管理",
    "feedback_list": "查看",
    "feedback_handle": "處理反饋",
    "proclamat_manager": "公告管理",
    "proclamat_manager_list": "查看公告",
    "proclamat_manager_update": "修改公告",
    "proclamat_manager_delete": "刪除公告",
    "proclamat_manager_save": "新增公告",
    "device_update_batch_model": "批量修改設備型號"
}
// 问题文档的内容
lg.questionDocumentArr=[
    ['問：接線設備安裝後指示燈不亮，處於離線狀態','答：請您熄火汽車後，用電筆和萬能表量測所接汽車線路電壓是否符合GPS追踪器電壓範圍一般是9-36V。<br/>接線注意事項：安裝接線人員需對汽車線路有瞭解，並有一定動手能力，以免接線不當給您的愛車造成損害。'],
    ['問：有線設備或者無線實时追跡設備，電話打通或者物聯網後臺開機狀態設備離線','答：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1.發短信重啓，觀察幾分鐘看是否上線。一般發RESET#具體請聯系經銷商確定。<br/>&nbsp；&nbsp；&nbsp；&nbsp；2.所在網絡連接不穩定，請移動車子到訊號良好區域。<br/>&nbsp；&nbsp；&nbsp；&nbsp；3.以上步驟操作後，還未能上線，需聯系移動運營商查看卡是否异常。'],
    ['問：月初和月底設備批量離線','答：請您査詢卡是否是欠費停機，如果是欠費請及時充值，恢復使用。'],
    ['問：車子行駛，GPS線上位置不更新','答：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1.接線設備可以發短信STATUS#查看衛星訊號接收狀態，看到GPS:searching satellite就是衛星訊號一直處於蒐索中，這種情況需要檢查安裝位置，是否有按照說明書要求去安裝的。正面朝上，上方不能有金屬遮擋。<br/>&nbsp；&nbsp；&nbsp；&nbsp；2.發短信STATUS#，返回狀態是GPS:OFF，請再發FACTORY#，收到回復OK後，觀察5分鐘看位置是否有更新<br/>&nbsp；&nbsp；&nbsp；&nbsp；3.按照以上2種方法還不能排除故障，請聯系賣家返修。'],
    ['問：為何充電充了很久電量平臺還是顯示不滿電呢？','答：平臺電量顯示是依據設備迴響回來的資訊做一個數據解析來判斷設備當前電量的，在某些特殊情况下會出現電量顯示誤差解決方案：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1、設備電量數據與設備定位數據是一起上傳的，如果充電許久，電量未曾發生變化，請您：①帶上您的設備移動100-300米的位置，讓設備的位置資訊更新，使其電量數據與位置數據可以一起迴響到平臺從而是電量顯示重繪。<br/>&nbsp；&nbsp；&nbsp；&nbsp；2、根據電源指示燈變化判定是否滿電，（以S15為例）操作步驟如下：①充電8-10個小時，然後電源指示燈變黃綠色後，拔掉充電線後，在插入充電線，15分鐘內電源指示燈再變為變黃綠色即為滿電；其它型號請查看說明書。<br/>&nbsp；&nbsp；&nbsp；&nbsp；3、充很久也充不滿電，這種情況有可能是充電插頭電壓低於1A，請您用電壓5V、1A的充電頭充8-10小時即可。'],
    ['問：支持GPS斷油電指令下發成功了，為什麼車都油電還是沒有斷呢？','答：斷油電指令下發成功後，必須在以下條件滿足設備才會執行斷油電：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1、確保設備的接線正確，按照說明書接線圖接線。<br/>&nbsp；&nbsp；&nbsp；&nbsp；2、設備正常工作，處於靜止或行駛狀態，有定位，未離線，且車輛時速不超過20公里；<br/>如果車輛處於離線、未定位或車輛時速超過20公里以上，即使斷油電指令下發成功，終端也是不會執行的。'],
    ['問：三年無線產品第一次安裝好，顯示裝置未定位或者線上','答：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1、打開開關觀察指示燈是否閃爍，例如S18黃色與綠色指示燈同時快閃為正常，慢閃在蒐索訊號中，不亮設備故障。（不同型號指示燈狀態會不一樣，其它型號請看說明書）<br/>&nbsp；&nbsp；&nbsp；&nbsp；2、指示燈閃爍不上線，如果在訊號較差位置開機請拿到訊號良好區域開機。訊號良好區域也不上線，可關機1分鐘，重新裝卡再開機測試。'],
    ['問：有線產品第一次安裝好，顯示裝置未定位','答：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1、觀察終端GPS狀態指示燈是否正常，按照不同型號的說明書查看指示燈狀態。<br/>&nbsp；&nbsp；&nbsp；&nbsp；2、指示燈不亮設備沒能正常通電，換地方取電測試。<br/>&nbsp；&nbsp；&nbsp；&nbsp；3、（綠黃色）卡指示燈不亮，斷電重新安裝卡，再通電看到常亮為正常。<br/>&nbsp；&nbsp；&nbsp；&nbsp；4、確定設備裏的SIM卡號沒有欠費停機情况，GPRS上網功能是否正常。<br/>&nbsp；&nbsp；&nbsp；&nbsp；5、設備所在地方無GSM網絡，例如底下室，隧道等，訊號弱的地區，請開車到GPRS覆蓋好的地方測試。<br/>&nbsp；&nbsp；&nbsp；&nbsp；6、定位器的安裝位置不要過於封閉，不要有金屬物摭擋，在車內的安裝位置儘量靠上。不然影響訊號接收。<br/>&nbsp；&nbsp；&nbsp；&nbsp；7、正常開機，停在訊號良好區域不上線，可重新發上線指令查看IP介面以及卡連結網絡是否正常。'],
    ['問：接線設備安裝後指示燈不亮，處於離線狀態','解決方案：<br/>&nbsp；&nbsp；&nbsp；&nbsp；請您熄火汽車後，用電筆和萬能表量測所接汽車線路電壓是否符合GPS追踪器電壓範圍一般是9-36V。<br/>&nbsp；&nbsp；&nbsp；&nbsp；接線注意事項：<br/>&nbsp；&nbsp；&nbsp；&nbsp；安裝接線人員需對汽車線路有瞭解，並有一定動手能力，以免接線不當給您的愛車造成損害。'],
    ['問：有線設備或者無線實时追跡設備，電話打通或者物聯網後臺開機狀態設備離線','答：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1.發短信重啓，觀察幾分鐘看是否上線。一般發RESET#具體請聯系經銷商確定。<br/>&nbsp；&nbsp；&nbsp；&nbsp；2.所在網絡連接不穩定，請移動車子到訊號良好區域。<br/>&nbsp；&nbsp；&nbsp；&nbsp；3.以上步驟操作後，還未能上線，需聯系移動運營商查看卡是否异常。'],
    ['問：月初和月底設備批量離線','解決方案：請您査詢卡是否是欠費停機，如果是欠費請及時充值，恢復使用。'],
    ['問：車子行駛，GPS線上位置不更新','解決方案：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1.接線設備可以發短信STATUS#查看衛星訊號接收狀態，看到GPS:searching satellite就是衛星訊號一直處於蒐索中，這種情况需要檢查安裝位置，是否有按照說明書要求去安裝的。正面朝上，上方不能有金屬遮擋。<br/>&nbsp；&nbsp；&nbsp；&nbsp；2.發短信STATUS#，返回狀態是GPS:OFF，請再發FACTORY#，收到回復OK後，觀察5分鐘看位置是否有更新。<br/>&nbsp；&nbsp；&nbsp；&nbsp；3.按照以上2種方法還不能排除故障，請聯系賣家返修。'],
    ['問：為何充電充了很久電量平臺還是顯示不滿電呢？','平臺電量顯示是依據設備迴響回來的資訊做一個數據解析來判斷設備當前電量的，在某些特殊情况下會出現電量顯示誤差<br/>解決方案：<br/>&nbsp；&nbsp；&nbsp；&nbsp；1、設備電量數據與設備定比特數據是一起上傳的，如果充電許久，電量未曾發生變化，請您：①帶上您的設備移動100-300米的位置，讓設備的位置資訊更新，使其電量數據與位置數據可以一起迴響到平臺從而是電量顯示重繪；<br/>&nbsp；&nbsp；&nbsp；&nbsp；2、根據電源指示燈變化判定是否滿電，（以S15為例）操作步驟如下：①充電8-10個小時，然後電源指示燈變黃綠色後，拔掉充電線後，在插入充電線，15分鐘內電源指示燈再變為變黃綠色即為滿電；其它型號請查看說明書。<br/>&nbsp；&nbsp；&nbsp；&nbsp；3、充很久也充不滿電，這種情況有可能是充電插頭電壓低於1A，請您用電壓5V、1A的充電頭充8-10小時即可。']
]
lg.webOptDoc='敬請期待...';
lg.appOptDoc='敬請期待...';
// 查詢參數的幫助
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push('<tr>');
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push('<td>查詢終端密碼</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">PHONE:</td>');
html.push('<td>查詢終端內置SIM卡號碼</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">USER:</td>');
html.push('<td>查詢車主手機號碼</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SPEED:</td>');
html.push('<td>查詢超速報警限速值</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">FREQ:</td>');
html.push('<td>查詢開啟追蹤後的上報頻率,單位是秒</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">TRACE:</td>');
html.push('<td>查詢是否開啟追蹤,1是開啟追蹤,0是關閉追蹤</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push('<td>查詢非法移位報警判定範圍,單位是米</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIB:</td>');
html.push('<td>查詢是否開啟振動短信報警,1為開啟0為關閉');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBL:</td>');
html.push('<td>查詢振動靈敏度0~15,0為最高靈敏度,太高可能會誤報,15為最低靈敏度</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push('<td>查詢是否開啟振動電話報警,1為開啟0為關閉');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push('<td>查詢是否開啟GPS過濾漂移功能,1為開啟0為關閉,如果開啟則防盜器在5分鐘內沒有發生振動,則進入靜止狀態,過濾所有GPS漂移點</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push('<td>查詢是否開啟休眠功能,1為開啟0為關閉,如果開啟則防盜器在30分鐘內沒有發生振動,則進入休眠狀態,關閉GPS斷鏈,從而節省電量</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">POF:</td>');
html.push('<td>查詢是否開啟斷電報警功能,1為開啟0為關閉</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">GPS:</td>');
html.push('<td>查詢GPS接收的衛星編號和強度,例如：2300 1223 3431 。。。 壹共12組四位數,2300表示接收到編號23衛星信號強度為00,1223表示接收到編號為12的衛星信號強度為23</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VBAT:</td>');
html.push('<td>查詢電池電壓，充電接口電壓，充電電流大小,例如：VBAT=3713300：4960750：303500 表示電池電壓為3713300uV，即3.71v,充電電壓為4.96V，充電電流303mA</td>');
html.push('</tr>');
html.push('</table>');
lg.queryparamhelp = html.join("");

// 設置參數的幫助
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push('<tr>');
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push('<td>設置終端密碼,密碼只能為6位數字</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">PHONE:</td>');
html.push('<td>設置終端內SIM卡號碼</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">USER:</td>');
html.push('<td>設置車主手機號碼</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SPEED:</td>');
html.push('<td>設置超速報警限速值,範圍應當在0~300之間</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">FREQ:</td>');
html.push('<td>設置開啟追蹤後的上報頻率,單位是秒</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">TRACE:</td>');
html.push('<td>設置是否開啟追蹤,1是開啟追蹤,0是關閉追蹤</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push('<td>設置非法移位報警判定範圍,單位是米</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIB:</td>');
html.push('<td>設置是否開啟振動短信報警,1為開啟0為關閉');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBL:</td>');
html.push('<td>設置振動靈敏度0~15,0為最高靈敏度,太高可能會誤報,15為最低靈敏度</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push('<td>設置是否開啟振動電話報警,1為開啟0為關閉');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push('<td>設置是否開啟GPS過濾漂移功能,1為開啟0為關閉,如果開啟則防盜器在5分鐘內沒有發生振動,則進入靜止狀態,過濾所有GPS漂移點</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push('<td>設置是否開啟休眠功能,1為開啟0為關閉,如果開啟則防盜器在30分鐘內沒有發生振動,則進入休眠狀態,關閉GPS斷鏈,從而節省電量</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">POF:</td>');
html.push('<td>設置是否開啟斷電報警功能,1為開啟0為關閉</td>');
html.push('</tr>');
html.push('</table>');
lg.setparamhelp = html.join("");


//批量添加
html = [];
html.push('<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox"  ' +
    'style="z-index: 999;position:absolute;left:120px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加到:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,'+'bulkAdds_treeDiv'+','+'bulkAdds_seller'+')" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>平臺到期:</td>');
html.push('<td>');
html.push('<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>設備型號:</td>');
html.push('<td>'); 
html.push('<span class="select_box">'+
            '<span class="select_txt"></span>'+
            '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>'+
            '<div class="option" style="">'+
                '<div class="searchDeviceBox">'+
                    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">'+
                    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>'+
                '</div>'+
                '<div id="deviceList"></div>'+
            '</div>'+
            '</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加設備:</td>');
html.push('<td>');
html.push('<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>');
lg.bulkAdds = html.join('');



//批量續費
html = [];
html.push('<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> ');
html.push('</div>');
html.push('<form id="bs_form">');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加設備:</td>');
html.push('<td>');
html.push('<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('<span class="re_addNumBox">當前：<span id="account_re_addNum">0</span>');
html.push('</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<tr>');
html.push('<td style="text-align:right;"><span style="color:red">*</span>卡類型</td>');
html.push('<td>');
html.push('<input  type="radio" name="red_cardType"');
html.push('class="easyui-validatebox"  value="3" checked><label>一年</label></input>');
html.push('<input  type="radio" name="red_cardType" style="margin-left:15px;" ');
html.push('class="easyui-validatebox" value="4"><label>終身</label></input>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right">扣除點數</td>');
html.push('<td>');
html.push('<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">');
html.push('</td>');
html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>用戶到期:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>備註</td>');
html.push('<td>');
html.push('<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="re_renewMachines" title="'+lg.renew+'" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="re_reset" title="'+lg.reset+'"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('</form>');
lg.bulkRenew = html.join('');



//批量銷售，myAccount
html = [];
html.push('<div id="bulkSales_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:120px;top:88px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> ');
html.push('</div>');
html.push('<form id="bs_form">');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>銷售給:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,'+'bulkSales_treeDiv'+','+'bulkSales_seller'+')" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push('<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>');
html.push('</td>');
html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>用戶到期:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加設備:</td>');
html.push('<td>');
html.push('<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('<span class="bs_addNumBox">當前：<span id="account_bs_addNum">0</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="bs_sellMachines" title="'+lg.sell+'"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="bs_reset" class="swd-gray-btn" title="'+lg.reset+'"  style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('</form>');
lg.bulkSales = html.join('');


//批量轉移1，彈出框
html = [];
html.push('<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:127px;top:172px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>目標客戶:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加設備:</td>');
html.push('<td>');
html.push('<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >批量添加</a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push('</div>');
html.push('<a id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >轉移</a>');
html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)">取消</a>');

lg.bulkTransfer = html.join('');

//批量轉移2,myClient
html = [];
html.push('<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:117px;top:84px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>目標客戶:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>添加設備:</td>');
html.push('<td>');
html.push('<img  id="bt_addMachines" style="cursor:pointer" title="'+lg.addTo+'" src="../../images/main/myAccount/add3.png" />');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push('</div>');
html.push('<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');

lg.bulkTransfer2 = html.join('');


//批量轉移用戶
html = [];
html.push('<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:116px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>目標客戶:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>');
html.push('</td>');
html.push('<td></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');

lg.bulkTransferUser = html.join('');
window.lg = lg