var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
    site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
    site = 'Forcegps'
  }
var lg = {  //葡萄牙语
    //common common
    user_guide: 'Guia de usuario',
    remoteSwitch: "Remote Switch",
    pageTitle: 'WhatsGPS Global Tracking System|Vehicle GPS Tracker|3G Tracker|mini 4G Tracker|GPSNow|Car Locator',
    description: site+" is dedicated to providing users with intelligent cloud location services. It is the world's leading location service platform.",
    pageLang: 'Português',
    inputCountTips: 'Por favor entrar com a conta / IMEI',
    inputPasswordTips: 'Por favor insira sua senha',
    appDownload: 'baixar aplicativo',
    rememberPassword: 'Lembre me',
    forgetPassword: 'Esqueceu sua senha',
    siteName: 'WhatsGPS',
    noToken: " Por favor mandar símbolo",
    loginFirst: 'Entrar primeiro ',
    move: 'Mover',
    stop: 'Parado',
    query: 'Verificar',
    imeiQuery: 'IMEI',
    delete: 'Apagar',
    update: 'Atualizar',
    cancel: 'Cancelar',
    soft: 'Não.',
    more: 'Mais',
    edit: 'Editar',
    useful:'útil',
    useless:'inútil',
    replyFeedback:'Feedback sobre "$"',
    add: 'Adicionar',
    addTo: 'Adicionar',
    addDevice: 'Adicionar Dispositivo',
    machineName: 'Alvo',
    searchDevice: 'Dispositivo',
    date: 'Data hora',
    LatestUpdate: 'atualizar',
    engine: 'Ligado',
    locTime: 'Tempo GPS',
    locType: 'tipo de localização',
    startLoc: 'Começar localização',
    endLoc: 'Localização final',
    address: 'Endereço',
    noAddressTips: 'Não foi possível obter informações de endereço',
    lonlat: 'Latitude e Longitude',
    carNO: 'Número da placa',
    imei: 'IMEI',
    IMEI: "IMEI",
    simNO: 'Tarjeta SIM',
    activeTime: 'Ativar hora',
    expireTime: 'Tempo expirado',
    acceptSubordinateAlarm: 'Aceitar Alarme Subordinado',
    acceptAlarmTips1: 'Depois de verificar',
    acceptAlarmTips2: 'Receberá informações de alarme do dispositivo de todos os clientes subordinados',
    speed: 'velocidade',
    y: "Anos",
    M: "Meses",
    d: "Dias",
    h: "Horas",
    min: "Minutos",
    s: "Segundos",
    _year: 'y',
    _month: 'm',
    _day: 'd',
    _hour: 'h',
    _minute: 'm',
    _second: 's',
    confirm: 'Confirme',
    yes: "Sim",
    car: 'carro',
    not: "Não",
    m: 'Metros',
    account: 'Conta',
    psw: 'Senha',
    save: 'Salve',
    operator: 'Operar',
    queryNoData: 'Consulta sem data',
    name: 'Nome',
    type: 'Modelo',
    open: 'Ligar',
    close: 'Desligar',
    send: 'Enviar',
    alarm: 'Alarme',
    alarmSetting: 'Configuração de alarme',
    look: 'Visão',
    tailAfter: 'Rastreamento',
    history: 'Reprodução',
    dir: 'Pasta',
    locStatus: "Estado de localização",
    machineTypeText: 'Modelo',
    carUser: 'Usuário do carro',
    machine: 'Alvo',
    unknowMachineType: 'Máquina desconhecida',
    noCommandRecord: 'Comandos não servem para esse dispositivo',
    type1: 'Tipo',
    role: 'Função',
    roles: 'funções',
    timeType: 'Tipo de tempo',
    moveSpeed: 'Velocidade de movimento',
    signal: 'Sinal',
    loc: 'Posição',
    wiretype: 'Tipo',
    wire: 'com fio',
    wireless: 'sem fio',
    expire: 'Expirado',
    hour: 'Hora',
    hourTo: 'Para hora',
    remark: 'Observação',
    remarkInfo: 'Observação',
    noPriviledges: 'A conta não tem privilégio de operação',
    commandNoOpen: 'O comando do dispositivo atual ainda não está aberto para uso',
    choseDelelePhone: 'Por favor selecione o número para deletar primeiro',
    streetView: 'Vista da rua',
    wrongFormat: 'Erro no formato de entrada',
    inputFiexd: 'Número fixo de entrada',
    serialNumberStart: 'O primeiro número de IMEI consecutivos',
    serialNumberEnd: 'O útimo número de IMEI consecutivo',
    clickSearchFirst: 'Por favor clique primeiro no dispositivo de pesquisa!',
    isDeleteDevice: 'o dispositivo não pde ser restaurado após ser excluido É excluído?',
    //平台错误代码提示
    errorTips: 'Falha na operação com código de erro：',
    error10003: 'Senha incorreta',
    error90010: 'O dispositivo está offline e o envio do comando personalizado falhou!',
    error70003: 'O valor do controle remoto não pode estar vazio',
    error70006: 'Não apóia ou não tem o direito de emitir a instrução',
    error20001: 'O ID do veículo não pode estar vazio',
    error20012: 'Veículo não ativado',
    error10012: 'Erro de senha antiga',
    error10017: 'Excluir falhou, apague o sub-usuário!',
    error10023: 'Excluir falhou, o usuário tem dispositivo',
    error20008: 'Adicionar falha, o IMEI já existe',
    error20006: 'Por favor insira um IMEI de comprimento 15',
    error10019: 'Formato incorreto do telefone de contato',
    error10024: 'Não repita vendas',
    error120003: 'Compartilhar links desativados',
    error10025: 'A informação modificada do dispositivo não pode estar vazia',
    error2010: 'Por favor carregue o arquivo',
    error20002: 'Nenhum número IMEI existe',
    error10081: 'Número insuficiente de cartões de renovação',
    error10082: 'Não há necessidade de recarregar para o dispositivo vitalício',
    error3000: 'A função foi atribuída à conta do sistema e não pode ser excluída',
    error103: 'A conta foi desativada, entre em contato com seu provedor de serviços',
    error124: 'Não pode operar sobre si mesmo',
    // 登陆相关 login.js
    logining: 'Conecte-se...',
    login: 'Conecte-se',
    userEmpty: 'Usuário vazio',
    pswEmpty: 'Senha vazia',
    prompt: 'Lembrete',
    accountOrPswError: 'Erro de conta ou senha',
    UserNameAlreadyExist: 'A conta de login já existia',
    noQualified: 'Não há informação qualificada',
    //main.js
    systemName: 'WhatsGPS',
    navTitle_user: ['Monitor', 'Relatório', 'Dispositivo'],
    navTitle_dealer: ['Minha conta', 'Meu cliente', 'Monitor', 'Mais operações'],
    exitStytem: 'Saída',
    user: 'Do utilizador',
    UserCenter: 'Do utilizador',
    alarmInfo: 'Alarme',
    confirmExit: 'Confirme a saída?',
    errorMsg: 'Mensagem de erro: ',
    logintimeout: 'Tempo limite de login!',
    clearAlarm: 'Claro',
    clear: 'Claro',
    searchbtn: 'Do utilizador',
    print: 'Impressão',
    export: 'Exportar',
    //feedback
    feedback: 'Comentários',
    feedback_sublime: 'Enviar',
    alerttitle: 'O título não pode ficar em branco!',
    alertcontent: 'O feedback não pode estar vazio!',
    submitfail: 'Submissão falhada！',
    saveSuccess: 'Salve o sucesso！',
    submitsuccess: 'Submetido com sucesso! Vamos processar o seu feedback o mais breve possível~',
    adviceTitle: 'Título',
    adviceTitle_p: 'Título de pergunta e opinião',
    adviceContent: 'Perguntas e opiniões',
    adviceContent_p: 'Descreva brevemente as perguntas e comentários que você deseja comentar e continuaremos a melhorar para você.',
    contact: 'Informações de contato',
    contact_p: 'Preencha seu telefone ou email',
    //monitor.js
    myMachine: 'Dispositivo',
    all: 'Todos',
    online: 'Conectados',
    offline: 'Desligado',
    unUse: 'Inativo',
    group: 'Grupo',
    moveGruop: 'Move to group',
    arrearage: 'Arrearage',
    noStatus: 'Nenhum status',
    inputMachineName: 'Alvo/IMEI',
    defaultGroup: 'Grupo padrão',
    offlineLessOneDay: 'Desligada<Um dia',
    demoUserForbid: 'Os usuários experientes não podem usar esse recurso',
    shareTrack: 'Compartilhar localizacão',
    shareName: 'Nome',
    liveShare: 'Compartilhamento de faixas em tempo real',
    expiration: 'Data de validade',
    getShareLink: 'Criar link',
    copy: 'Cópia de',
    copySuccess: 'Cópia bem sucedida!',
    enlarge: 'Prolongar',
    shareExpired: 'Link de compartilhamento expirado',
    LinkFailure: 'Falha na abertura do link',
    inputShareName: 'Insira um nome de compartilhamento',
    inputValid: 'Por favor insira o tempo de falha correto',
    //statistics.js
    runOverview: "Detalhes em movimento",
    runSta: 'Detalhes em movimento',
    mileageSta: 'Relatório de milhagem',
    tripSta: 'Relatório de viagem',
    overSpeedDetail: 'Lista de velocidade',
    stopDetail: 'Lista de estacionamento',
    alarmSta: 'Relatório de alarme',
    alarmOverview: 'Visão geral do alarme',
    alarmDetail: 'Detalhes do alarme',
    shortcutQuery: 'Verificação rápida',
    today: 'Hoje',
    yesterday: 'Ontem',
    lastWeek: 'Semana passada',
    thisWeek: 'Esta semana',
    thisMonth: 'Este mês',
    lastMonth: 'Mês passado',
    mileageNum: 'Quilometragem(Km)',
    overSpeedNum: 'Excesso de velocidade(km/h)',
    overSpeed: 'Excesso de velocidade',
    stopTimes: 'Fique(vezes)',
    searchMachine: 'Procurar',
    speedNum: 'Rapidez(km/h)',
    querying: 'Consultando',
    stopTime: 'Tempo estático',
    HisToryStopTime: 'Estacionamento',
    clickLookLoc: 'Clique para ver o endereço',
    lookLoc: 'Localização da consulta',
    noData: 'Sem dados',
    alarmTime: 'Hora do alarme',
    vibrationLevel: 'Nível de vibração',
    vibrationWay: 'Tipo de alarme',
    acc: 'ACC',
    accStatistics: 'ACC estatística',
    accType: ['Todos', 'Acc on', 'Acc off'],
    accstatus: ['Ligado', 'desligado'],
    openAccQuery: 'Consulta de ACC',
    runtime: 'Tempo de execução',
    //监控页面修改
    run: 'Viagem',
    speed: 'Velocidade',
    //设备管理
    machineManage: 'Dispositivo',
    deviceTable: 'Meus alvos',
    status: 'estado',
    havaExpired: 'expirado',
    expiredIn60: 'Expira em 60',
    expiredIn7: 'Expira em 7',
    normal: 'Normal',
    allMachine: 'Todos',
    allMachine1: 'Todos os alvos',
    expiredIn7Machine: 'Expira em 7 dias',
    expiredIn60Machine: 'Expira em 60 dias',
    havaExpiredMachine: 'Alvos expirados',

    //history.js
    replay: 'toque',
    replaytitle: '  Reprodução',
    choseDate: 'Selecione a data',
    from: 'de',
    to: 'Para',
    startTime: 'Hora de início',
    endTime: 'Fim de tempo',
    pause: 'Pausa',
    slow: 'Lento',
    mid: 'Médio',
    fast: 'Rápido',
    startTimeMsg: 'Começar tempo msg',
    endTimeMsg: 'Fim de tempo msg',
    smallEnd: "Hora de término menor que a hora de início, escolha novamente!",
    bigInterval: 'Intervalo de tempo inferior a 31 dias!',
    trackisempty: "Pista vazia",
    longitude: 'Longitude',
    latitude: 'Latitude',
    direction: 'Direção',
    stopMark: 'Marcador de permanência',
    setStopTimes: [
        {
            text: '1 minutos',
            value: '1'
        },
        {
            text: '2 minutos',
            value: '2'
        },
        {
            text: '3 minutos',
            value: '3'
        },
        {
            text: '5 minutos',
            value: '5'
        },
        {
            text: '10 minutos',
            value: '10'
        },
        {
            text: '15 minutos',
            value: '15'
        },
        {
            text: '20 minutos',
            value: '20'
        },
        {
            text: '30 minutos',
            value: '30'
        },
        {
            text: '45 minutos',
            value: '45'
        },
        {
            text: '1 horas',
            value: '60'
        },
        {
            text: '6 horas',
            value: '360'
        },
        {
            text: '12 horas',
            value: '720'
        },
    ],
    filterDrift: 'Remover localização de desvio',
    userType: ['Admin', 'Revendedor', 'Usuário final', 'Logística', 'Aluguel', 'Usuário de carro', 'Controle de risco'],
    userTypeArr: ["Admin", "Revendedor", "Usuário final", 'Logística', "Aluguel", 'Usuário de carro'],
    machineType: {
        '0':'Tipo de máquina',
        '1':'S15',
        '2':'S05',
        '93':'S05L',
        '94': 'S309',
        '95': 'S15L',
        '96':'S16L',
        '96':'S16LA',
        '97':'S16LB',
        '3':'S06',
        '4':'SW06',
        '5':'S001',
        '6':'S08',
        '7':'S09',
        '8':'GT06',
        '9':'S08V',
        '10':'S01',
        '11':'S01T',
        '12':'S116',
        '13':'S119',
        '14':'TR06',
        '15':'GT06N',
        '16':'S101',
        '17':'S101T',
        '18':'S06U',
        '19':'S112U',
        '20':'S112B',
        '21':'SA4',
        '22':'SA5',
        '23':'S208',
        '24':'S10',
        '25':'S101E',
        '26':'S709',
        '99':'S709L',
        '27':'S1028',
        '28':'S102T1',
        '29':'S288',
        '30':'S18',
        '31':'S03',
        '32':'S08S',
        '33':'S06E',
        '34':'S20',
        '35':'S100',
        '36':'S003',
        '37':'S003T',
        '38':'S701',
        '39':'S005',
        '40':'S11',
        '41':'T2A',
        '42':'S06L',
        '43':'S13',
        '86':'S13-B',
        '44':'GT800',
        '45':'S116M',
        '46':'S288G',
        '47':'S09L',
        '48':'S06A',
        '49':'S300',
        '50':'',
        '51':'GS03A',
        '52':'GS03B',
        '53':'GS05A',
        '54':'GS05B',
        '55':'S005T',
        '56':'AT6',
        '57':'GT02A',
        '58':'GT03C',
        '59':'S5E',
        '60':'S5L',
        '61':'S102L',
        '85':'S105L',
        '62':'TK103',
        '63':'TK303',
        '64':'ET300',
        '65':'S102A',
        '91':'S102A-D',
        '66':'S708',
        '67':'MT05A',
        '68':'S709N',
        '69':'',
        '70':'GS03C',
        '71':'GS03D',
        '72':'GS05C',
        '73':'GS05D',
        '74':'S116L',
        '75':'S102',
        '76':'S102T',
        '77':'S718',
        '78':'S19',
        '79':'S101A',
        '80':'VT03D',
        '81':'S5L-C',
        '82':'S710',
        '83':'S03A',
        '84':'C26',
        '87':'S102M',
        '88':'S101-B',
        '92':'LK720',
        '89':'S116-B',
        '90':'X3'
      },
    alarmType: ['Tipo de alarme', 'Alarme de vibração', 'Alarme de desligamento', 'Alarme de bateria fraca', 'Alarme SOS', 'Alarme de excesso de velocidade', 'Geofence out alarme', 'Alarme de deslocamento', 'Alarme de bateria baixa externa',
        'Alarme fora da área', 'Desmonte o alarme', 'Alarme de detecção de luz', 'Alarme magnético', 'Desmontar alarme', 'Alarme bluetooth', 'Alarme de blindagem de sinal', 'Alarme de estação base falsa', 'Cerca Geo em alarme', 'Cerca Geo em alarme',
        'Geo cerca de alarme', 'Alarme de porta aberta', 'Condução de fadiga', 'Entrada de hipoteca', 'Sair do ponto de hipoteca', 'Estadia de hipoteca', 'Terminal offline', 'Cerca Geo em alarme', 'Geo cerca de alarme', 'Cerca Geo em alarme', 'Geo cerca de alarme', 'Alarme de Combustível', 'ACC ON Alarme', 'ACC OFF Alarme', 'Alarme de colisão'],
    alarmTypeNew:  {
        '40': 'Alarme de alta temperatura',
        '45': 'Alarme de baixa temperatura',
        '50': 'overvoltage Alarm',
        '55': 'undervoltage Alarm',
        '60': 'Alarme de estacionamento'
    },
    alarmNotificationType: [
        { type: 'Alarme de vibração', valor: 1 },
        { type: 'Alarme de desligamento', valor: 2 },
        { type: 'Alarme de bateria fraca', valor: 3 },
        { type: 'Alarme SOS', valor: 4 },
        { type: 'Alarme de excesso de velocidade', valor: 5 },
        // {type:'Geofence out alarme',valor:6},
        { type: 'Alarme de deslocamento', valor: 7 },
        { type: 'Alarme de bateria baixa externa', value: 8 },
        { type: 'Alarme fora da área', valor: 9 },
        { type: 'Desmonte o alarme', valor: 10 },
        { type: 'Alarme de detecção de luz', valor: 11 },
        { type: 'Desmontar alarme', valor: 13 },
        { type: 'Alarme de blindagem de sinal', valor: 15 },
        { type: 'Alarme de estação base falsa', valor: 16 },
        // {type:'Cerca Geo em alarme',valor:17},
        // {type:'Cerca Geo em alarme',valor:18},
        // {type:'Geo cerca de alarme',valor:19},
        { type: 'Condução de fadiga', valor: 21 },
        { type: 'Entrada de hipoteca', valor: 22 },
        { type: 'Sair do ponto de hipoteca', valor: 23 },
        { type: 'Estadia de hipoteca', valor: 24 },
        { type: 'Terminal offline', valor: 25 },
        // {type:'Cerca Geo em alarme (controle de risco)',valor:26},
        // {type:'Geo cerca de alarme(Controle de risco)',valor:27},
        { type: 'Geo cerca de alarme', valor: 26 },
        { type: 'Geo cerca de alarme', valor: 27 },
        { type: 'Alarme de Combustível', value: 30 },
        { type: 'ACC ON Alarme', value: 31 },
        { type: 'ACC OFF Alarme', value: 32 },
        { type: 'Alarme de colisão', value: 33 }
    ],
    alarmTypeText: 'Tipo de alarme',
    alarmNotification: 'Notificação',
    pointType: ['Tipo de ponto', 'Localização de satélite', 'Localização da bússola', 'Localização LBS', 'Localização WIFI'],

    cardType: ['Tipo', 'Novo cartão', 'Novo cartão Vitalício', 'Novo cartão', 'Cartão vitalício'],
    // 东南西北
    directarray: ["Leste", "Sul", "Oeste", "Norte"],
    // 方向字段
    directionarray: ["Norte devido", "Nordeste", "Leste devido", "Sudeste", "Devido ao sul", "Sudoeste", "Oeste devido", "Noroeste"],
    // 定位方式
    pointedarray: ["Indefinido", "GPS", "LAC", "Localização LAC", "Localização WIFI"],


    //map Relevant 
    ruler: 'Governante',
    distance: 'Tráfego',
    baidumap: "Baidu Map",
    map: 'Mapa',
    satellite: 'Satélite',
    ThreeDimensional: '3D',
    baidusatellite: "Satélite Baidu",
    googlemap: "Google Map",
    googlesatellite: "Satélite do Google",
    fullscreen: "Tela cheia",
    noBaidumapStreetView: 'Localização atual em mapas do Baidu sem o Street View',
    noGooglemapStreetView: 'Localização atual no Google Maps sem o Street View',
    exitStreetView: 'Sair da vista da rua',
    draw: "Desenhar",
    finish: "Terminar",
    unknown: 'Desconhecido',
    realTimeTailAfter: 'Rastreamento em tempo real',
    trackReply: 'Reprodução de História',
    afterRefresh: "Atualizar",
    rightClickEnd: "Clique direito Fim,raio：",
    rightClickEndGoogle: "Clique direito Fim------------------------raio：",


    //tree Relevant 
    currentUserMachineCount: 'Contagem atual de máquinas do usuário',
    childUserMachineCount: 'Contagem de máquinas do usuário filho',

    //Window relevant

    electronicFence: 'Cerca Geo',
    drawTrack: 'Definir Geofence',
    showOrHide: 'Aparecer/esconder',
    showDeviceName: 'Nome do alvo',
    circleCustom: 'Usuário definido',
    circle200m: 'Raio de 200 metros',
    polygonCustom: 'Definição de polígono',
    drawPolygon: 'Desenhe um polígono',
    drawCircle: 'Desenhar um círculo',
    radiusMin100: 'O raio da cerca desenhada é de pelo menos 20 metros. Por favor, redesenhe-a. Raio de cerca atual:',
    showAllFences: 'Mostrar todas as cercas',
    lookEF: 'Verifique a cerca',
    noEF: 'Nenhum dado de geofence detectado ',
    hideEF: 'Ocultar geofence',
    blockUpEF: 'Cerca perto',
    deleteEF: 'Excluir cerca',
    isStartUsing: 'Seja para ativar',
    startUsing: 'Habilitar',
    stopUsing: 'Desabilitar',
    nowEFrange: 'Faixa de cerca atual',
    enableSucess: 'Ativar o sucesso',
    unableSucess: 'Desativar o sucesso',
    sureDeleteMorgage: 'Excluir ponto de hipoteca',
    enterMorgageName: 'Insira o nome do ponto de hipoteca',
    openMorgagelongStayAlarm: 'Iniciar estadia do ponto de hipoteca',
    openMorgageinOutAlarm: 'Iniciar entrada e sair do ponto de hipoteca',
    setEFSuccess: 'Definir geofence com sucesso',
    setElectronicFence: 'Definir cerca eletrônica',
    drawFence: 'Puxar cerca',
    drawMorgagePoint: 'Desenhe o ponto de hipoteca',
    customFence: 'Personalização de vedação',
    enterFenceTips: 'Entrando no alarme',
    leaveFenceTips: 'Deixe o alarme',
    inputFenceName: 'Pls nome da cerca de entrada',
    relation: 'Agrupar',
    relationDevice: 'Associar dispositivo',
    unRelation: 'Não associado',
    hadRelation: 'Associado já',
    quickRelation: 'Pacote de um clicle',
    cancelRelation: 'Cancelar pacote',
    relationSuccess: 'Pacote bem sucedido',
    cancelRelationSuccess: 'Cancelar sucesso',
    relationFail: 'Cancelar falhou',
    deviceList: 'Lista de dispositivos',
    isDeleteFence: 'Se excluir a cerca',
    choseRelationDeviceFirst: 'Pls primeiro selecione o dispositivo a ser associado!',
    choseCancelRelationDeviceFirst: 'Pls primeiro selecione o dispositivo a ser desassociado!',
    selectOneTips: 'Por favor, selecione pelo menos um método de alarme',
    radius: 'Raio',
    //设置二押点页面
    setMortgagePoint: 'Definir ponto de hipoteca',

    circleMortage: 'Ponto de hipoteca do círculo',
    polygonMorgage: 'Ponto de hipoteca do polígono',
    morgageSet: 'ponto de hipoteca já definido',
    operatePrompt: 'Operar pronto',
    startDrawing: 'Clique para começar a desenhar',
    drawingtip1: 'Clique com o botão esquerdo do mouse para começar a desenhar, clique duas vezes para finalizar o desenho',
    drawingtip2: 'Clique com o botão esquerdo do mouse e arraste para começar a desenhar',

    /************************************************/
    endTrace: 'Fim vestígio',
    travelMileage: 'Milhagem de viagem',
    /************************************************/
    myAccount: 'Conta',
    serviceProvide: 'Provedor de serviço',
    completeInfo: 'Informações completas, como contatos, número de telefone',
    clientName: 'Nome do cliente',
    loginAccount: 'Conta',
    linkMan: 'Contato',
    linkPhone: 'Tel/Mob',
    clientNameEmpty: "Nome do Cliente Vazio!",
    updateSuccess: 'Atualização feita com sucesso!',
    /************************************************/
    oldPsw: 'Senha antiga',
    newPsw: 'Nova senha',
    confirmPsw: 'Confirme a senha',
    pswNoSame: 'Senha não é a mesma',
    pswUpdateSuccess: "Sucesso de atualização de senha!",
    email: 'O email',
    oldPwdWarn: 'Por favor insira uma senha antiga',
    newPwdWarn: 'Por favor insira uma nova senha',
    pwdConfirmWarn: 'Por favor, confirme a nova senha',
    /************************************************/
    //Custom popup components
    resetPswFailure: 'Falha na senha de descanso',
    notification: 'Notificação',
    isResetPsw_a: 'Tem certeza de redefinir o ',
    isResetPsw_b: '`Redefinir senha?',
    pwsResetSuccess_a: 'já redefinir o ',
    pwsResetSuccess_b: '`s senha para 123456',
    /************************************************/
    machineSearch: 'Informação do dispositivo',
    search: 'Procurar',
    clientRelation: 'Relação com o cliente',
    machineDetail: 'Detalhes do dispositivo',
    machineDetail2: 'Detalhes do dispositivo',
    machineCtrl: 'Comando',
    transfer: 'Transferir',
    belongCustom: 'Pertencer ao cliente',
    addImeiFirst: 'Adicione o IMEI primeiro!',
    addUserFirst: 'Adicionar o usuário primeiro!',
    transferSuccess: 'Mova o sucesso!',
    multiAdd: 'Lote adicionar',
    multiImport: 'Importar',
    multiRenew: 'Lote renovar',
    //批量修改设备begin
    editDevice:'Modificar o modelo do dispositivo',
    deviceAfter: 'Modelo do dispositivo (após modificação)',
    editDeviceTips:'Confirme se o dispositivo a ser modificado é do mesmo modelo e não está ativo!',
    pleaseChoseDevice: 'Selecione o dispositivo a ser modificado primeiro!',
    editResult:'Editar resultado',
    successCount:'Dispositivo modificado com sucesso:',
    failCount:'Dispositivos com falha:',
    //批量修改设备end
    multiDelete: 'Apagar lote',
    canNotAddImei: 'IMEI não existe, não pode adicionar IMEI',
    importTime: 'Data de fábrica',
    loginName: 'Conta',
    platformDue: 'Plataforma vencida',
    machinePhone: 'Cartão SIM',
    userDue: 'Usuário expirou',
    overSpeedAlarm: 'Alarme de excesso de velocidade',
    changeIcon: 'Ícone',
    dealerNote: 'Observação',
    noUserDue: 'Nenhum usuário devido',
    phoneLengththan3: 'O comprimento do telefone deve ser maior que 3',
    serialNumberInput: 'Entrada do número de série',

    /************************************************/
    sending: 'Envio.....Por favor, espere...',
    sendFailure: 'Enviar falha',
    ctrlName: 'Nome',
    interval: 'Intervalo de tempo',
    intervalError: 'Erro de formato de intervalo',
    currectInterval: "Por favor, insira o intervalo de tempo correto!",
    intervalLimit: 'Faixa de intervalo de tempo 10-720,(Minuto)',
    intervalLimit2: 'Faixa de intervalo de tempo 10-5400,(Segundo)',
    intervalLimit3: 'Faixa de intervalo de tempo 5-1440,(Minuto)',
    intervalLimit4: 'Faixa de intervalo de tempo 3-999,(Segundo)',
    intervalLimit5: 'Faixa de intervalo de tempo 10-10800,(Segundo)',
    intervalLimit1: 'Faixa de intervalo de tempo 1-999,(Minuto). 000，significa fechar modo de recomeço de tempo',
    intervalLimit6: 'Time interval range 1-65535,(Second)',
    intervalLimit7: 'Time interval range 1-999999,(Second)',
    intervalLimit8: 'Time interval range 0-255, 0 means to close',
    intervalLimit9: 'Time interval range 3-10800,(Second)',
    intervalLimit10: 'Faixa de intervalo de tempo 3-86400,(Segundo)',
    intervalLimit11: 'Faixa de intervalo de tempo 180-86400,(Segundo)',
    intervalLimit22: 'Faixa de intervalo de tempo 60-86400,(Segundo)',
    intervalLimit23: 'Faixa de intervalo de tempo 5-60,(Segundo)',
    intervalLimit24: 'Faixa de intervalo de tempo 10-86400,(Segundo)',
    intervalLimit25: 'Faixa de intervalo de tempo 5-43200,(Minuto)',
    intervalLimit12: 'Time interval range 10-60,(Second)',
    intervalLimit13: 'Defina o intervalo de tempo de 1-24 (significa 1-24 horas) ou 101-107 (significa 1-7 dias)',
    intervalLimit14: 'Definir intervalo de tempo de intervalo 10-3600, unidade (segundo); padrão: 10s',
    intervalLimit15: 'Definir intervalo de tempo de intervalo 180-86400, unidade (segundo); padrão: 3600s',
    intervalLimit16: 'Definir intervalo de tempo de intervalo de 1 a 72, unidade (hora); padrão: 24 horas',
    intervalLimit17:'Configuração da faixa de temperatura -127-127, unidade (° C)',
    intervalLimit18: 'Definir intervalo de tempo de intervalo 5-18000, unidade (segundo)',
    intervalLimit19: 'Definir intervalo de tempo de intervalo 10-300, unidade (segundo)',
    intervalLimit20: 'Definir intervalo de tempo de intervalo 5-399, unidade (segundos)',
    intervalLimit21: 'Definir intervalo de tempo de intervalo 5-300, unidade (segundos)',
    noInterval: 'Por favor entrada tempo de intervalo!',
    intervalTips: 'Desligue o modo de rastreamento, definindo o tempo de despertar do alarme',
    phoneMonitorTips: 'After the command is sent, the device will actively dial the callback number to implement monitoring.',
    time1: 'Tempo1',
    time2: 'Tempo2',
    time3: 'Tempo3',
    time4: 'Tempo4',
    time5: 'Tempo5',
    intervalNum: 'Intervalo(minutos)',
    sun: 'Dom.',
    mon: 'Seg.',
    tue: 'Ter.',
    wed: 'Qua.',
    thu: 'Qui.',
    fri: 'Sex.',
    sat: 'Sab.',
    awakenTime: 'Hora do despertar',
    centerPhone: 'Telefone central',
    inputCenterPhone: 'Telefone do centro de entrada!',
    phone1: 'Número 一',
    phone2: 'Número 二',
    phone3: 'Número 三',
    phone4: 'No.4',
    phone5: 'No.5',
    inputPhone: 'Telefone de entrada',
    offlineCtrl: 'Offline ctrl，As instruções off-line serão enviadas automaticamente para o dispositivo depois que o dispositivo estiver on-line',
    terNotSupport: 'Terminal não suporta',
    terReplyFail: 'Resposta do terminal falhou',
    machineInfo: 'Informação do dispositivo',

    /************************************************/
    alarmTypeScreen: 'Tela do tipo de alarme',
    allRead: 'Tudo lido',
    read: 'Ler',
    noAlarmInfo: 'Nenhuma informação de alarme removível',
    alarmTip: 'Gorjeta : Filtrar este tipo de informação de alrme por não verificado',

    /************************************************/
    updatePsw: 'Senha',
    resetPsw: 'Redefinir senha',

    /************************************************/
    multiSell: 'Vendas a granel',
    sell: 'Vendas',
    sellSuccess: 'Vender sucesso!',
    modifySuccess: 'Modifique o sucesso',
    modifyFail: 'Modificar falha',
    multiTransfer: 'Lote em lote',
    multiUserExpires: 'A modificação em lote dos usuários expira',
    batchModifying: 'Modificação em lote',
    userTransfer: 'Mover usuário',
    machineRemark: 'Observação',
    sendCtrl: 'Comando Enviar',
    ctrl: "Comando",
    ctrlLog: 'Ctrl registro',
    ctrlLogTips: 'Histórico de comando',
    s06Ctrls: ['Parar motor', 'Mecanismo de restauração', 'Localização da consulta'],
    ctrlType: 'Nome do comando',
    resInfo: 'Resultado',
    resTime: 'Tempo de resposta',
    ctrlSendTime: 'Hora de envio',
    // csv文件导入上传
    choseCsv: 'Selecione o arquivo CSV',
    choseFile: 'Selecione o arquivo',
    submit: 'Enviar',
    targeDevice: 'O dispositivo alvo',
    csvTips_1: '1.Salve o arquivo excel como formato CSV',
    csvTips_2: '2.Importe o arquivo CSV para o sistema',
    importExplain: 'Instruções de importação:',
    fileDemo: 'Exemplo de formato de arquivo',

    // add
    sendType: 'Tipo',
    onlineCtrl: 'Comando online',
    offCtrl: 'Comando off-line',
    resStatus: ['Não enviado', 'inválido', 'foi emitido', 'Execute com sucesso', 'execução falhou', 'sem resposta'],
    /************************************************/
    addSubordinateClient: 'Adicionar subconta',
    noSubordinateClient: 'Nenhuma subconta',
    superiorCustomerEmpty: 'Escolha superior',
    noCustomerName: "Nome do cliente",
    noLoginAccount: 'Do utilizador',
    noPsw: 'Senha',
    noConfirmPsw: 'Confirme a senha',
    pswNotAtypism: 'Senha não atipismo!',
    addSuccess: 'Adicione o sucesso!',
    superiorCustomer: 'Superior',
    addVerticalImei: 'Adicionar IMEI vertical',
    noImei: 'Sem IMEI',
    addImei_curr: 'Insira o número IMEI',
    no: 'Unidade',
    aRowAImei: 'Um IMEI para uma linha',

    /*
    * dealer  Beginning of interface translation 
    *
    * */
    //main.js
    imeiOrUserEmpty: "IMEI ou usuário vazio!",
    accountEmpty: 'IMEI/nome/conta é obrigatório',
    queryNoUser: "Inquerir NoUser",
    queryNoIMEI: "Consulta sem IMEI",
    imeiOrClientOrAccount: 'IMEI/nome/conta',
    dueSoon: 'Para expirar',
    recentlyOffline: 'Desligada',
    choseSellDeviceFirst: 'Por favor, selecione o equipamento a ser vendido primeiro !',
    choseDeviceFirst: 'Por favor, selecione o dispositivo para mover primeiro!',
    choseDeviceExpiresFirst: 'Por favor, selecione o dispositivo a ser modificado primeiro!',
    choseRenewDeviceFirst: 'Por favor, selecione o dispositivo para renovar primeiro!',
    choseDeleteDeviceFirst: 'Por favor, selecione o dispositivo para excluir primeiro!',
    choseClientFirst: 'Por favor, selecione o cliente para mover primeiro!',

    //myClient.js
    clientList: 'Clientes',
    accountInfo: 'Informação da conta',
    machineCount: 'Quantidade do dispositivo',
    stock: 'Total',
    inventory: 'Estoque',
    subordinateClient: 'Sub conta',
    datum: 'Em formação',
    monitor: 'Monitor',
    dueMachineInfo: 'Expiração',
    haveExpired: 'Expirado',
    timeRange: ['Em 7 dias', 'dentro de 30 dias', 'dentro de 60 dias', 'dentro de 7-30dias', 'dentro 30-60dias'],
    offlineMachineInfo: 'informações de dispositivos desligadas',
    timeRange1: ['Dentro de uma hora', 'dentro de um dia', 'em 7 dias ', 'dentro de um mês', 'dentro de 60 dias', 'mais de 60 dias', 'entre 1 hora e 7 dias', 'entre 1 dia a 7 dias', 'entre 7 dias a 30 dias', 'entre 30 dias a 60 dias'],
    offlineTime: 'Alcance',
    includeSubordinateClient: 'Sub conta',

    stopMachineInfo: 'Informação estática',
    stopTime1: 'Tempo estático',
    unUseMachineInfo: 'Informação não ativa',
    unUseMachineCount: 'Quantidade não ativa',

    sellTime: 'Tempo de venda',
    detail: 'Detalhes',
    manageDevice: 'Gerenciar dispositivo',
    details: 'Detalhes',
    deleteSuccess: 'Excluir sucesso!',
    deleteFail: 'Apagar falha!',
    renewalLink: 'Link de Renovação',
    deleteGroupTips: 'Whether to delete a group',
    addGroup: 'Add group',
    jurisdictionRange: 'Modificar funções',
    machineSellTransfer: 'Venda de máquina',
    monitorMachineGroup: 'Monitor',
    jurisdictionArr: ['O cliente gerencia', 'Gerente de mensagem', 'Conjunto GEO', 'Gerenciamento de alarme', 'conta virtual gerenciar', 'Instrução de despacho'],
    confrimDelSim: 'Confirme a exclusão do cartão SIM:',

    // 右键菜单
    sellDevice: 'Vender dispositivo',
    addClient: 'Adicionar usuário',
    deleteClient: 'Deletar usuário',
    resetPassword: 'Redefinir senha',
    transferClient: 'Mover usuário',
    ifDeleteClient: 'Excluir',

    //myAccount
    myWorkPlace: 'Minha conta',
    availablePoints: 'Cartão disponível',
    yearCard: 'Cartão anual',
    lifetimeOfCard: 'Cartão vitalício',
    oneyear: 'Um ano',
    lifetime: 'Vitalício',
    commonImportPoint: 'Novo cartão',
    lifetimeImportPoint: 'Novo cartão Vitalício',
    myServiceProvide: 'Fornecedor',
    moreOperator: 'Mais operação',
    dueMachine: 'Dispositivo expirado',
    offlineMachine: 'Dispositivo desligado',
    quickSell: 'Venda rápida',
    sellTo: 'Alvo',
    machineBelong: 'Pertence a',
    reset: 'Restabelecer',
    targetCustomer: 'Usuário alvo',
    common_lifetimeImport: 'Cartão anual(0),cartão vitalício(0)',
    cardType1: 'Tipo',
    credit: 'Quantidade',
    generateImportPoint: 'Criar novo cartão',
    generateImportPointSuc: 'Cartão de importação bem sucedido!',
    generateImportPointFail: 'Falha no cartão de importação!',
    year_lifeTimeCard: 'Novo cartão(0),Novo cartão Vitalício(0)',
    generateRenewPoint: 'Criar cartão de renovação',
    transferTo: 'Mover',
    transferPoint: 'Quantidade',
    transferRenewPoint: 'Transferir cartão de renovação',
    pointHistoryRecord: 'Histórico do cartão',
    newGeneration: 'Novo',
    operatorType: 'Operação',
    consume: 'Consumir',
    give: 'Dar',
    income: 'Renda',
    pay: 'Despesas',
    imeiErr: 'Entrada de Pls últimos 6 números de IMEI!',
    accountFirstPage: 'Casa',


    /*
    * dealer  End of interface translation
    *
    * */
    // 1.4.8 risk control
    finrisk: 'Controle de risco financeiro',
    attention: 'Se inscrever',
    cancelattention: 'Excluir assinatura',
    poweroff: 'Desligar',
    inout: 'Dentro e fora',
    inoutEF: 'Dentro e fora da cerca',
    longstay: 'Estadia de hipoteca',
    secsetting: 'Configuração de hipoteca ',
    EFsetting: 'Configuração de cerca geográfica',
    polygonFence: 'Cerca polígono',
    cycleFence: 'Cerca do ciclo',
    haveBeenSetFence: 'Foram definidos vedação',
    haveBeenSetPoint: 'Ter sido definido ponto de hipoteca',
    drawingFailed: 'Falha no desenho. Por favor, redesenhe!',
    inoutdot: 'Dentro e fora',
    eleStatistics: 'Estatísticas de eletricidade',
    noData: 'Sem dados',
    // 进出二押点表格
    accountbe: 'Conta',
    SMtype: 'Tipo de ponto de hipoteca',
    SMname: 'Nome do ponto de hipoteca',
    time: 'Tempo',
    position: 'Posição',
    lastele: 'Bateria restante',
    statisticTime: 'Data das estatísticas',
    searchalarmType: ['Todos', 'desligada', 'desligar', 'dentro e fora da cerca', 'dentro e fora', 'estadia de hipoteca'],
    remarks: ['Ponto de hipoteca', 'Empresa de garantia', 'Desmontar ponto', 'Mercado de segunda mão'],
    focusOnly: 'Somente foco',

    autoRecord: 'Automatic recording',
    /******************************************************set command start**********************************8*/
    setCtrl: {
        text: 'Comando set ',
        value: ''
    },
    moreCtrl: {
       text: 'Mais instruções',
       value: ''
    },
    sc_openTraceModel: {
        text: 'Definir modelo de rastreamento',
        value: '0'

    },
    sc_closeTraceModel: {
        text: 'Fechar o modelo de rastreio',
        value: '1'
    },
    sc_setSleepTime: {
        text: 'Definir o tempo de sono',
        value: '2'
    },
    sc_setAwakenTime: {
        text: 'Defina o tempo de despertar',
        value: '3'
    },
    sc_setDismantleAlarm: {
        text: 'Definir desmontar alarme',
        value: '4'
    },
    sc_setSMSC: {
        text: 'Número do centro',
        value: '5'
    },
    sc_delSMSC: {
        text: 'Excluir SMSC',
        value: '6'
    },
    sc_setSOS: {
        text: 'Adicionar SOS',
        value: '7'
    },
    sc_delSOS: {
        text: 'Excluir SOS',
        value: '8'
    },
    sc_restartTheInstruction: {
        text: 'Restabelecer',
        value: '9'
    },
    sc_uploadTime: {
        text: 'Definir intervalo de envio',
        value: '10'
    },
    /*Alarm clock time setting
     Timing reback time setting
     Dismantle alarm setting
     Week mode open close*/
    sc_setAlarmClock: {
        text: 'Definir o despertador',
        value: '11'
    },
    sc_setTimingRebackTime: {
        text: 'Definir tempo de recomeço de tempo',
        value: '12'
    },
    sc_openWeekMode: {
        text: 'Modo semana aberta',
        value: '13'
    },
    sc_closeWeekMode: {
        text: 'Fechar o modo de semana',
        value: '14'
    },

    sc_powerSaverMode: {
        text: 'Intervalo de envio ',
        value: '15'
    },
    sc_carCatchingMode: {
        text: 'Modo de rastreamento',
        value: '16'
    },
    sc_closeDismantlingAlarm: {
        text: 'Fechar desmantelamento do alarme',
        value: '17'
    },
    sc_openDismantlingAlarm: {
        text: 'Alarme de desmontagem aberto',
        value: '18'
    },
    sc_VibrationAlarm: {
        text: 'Definir alarme de vibração',
        value: '19'
    },
    sc_timeZone: {
        text: 'Time zone setting',
        value: '20'
    },
    sc_phoneMonitor: {
        text: 'telephone listening',
        value: '21'
    },
    sc_stopCarSetting: {
        text: 'Parking settings',
        value: '22'
    },
    sc_bindAlarmNumber: {
        text: 'Binding the alarm number',
        value: '23'
    },
    sc_bindPowerAlarm: {
        text: 'Power failure alarm',
        value: '24'
    },
    sc_fatigueDrivingSetting: {
        text: 'Fatigue driving setup',
        value: '25'
    },
    sc_peripheralSetting: {
        text: 'peripheral settings',
        value: '26'
    },
    sc_SMSAlarmSetting: {
        text: 'Set SMS alert',
        value: '27'
    },
    sc_autoRecordSetting: {
        text: 'Automatic recording settings',
        value: '28'
    },
    sc_monitorCallback: {
        text: 'listen callback',
        value: '29'
    },
    sc_recordCtrl: {
        text: 'recording instructions',
        value: '30'
    },
    sc_unbindAlarmNumber: {
        text: 'Unbind alarm number',
        value: '31'
    },
    sc_alarmSensitivitySetting: {
        text: 'Vibration alarm sensitivity setting',
        value: '32'
    },
    sc_alarmSMSsettings: {
        text: 'Vibration alarm SMS settings',
        value: '33'
    },
    sc_alarmCallSettings: {
        text: 'Vibration alarm call settings',
        value: '34'
    },
    sc_openFailureAlarmSetting: {
        text: 'Turn on power failure alarm',
        value: '35'
    },
    sc_restoreFactory: {
        text: 'Restore Factory',
        value: '36'
    },
    sc_openVibrationAlarm: {
        text: 'Turn on the vibration alarm',
        value: '37'
    },
    sc_closeVibrationAlarm: {
        text: 'Turn off the vibration alarm',
        value: '38'
    },
    sc_closeFailureAlarmSetting: {
        text: ' Turn off the power failure alarm',
        value: '39'
    },
    sc_feulAlarm: {
        text: 'Configuração do Alarme de Combustível',
        value: '40'
    },
    //1.6.72
    sc_PowerSavingMode: {
        text: 'Modo de economia de energia',
        value: '41'
    },
    sc_sleepMode: {
        text: 'Modo dormir',
        value: '42'
    },
    sc_alarmMode: {
        text: 'Modo de alarme',
        value: '43'
    },
    sc_weekMode: {
        text: 'Modo de semana',
        value: '44'
    },
    sc_monitorNumberSetting: {
        text: 'Configuração do número do monitor',
        value: '45'
    },
    sc_singlePositionSetting: {
        text: 'Modo de posicionamento único',
        value: '46'
    },
    sc_timingworkSetting: {
        text: 'Modo de tempo de trabalho',
        value: '47'
    },
    sc_openLightAlarm: {
        text: 'Alarme do sensor de luz aberta',
        value: '48'
    },
    sc_closeLightAlarm: {
        text: 'Alarme do sensor de luz fechada',
        value: '49'
    },
    sc_workModeSetting: {
        text: 'Configuração do modo de trabalho',
        value: '50'
    },
    sc_timingOnAndOffMachine: {
        text: 'Configuração do interruptor de temporização',
        value: '51'
    },
    sc_setRealTimeTrackMode: {
        text: 'Definir modo de perseguição em tempo real',
        value: '52'
    },
    sc_setClockMode: {
        text: 'Definir modo de alarme',
        value: '53'
    },
    sc_openTemperatureAlarm:{
        text:'Ativar alarme de temperatura',
        value:'54'
    },
        sc_closeTemperatureAlarm:{
        text:'Desativar alarme de temperatura',
        value:'55'
    },
    sc_timingPostbackSetting: {
        text: 'Configurações de postagem de tempo',
        value: '56'
    },
    sc_remoteBoot: {
        text: 'Inicialização remota',
        value: '57'
    },
    sc_smartTrack: {
        text: 'Faixa inteligente',
        value: '58'
    },
    sc_cancelSmartTrack: {
        text: 'Cancelar faixa inteligente',
        value: '59'
    },
    sc_cancelAlarm: {
        text: 'Cancelar alarme',
        value: '60'
    },
    sc_smartPowerSavingMode: {
        text: 'Definir modo de economia de energia inteligente',
        value: '61'
    },
    sc_monitorSetting: {
        text: "Monitor",
        value: '62'
      },
    // 指令重构新增翻译
    sc_timedReturnMode: {
        text: 'Modo de retorno cronometrado',
        value: '100'
    },
    sc_operatingMode: {
        text: 'Modo operacional',
        value: '101'
    },
    sc_realTimeMode : {
        text: 'Modo de posicionamento em tempo real',
        value: '102'
    },
    sc_alarmMode : {
        text: 'Modo de alarme',
        value: '103'
    },
    sc_weekMode : {
        text: 'Modo semana',
        value: '104'
    },
    sc_antidemolitionAlarm : {
        text: 'Alarme anti-demolição',
        value: '105'
    },
    sc_vibrationAlarm : {
        text: 'Alarme de vibração',
        value: '106'
    },
    sc_monitoringNumber : {
        text: 'Gerenciamento do número de monitoramento',
        value: '107'
    },
    sc_queryMonitoring : {
        text: 'Número de monitoramento da consulta',
        value: '108'
    },
    sc_electricityControl : {
        text: 'Controle de óleo e eletricidade',
        value: '109'
    },
    sc_SOSnumber : {
        text: 'Gerenciamento de número SOS',
        value: '110'
    },
    sc_SleepCommand : {
        text: 'Comando dormir',
        value: '201'
    },
    sc_RadiusCommand : {
        text: 'Raio de deslocamento',
        value: '202'
    },
    sc_punchTimeMode:{
        text:'打卡模式',
        value:'203'  
    },
    sc_intervelMode:{
        text:'时间段模式',
        value:'204'  
    },
    sc_activeGPS:{
        text:'激活GPS',
        value:'205'  
    },
    sc_lowPowerAlert: {
        text: 'Lembrete de bateria fraca',
        value: '206'
    },
    sc_SOSAlert: {
        text: 'SOS报警',
        value: '207'
    },
    mc_cuscom : {
        text: 'Instrução personalizada',
        value: '1'
    },
    NormalTrack: 'Modo de rastreamento normal',
    listeningToNumber:'Tem certeza de que deseja verificar o número de monitoramento de?',
    versionNumber:'Tem certeza de que deseja verificar o número da versão de?',
    longitudeAndLatitudeInformation:'Tem certeza de que deseja verificar as informações de latitude e longitude de?',
    equipmentStatus:'Tem certeza de que deseja verificar o status de?',
    public_parameter:'Tem certeza de que deseja verificar os parâmetros de？',
    GPRS_parameter:'Tem certeza de que deseja verificar os parâmetros GPRS de？',
    deviceName: 'Tem certeza de que deseja rolar o dispositivo？',
    SMS_alert:'Tem certeza de que deseja verificar o alarme de lembrete de SMS de？',
    theBindingNumber:'Tem certeza de que deseja verificar o número obrigatório de？',
    intervalTimeRange:'A configuração do intervalo de tempo do intervalo é 001-999, unidade (minuto)',
    pleaseChoose:'Por favor, escolha',
    RealTimeCarChase: 'Tem certeza de que deseja definir este dispositivo para o modo de perseguição de carro em tempo real?',
    inputPhoneNumber: "Por favor, digite o número de telefone",
    inputCorPhoneNumber: "Por favor, digite o número de telefone correto",
    autoCallPhone: "Dica: depois que o comando for executado com sucesso, o terminal discará automaticamente o número definido",
    limitTheNumberOfCellPhoneNumbers1:'Este comando suporta até 5 números de telefone celular',
    limitTheNumberOfCellPhoneNumbers2:'Este comando suporta até 3 números de telefone celular',
    equipmentTorestart:'Tem certeza que deseja reiniciar este dispositivo?',
    remindTheWay:'Maneira de lembrar',
    alarmWakeUpTime:'Hora de despertar do alarme',
    alarmWakeUpTime1:'Hora de despertar do alarme 1',
    alarmWakeUpTime2:'Hora de despertar do alarme 2',
    alarmWakeUpTime3:'Hora de despertar do alarme 3',
    alarmWakeUpTime4:'Hora de despertar do alarme 4',
    sensitivityLevel:'Selecione o nível de sensibilidade',
    parking_time:'Tempo de estacionamento',
    selectWorkingMode:'Selecione o modo de trabalho',
    Alarm_value:'Valor do alarme',
    Buffer_value:'Valor do buffer',
    gqg_disconnect:'desconectar',
    gqg_turnOn:'Ligue',
    Return_interval:'Intervalo de retorno',
    gq_startTime:'Horário de início',
    gq_restingTime:'Tempo de descanso',
    gq_Eastern:'Fuso horário do leste',
    gq_Western:'Fuso horário ocidental',

    gq_driver:'Alarme de fadiga ao dirigir',
    gq_deviceName:'Tem certeza de que deseja rolar este dispositivo?',
    gq_noteAlarm:'Tem certeza de que deseja verificar o alarme de lembrete de SMS?',
    gq_restoreOriginal:'Tem certeza de que deseja restaurar este equipamento para a fábrica original?',
    gq_normalMode:'Modo normal',
    gq_IntelligentsleepMode:'Modo de espera inteligente',
    gq_DeepsleepMode:'Modo de sono profundo',
    gq_RemotebootMode:'Modo de inicialização remota',
    gq_IntelligentsleepModeTips:'Tem certeza de que deseja definir o modo de suspensão inteligente',
    gq_DeepsleepModeTips:'Tem certeza de que deseja definir o modo de suspensão profunda',
    gq_RemotebootModeTips:'Tem certeza que deseja definir para o modo de inicialização remota',
    gq_normalModeTips:'Tem certeza de que deseja definir para o modo normal',
    gq_sleepModeTips:'Tem certeza de que deseja colocar este dispositivo no modo de espera?',
    gq_Locatethereturnmode:'Modo de retorno de posicionamento',
    gq_regularWorkingHours:'Período de trabalho cronometrad',
    gq_AlarmType:{
        text: 'Tipo de alarme',
        value: '111'
    },
    IssuedbyThePrompt:'O comando foi emitido, aguarde a resposta do dispositivo',
    platformToinform:'Notificação de plataforma',
    gq_shortNote:'Notificação SMS', 
    /************指令白话文**********************/
    closeDismantlingAlarm: 'Fechar desmantelamento do alarme',
    openDismantlingAlarm: 'Alarme de desmontagem aberto',
    closeTimingRebackMode: 'Fechar o modo de recuo da temporização',
    minute: 'minutos',
    timingrebackModeSetting: 'Intervalo de envio:',
    setWakeupTime: 'Definir o tempo de ativação:',
    weekModeSetting: 'Configuração do modo de semana:',
    closeWeekMode: 'Fechar o modo de semana',
    setRealtimeTrackMode: 'Definir o modo de rastreamento em tempo real',
    fortification: 'Fortificação',
    disarming: 'Desarmamento',
    settimingrebackmodeinterval: 'Definir o intervalo do modo de rechamada de temporização:',
    oilCutCommand: 'Comando de corte de óleo',
    restoreOilCommand: 'Restaure o comando do óleo',
    turnNnTheVehiclesPower: 'Ligue o poder de veículos',
    turnOffTehVehiclesPower: 'Desligue o poder de veículos',
    implementBrakes: 'Implemente os freios',
    dissolveBrakes: 'Dissolver freios',
    openVoiceMonitorSlarm: 'Alarme de monitor de voz aberto',
    closeVoiceMonitorAlarm: 'Fechar alarme do monitor de voz',
    openCarSearchingMode: 'Modo de pesquisa de carro aberto',
    closeCarSearchingMode: 'Fechar o modo de pesquisa de carro',
    unrecognizedCommand: 'Comando não reconhecido',
    commandSendSuccess: 'Parabéns! O dispositivo executou o comando com sucesso!',

    /********************************************设置指令结束**************************************************/


    /********************************************查询指令开始**************************************************/
    queryCtrl: {
        text: 'Comando de consulta',
        value: ''

    },
    /*参数设置查询*/
    qc_softwareVersion: {

        text: 'Versão do programa de consulta',
        value: '1'
    },
    qc_latlngInfo: {
        text: 'Consulta latitude e longitude',
        value: '2'
    },
    qc_locationHref: {
        text: 'Configuração de parâmetros de consulta',
        value: '3'
    },
    qc_status: {
        text: 'Status da consulta',
        value: '4'
    },
    qc_gprs_param: {
        text: 'Parâmetro GPRS de consulta',
        value: '5'
    },
    qc_name_param: {
        text: 'Roll call',
        value: '6'
    },
    qc_SMSReminderAlarm_param: {
        text: 'Query SMS reminder alarm',
        value: '7'
    },
    qc_bindNumber_param: {
        text: 'Query binding number',
        value: '8'
    },

    /********************************************查询指令结束**************************************************/

    /*******************************************控制指令开始***************************************************/

    controlCtrl: {
        text: 'Comando de controle',
        value: ''
    },
    cc_offOilElectric: {
        text: 'Fora de óleo elétrico',
        value: '1'
    },
    cc_recoveryOilElectricity: {
        text: 'Eletricidade de óleo de recuperação',
        value: '2'
    },
    cc_factorySettings: {
        text: 'Configuração de fábrica',
        value: '4'
    },
    cc_fortify: {
        text: 'Fortalecer',
        value: '75'
    },
    cc_disarming: {
        text: 'Desarmamento',
        value: '76'
    },
    cc_brokenOil: {
        text: 'Fuel cut instruction',
        value: '7'
    },
    cc_RecoveryOil: {
        text: 'Recovery fuel circuit',
        value: '8'
    },

    /*******************************************控制指令结束***************************************************/


    /*
* m--》min
* 2018-01-23
* */
    km: 'KM', mileage: 'Quilometragem', importMachine: 'Adicionar Dispositivo', transferImportPoint: 'Transferir novo cartão',
    machineType1: 'Modelo', confirmIMEI: 'Por favor, certifique-se que a validade do IMEI não. e Nome do modelo antes de importar, pois a operação é irrevogável.',
    renew: 'Renovar', deductPointNum: 'Quantidade', renewSuccess: 'Renovar o sucesso!',
    wireType: [
        {
            text: 'Com fio',
            value: 'Falso'
        },
        {
            text: 'Sem fio',
            value: 'Verdade'
        }
    ],
    vibrationWays: [
        {
            text: 'Plataforma',
            value: 0
        },
        {
            text: 'Plataforma+Mensagem',
            value: 1
        },
        {
            text: 'Plataforma+Mensagem+Telefone',
            value: 2
        }
    ],
    addMachineType: [{
        text: 'S06',
        value: '3'
    },
    // SO6子级-----start-----------
    {
        text: 'GT06',
        value: '8'
    }, {
        text: 'S08V',
        value: '9'
    }, {
        text: 'S01',
        value: '10'
    }, {
        text: 'S01T',
        value: '11'
    }, {
        text: 'S116',
        value: '12'
    }, {
        text: 'S119',
        value: '13'
    }, {
        text: 'TR06',
        value: '14'
    }, {
        text: 'GT06N',
        value: '15'
    }, {
        text: 'S101',
        value: '16'
    }, {
        text: 'S101T',
        value: '17'
    }, {
        text: 'S06U',
        value: '18'
    }, {
        text: 'S112U',
        value: '19'
    }, {
        text: 'S112B',
        value: '20'
    }
        // SO6子级-----end-----------
        , {
        text: 'S15/S02F',
        value: '1'
    }, {
        text: 'S05',
        value: '2'
    }, {
        text: 'SW06',
        value: '4'
    }, {
        text: 'S001',
        value: '5'
    }, {
        text: 'S08',
        value: '6'
    }, {
        text: 'S09',
        value: '7'
    }],

    /*
    * 2018-02-02
    * */
    maploadfail: "Desculpe, carregar mapa atual falhou, você quer mudar para outro mapa?",

    /*
    2018-03-06新增 控制指令
    * */
    cc_openPower: {
        text: 'Poder aberto do veículo',
        value: '7'
    },
    cc_closePower: {
        text: 'Poder do veículo colse',
        value: '8'
    },
    cc_openBrake: {
        text: 'Freio aberto',
        value: '9'
    },
    cc_closeBrake: {
        text: 'Fechar o freio',
        value: '10'
    },
    cc_openAlmrmvoice: {
        text: 'Alarme aberto',
        value: '11'
    },
    cc_closeAlmrmvoice: {
        text: 'Alarme fechado',
        value: '12'
    },
    /*2018-03-06新增 控制指令
    * */
    cc_openFindCar: {
        text: 'Pesquisa aberta de veículos',
        value: '13'
    },
    cc_closeFindCar: {
        text: 'Fechar pesquisa de veículo',
        value: '14'
    },
    /*2018-03-19
    * */
    EF: 'GeoFence',

    /*
    2018-03-29，扩展字段
    * */
    exData: ['Poder', 'Voltagem',"Volume de óleo","temperatura", "resistência"],

    /*
    2018-04-10
    * */
    notSta: 'LBS não incluído nas estatísticas',

    // 油量统计
    fuelSetting: 'Fuel Setting',
    mianFuelTank: 'Main Fuel Tank',
    auxiliaryTank: 'Auxiliary tank',
    maximum: 'Max',
    minimum: 'Min',
    FullTankFuel: 'Full Tank Fuel',
    fuelMinValue: 'O volume de combustível não pode ser inferior a 10L',
    standardSetting: 'Standard Setting',
    emptyBoxMax: 'Max of Empty',
    fullBoxMax: 'Max of Full',
    fuelStatistics: 'Fuel Statistics',
    settingSuccess: 'Successed',
    settingFail: 'Failed',
    pleaseInput: 'Please input',
    fuelTimes: 'Fuel Times',
    fuelTotal: 'Fuel Total',
    refuelingTime: 'Tempo de reabastecimento',
    fuelDate: 'Fuel Date',
    fuel: 'Fuel',
    fuelChange: 'Fuel Change',
    feulTable: 'Fuel Analysis Table',
    addFullFilter: 'Please add the full filter',
    enterIntNum: 'Please enter a positive integer',
    // 温度统计
    tempSta: 'Temperature Statistics',
    tempTable: 'Temperature Analysis Table',
    industrySta: 'Industry Statistics',
    temperature: 'Temperature',
    temperature1: 'Temperature1',
    temperature2: 'Temperature2',
    temperature3: 'Temperature3',
    tempRange: 'Temperature Range',
    tempSetting:'Configuração de temperatura',
    tempSensor:'Sensor de temperatura',
    tempAlert:'Após o sensor de temperatura ser desligado, ele não receberá dados de temperatura!',
    phoneNumber: 'Mobile Number',
    sosAlarm: 'SOS Alarm',
    undervoltageAlarm: 'undervoltage Alarm',
    overvoltageAlarm: 'overvoltage Alarm',
    OilChangeAlarm: 'Oil change Alarm',
    accDetection: 'ACC detection',
    PositiveAndNegativeDetection: 'Positive and negative detection',
    alermValue: 'Alarm value',
    bufferValue: 'Buffer value',
    timeZoneDifference: 'Time zone difference',
    meridianEast: 'Meridian East',
    meridianWest: 'Meridian West',
    max12hour: "Can't be more than 12 hours",
    trackDownload: 'Track Download',
    download: 'Download',
    multiReset: 'Bulk Reset',
    resetSuccess: 'Reset Success',
    multiResetTips: 'Test data such as device activation time and track will be cleared after reset, device status is reset to not enabled online or offline.',
    point: 'Point',
    myplace: 'My Place',
    addPoint: 'Add a point',
    error10018: 'The number of import points is insufficient',
    error110:'Objeto não existe',
    error109:'Limite máximo excedido',
    error20013:'O tipo de dispositivo não existe',
    error90001:'O número de série do tipo de dispositivo não pode estar vazio',
    error20003:'Imei não pode estar vazio',
    inputName: 'Please enter a name',
    virtualAccount: 'Virtual Account',
    createTime: 'Create Time',
    permission: 'Permission',
    permissionRange: 'Permission Range',
    canChange: 'Modifiable Function',
    fotbidPassword: 'Comentários',
    virtualAccountTipsText: 'When creating a virtual account, it is the alias account of the currently logged-in dealer account. You can set permissions for the virtual account. To create a virtual account for an end user, you can first change the end user type to a reseller, then log in with that reseller, create a virtual account, and then change the type back to the end user.',
    noOperationPermission: 'Virtual account has no operation permission',
    number: 'Number',
    rangeSetting: 'Scope Setting',
    setting: 'Settings',
    // 1.6.1
    duration: 'Duration',
    voltageSta: 'Voltage Statistics',
    voltageAnalysis: 'Voltage Analysis',
    voltageEchart: 'Voltage analysis sheet',
    platformAlarm: 'Platform Alarm',
    platformAlarm1: 'Telephone',
    platformAndPhone: 'Telephone+Platform Alarm',
    smsAndplatformAlarm: 'SMS+Platform Alarm',
    smsAndplatformAlarm1: 'SMS',
    smsAndplatformAlarmandPhone: 'Platform Alarm+SMS+Telephone',
    smsAndplatformAlarmandPhone1: 'SMS+Telephone',
    more_speed: 'Km',
    attribute: 'Attribute',
    profession: 'Professional',
    locationPoint: 'Location Point',
    openPlatform: 'Open API',
    experience: 'Demo',
    onlyViewMonitor: 'Only view Moniter',
    inputAccountOrUserName: 'Please enter an account number or username',
    noDeviceTips: 'Did not find relevant equipment information, check customers',
    noUserTips: 'Did not find relevant user information, check equipment',
    clickHere: 'click here',
    pointIntervalSelect: 'Track point interval',
    payment: 'Payment',
    pleaceClick: 'Click',
    paymentSaveTips: 'Safety Tip: Please confirm with the service provider the validity of the link',
    fuelAlarmValue: 'Valor do alarme da quantidade de óleo',
    fuelConsumption: 'Consumo de óleo',
    client: 'Cliente',
    create: 'Estabelecer',
    importPoint: 'Tarjetas de importación',
    general: 'Comum',
    lifelong: 'Vitalício',
    renewalCard: 'Cartão de recarga',
    settingFuelFirst: 'Primeiro defina o valor do alarme de quantidade de óleo e envie o comando!',
    overSpeedSetting: 'Configuração de velocidade',
    kmPerHour: 'km/h',
    times: 'Vezes',
    total: 'Total',
    primary: 'Primário',
    minor: 'Adjunto',
    unActiveTips: 'O dispositivo não está ativado e não pode ser usado.',
    arrearsTips: 'O dispositivo está em atraso e não pode usar esse recurso',
    loading: 'Carregando',
    expirationReminder: 'Recordação Da Expiração',
    projectName: 'Nome do Projeto',
    expireDate: 'Data de validade',
    changePwdTips: 'A SUA senha é Muito simples, é um Risco de segurança, alteração de senha, por favor.',
    pwdCheckTips1: 'Sugestões são 6-20 letras, números ou símbolos',
    pwdCheckTips2: 'A senha digitada de intensidade Muito fraca',
    pwdCheckTips3: 'A senha também Pode ser um pouco Mais Complicado',
    pwdCheckTips4: 'A SUA senha é Segura',
    pwdLevel1: 'Fraco',
    pwdLevel2: 'Médio',
    pwdLevel3: 'Forte',
    comfirmChangePwd: 'Confirmar Mudar o Pwd',
    notSetYet: 'Ainda não definido',
    liter: 'Litro',
    arrearageDayTips: 'Dias devido',
    todayExpire: 'Devido hoje',
    forgotPwd: 'Esqueceu sua senha?',
    forgotPwdTips: 'Entre em contato com o vendedor para alterar sua senha',
    //1.6.7
    commonProblem: 'Senha',
    instructions: 'Instruções',
    webInstructions: 'Instrução na Web ',
    appInstructions: 'Instrução de APP',
    acceptAlarmNtification: 'Notificação de alarme',
    alarmPeriod: 'Período de Alarme',
    whiteDay: 'Dia',
    blackNight: 'Noite',
    allDay: 'Dia inteiro',
    alarmEmail: 'Correio de Alarme',
    muchEmailTips: "Várias caixas de correio podem ser inseridas, separadas pelo símbolo';'.",
    newsCenter: 'Centro de notificações',
    allNews: 'Todosl',
    unReadNews: 'não lida',
    readNews: 'Ler',
    allTypeNews: 'Tipos de notificação',
    alarmInformation: 'Informação de alarme',
    titleContent: 'Título',
    markRead: 'Marque lido',
    allRead: 'Todos lidos',
    allDelete: 'Excluir tudo',
    selectFirst: 'Selecione primeiro!',
    updateFail: 'Atualização falhou!',
    ifAllReadTips: 'Todos lidos?',
    ifAllDeleteTips: 'Excluir tudo?',
    stationInfo: 'Informação da estação',
    phone: ' Telefone',
    //1.6.72
    plsSelectTime: 'Por favor escolha o tempo!',
    customerNotFound: 'Não consegue encontrar o cliente',
    Postalcode: 'Posizione',
    accWarning: 'Allarme ACC',
    canInputMultiPhone: 'È possibile inserire più numeri di cellulare, si prega di utilizzare; separato',
    noLocationInfo: 'O dispositivo não TEM informações de localização ainda.',
    //1.6.9
    fenceName: 'nome da cerca',
    fenceManage: 'Gerenciamento de cercas geográficas',
    circular: 'círculo',
    polygon: 'polígono',
    allFence: 'Todas as cercas',
    shape: 'forma',
    stationNews: 'Mensagem do Site',
    phonePlaceholder: 'Você pode inserir vários números, separados por ","',
    addressPlaceholder: 'Digite o endereço e o código postal em ordem, separados por ","',
    isUnbind: 'Deseja desvincular',
    alarmCar: 'veículo de alarme',
    alarmAddress: 'local do alarme',
    chooseAtLeastOneTime: 'Selecione pelo menos uma vez',
    alarmMessage: 'Não há detalhes dessas informações de alarme',
    navigatorBack: 'return to superior',
    timeOverMessage: 'O tempo de expiração do usuário não pode ser maior que o tempo de expiração da plataforma',
    //1.7.0
    userTypeStr: 'Tipo de Usuário',
    newAdd: 'Novo',
    findAll: 'Total',
    findStr: 'Dados correspondentes',
    customColumn: 'Personalizar',
    updatePswErr: 'Falha na senha atualizada',
    professionalUser: 'Usuário profissional ou não?',
    confirmStr: 'Confirme',
    inputTargetCustomer: 'Cliente alvo de entrada',
    superiorUser: 'Usuário Superior',
    speedReport: 'Relatório de velocidade',
    createAccount: 'Criar Conta',
    push: 'Push',
    searchCreateStr: 'Ele operará uma conta e transferirá o dispositivo para esta conta.',
    allowIMEI: 'Permitir login IMEI',
    defaultPswTip: 'A senha padrão tem os últimos 6 dígitos do IMEI',
    createAccountTip: 'Conta criada e dispositivo transferido com êxito',
    showAll: 'Mostrar tudo',
    bingmap: 'Mapa do Bing',
    areaZoom: 'Zoom',
    areaZoomReduction: 'Restaurar zoom',
    reduction: 'Redução',
    saveImg: 'Salvar como imagem',
    fleetFence: 'Cerca de frota',
    alarmToSub: 'Notificação de alarme',
    bikeFence: 'Cerca de bicicleta',
    delGroupTip: 'Delete falhou, por favor delete OS Pontos de interesse primeiro!',
    isExporting: 'Exportação...',
    addressResolution: 'Resolução do endereço...',
    simNOTip: 'O número do cartão SIM pode ser apenas um número',
    unArrowServiceTip: 'O tempo de expiração do usuário para os seguintes dispositivos é maior que o tempo de expiração da plataforma. Selecione novamente. O número do dispositivo é:',
    platformAlarmandPhone: 'Alarme da plataforma + Tel',
    openLightAlarm: 'Alarme do sensor de luz aberta',
    closeLightAlarm: 'Alarme do sensor de luz fechada',
    ACCAlarm: 'Alarme ACC',
    translateError: 'Falha na transferência, o usuário de destino não tem permissão',
    distanceTip: 'Clique em OK, clique duas vezes para concluir',
    workMode: 'Modo de trabalho',
    workModeType: '0: modo normal; 1 modo de suspensão inteligente; 2 Modo de suspensão profunda',
    clickToStreetMap: 'Clique para abrir o mapa Da Vista Da Rua',
    current: 'Atual',
    remarkTip: 'Nota: Não venda cartões de diferentes tipos de pacotes ao mesmo tempo',
    searchRes: 'Resultado da pesquisa',
    updateIcon: 'Alterar ícone',
    youHaveALarmInfo: 'Você tem uma mensagem de alerta',
    moveInterval: 'Intervalo de exercício',
    staticInterval: 'Intervalo de tempo de inatividade',
    notSupportTraffic: 'Coming soon.',
    ignite:'Acc ON',
    flameout:'Acc OFF',
    generateRenewalPointSuc:'Gere pontos de renovação com sucesso',
    noGPSsignal:'Não posicionado',
    imeiErr2:'Digite pelo menos 6 últimos dígitos do número IMEI',
    searchCreateStr2:'Isso criará uma conta e transferirá este dispositivo para o nome da conta',
    addUser:'Adicionar usuário',
    alarmTemperature:'Valor da temperatura do alarme',
    highTemperatureAlarm:'Alarme de alta temperatura',
    lowTemperatureAlarm:'Alarme de baixa temperatura',
    temperatureTip:'Por favor, insira o valor da temperatura！',
    locMode:'Modo de posicionamento',
    imeiInput:'Digite o número IMEI',
    noResult:'Nenhum resultado correspondente',
    noAddressKey:'Temporarily unable to get address information',
    deviceGroup: 'Grupo de dispositivos',
    shareManage: 'Compartir Administrar',
    lastPosition: 'última posição',
    defaultGroup: 'Grupo padrão',
    tankShape: 'Forma do tanque',
    standard: 'Padrão',
    oval: 'oval',
    irregular: 'Irregular',
    //1.8.4
    inputAddressOrLoc:'Digite o endereço / latitude e longitude',
    inputGroupName:'Digite um nome de grupo',
    lock:'trava',
    shareHistory:'Compartilhar faixa',
    tomorrow:'Amanhã',
    threeDay:'Três dias',
    shareSuccess:'Compartilhe o sucesso',
    effective:'Válido',
    lapse:'Inválido',
    copyShareLink:'Copiar link para compartilhar',
    openStr:'Aberto',
    closeStr:'Fora',
    linkError:'Erro de link',
    inputUserName:'Nome do usuário de entrada',
    barCodeStatistics:'Estatísticas do código de barras',
    barCode:'Código de barras',
    sweepCodeTime:'Tempo de leitura do código de barras',
    workModeType2: '1 modo de suspensão inteligente; 2 modo de suspensão profunda; 3 modo de ligar / desligar remoto',
    remoteSwitchMode: 'Modo de comutação remota',
     saleTime :'Data da venda',
     onlineTime :'上线日期',
     dayMileage:'Hoje quilometragem',
     imeiNum:'Número IMEI',
     overSpeedValue:'Limite de velocidade excessiva',
     shareNoOpen:'O link de compartilhamento não está ativado',
     addTo2:'Customer',
     overDue: 'Expired',
      openInterface: 'Interface aberta',
      privacyPolicy: 'Política de privacidade',
    serviceTerm: 'Termos de serviço',
     importError: 'Falha ao adicionar, o número do dispositivo (IMEI) deve ser um número de 15 dígitos',
     importResult: 'Importar resultados',
     totalNum: 'total',
     successInfo: 'sucesso',
     errorInfo: 'Fail',
     repeatImei: 'IMEI repeat',
includeAccount: 'subconta',
formatError:'Malformado',
importErrorInfo:'Digite o número IMEI de 15 dígitos',
totalMileage: 'Total Mileage',
     totalOverSpeed: 'Excesso de velocidade total (vezes)',
     totalStop: 'Total de paradas (vezes)',
     totalOil: 'óleo total',
     timeChoose: 'Time selection',
     intervalTime: 'Interval time',
    default: 'Padrão',
     idleSpeedStatics: 'Estatísticas de velocidade ociosa',
     offlineStatistics: 'Estatísticas offline',
     idleSpeed: 'Velocidade ociosa',
     idleSpeedTimeTip1: 'O tempo ocioso não pode estar vazio',
     idleSpeedTimeTip2: 'O tempo ocioso deve ser um número inteiro positivo',
     averageSpeed: 'Velocidade média',
     averageOil: 'Consumo médio de combustível',
     oilImgTitle: 'Tabela de análise de óleo',
     oilChangeDetail: 'Detalhes da mudança de combustível',
    machineNameError:"O nome do dispositivo não pode conter símbolos especiais (/ ')",
    remarkError:'La información del comentario no puede exceder las 50 palabras',
    defineColumnTip:'Verifique até 12 itens',
    pswCheckTip: 'A sugestão é uma combinação de 6 a 20 dígitos, letras e símbolos',
     chooseGroup: 'Por favor, selecione um grupo',
     chooseAgain: 'Você pode consultar os dados apenas nos últimos seis meses, selecione novamente！',
     noDataTip: 'Sem dados！',
     noMachineNameError: 'Selecione um dispositivo！',
     loginAccountError: 'A conta de login não pode ter 15 dígitos！',
     includeExpire: 'Qual expira',
    groupNameTip: 'O nome do grupo não pode estar vazio！',
    outageTips:'Tem certeza de que o óleo foi cortado?',
    powerSupplyTips:'Você tem certeza de restaurar o óleo?',//葡萄牙
    centerPhoneTips:'Vui lòng nhập số',
    centerPhoneLenTips: 'Vui lòng nhập 8-20 chữ số',
    passworldillegal: "Existem caracteres ilegais",
    // 2.0.0 POI，权限版本
    singleAdd:'Único adicionar',
    batchImport:'Importação em lote',
    name:'Nome',
    icon:'Icon',
    defaultGroup:'Grupo padrão',
    remark:'Comentário',
    uploadFile:'Carregar arquivo',
    exampleDownload:'Download de exemplo',
    uploadFiles:'Carregar arquivo',
    poiTips1:'Você pode importar POI carregando um arquivo Excel com informações relacionadas. Siga o formato do exemplo para preparar o arquivo',
    poiTips2:'Nome: Obrigatório, não mais de 32 caracteres',
    poiTips3:'Ícone: obrigatório, insira 1,2,3,4',
    poiTips4:'Latitude：Obrigatório',
    poiTips5:'Longitude：Obrigatório',
    poiTips6:'Nome do grupo: opcional, não mais do que 32 caracteres. Se o nome do grupo não for preenchido, o ponto POI pertence ao grupo padrão. Se o nome do grupo preenchido for consistente com o nome do grupo criado, o ponto POI pertence ao grupo criado. O nome do grupo não foi criado, o sistema irá adicionar o grupo',
    poiTips7:'Comentários: Opcional, não mais do que 50 caracteres',
    // 权限相关
    roleLimit: 'Permissões de função',
    operateLog: 'Registro de operação',
    sysAccountManage: 'Conta de autoridade',
    rolen: 'Funções',
    rolename: 'Nome do papel',
    addRole: 'Novo papel',
    editRole: 'Editar papel',
    deleteRole: 'Excluir papel',
    delRoleTip: 'Tem certeza de que deseja excluir esta função?',
    delAccountTip: 'Tem certeza de que deseja excluir esta conta?',
    limitconfig: 'Perfil de Direitos',
    newAccountTip1: 'A conta de autoridade é semelhante à conta virtual antiga e é a subconta do administrador. Os administradores podem criar contas com privilégios e atribuir funções diferentes a contas com privilégios para que contas diferentes possam ver diferentes conteúdos e operações na plataforma.',
    newAccountTip2: 'Processo de criação de uma conta de permissão:',
    newAccountTip31: '1. Na página de gerenciamento de funções,',
    newAccountTip32: 'Novo papel',
    newAccountTip33: ', E configurar permissões para a função;',
    newAccountTip4: '2. Na página de gerenciamento de conta de autoridade, crie uma nova conta de autoridade e atribua funções à conta.',
    newRoleTip1: 'Os administradores podem criar funções e configurar diferentes permissões de operação para diferentes funções para atender às necessidades de negócios em diferentes cenários.',
    newRoleTip2: 'Por exemplo, configure se uma função financeira tem permissão para localizar e monitorar, se tem permissão para adicionar clientes, se tem permissão para modificar informações do dispositivo, etc.',
    "refuelrate": "Taxa de reabastecimento",
    "refuellimit": "Quando o aumento de óleo por minuto for maior que xxxxL e menor que xxxxL, é considerado reabastecimento.",
    "refueltip": "A taxa máxima de reabastecimento não deve ser inferior à taxa mínima de reabastecimento!",
    viewLimitConf: 'Ver configurações de permissão',
    viewLimit: 'Ver permissões',
    newSysAcc: 'Nova conta do sistema',
    editSysAcc: 'Editar conta de permissão',
    virtualAcc: 'Conta virtual',
    oriVirtualAcc: 'Conta virtual original',
    virtualTip: 'O módulo de conta virtual foi atualizado para um módulo de conta do sistema, crie uma nova conta do sistema',
    operaTime: 'Tempo operacional',
    ipaddr: 'endereço de IP',
    businessType: 'Tipo de Negócio',
    params: 'Parâmetro de solicitação',
    operateType: 'Tipo de operação',
    uAcc: 'conta de usuário',
    uName: 'nome do usuário',
    uType: 'tipo de usuário',
    logDetail: 'Detalhes de registro',
    delAccount: 'Deletar conta',
    modifyTime: 'Modificar hora',
    unbindlimit: 'Não é possível criar uma cerca eletrônica de um clique sem permissões de dispositivo associadas!',
    setSmsTip: 'Se você configurar a notificação por SMS, você precisa ativar a notificação da plataforma primeiro; se a entrega da notificação da plataforma for bem-sucedida, a notificação por SMS não foi bem-sucedida, você precisa ativar a notificação por SMS novamente',
    cusSetComTip: 'Isenção de responsabilidade: O risco trazido pelas instruções personalizadas não tem nada a ver com a plataforma',
    cusSetComPas: 'Por favor, digite a senha da conta de login atual',
    cusSetComDes1: 'As instruções personalizadas suportam apenas instruções online.',
    cusSetComDes2: 'Se o dispositivo não responder dentro de dois minutos após o envio do comando, o processo será encerrado e o status do comando será considerado sem resposta.',
    cueSetComoffline: "O dispositivo não responde e o envio do comando personalizado falhou!",
    fbType: 'Tipo de feedback',
    fbType1: 'Consultivo',
    fbType2: 'Defeituoso',
    fbType3: 'experiência de usuário',
    fbType4: 'Novas sugestões de recursos',
    fbType5: 'de outros',
    upload: 'Envio',
    uploadImg: 'Enviar Imagem',
    uploadType: 'Faça upload de arquivos do tipo .jpg .png .jpeg .gif',
    uploadSize: 'O arquivo de upload não pode ser maior que 3M',
    fbManager: 'Gerenciamento de feedback',
    blManager: 'Gestão de anúncios',
    fbUploadTip: 'Selecione o tipo de feedback',
    menuPlatform: "Notícias de plataforma",
    menuFeedback: "Comentários",
    menuBulletin: "Anúncio da plataforma",
    // 新增驾驶行为
    BdfhrwetASDFFEGGREGRDAF: "Comportamento de direção",
    BtyjdfghtwsrgGHFEEGRDAF: "Aceleração rápida",
    BtyuwyfgrWERERRTHDAsdDF: "Desaceleração rápida",
    Be2562h253grgsHHJDbRDAF: "Curva acentuada",
    celTemperature:'Celsius Temperatura'
}
// 权限tree
lg.limits = {
    "ACC_statistics": "ACC estatística",
    "Account_Home": "Casa",
    "Add": "Novo",
    "Add_POI": "Adicionar POI",
    "Add_customer": "Adicionar usuário",
    "Add_device_group": "Adicionar grupo de dispositivos",
    "Add_fence": "Adicionar cerca",
    "Add_sharing_track": "Adicionar trilha de compartilhamento",
    "Add_system_account": "Nova permissão de conta",
    "Alarm_details": "Detalhes do alarme",
    "Alarm_message": "Mensagem de alarme",
    "Alarm_overview": "Visão geral do alarme",
    "Alarm_statistics": "Relatório de alarme",
    "All_news": "Todosl",
    "Associated_equipment": "Associar dispositivo",
    "Available_points": "Cartão disponível",
    "Barcode_statistics": "Estatísticas do código de barras",
    "Batch_Import": "Importação em lote",
    "Batch_renewal": "Lote renovar",
    "Batch_reset": "Bulk Reset",
    "Bulk_sales": "Vendas a granel",
    "Call_the_police": "Alarme",
    "Customer_details": "Detalhes do cliente",
    "Customer_transfer": "Mover usuário",
    "Delete_POI": "Apagar POI",
    "Delete_account": "Deletar conta",
    "Delete_customer": "Deletar usuário",
    "Delete_device": "Apagar dispositivo",
    "Delete_device_group": "Excluir grupo de dispositivos",
    "Delete_fence": "Excluir cerca",
    "Delete_role": "Excluir papel",
    "Device_List": "Lista de dispositivos",
    "Device_grouping": "Grupo de dispositivos",
    "Device_transfer": "Transferência de dispositivo",
    "Due_reminder": "Recordação Da Expiração",
    "Edit_details": "Editar Detalhes",
    "Equipment_management": "Dispositivo",
    "My_clinet": "Dispositivo",
    "Fence": "GeoFence",
    "Fence_management": "Gerenciamento de cercas geográficas",
    "Generate": "Gerar",
    "Generate_lead-in_points": "Criar novo cartão",
    "Generate_renewal_points": "Criar cartão de renovação",
    "Have_read": "Claro",
    "Idle_speed_statistics": "Estatísticas de velocidade ociosa",
    "Import": "Importar",
    "Import_Device": "Adicionar Dispositivo",
    "Industry_Statistics": "Industry Statistics",
    "Location_monitoring": "Monitor",
    "Log_management": "Gerenciamento de log",
    "Mark_read": "Marque lido",
    "Menu_management": "Gestão do menu",
    "Message_Center": "Centro de notificações",
    "Mileage_statistics": "Relatório de milhagem",
    "Modify_POI": "Modificar POI",
    "Modify_device_details": "Modificar detalhes do dispositivo",
    "Modify_device_group": "Modificar grupo de dispositivos",
    "Modify_role": "Modificar função",
    "Modify_sharing_track": "Modificar faixa de compartilhamento",
    "Modify_user_expiration": "A modificação em lote dos usuários expira",
    "More": "Mais",
    "My_client": "Meu cliente",
    "New_role": "Novo papel",
    "New_users": "Adicionar usuário",
    "Oil_statistics": "Fuel Statistics",
    "POI_management": "Gestão POI",
    "Points_record": "Histórico do cartão",
    "Push": "Push",
    "Quick_sale": "Venda rápida",
    "Renew": "Renovar",
    "Replay": "Reprodução",
    "Role_management": "Gestão de funções",
    "Run_overview": "Detalhes em movimento",
    "Running_statistics": "Detalhes em movimento",
    "Sales_equipment": "Vender dispositivo",
    "Set_expiration_reminder": "Gestão de funções",
    "Share_track": "Compartilhar faixa",
    "Sharing_management": "Compartir Administrar",
    "Speeding_detailed_list": "Lista de velocidade",
    "Statistical_report": "Relatório",
    "Stay_detailed_list": "Lista de estacionamento",
    "System_account_management": "Conta de autoridade",
    "Temperature_statistics": "Temperature Statistics",
    "Transfer": "Mover usuário",
    "Transfer_group": "Grupo de transferência",
    "Transfer_point": "Transferir novo cartão",
    "Transfer_renewal_point": "Transferir cartão de renovação",
    "Trip_statistics": "Relatório de viagem",
    "Unlink": "Desvincular",
    "View": "Visão",
    "View_POI": "Ver POI",
    "View_device_group": "Ver grupo de dispositivos",
    "View_fence": "Verifique a cerca",
    "View_role": "Ver função",
    "View_sharing_track": "Ver trilha de compartilhamento",
    "Virtual_account": "Conta virtual",
    "Voltage_analysis": "Voltage Analysis",
    "Voltage_statistics": "Voltage Statistics",
    "batch_deletion": "Apagar lote",
    "change_Password": "Comentários",
    "delete": "Apagar",
    "edit": "Editar",
    "instruction": "Comando",
    "modify": "Atualizar",
    "monitor": "Monitor",
    "my_account": "Minha conta",
    "reset_Password": "Redefinir senha",
    "share_it": "Compartilhar localizacão",
    "sub_user": "Sub conta",
    "track": "Rastreamento", 
    "Custom_Order": "Instrução personalizada",
    "GeoKey_Manager": "Gestão GeoKey",
    "GeoKey_Update": "modificar",
    "GeoKey_Delete": "excluir",
    "GeoKey_Add": "adicionar à",
    "GeoKey_View": "Visão",
    "feedback_manager": "Gerenciamento de feedback",
    "feedback_list": "Visão",
    "feedback_handle": "Processando feedback",
    "proclamat_manager": "Gestão de anúncios",
    "proclamat_manager_list": "Ver anúncio",
    "proclamat_manager_update": "Anúncio de modificação",
    "proclamat_manager_delete": "Excluir anúncio",
    "proclamat_manager_save": "Novo anúncio",
    "device_update_batch_model": "Modelo de dispositivo de modificação em lote"
}
// 问题文档的内容
lg.questionDocumentArr = [
    ['P: A luz indicadora fica apagada depois que o dispositivo de fiação é instalado e está off-line.', 'A: Depois de desligar o carro, use a caneta elétrica e o medidor universal para medir se a tensão da linha conectada está alinhada com a faixa de voltagem do rastreador GPS é geralmente 9-36V.<br/>Precauções de instalação: O pessoal de instalação e fiação precisa ter uma compreensão da linha do carro e ter certa habilidade prática para evitar danos ao seu carro causados por fiação inadequada.'],
    ['P: Dispositivo com fio ou dispositivo de rastreamento em tempo real sem fio, chamada telefônica ou dispositivo de estado de inicialização de fundo da IoT off-line', 'A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Envie uma mensagem de texto para reiniciar, observe alguns minutos para ver se ela está on-line. Geralmente envie RESET # entre em contato com o revendedor para determinar.<br/>&nbsp;&nbsp;&nbsp;&nbsp2. A conexão de rede é instável, por favor, mova o carro para uma área de bom sinal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Após as etapas acima, você não poderá entrar on-line. É necessário entrar em contato com a operadora móvel para verificar se o cartão está anormal.'],
    ['Q: O equipamento está off-line off-line no início e no final do mês.', "A: Por favor, verifique se o cartão está em atraso. Se estiver em atraso, recarregue-o a tempo e continue a usá-lo."],
    ['Q: O carro está dirigindo, a posição on-line do GPS não está atualizada.', 'A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Dispositivo de fiação pode enviar SMS STATUS # para verificar o status de recepção do sinal de satélite, consulte GPS: pesquisando satélite é o sinal de satélite tem sido na busca, esta situação precisa verificar o local de instalação, se ele é instalado de acordo com as instruções. Face para cima, não há tampa de metal no topo.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Envie SMS STATUS #, status de retorno é GPS: OFF, por favor envie FACTORY # novamente, depois de receber a resposta OK, observe 5 minutos para ver se há alguma atualização.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. De acordo com os dois métodos acima, a falha não pode ser eliminada Entre em contato com o vendedor para reparo.'],
    ['P: Por que a plataforma de carregamento está cobrando por muito tempo ainda mostrando que não está cheia?', 'R: A exibição de energia da plataforma é baseada nas informações retornadas pelo dispositivo para fazer uma análise de dados para determinar a potência atual do dispositivo.Em alguns casos especiais, a solução de erro de exibição de energia será exibida:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Os dados de energia do dispositivo e os dados de posicionamento do dispositivo são carregados juntos.Se a bateria não tiver sido trocada por um longo tempo, por favor: 1 Traga seu dispositivo para mover 100-300 metros para atualizar as informações de localização do dispositivo. Os dados e os dados de localização podem ser retornados juntos à plataforma para ser uma atualização de exibição de energia.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. De acordo com a alteração do indicador de potência, determine se está totalmente carregada (tome S15 como exemplo) .As etapas de operação são as seguintes: 1 carga 8-10 horas, depois o indicador de energia fica amarelo verde, após desconectar a linha de carregamento, insira o cabo de carregamento. Dentro de 15 minutos, o indicador de energia ficará amarelo e verde até a potência máxima, consulte o manual para outros modelos.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, cobrando por um longo tempo também é cheio de eletricidade, esta situação pode ser a tensão do plugue de carregamento é inferior a 1A, por favor, carregue 5V, 1A cabeça de carregamento por 8-10 horas.'],
    ['Q: O comando de desligamento do GPS foi emitido com sucesso Por que o carro ainda não está quebrado?', 'Resposta: Depois que o comando de desligamento é emitido com sucesso, o equipamento deve ser executado sob as seguintes condições:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Certifique-se de que a fiação do equipamento esteja correta e siga o diagrama de fiação do manual.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, o equipamento funciona normalmente, está em um estado estático ou de condução, tem posicionamento, não está off-line, e a velocidade do veículo não exceda 20 km;<br/>Se o veículo estiver desligado, não posicionado ou a velocidade do veículo exceder 20 quilômetros por hora, o terminal não será executado mesmo se o comando de corte de energia for emitido com sucesso.'],
    ['P: Três anos de produtos sem fio são instalados pela primeira vez, o dispositivo de exibição não está posicionado ou on-line.', 'A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Ligue o interruptor para verificar se a luz indicadora está piscando, por exemplo, os indicadores amarelo e verde S18 piscam ao mesmo tempo que o normal, e a luz piscante está no sinal de busca O dispositivo não está aceso. (O status de diferentes modelos será diferente. Por favor, consulte o manual para outros modelos)<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. A luz indicadora pisca não na linha Se o sinal estiver ligado em uma posição ruim, por favor, coloque o sinal em uma boa área. A área boa do sinal não está na linha, você pode desligar por 1 minuto, reinstalar o cartão e iniciar o teste.'],
    ['P: O produto a cabo é instalado pela primeira vez e o dispositivo de exibição não está posicionado.', 'A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Observe se o indicador de status do GPS do terminal é normal Verifique o status do indicador de acordo com as instruções de diferentes modelos.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Se a luz indicadora estiver apagada, o dispositivo não poderá ser alimentado normalmente.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, (verde amarelo) indicador do cartão não está aceso, desligar e reinstalar o cartão e, em seguida, ligar para ver a luz normal é normal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;4. Determine se o número do cartão SIM no dispositivo não está em atraso e se a função de acesso à Internet GPRS é normal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;5. Não há rede GSM no local onde o equipamento está localizado, como porão, túnel, etc., onde o sinal é fraco, por favor dirija até o local onde a cobertura GPRS é boa.<br/>&nbsp;&nbsp;&nbsp;&nbsp;6, a posição do posicionador não deve ser muito fechada, não tem objetos de metal, tanto quanto possível na posição de instalação do carro. Caso contrário, isso afeta a recepção do sinal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;7, inicialização normal, parar na área de bom sinal não está online, você pode re-emitir o comando de linha para verificar a interface IP e rede de link de cartão é normal.'],
    ['P: A luz indicadora fica apagada depois que o dispositivo de fiação é instalado e está off-line.', 'Solução:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Depois de desligar o carro, use a caneta elétrica e o medidor universal para medir se a tensão da linha do carro conectada está alinhada com a faixa de tensão do rastreador GPS é geralmente 9-36V.<br/>&nbsp;&nbsp;&nbsp;&nbsp;Precauções de fiação:<br/>&nbsp;&nbsp;&nbsp;&nbsp;O pessoal de instalação e fiação precisa ter uma compreensão da linha do carro e ter certa capacidade prática de evitar danos ao seu carro causados por fiação inadequada.'],
    ['P: Dispositivos com fio ou dispositivos de rastreamento em tempo real sem fio, chamadas telefônicas ou dispositivo de status de inicialização em segundo plano da IoT offline ', ' A:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Envie uma mensagem de texto para reiniciar, observe alguns minutos para ver se ela está on-line. Geralmente envie RESET # entre em contato com o revendedor para determinar.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. A conexão de rede é instável Por favor, mova o carro para uma boa área de sinal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Após as etapas acima, você não poderá entrar on-line. É necessário entrar em contato com a operadora móvel para verificar se o cartão está anormal.'],
    ['P: O equipamento está off-line off-line no início e no final do mês. ”Solução: verifique se o cartão está com atraso. Se estiver atrasado, recarregue-o a tempo e continue a usá-lo.'],
    ['Q: O carro está dirigindo, a posição on-line do GPS não está atualizada.', 'Solução:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Dispositivo de fiação pode enviar SMS STATUS # para verificar o status de recepção do sinal de satélite, consulte GPS: pesquisando satélite é o sinal de satélite tem sido na busca, esta situação precisa verificar o local de instalação, se ele é instalado de acordo com as instruções. Face para cima, não há tampa de metal no topo.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Envie SMS STATUS #, o status de retorno é GPS: OFF, por favor envie FACTORY # novamente, depois de receber a resposta OK, observe 5 minutos para ver se a posição está atualizada.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. De acordo com os dois métodos acima, a falha não pode ser eliminada Entre em contato com o vendedor para reparo.'],
    ['P: Por que a plataforma de carregamento está cobrando por muito tempo ainda mostrando que não está cheia?', 'O display de energia da plataforma é baseado nas informações retornadas do dispositivo para fazer uma análise de dados para determinar a potência atual do dispositivo.Em alguns casos especiais, o erro de exibição de energia ocorrerá.<br/>Solução:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Os dados de energia do dispositivo e os dados de posicionamento do dispositivo são carregados juntos.Se a bateria não tiver sido trocada por um longo tempo, por favor: 1 Traga seu dispositivo para mover 100-300 metros para atualizar as informações de localização do dispositivo. Os dados e dados de localização podem ser retornados juntos à plataforma para ser uma atualização de exibição de energia;<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. De acordo com a alteração do indicador de potência, determine se está totalmente carregada (tome S15 como exemplo) .As etapas de operação são as seguintes: 1 carga 8-10 horas, depois o indicador de energia fica amarelo verde, após desconectar a linha de carregamento, insira o cabo de carregamento. Dentro de 15 minutos, o indicador de energia ficará amarelo e verde até a potência máxima, consulte o manual para outros modelos.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, cobrando por um longo tempo também é cheio de eletricidade, esta situação pode ser a tensão do plugue de carregamento é inferior a 1A, por favor, carregue 5V, 1A cabeça de carregamento por 8-10 horas.']
]
lg.webOptDoc = 'Em breve...';
lg.appOptDoc = 'Em breve...';
// 查询参数的帮助
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push('<tr>');
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push('<td>Query terminal password</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">PHONE:</td>');
html.push('<td>Query terminal built-in SIM card</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">USER:</td>');
html.push('<td>Query owner phone number</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SPEED:</td>');
html.push('<td>Query the value of speed limit</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">FREQ:</td>');
html.push('<td>Query the frequency of tracking,the unit is seconds</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">TRACE:</td>');
html.push('<td>Query whether tracking is enabled.1:enable,0:disable</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push('<td>Query the illegal migration alarm range,Unit: meter</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIB:</td>');
html.push('<td>Query whether SMS alarm is enabled,1:enable,0:disable');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBL:</td>');
html.push('<td>Query vibration sensitivity is 0 to 15,0 is the highest sensitivity, too high may be false alarm, 15 is the lowest sensitivity</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push('<td>Query whether call alarm is enabled,1:enable,0:disable');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push('<td>Query whether GPS filter drift is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the stationary state with no vibration occurs within 5 minutes,and filter all GPS drift</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push('<td>Query whether sleep function is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the sleeping mode with no vibration occurs within 30 minutes,it will close GPS function and save power </td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">POF:</td>');
html.push('<td>Query whether the power off alarm is enabled,1:enbale,0:disabled</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">GPS:</td>');
html.push('<td>Query the satellite signal strength,For example：2300 1223 3431 。。。 a total of 12 sets of four-digit,2300 means: The signal strength from Number 23 satellite is 0,1223 means: The signal strength from Number 12 satellite is 23</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VBAT:</td>');
html.push('<td>Query battery voltage, charging port voltage Charge current For example: VBAT = 3713300:4960750:303500 Indicates that the battery voltage is 3713300uV 3.71v Applied to the charging voltage on the chip 4.96V,Charging current 303mA</td>');
html.push('</tr>');
html.push('</table>');
lg.queryparamhelp = html.join("");

// 设置参数的帮助
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push('<tr>');
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push('<td>Set terminal password,which is only 6 digits</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">PHONE:</td>');
html.push('<td>Set SIM number of the terminal</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">USER:</td>');
html.push('<td>Set the number of mobile phone owners</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SPEED:</td>');
html.push('<td>Set the speed limit value,0-300</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">FREQ:</td>');
html.push('<td>Set up the reported frequency when turn on tracking,Unit:secend</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">TRACE:</td>');
html.push('<td>Set up whether open the track,1:open,0:close</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push('<td>Set up the illegal migration alarm range,Unit: meter</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIB:</td>');
html.push('<td>Set up whether SMS alarm is enabled,1:enable,0:disable');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBL:</td>');
html.push('<td>Set up vibration sensitivity of 0 to 15,0 is the highest sensitivity, too high may be false alarm, 15 is the lowest sensitivity</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push('<td>Set up whether call alarm is enabled,1:enable,0:disable');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push('<td>Set up whether GPS filter drift is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the stationary state with no vibration occurs within 5 minutes,and filter all GPS drift</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push('<td>Set up whether sleep function is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the sleeping mode with no vibration occurs within 30 minutes,it will close GPS function and save power</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">POF:</td>');
html.push('<td>Set up whether the power off alarm is enabled,1:enbale,0:disabled</td>');
html.push('</tr>');
html.push('</table>');
lg.setparamhelp = html.join("");


//批量添加
html = [];
html.push('<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox"  ' +
    'style="z-index: 999;position:absolute;left:153px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Customer:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' + 'bulkAdds_treeDiv' + ',' + 'bulkAdds_seller' + ')" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Platform Due:</td>');
html.push('<td>');
html.push('<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Model:</td>');
html.push('<td>');
html.push('<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    '</div>' +
    '<div id="deviceList"></div>' +
    '</div>' +
    '</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>');
html.push('<td>');
html.push('<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>');
lg.bulkAdds = html.join('');

//批量续费
html = [];
html.push('<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> ');
html.push('</div>');
html.push('<form id="bs_form">');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Add device:</td>');
html.push('<td>');
html.push('<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('<span class="re_addNumBox">Atual：<span id="account_re_addNum">0</span>');
html.push('</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<tr>');
html.push('<td style="text-align:right;"><span style="color:red">*</span>Type</td>');
html.push('<td>');
html.push('<input  type="radio" name="red_cardType"');
html.push('class="easyui-validatebox"  value="3" checked><label>Anual</label></input>');
html.push('<input  type="radio" name="red_cardType" style="margin-left:15px;" ');
html.push('class="easyui-validatebox" value="4"><label>Vitalício</label></input>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right">Quantity</td>');
html.push('<td>');
html.push('<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">');
html.push('</td>');
html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>User Due:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Remark</td>');
html.push('<td>');
html.push('<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="re_renewMachines" title="'+lg.renew+'" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="re_reset" title="'+lg.reset+'"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('</form>');
lg.bulkRenew = html.join('');

//批量销售，myAccount
html = [];
html.push('<div id="bulkSales_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:134px;top:88px;border: 1px solid #ccc !important;height:200px;width:250px;"> ');
html.push('<ul style="height:200px;" class="ztree" id="bulkSales_tree"></ul> ');
html.push('</div>');
html.push('<form id="bs_form">');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Customer:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' + 'bulkSales_treeDiv' + ',' + 'bulkSales_seller' + ')" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push('<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>');
html.push('</td>');
html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>User Due:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>');
html.push('<td>');
html.push('<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('<span class="bs_addNumBox">Atual：<span id="account_bs_addNum">0</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="bs_sellMachines" title="'+lg.sell+'"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="bs_reset" class="swd-gray-btn" title="'+lg.reset+'"  style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('</form>');
lg.bulkSales = html.join('');


//批量转移1，弹出框
html = [];
html.push('<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:152px;top:171px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>target client:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>');
html.push('<td>');
html.push('<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >Batch Add</a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push('</div>');
html.push('<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Move</a>');
html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)" >Cancel</a>');

lg.bulkTransfer = html.join('');

//批量转移2,myClient
html = [];
html.push('<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:142px;top:83px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>target client:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>');
html.push('<td>');
html.push('<img  id="bt_addMachines" style="cursor:pointer" title="'+lg.addTo+'" src="../../images/main/myAccount/add3.png" />');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push('</div>');
html.push('<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');

lg.bulkTransfer2 = html.join('');


//批量转移用户
html = [];
html.push('<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:141px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>target client:</td>');
html.push('<td>');
html.push('<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>');
html.push('</td>');
html.push('<td></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');

lg.bulkTransferUser = html.join('');

window.lg = lg




