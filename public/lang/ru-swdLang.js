var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
var lg = {  //俄罗斯语
  //common common
  user_guide: 'Гид пользователя',
  remoteSwitch: "Удаленный переключатель",
  pageTitle: 'WhatsGPS Global Tracking System|Vehicle GPS Tracker|3G Tracker|mini 4G Tracker|GPSNow|Car Locator',
  description: site + " is dedicated to providing users with intelligent cloud location services. It is the world's leading location service platform.",
  pageLang: 'Русский',
  inputCountTips: 'Введите логин',
  inputPasswordTips: 'Введите пароль',
  appDownload: 'Скачать приложения',
  rememberPassword: 'Запомнить меня',
  forgetPassword: 'Забыли пароль',
  siteName: 'WhatsGPS',
  noToken: " Пожалуйста отправьте токен",
  loginFirst: 'Первый логин',
  move: 'Переместить',
  stop: 'Статично',
  query: 'Проверить',
  imeiQuery: 'IMEI',
  delete: 'Удалить',
  update: 'Обновить',
  cancel: 'Отменить',
  soft: 'Номер',
  more: 'Подробнее',
  edit: 'Редактировать',
  useful:'полезный',
  useless:'бесполезный',
  replyFeedback:'Отзыв о "$"',
  add: 'Добавить',
  addTo: 'Добавить к',
  addDevice: 'Добавить устройство',
  machineName: 'Цель',
  searchDevice: 'Устройство',
  date: 'Дата',
  LatestUpdate: 'Сигнал',
  engine: 'Зажигание',
  locTime: 'GPS время',
  locType: 'Тип местоположение',
  startLoc: 'Начало движения',
  endLoc: 'Конец движения',
  address: 'Адрес',
  noAddressTips: 'Невозможно получить адресную информацию',
  lonlat: 'Широта и долгота',
  carNO: 'Номер авто',
  imei: 'IMEI',
  IMEI: "IMEI",
  simNO: 'Номер SIM карты',
  activeTime: 'Время активации',
  expireTime: 'Срок действия',
  acceptSubordinateAlarm: 'Принять подчиненную тревогу',
  acceptAlarmTips1: 'Тревога после проверки',
  acceptAlarmTips2: 'получать данные тревог от всех подчиненных клиентов',
  speed: 'Скорость',
  y: "Года",
  M: "Месяцы",
  d: "Дни",
  h: "Часы",
  min: "Минуты",
  s: "Секунды",
  _year: 'y',
  _month: 'm',
  _day: 'd',
  _hour: 'h',
  _minute: 'm',
  _second: 's',
  confirm: 'подтвердить',
  yes: "Да",
  car: 'авто',
  not: "Нет",
  m: 'Метры',
  account: 'Аккаунт',
  psw: 'Пароль',
  save: 'Сохранить',
  operator: 'Оператор',
  queryNoData: 'Нет данных',
  name: 'имя',
  type: 'Модуль',
  open: 'Открыть',
  close: 'близко',
  send: 'Отправить',
  alarm: 'Тревога',
  alarmSetting: 'Настройки тревоги',
  look: 'Вид',
  tailAfter: 'Отслеживание',
  history: 'История',
  dir: 'Дир',
  locStatus: "Статус местоположения",
  machineTypeText: 'Модуль',
  carUser: 'Водитель',
  machine: 'Цель',
  unknowMachineType: 'Неизвестная машина',
  noCommandRecord: 'Недопустимая команда для устройства',
  type1: 'Тип',
  role: 'Роль',
  roles: 'роли',
  timeType: 'тип времени',
  moveSpeed: 'Скорость движения',
  signal: 'Сигнал',
  loc: 'Позиционирование',
  wiretype: 'Тип',
  wire: 'проводной',
  wireless: 'без проводной',
  expire: 'Истекший',
  hour: 'Часы',
  hourTo: 'Часов до',
  remark: 'Пометка',
  remarkInfo: 'Пометка',
  noPriviledges: 'Не достаточно привилегий',
  commandNoOpen: 'Команда еще не доступна',
  choseDelelePhone: 'Выберите номер для удаления',
  streetView: 'Просмотр улиц',
  wrongFormat: 'Ошибка формата ввода',
  inputFiexd: 'Номер исправлен',
  serialNumberStart: 'Первое число IMEI',
  serialNumberEnd: 'Последнее число IMEI',
  clickSearchFirst: 'Нажмите на поиск устройства!',
  isDeleteDevice: 'Восстановление устройства после удаления не возможно. Продолжить?',
  //????????
  errorTips: 'Код ошибки?',
  error10003: 'неправильный пароль',
  error90010: 'Устройство не в сети, и не удалось отправить пользовательскую команду!',
  error70003: 'Значение дистанционного управления не может быть пустым',
  error70006: 'Не поддерживает или не имеет права выдавать инструкцию',
  error20001: 'ID транспортного средства не может быть пустым',
  error20012: 'Автомобиль не активирован',
  error10012: 'Ошибка старого пароля',
  error10017: 'Сначала удалите подчиненного пользователя!',
  error10023: 'Сначала удалите устройство пользователя',
  error20008: 'В системе уже есть такой IMEI',
  error20006: 'Введите IMEI длиной 15 знвков',
  error10019: 'Не верный формат номере телефона',
  error10024: 'Не повторяйте продажи',
  error120003: 'Общая ссылка отключена',
  error10025: 'Измененная информация не может быть пустой',
  error2010: 'Загрузите файл',
  error20002: 'Заполните IMEI',
  error10081: 'Недостаточное количество карт продления',
  error10082: 'Нет необходимости подзаряжать устройство на всю жизнь',
  error3000: 'A função foi atribuída à conta do sistema e não pode ser excluída',
  error103: 'Аккаунт отключен, обратитесь к поставщику услуг.',
  error124: 'Не может работать сам по себе',
  // ???? login.js
  logining: 'Загрузка...',
  login: 'Авторизация',
  userEmpty: 'Имя пользователя пусто',
  pswEmpty: 'Пароль пустой',
  prompt: 'Запомнить',
  accountOrPswError: 'Не верные имя пользователя или пароль',
  UserNameAlreadyExist: 'Такое имя пользователя уже есть',
  noQualified: 'Уточняющая информация отсутсвует',
  //main.js

  systemName: 'WhatsGPS',
  navTitle_user: ['Монитор', 'Отчеты', 'Устройства'],
  navTitle_dealer: ['Главная', 'Бизнес', 'Монитор', 'Больше операций'],
  exitStytem: 'Выход',
  user: 'Пользователь',
  UserCenter: 'Пользователи',
  alarmInfo: 'Тревога',
  confirmExit: 'Подтвердите выход?',
  errorMsg: 'Сообщение об ошибке: ',
  logintimeout: 'Время сессии вышло!',
  clearAlarm: 'Очистить',
  clear: 'Очистить',
  searchbtn: 'Поиск',
  print: 'Печать',
  export: 'Экспорт',
  //feedback

  feedback: 'Обратная связь',
  feedback_sublime: 'отправить',
  alerttitle: 'Заголовок не может быть пустым!',
  alertcontent: 'Текст сообщения не может быть пустым!',
  submitfail: 'Ошибка отправки?',
  saveSuccess: 'Сохранено успешно?',
  submitsuccess: 'Успешная отправка! Мы ответим как можно скорее~',
  adviceTitle: 'Заголовок',
  adviceTitle_p: 'Заголовок',
  adviceContent: 'Текст сообщения',
  adviceContent_p: 'Ваши пожелания позволяют делать сервис лучше',
  contact: 'Контактная информация',
  contact_p: 'Укажите email или телефон',
  //monitor.js

  myMachine: 'Устройство',
  all: 'Все',
  online: 'Онлайн',
  offline: 'Не в сети',
  unUse: 'Не включен',
  group: 'Группа',
  moveGruop: 'Переместить в группу',
  arrearage: 'недоимка',
  noStatus: 'Нет статуса',
  inputMachineName: 'Цель/IMEI',
  defaultGroup: 'дефолт',
  offlineLessOneDay: 'Оффлайн меньше одного дня',
  demoUserForbid: 'Опытные пользователи не могут использовать эту функцию',
  shareTrack: 'Общее расположение',
  shareName: 'Имя',
  liveShare: 'Отслеживание в режиме реального времени',
  expiration: 'Эффективное время',
  getShareLink: 'Создать ссылку',
  copy: 'Копировать',
  copySuccess: 'Скопировано удачно!',
  enlarge: 'Расширять',
  shareExpired: 'Ссылку для обмена истекла',
  LinkFailure: 'Ошибка открытия ссылки',
  inputShareName: 'Введите имя общего ресурса',
  inputValid: 'Пожалуйста впишите правильное время',
  //statistics.js

  runOverview: "Перемещение Обзор",
  runSta: 'Перемещение Обзор',
  mileageSta: 'Отчет о пробеге',
  tripSta: 'Отчет о поездке',
  overSpeedDetail: 'Pазноса',
  stopDetail: 'Oставаться',
  alarmSta: 'Отчет о тревоге',
  alarmOverview: 'Обзор тревоги',
  alarmDetail: 'Детали будильника',
  shortcutQuery: 'Быстрая проверка',
  today: 'Сегодня',
  yesterday: 'Вчера',
  lastWeek: 'Прошлая неделя',
  thisWeek: 'На этой неделе',
  thisMonth: 'В этом месяце',
  lastMonth: 'Последний месяц',
  mileageNum: 'Пробег(Км)',
  overSpeedNum: 'разноса скорости(км/ч)',
  overSpeed: 'разноса скорости',
  stopTimes: 'Пребывание(Время)',
  searchMachine: 'Поиск',
  speedNum: 'Скорость(км/ч)',
  querying: 'Запрос',
  stopTime: 'оставаться',
  HisToryStopTime: 'оставаться',
  clickLookLoc: 'Нажмите, чтобы посмотреть адрес',
  lookLoc: 'Расположение запроса',
  noData: 'Нет данных',
  alarmTime: 'Время тревоги',
  vibrationLevel: 'Уровень вибрации',
  vibrationWay: 'Тип тревоги',
  accStatistics: 'Статистика Зажигания',
  accType: ['Все', 'Зажигание On', 'Зажигание Off'],
  accstatus: ['вкл', 'выкл'],
  openAccQuery: 'Запрос зажигания',
  runtime: 'Время выполнения',
  //??????

  run: 'Путешествие',
  speed: 'разноса',
  //????

  machineManage: 'Устройства',
  deviceTable: 'Мои цели',
  status: 'Статус',
  havaExpired: 'Истек срок действия',
  expiredIn60: 'Истек срок действия в 60',
  expiredIn7: 'Истек в 7',
  normal: 'Нрмально',
  allMachine: 'Все',
  allMachine1: 'Все цели',
  expiredIn7Machine: '7 дней истек',
  expiredIn60Machine: '60 дней истек',
  havaExpiredMachine: 'Истекшие цели',


  //history.js

  replay: 'Играть',
  replaytitle: ' Воспроизведение',
  choseDate: 'Выбрать дату',
  from: 'От',
  to: 'Для',
  startTime: 'Время начала',
  endTime: 'Время окончания',
  pause: 'Пауза',
  slow: 'Медленно',
  mid: 'Средне',
  fast: 'Быстро',
  startTimeMsg: 'Время начала сообщения',
  endTimeMsg: 'Время окончания сообщения',
  smallEnd: "Время окончания меньше времени начала, Пожалуйста, выберите еще раз!",
  bigInterval: 'Интервал времени менее 31 дня!',
  trackisempty: "Трек пустой",
  longitude: 'Долгота',
  latitude: 'Широта',
  direction: 'Направление',
  stopMark: 'Стоп знак',
  setStopTimes: [

    {

      text: '1 минута',
      value: '1'

    },
    {

      text: '2 минуты',
      value: '2'

    },
    {

      text: '3 минуты',
      value: '3'

    },
    {

      text: '5 минут',
      value: '5'

    },
    {

      text: '10 минут',
      value: '10'

    },
    {

      text: '15 минут',
      value: '15'

    },
    {

      text: '20 минут',
      value: '20'

    },
    {

      text: '30 минут',
      value: '30'

    },
    {

      text: '45 минут',
      value: '45'

    },
    {

      text: '1 час',

      value: '60'
    },
    {
      text: '6 часов',
      value: '360'
    },
    {
      text: '12 часов',
      value: '720'
    },
  ],

  filterDrift: 'Remove drift location',
  userType: ['Админ', 'Дилер', 'пользователь', 'Логистика', 'Аренда', 'Пользователь авто', 'Контроль рисков'],
  userTypeArr: ["Админ", "Дилер", "пользователь", 'Логистика', "Аренда", 'Пользователь авто'],
  machineType: {
    '0':'Machine Type',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '83':'S03A',
	'84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: ['Тип сигнализации', 'Тревога вибрации', 'Выключить тревогу', 'Тревога низкого заряда', 'SOS тревога', 'Тревога превышения скорости', 'Тревога вне гео-зоны', 'Сигнал тревоги смещения', 'Низкий заряд внешей батареи',
    'Вне зоны тревоги', 'Тревога вмешательства', 'Чуствительность световой тревоги', 'Чуствительность магнитной тревоги', 'Тревога демонтажа', 'Bluetooth тревога', 'Сигнал тревоги заглушен', 'Ложная тревога базовой станции', 'Тревога в гео-зоны', 'Тревога в гео-зоне',
    'Тревога вне гео-зоны', 'Сигнал открытия двери', 'Усталость за рулем', 'Заложить точку входа', 'Заложить точку выхода', 'Заложить точку остановки', 'Терминал вне сети', 'Тревога в гео-зоне', 'Тревога вне гео-зоне', 'Тревога в гео-зоне', 'Тревога вне гео-зоны', 'Топливная сигнализация','Тревога ACC ON', 'Тревога ACC OFF', 'Столкновение тревоги'],
  alarmTypeNew:  {
    '40': 'Сигнализация высокой температуры',
    '45': 'Сигнализация низкой температуры',
    '50': 'аварийный сигнал о повышенном напряжении',
    '55': 'аварийный сигнал о пониженном напряжении',
    '60': 'Парковочная сигнализация'
  },
  alarmNotificationType: [
    { type: 'Тревога вибрации', value: 1 },
    { type: 'Тревога выключена', value: 2 },
    { type: 'Тревога низкого заряда батареи', value: 3 },
    { type: 'SOS тревога', value: 4 },
    { type: 'Тревога превышения скорости', value: 5 },
    // {type:'Тревога вне гео-зоны',value:6},
    { type: 'Сигнал тревоги смещения', value: 7 },
    { type: 'Низкий заряд внешей батареи', value: 8 },
    { type: 'Вне зоны тревоги', value: 9 },
    { type: 'Тревога вмешательства', value: 10 },
    { type: 'Чуствительность световой тревоги', value: 11 },
    { type: 'Тревога демонтажа', value: 13 },
    { type: 'Сигнал тревоги заглушен', value: 15 },
    { type: 'Ложная тревога базовой станции', value: 16 },
    // {type:'Тревога в гео-зоне',value:17},
    // {type:'Тревога в гео-зоне',value:18},
    // {type:'Тревога вне гео-зоны',value:19},
    { type: 'Усталость за рулем', value: 21 },
    { type: 'Заложить точку входа', value: 22 },
    { type: 'Заложить точку выхода', value: 23 },
    { type: 'Заложить точку остановки', value: 24 },
    { type: 'Терминал вне сети', value: 25 },
    // {type:'Тревога в гео-зоне(Контроль риска)',value:26},
    // {type:'Тревога вне гео-зоны(Контроль риска)',value:27},
    { type: 'Тревога в гео-зоне', value: 26 },
    { type: 'Тревога вне гео-зоны', value: 27 },
    { type: 'Топливная сигнализация', value: 30 },
    { type: 'Тревога ACC ON', value: 31 },
    { type: 'Тревога ACC OFF', value: 32 },
    { type: 'Столкновение тревоги', value: 33 }
  ],
  alarmTypeText: 'Тип тревоги',
  alarmNotification: 'Уведомление',
  pointType: ['Тип точки', 'Спутниковое Расположение', 'Расположение по компасу', 'LBS Разположение', 'WIFI Расположение'],
  cardType: ['Тип', 'Новая карта', 'Новая Карта На Всю Жизнь', 'Новая карта', 'Новая Карта На Всю Жизнь'],
  // ????
  directarray: ["Восток", "Юг", "Запад", "Север"],
  // ????
  directionarray: ["На север", "Северо-Восток", "На восток", "Юго-Восток", "Строго На Юг", "Юго-Запад", "Строго На Запад", "Северо-Запад"],
  // ????
  pointedarray: ["Не определено", "GPS", "LAC", "LAC расположение", "расположение WiFi "],
  //map Relevant 
  ruler: 'Управляющий',
  distance: 'Трафик',
  baidumap: "Baidu карта",
  map: 'Карта',
  satellite: 'Спутник',
  ThreeDimensional: '3D',
  baidusatellite: "Baidu спутник",
  googlemap: "Google карты",
  googlesatellite: "Google спутник",
  fullscreen: "Полный экран",
  noBaidumapStreetView: 'Текущее местоположение на Картах Baidu без Вида на улицу',
  noGooglemapStreetView: 'Текущее местоположение на Картах Google без Вида на улицу',
  exitStreetView: 'Выход на улицу',
  draw: "Рискнуть",
  finish: "Финиш",
  unknown: 'Неизвестный',
  realTimeTailAfter: 'Слежение в реальном времени',
  trackReply: 'Воспроизведение Истории',
  afterRefresh: "Обновить",
  rightClickEnd: "щелкните правой кнопкой по концу радиуса?",
  rightClickEndGoogle: "щелкните правой кнопкой по Концу------------------------радиус?",




  //tree Relevant 

  currentUserMachineCount: 'Количество машин текущего пользователя',
  childUserMachineCount: 'количество дочерних пользовательских машин',


  //Window relevant



  electronicFence: 'Гео-Зона',
  drawTrack: 'Установить гео-зону',
  showOrHide: 'Показать / Скрыть',
  showDeviceName: 'Целевое имя',
  circleCustom: 'Определяется пользователем',
  circle200m: 'Радиус 200 метров',
  polygonCustom: 'Определение полигона',
  drawPolygon: 'Нарисовать полигон',
  drawCircle: 'Нарисуй круг',
  radiusMin100: 'Радиус нарисованной територии составляет не менее 20 метров. Пожалуйста, перерисуйте. Текущий радиус ограждения:',
  showAllFences: 'Показать все территории',
  lookEF: 'Проверить ограждение',
  noEF: 'Данные гео-зоны не обнаружены',
  hideEF: 'Скрыть геозону',
  blockUpEF: 'Закрыть геозону',
  deleteEF: 'Удалить геозону',
  isStartUsing: 'Следует ли включить',
  startUsing: 'Включить',
  stopUsing: 'Выключить',
  nowEFrange: 'Текищий диапазон территории',
  enableSucess: 'Влючено успешно',
  unableSucess: 'Выключено успешно',
  sureDeleteMorgage: 'Удалить заложенную точку',
  enterMorgageName: 'Введите имя заложенной точки',
  openMorgagelongStayAlarm: 'Остановиться в заложенной начальной точке',
  openMorgageinOutAlarm: 'Начало входа и выхода из заложенной точки',
  setEFSuccess: 'Гео-зона успешно установлена',
  setElectronicFence: 'Установить электронную территорию',
  drawFence: 'Нарисрвать территорию',
  drawMorgagePoint: 'Нарисовать заложенную точку',
  customFence: 'Создать територию',
  enterFenceTips: 'Ввод будильника',
  leaveFenceTips: 'Оставь будильник',
  inputFenceName: 'Пожалуйста, введите имя территории',
  relation: 'Связать',
  relationDevice: 'Объединенное устройство',
  unRelation: 'Не связанный',
  hadRelation: 'Связанный уже',
  quickRelation: 'Связка с одним кликом',
  cancelRelation: 'Отмените связку',
  relationSuccess: 'Успешная связка',
  cancelRelationSuccess: 'Успешно отменено',
  relationFail: 'Отмена не удалась ',
  deviceList: 'Список устройств',
  isDeleteFence: 'Хотите удалить территорию',
  choseRelationDeviceFirst: 'Пжл сначала выберите устройство, которое будет связано!',
  choseCancelRelationDeviceFirst: 'Пожалуйста, сначала выберите устройство, чтобы не иметь связи!',
  selectOneTips: 'Пожалуйста, выберите хотя бы один способ сигнализации',
  radius: 'Радиус',
  //???????

  setMortgagePoint: 'Настроить заложенные точки',


  circleMortage: 'Заложить точку круга',
  polygonMorgage: 'Заложить точку полигона',
  morgageSet: 'Заложенная точка уже установлена',
  operatePrompt: 'Оперативная подсказка',
  startDrawing: 'Нажмите, чтобы начать рисовать',
  drawingtip1: 'Кликните левой кнопкой мыши, чтобы начать рисовать, дважды щелкните, чтобы закончить рисование',
  drawingtip2: 'Щелкните левой кнопкой мыши и перетащите, чтобы начать рисовать',


  /************************************************/

  endTrace: 'Конец слежения',
  travelMileage: 'Пробег путешествия',
  /************************************************/

  myAccount: 'Аккаунт',
  serviceProvide: 'Сервис провайдер',
  completeInfo: 'Полная информация, например контакты, номер телефона',
  clientName: 'Имя клиента',
  loginAccount: 'учетная запись',
  linkMan: 'Контакт',
  linkPhone: 'Тел/Моб',
  clientNameEmpty: "Пустое имя клиента!",
  updateSuccess: 'Обновлено успешно!',
  /************************************************/

  oldPsw: 'Старый пароль',
  newPsw: 'Новый пароль',
  confirmPsw: 'Подтвердите пароль',
  pswNoSame: 'Пароль не совпадает',
  pswUpdateSuccess: "Обновление пароля успешно!",
  email: 'Email',
  oldPwdWarn: 'Пожалуйста введите старый пароль',
  newPwdWarn: 'Пожалуйста введите новый пароль',
  pwdConfirmWarn: 'Please confirm new password',
  /************************************************/

  //Custom popup components

  resetPswFailure: 'Ошибка сброса пароля',
  notification: 'Уведомление',
  isResetPsw_a: 'Вы уверены, что хотите сбросить',
  isResetPsw_b: '`s Пароль?',
  pwsResetSuccess_a: 'Уже сбросили',
  pwsResetSuccess_b: '`s пароль 123456',
  /************************************************/

  machineSearch: 'Инфо устройства',
  search: 'Поиск',
  clientRelation: 'Отношение с клиентами',
  machineDetail: 'детали',
  machineDetail2: 'детали',
  machineCtrl: 'Команды Устройств',
  transfer: 'Переместить',
  belongCustom: 'принадлежать',
  addImeiFirst: 'Сначала добавьте IMEI!',
  addUserFirst: 'Сначала Добавьте Пользователя!',
  transferSuccess: 'Перемещение успешно!',
  multiAdd: 'Пакетное Добавление',
  multiImport: 'Импорт',
  multiRenew: 'возобновлять',
  //批量修改设备begin
  editDevice:'Изменить модель устройства',
  deviceAfter: 'Модель устройства (после модификации)',
  editDeviceTips:'Подтвердите, что модифицируемое устройство относится к той же модели и неактивно!',
  pleaseChoseDevice: 'Пожалуйста, сначала выберите устройство, которое нужно изменить!',
  editResult:'Редактировать результат',
  successCount:'Успешно модифицированное устройство:',
  failCount:'Неисправные устройства:',
  //批量修改设备end
  multiDelete: 'Пакетное Удаление',
  canNotAddImei: 'IMEI не существует, не может добавить IMEI',
  importTime: 'Дата производства',
  loginName: 'Аккаунт',
  platformDue: 'Платформа Связи',
  machinePhone: 'SIM Карта',
  userDue: 'Должный пользователь',
  overSpeedAlarm: 'Тревога превышения',
  changeIcon: 'символ',
  dealerNote: 'комментарий',
  noUserDue: 'Пользователь не должен',
  phoneLengththan3: 'Длина телефона должна быть больше 3',
  serialNumberInput: 'Ввод серийного номера',


  /************************************************/

  sending: 'Отправка.....Пожалуйста подождите...',
  sendFailure: 'Ошибка отправки',
  ctrlName: 'Имя',
  interval: 'Интервал времени',
  intervalError: 'Ошибка формата интервала',
  currectInterval: "Пожалуйста, введите правильный временной интервал!",
  intervalLimit: 'Номер центра 10-720,(Минут)',
  intervalLimit2: 'Номер центра 10-5400,(Секунд)',
  intervalLimit3: 'Номер центра 5-1440,(Минут)',
  intervalLimit4: 'Номер центра 3-999,(Секунд)',
  intervalLimit5: 'Номер центра 10-10800,(Секунд)',
  intervalLimit1: 'Номер центра 1-999,(Минут). 000?значит близкий режим возврата времени',
  intervalLimit6: 'Номер центра  1-65535,(Секунд)',
  intervalLimit7: 'Номер центра  1-999999,(Секунд)',
  intervalLimit8: 'Номер центра  0-255, 0 значит закрыть',
  intervalLimit9: 'Номер центра  3-10800,(Секунд)',
  intervalLimit10: 'Номер центра 3-86400,(Секунд)',
  intervalLimit11: 'Номер центра 180-86400,(Секунд)',
  intervalLimit22: 'Номер центра 60-86400,(Секунд)',
  intervalLimit23: 'Номер центра 5-60,(Секунд)',
  intervalLimit24: 'Номер центра 10-86400,(Секунд)',
  intervalLimit25: 'Номер центра 5-43200,(Минут)',
  intervalLimit12: 'Номер центра  10-60,(Секунд)',
  intervalLimit13: 'Установите интервал времени в диапазоне 1-24 (означает 1-24 часа) или 101-107 (означает 1-7 дней)',
  intervalLimit14: 'Установите интервал времени 10-3600, единица (секунда), по умолчанию: 10 с',
  intervalLimit15: 'Установить интервал времени в диапазоне 180-86400, единица (секунда), по умолчанию: 3600 с',
  intervalLimit16: 'Установите интервал времени в диапазоне 1-72, единица измерения (час), по умолчанию: 24 часа',
  intervalLimit17:'Диапазон настройки температуры -127-127, ед. (° C)',
  intervalLimit18: 'Установить интервал времени 5-18000, единица (секунда)',
  intervalLimit19: 'Установить интервал времени в диапазоне 10-300, единица измерения (секунда)',
  intervalLimit20: 'Установить интервал времени 5-399, единица (секунды)',
  intervalLimit21: 'Установить интервал времени 5-300, единица (секунды)',
  noInterval: 'Пожалуйста, введите интервал времени!',
  intervalTips: 'Выключите режим отслеживания, установив время пробуждения тревоги',
  phoneMonitorTips: 'После отправки команды устройство будет активно набирать номер обратного вызова для осуществления мониторинга.',
  time1: 'Время1',
  time2: 'Время2',
  time3: 'Время3',
  time4: 'Время4',
  time5: 'Время5',
  intervalNum: 'Интервал(минуты)',
  sun: 'Пн.',
  mon: 'Вт.',
  tue: 'Ср.',
  wed: 'Чт.',
  thu: 'Пт.',
  fri: 'Сб.',
  sat: 'Вс.',
  awakenTime: 'Время пробуждения',
  centerPhone: 'Основной телефон',
  inputCenterPhone: 'Ввести основной телефон!',
  phone1: 'Номер.1',
  phone2: 'Номер.2',
  phone3: 'Номер.3',
  phone4: 'Номер.4',
  phone5: 'Номер.5',
  inputPhone: 'Ввести телефон',
  offlineCtrl: 'В автономном режиме сочетание клавиш Ctrl?Автономные инструкции будут автоматически отправлены на устройство после подключения устройства',
  terNotSupport: 'Терминал не поддерживается',
  terReplyFail: 'Терминальный не отвечает',
  machineInfo: 'Информация Об Устройстве',


  /************************************************/

  alarmTypeScreen: 'Тип экрана сигнала тревоги',
  allRead: 'Все прочитано',
  read: 'Читать',
  noAlarmInfo: 'Нет чистой информации о тревоге',
  alarmTip: 'Tip : Отфильтруйте этот тип информации, об непроверенных аварийных сигналах ',


  /************************************************/

  updatePsw: 'Пароль',
  resetPsw: 'Сбросить пароль',


  /************************************************/

  multiSell: 'продаж',
  sell: 'продаж',
  sellSuccess: 'Продано успешно!',
  modifySuccess: 'Изменено успешно',
  modifyFail: 'Ошибка изменения',
  multiTransfer: 'Пакетное Перемещение',
  multiUserExpires: 'Пользовательское пакетное изменение истекает',
  batchModifying: 'Пакетная модификация',
  userTransfer: 'Перемещение пользователя',
  machineRemark: 'Замечание',
  sendCtrl: 'Отправить команду',
  ctrl: "Команда",
  ctrlLog: 'журнал команд',
  ctrlLogTips: 'журнал команд',
  s06Ctrls: ['Остановка двигателя', 'Перезапуск двигателя', 'расположение запроса'],
  ctrlType: 'Имя команды',
  resInfo: 'Результат',
  resTime: 'Время ответа',
  ctrlSendTime: 'Отправить Время',
  // csv??????

  choseCsv: 'Выберите файл CSV',
  choseFile: 'Выбрать файл',
  submit: 'Представить',
  targeDevice: 'Устройство назначения',
  csvTips_1: '1.Сохраните файл excel в формате CSV',
  csvTips_2: '2.Импорт CSV-файла в систему',
  importExplain: 'Выгрузить инструкцию:',
  fileDemo: 'Пример формата файла',


  // add

  sendType: 'Тип',
  onlineCtrl: 'Онлайн инструкция',
  offCtrl: 'Оффлайн инструкция',
  resStatus: ['Не отправлено", "Истек", " выдано', 'Выполнено успешно", "Успешное исполнение", " нет ответа'],
  /************************************************/

  addSubordinateClient: 'Добавить субсчет',
  noSubordinateClient: 'Нет субсчета',
  superiorCustomerEmpty: 'Выбрать руководителя',
  noCustomerName: "Имя клиента",
  noLoginAccount: 'Пользователь',
  noPsw: 'Пароль',
  noConfirmPsw: 'Подтвердите пароль',
  pswNotAtypism: 'Пароль не правильный!',
  addSuccess: 'Успешно добавлен',
  superiorCustomer: 'Руководитель',
  addVerticalImei: 'Добавить вертикальный IMEI',
  noImei: 'Нет IMEI',
  addImei_curr: 'Введите номер IMEI ',
  no: 'Блок',
  aRowAImei: 'Один IMEI для одной строки',


  /*

  * dealer Beginning of interface translation 

  *

  * */

  //main.js

  imeiOrUserEmpty: "IMEI или пользователь пустой!",
  accountEmpty: 'IMEI/Имя/Требуемый аккаунт',
  queryNoUser: "запрос нет пользователя",
  queryNoIMEI: "запрос нет IMEI",
  imeiOrClientOrAccount: 'IMEI/Имя/Аккаунт',
  dueSoon: 'Истекает',
  recentlyOffline: 'Оффлайн',
  choseSellDeviceFirst: 'Пожалуйста выберите оборудование, которое нужно продать сперва!',
  choseDeviceFirst: 'Выберите устройство для первого перемещения!',
  choseDeviceExpiresFirst: 'Сначала выберите устройство для изменения!',
  choseRenewDeviceFirst: 'Сначала выберите устройство для обновления!',
  choseDeleteDeviceFirst: 'Сначала выберите устройство для удаления!',
  choseClientFirst: 'Пожалуйста, выберите клиента, чтобы двигаться первым!',


  //myClient.js

  clientList: 'Клиенты',
  accountInfo: 'Информация аккаунта',
  machineCount: 'Количество Устройств',
  stock: 'Итого',
  inventory: 'Сток',
  subordinateClient: 'Субсчёт',
  datum: 'Информация',
  monitor: 'Монитор',
  dueMachineInfo: 'Истекает',
  haveExpired: 'Истёк',
  timeRange: ['в пределах 7 дней', 'в пределах 30 дней', 'в пределах 60 дней', 'в пределах 7-30 дней', 'в пределах 30-60 дней'],
  offlineMachineInfo: 'информация об автономных устройствах',
  timeRange1: ['в течение часа', 'в течение дня', 'в течение 7 дней', 'в течение месяца', 'в течение 60 дней', 'более 60 дней', 'от 1 часа до 7 дней', 'от 1 дня до 7 дней', 'от 7 дней до 30 дней', 'от 30 дней до 60 дней'],
  offlineTime: 'Диапазон',
  includeSubordinateClient: 'Субсчёт',


  stopMachineInfo: 'Статическая информация',
  stopTime1: 'оставаться',
  unUseMachineInfo: 'Неактивная Информация',
  unUseMachineCount: 'Неактивная Сумма',


  sellTime: 'Время продажи',
  detail: 'Деталь',
  manageDevice: 'Детали',
  details: 'Детали',
  deleteSuccess: 'Удалено успешно!',
  deleteFail: 'Удалено успешно!',
  renewalLink: 'Обновление Ссылка',
  deleteGroupTips: 'Можно ли удалить группу',
  addGroup: 'Добавить группу',
  jurisdictionRange: 'Изменить функции',
  machineSellTransfer: 'Продажа Машин',
  monitorMachineGroup: 'Монитор',
  jurisdictionArr: ['Управление клиентом', 'Управление сообщениями', 'GEO установка', 'Управление сигналом тревоги', 'Управление виртуальной учетной записью', 'Инструкция по отправке'],
  confrimDelSim: 'Подтверждение удаления SIM-карты:',


  // ????

  sellDevice: 'Устройство Для Продажи',
  addClient: 'Добавить пользователей',
  deleteClient: 'Удалить пользователей',
  resetPassword: 'Сбросить пароль',
  transferClient: 'Переместить Пользователя',
  ifDeleteClient: 'Удалить',


  //myAccount

  myWorkPlace: 'Мой аккаунт',
  availablePoints: 'баланс',
  yearCard: 'Годовая карточка',
  lifetimeOfCard: 'Пожизненная карточка',
  oneyear: 'годовой',
  lifetime: 'пожизненный',
  commonImportPoint: 'Новая карта',
  lifetimeImportPoint: 'Новая карта на всю жизнь',
  myServiceProvide: 'Поставщик',
  moreOperator: 'Больше операций',
  dueMachine: 'зрелость',
  offlineMachine: 'не в сети',
  quickSell: 'Быстрая продажа',
  sellTo: 'Цель',
  machineBelong: 'Принадлежит',
  reset: 'Сброс',
  targetCustomer: 'Цель клиента',
  common_lifetimeImport: 'Годовая карта(0),Пожизненная карта(0)',
  cardType1: 'Тип',
  credit: 'Количество',
  generateImportPoint: 'Создать новую карту',
  generateImportPointSuc: 'Успешный импорт карты!',
  generateImportPointFail: 'Импорт карты прерван!',
  year_lifeTimeCard: 'Новая карта(0),Новая пожизненная карта(0)',
  generateRenewPoint: 'Создать оновлённую карту',
  transferTo: 'Перемещение',
  transferPoint: 'Количество',
  transferRenewPoint: 'Востановленная карта',
  pointHistoryRecord: 'История карт',
  newGeneration: 'Новый',
  operatorType: 'Операция',
  consume: 'Уничтожить',
  give: 'Дать',
  income: 'Прибыль',
  pay: 'Расходы',
  imeiErr: 'Пожалуйста, введите последние 6 цифр номера IMEI!',
  accountFirstPage: 'Домой',




  /*

  * dealer End of interface translation

  *

  * */

  // 1.4.8 risk control

  finrisk: 'Управление финансовыми рисками',
  attention: 'Подписка',
  cancelattention: 'Удалить подписку',
  poweroff: 'Выключить',
  inout: 'В и из',
  inoutEF: 'В и из территории',
  longstay: 'Заложить остаток',
  secsetting: 'Настройки точки заклада',
  EFsetting: 'Настройки гео-зоны',
  polygonFence: 'Зона полигона',
  cycleFence: 'Зона круга',
  haveBeenSetFence: 'Была установлена территория',
  haveBeenSetPoint: 'Была установлена заложенная точка',
  drawingFailed: 'Рисование не удалось. Пожалуйста, перерисуйте!',
  inoutdot: 'В и из',
  eleStatistics: 'Статистика электричества',
  noData: 'Нет данных',
  // ???????

  accountbe: 'Аккаунт',
  SMtype: 'Заложить тип точки',
  SMname: 'Заложить имя точки',
  time: 'Время',
  position: 'Позиция',
  lastele: 'Оставшийся аккумулятор',
  statisticTime: 'Дата статистики',
  searchalarmType: ['Все', 'оффлайн', 'отключение', 'в и за территорией', 'В и за', 'Заложить остановку'],
  remarks: ['Заложить точкук', 'Гарантийная компания', 'Убрать точку', 'Подержанный рынок'],
  focusOnly: 'Только фокус',
  autoRecord: 'Автоматическая запись',
  /******************************************************set command start**********************************8*/
  setCtrl: {
    text: 'Установить команду',
    value: ''
  },
  moreCtrl: {
    text: 'Дополнительные инструкции',
    value: ''
  },
  sc_openTraceModel: {
    text: 'Установить модель трассировки',
    value: '0'
  },
  sc_closeTraceModel: {
    text: 'Закрыть трассировочную модель',
    value: '1'
  },
  sc_setSleepTime: {
    text: 'Установить время сна',
    value: '2'
  },
  sc_setAwakenTime: {
    text: 'Установить время пробуждения',
    value: '3'
  },
  sc_setDismantleAlarm: {
    text: 'Установка сигнализации демонтажа',
    value: '4'
  },
  sc_setSMSC: {
    text: 'Основной номер',
    value: '5'
  },
  sc_delSMSC: {
    text: 'Удалить SMSC',
    value: '6'
  },
  sc_setSOS: {
    text: 'Добавить SOS',
    value: '7'
  },
  sc_delSOS: {
    text: 'Удалить SOS',
    value: '8'
  },
  sc_restartTheInstruction: {
    text: 'Сброс',
    value: '9'
  },
  sc_uploadTime: {
    text: 'Установить интервал загрузки',
    value: '10'
  },
  /*Alarm clock time setting

   Timing reback time setting

   Dismantle alarm setting

   Week mode open close*/

  sc_setAlarmClock: {
    text: 'Установить время тревоги',
    value: '11'
  },
  sc_setTimingRebackTime: {
    text: 'Установить время обратного отсчета',
    value: '12'
  },
  sc_openWeekMode: {
    text: 'Открыть режим недели',
    value: '13'
  },
  sc_closeWeekMode: {
    text: 'Закрыть режим недели',
    value: '14'
  },
  sc_powerSaverMode: {
    text: 'Интервал загрузки',
    value: '15'
  },
  sc_carCatchingMode: {
    text: 'Режим слежения',
    value: '16'
  },
  sc_closeDismantlingAlarm: {
    text: 'Закрыть тревогу демонтажа',
    value: '17'
  },
  sc_openDismantlingAlarm: {
    text: 'Открыть тревогу демонтажа',
    value: '18'
  },
  sc_VibrationAlarm: {
    text: 'Установка тревоги вибрации',
    value: '19'
  },
  sc_timeZone: {
    text: 'Настройка часового пояса',
    value: '20'
  },
  sc_phoneMonitor: {
    text: 'прослушивание телефонных разговоров',
    value: '21'
  },
  sc_stopCarSetting: {

    text: 'Параметры парковки',
    value: '22'

  },
  sc_bindAlarmNumber: {

    text: 'Количество привязанных сигнализаций',
    value: '23'

  },
  sc_bindPowerAlarm: {

    text: 'Сигнал тревоги отказа источника питания',
    value: '24'

  },
  sc_fatigueDrivingSetting: {

    text: 'Установка для усталостного вождения',
    value: '25'

  },
  sc_peripheralSetting: {

    text: 'периферийные настройки',
    value: '26'

  },
  sc_SMSAlarmSetting: {

    text: 'Установить SMS-оповещение',
    value: '27'

  },
  sc_autoRecordSetting: {

    text: 'Настройки автоматической записи',
    value: '28'

  },
  sc_monitorCallback: {

    text: 'прослушивание обратного вызова',
    value: '29'

  },
  sc_recordCtrl: {

    text: 'инструкции по записи',
    value: '30'

  },
  sc_unbindAlarmNumber: {
    text: 'Убрать номер тревоги',
    value: '31'
  },
  sc_alarmSensitivitySetting: {
    text: 'Настройка чувствительности вибрационной тревоги',
    value: '32'
  },
  sc_alarmSMSsettings: {
    text: 'Настройки SMS вибрационной сигнализации',
    value: '33'
  },
  sc_alarmCallSettings: {
    text: 'Настройки телефона с вибрационной сигнализацией',
    value: '34'
  },
  sc_openFailureAlarmSetting: {
    text: 'Включить сигнализацию сбоя питания',
    value: '35'
  },
  sc_restoreFactory: {
    text: 'Восстановить фабрику',
    value: '36'
  },
  sc_openVibrationAlarm: {
    text: 'Включить вибрационную сигнализацию',
    value: '37'
  },
  sc_closeVibrationAlarm: {
    text: 'Отключить вибрационную сигнализацию',
    value: '38'
  },
  sc_closeFailureAlarmSetting: {
    text: 'Отключить сигнал сбоя питания',
    value: '39'
  },
  sc_feulAlarm: {
    text: 'Настройка сигнализации количества масла',
    value: '40'
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: 'Режим энергосбережения',
    value: '41'
  },
  sc_sleepMode: {
    text: 'Режим сна',
    value: '42'
  },
  sc_alarmMode: {
    text: 'Режим тревоги',
    value: '43'
  },
  sc_weekMode: {
    text: 'Недельный режим',
    value: '44'
  },
  sc_monitorNumberSetting: {
    text: 'Настройка номера монитора',
    value: '45'
  },
  sc_singlePositionSetting: {
    text: 'режим одноразового позиционирования ',
    value: '46'
  },
  sc_timingworkSetting: {
    text: 'режим временной работы ',
    value: '47'
  },
  sc_openLightAlarm: {
    text: 'Датчик света открытого света',
    value: '48'
  },
  sc_closeLightAlarm: {
    text: 'Датчик тревоги ближнего света',
    value: '49'
  },
  sc_workModeSetting: {
    text: 'Настройка режима работы',
    value: '50'
  },
  sc_timingOnAndOffMachine: {
    text: 'Установка таймера',
    value: '51'
  },
  sc_setRealTimeTrackMode: {
    text: 'Установить режим чейза в реальном времени',
    value: '52'
  },
  sc_setClockMode: {
    text: 'Установить режим будильника',
    value: '53'
  },
  sc_openTemperatureAlarm:{
    text:'Включить температурный будильник',
    value:'54'
  },
    sc_closeTemperatureAlarm:{
    text:'Отключить температурный будильник',
    value:'55'
  },
  sc_timingPostbackSetting: {
    text: 'Настройки обратной передачи по времени',
    value: '56'
},
sc_remoteBoot: {
    text: 'Удаленная загрузка',
    value: '57'
},
sc_smartTrack: {
    text: 'Умное отслеживание',
    value: '58'
},
sc_cancelSmartTrack: {
    text: 'Отменить интеллектуальное отслеживание',
    value: '59'
},
sc_cancelAlarm: {
    text: 'Отменить будильник',
    value: '60'
},
sc_smartPowerSavingMode: {
  text: 'Установить режим Smart Power Saving',
  value: '61'
},
sc_monitorSetting: {
  text: "Монитор",
  value: '62'
},
// 指令重构新增翻译
sc_timedReturnMode: {
  text: 'Режим возврата по времени',
  value: '100'
},
sc_operatingMode: {
  text: 'Режим работы',
  value: '101'
},
sc_realTimeMode : {
  text: 'Режим позиционирования в реальном времени',
  value: '102'
},
sc_alarmMode : {
  text: 'Режим тревоги',
  value: '103'
},
sc_weekMode : {
  text: 'Недельный режим',
  value: '104'
},
sc_antidemolitionAlarm : {
  text: 'Сигнализация против сноса',
  value: '105'
},
sc_vibrationAlarm : {
  text: 'Вибрационная сигнализация',
  value: '106'
},
sc_monitoringNumber : {
  text: 'Контроль управления номерами',
  value: '107'
},
sc_queryMonitoring : {
  text: ' Контрольный номер запроса',
  value: '108'
},
sc_electricityControl : {
  text: 'Контроль нефти и электроэнергии',
  value: '109'
},
sc_SOSnumber : {
  text: 'Управление номерами SOS',
  value: '110'
},
sc_SleepCommand : {
  text: 'Команда сна',
  value: '201'
},
sc_RadiusCommand : {
    text: 'Радиус смещения',
    value: '202'
},
sc_punchTimeMode:{
    text:'打卡模式',
    value:'203'  
},
sc_intervelMode:{
    text:'时间段模式',
    value:'204'  
},
sc_activeGPS:{
    text:'激活GPS',
    value:'205'  
},
sc_lowPowerAlert: {
    text: 'Напоминание о низком заряде батареи',
    value: '206'
},
sc_SOSAlert: {
    text: 'SOS报警',
    value: '207'
},
mc_cuscom : {
  text: 'Индивидуальная инструкция',
  value: '1'
},
NormalTrack: 'Нормальный режим слежения',
listeningToNumber:'Вы уверены, что хотите проверить контрольный номер?',
versionNumber:'Вы уверены, что хотите проверить номер версии?',
longitudeAndLatitudeInformation:'Вы уверены, что хотите проверить информацию о широте и долготе?',
equipmentStatus:'Вы уверены, что хотите проверить статус?',
public_parameter:'Вы уверены, что хотите проверить параметры для？',
GPRS_parameter:'Вы уверены, что хотите проверить параметры GPRS для？',
deviceName: 'Вы уверены, что хотите свернуть устройство？',
SMS_alert:'Вы действительно хотите проверить сигнал напоминания по SMS?',
theBindingNumber:'ы уверены, что хотите проверить номер привязки？',
intervalTimeRange:' Диапазон установки интервала времени: 001-999, единица измерения (минуты)',
pleaseChoose:'Пожалуйста, выберите',
RealTimeCarChase: 'Вы уверены, что хотите установить это устройство в режим автомобильной погони в реальном времени?',
inputPhoneNumber: "Пожалуйста, введите номер телефона",
inputCorPhoneNumber: "Пожалуйста, введите правильный номер телефона",
autoCallPhone: "Совет: после успешного выполнения команды терминал автоматически наберет заданный номер",
limitTheNumberOfCellPhoneNumbers1:'Эта команда поддерживает до 5 номеров мобильных телефонов.',
limitTheNumberOfCellPhoneNumbers2:'Эта команда поддерживает до 3 номеров мобильных телефонов.',
equipmentTorestart:'Вы уверены, что хотите перезагрузить это устройство?',
remindTheWay:'Способ напоминания',
alarmWakeUpTime:'Время будильника',
alarmWakeUpTime1:'Время будильника 1',
alarmWakeUpTime2:'Время будильника 2',
alarmWakeUpTime3:'Время будильника 3',
alarmWakeUpTime4:'Время будильника 4',
sensitivityLevel:'Пожалуйста, выберите уровень чувствительности',
parking_time:'Время парковки',
selectWorkingMode:'Пожалуйста, выберите рабочий режим',
Alarm_value:'Значение тревоги',
Buffer_value:'Значение буфера',
gqg_disconnect:'отключить',
gqg_turnOn:'Включите',
Return_interval:'Интервал возврата',
gq_startTime:'Время начала',
gq_restingTime:'Время отдыха',
gq_Eastern:'Восточный часовой пояс',
gq_Western:'Западный часовой пояс',

gq_driver:'Сигнализация при вождении при усталости',
gq_deviceName:'Вы уверены, что хотите накатить это устройство?',
gq_noteAlarm:'Вы уверены, что хотите проверить сигнал напоминания по SMS?',
gq_restoreOriginal:'Вы уверены, что хотите восстановить это оборудование до заводского состояния?',
gq_normalMode:'Нормальный режим',
gq_IntelligentsleepMode:'Умный спящий режим',
gq_DeepsleepMode:'Режим глубокого сна',
gq_RemotebootMode:'Режим удаленной загрузки',
gq_IntelligentsleepModeTips:'Вы уверены, что хотите перейти в умный спящий режим?',
gq_DeepsleepModeTips:'Вы уверены, что хотите перейти в режим глубокого сна?',
gq_RemotebootModeTips:'Вы уверены, что хотите перейти в режим удаленной загрузки?',
gq_normalModeTips:'Вы уверены, что хотите перейти в обычный режим',
gq_sleepModeTips:'Вы действительно хотите перевести это устройство в спящий режим?',
gq_Locatethereturnmode:'Режим возврата позиционирования',
gq_regularWorkingHours:'Расчетный рабочий период',
gq_AlarmType:{
  text: 'Тип тревоги',
  value: '111'
},
IssuedbyThePrompt:'Команда отправлена, дождитесь ответа устройства.',
platformToinform:'Уведомление платформы',
gq_shortNote:'SMS-уведомление', 

  /************?????**********************/

  closeDismantlingAlarm: 'Закрытие демонтаж сигнализации',
  openDismantlingAlarm: 'Открыть демонтаж сигнализации',
  closeTimingRebackMode: 'Закрыть режим обратного отсчета времени',
  minute: 'минуты',
  timingrebackModeSetting: 'Интервал загрузки:',
  setWakeupTime: 'Установить время пробуждения:',
  weekModeSetting: 'Настройка режима недели:',
  closeWeekMode: 'Закрыть режим недели',
  setRealtimeTrackMode: 'Установить режим трека в реальном времени',
  fortification: 'Заблокировать',
  disarming: 'Разблокировать',
  settimingrebackmodeinterval: 'Установить интервал режима обратной синхронизации:',
  oilCutCommand: 'Команда отсечки масла',
  restoreOilCommand: 'Команда восстановки масла',
  turnNnTheVehiclesPower: 'Включить питание транспортного средства',
  turnOffTehVehiclesPower: 'Выключить питание транспортного средства',
  implementBrakes: 'Выполнить торможение',
  dissolveBrakes: 'Отпустить тормоза',
  openVoiceMonitorSlarm: 'Открыть голосовой монитор сигнализации',
  closeVoiceMonitorAlarm: 'Закрыть голосовой монитор сигнализации',
  openCarSearchingMode: 'открытый режим поиска автомобиля',
  closeCarSearchingMode: 'закрытый режим поиска автомобиля',
  unrecognizedCommand: 'Нераспознанная команда',
  commandSendSuccess: 'Поздравляем! Устройство выполнило команду успешно!',


  /********************************************??????**************************************************/





  /********************************************??????**************************************************/

  queryCtrl: {

    text: 'Query command',
    value: ''



  },
  /*??????*/

  qc_softwareVersion: {



    text: 'Версия запроса',
    value: '1'

  },
  qc_latlngInfo: {

    text: 'Запрос широты и долготы',
    value: '2'

  },
  qc_locationHref: {

    text: 'Параметр запроса',
    value: '3'

  },
  qc_status: {

    text: 'Статус запроса',
    value: '4'

  },
  qc_gprs_param: {

    text: 'Запрос GPRS парам',
    value: '5'

  },
  qc_name_param: {

    text: 'Обзор',
    value: '6'

  },
  qc_SMSReminderAlarm_param: {

    text: 'Запрос SMS напоминания о тревоге',
    value: '7'

  },
  qc_bindNumber_param: {

    text: 'Номер привязки запроса',
    value: '8'

  },


  /********************************************??????**************************************************/



  /*******************************************??????***************************************************/



  controlCtrl: {

    text: 'Управляющая команда',
    value: ''

  },
  cc_offOilElectric: {

    text: 'Выкл. электр. насос',
    value: '1'

  },
  cc_recoveryOilElectricity: {

    text: 'Перезапуск электр. насоса',
    value: '2'

  },
  cc_factorySettings: {

    text: 'Заводские настройки',
    value: '4'

  },
  cc_fortify: {

    text: 'Поддержка',
    value: '75'

  },
  cc_disarming: {

    text: 'Разблокировать',
    value: '76'

  },
  cc_brokenOil: {

    text: 'Инструкция по срезу топлива',
    value: '7'

  },
  cc_RecoveryOil: {

    text: 'Контур восстановления топлива',
    value: '8'

  },


  /*******************************************??????***************************************************/





  /*

* m--?min

* 2018-01-23

* */

  km: 'КМ', mileage: 'Пробег', importMachine: 'Добавить новый IMEI', transferImportPoint: 'Создать новую карту',
  machineType1: 'Модель', confirmIMEI: 'Пожалуйста, убедитесь в правильности номера IMEI и названия модели перед импортом, так как операция необратимая.',
  renew: 'Возобновить', deductPointNum: 'Количество', renewSuccess: 'Возобновлено успешно!',
  wireType: [

    {

      text: 'Монтаж',
      value: false

    },
    {

      text: 'беспроводной',
      value: true

    }

  ],
  vibrationWays: [

    {

      text: 'Платформа',
      value: 0

    },
    {

      text: 'Платформа + сообщение',
      value: 1

    },
    {

      text: 'Платформа + сообщение + телефон',
      value: 2

    }

  ],
  addMachineType: [{

    text: 'S06',
    value: '3'

  },
  // SO6??-----start-----------

  {

    text: 'GT06',
    value: '8'

  }, {

    text: 'S08V',
    value: '9'

  }, {

    text: 'S01',
    value: '10'

  }, {

    text: 'S01T',
    value: '11'

  }, {

    text: 'S116',
    value: '12'

  }, {

    text: 'S119',
    value: '13'

  }, {

    text: 'TR06',
    value: '14'

  }, {

    text: 'GT06N',
    value: '15'

  }, {

    text: 'S101',
    value: '16'

  }, {

    text: 'S101T',
    value: '17'

  }, {

    text: 'S06U',
    value: '18'

  }, {

    text: 'S112U',
    value: '19'

  }, {

    text: 'S112B',
    value: '20'

  }

    // SO6??-----end-----------

    , {

    text: 'S15/S02F',
    value: '1'

  }, {

    text: 'S05',
    value: '2'

  }, {

    text: 'SW06',
    value: '4'

  }, {

    text: 'S001',
    value: '5'

  }, {

    text: 'S08',
    value: '6'

  }, {

    text: 'S09',
    value: '7'

  }],


  /*

  * 2018-02-02

  * */

  maploadfail: "К сожалению, загрузка текущей карты не удалась. Хотите перейти на другую карту? ",


  /*

  2018-03-06?? ????

  * */

  cc_openPower: {

    text: 'Открыть питание машины',
    value: '7'

  },
  cc_closePower: {

    text: 'Закрыть питание машины',
    value: '8'

  },
  cc_openBrake: {

    text: 'Открыть тормоза',
    value: '9'

  },
  cc_closeBrake: {

    text: 'Закрыть тормоза',
    value: '10'

  },
  cc_openAlmrmvoice: {

    text: 'Открыть тревогу',
    value: '11'

  },
  cc_closeAlmrmvoice: {

    text: 'Закрыть тревогу',
    value: '12'

  },
  /*2018-03-06?? ????

  * */

  cc_openFindCar: {

    text: 'Открыть поиск автомобиля',
    value: '13'

  },
  cc_closeFindCar: {

    text: 'Закрыть поиск автомобиля',
    value: '14'

  },
  /*2018-03-19

  * */

  EF: 'Гео-зона',


  /*

  2018-03-29?????

  * */

  exData: ['Питание', 'вольтаж', 'Топливо', 'Температура', 'температура'],


  /*

  2018-04-10

  * */

  notSta: 'LBS не включен в статистику',


  // ????

  fuelSetting: 'Настройка топлива',
  mianFuelTank: 'Основной топливный бак',
  maximum: 'Макс',
  minimum: 'Мин',
  FullTankFuel: 'Полный бак топлива',
  fuelMinValue: 'Объем топлива не может быть менее 10 л.',
  standardSetting: 'Стандартные настройки',
  emptyBoxMax: 'Максимально пустой',
  fullBoxMax: 'Максимально полный',
  fuelStatistics: 'Статистика топлива',
  settingSuccess: 'Успешно',
  settingFail: 'Нудачно',
  pleaseInput: 'Пожалуйста, введите',
  fuelTimes: 'Время Топлива',
  fuelTotal: 'Общее Количество Топлива',
  refuelingTime: 'Время заправки',
  fuelDate: 'Дата Топлива',
  fuel: 'Топливо',
  fuelChange: 'Замена Топлива',
  feulTable: 'Таблица Анализа Топлива',
  addFullFilter: 'Пожалуйста, добавьте полный фильтр',
  enterIntNum: 'Введите положительное целое число',
  // ????

  tempSta: 'Статистика температуры',
  tempTable: 'Таблица анализа температуры',
  industrySta: 'Отраслевая статистика',
  temperature: 'Температура',
  temperature1: 'Температура1',
  temperature2: 'Температура2',
  temperature3: 'Температура3',
  tempRange: 'Температурный диапазон',
  tempSetting:'Настройка температуры',
  tempSensor:'Датчик температуры',
  tempAlert:'После выключения датчика температуры он не будет получать данные о температуре!',
  phoneNumber: 'Мобильный номер',
  sosAlarm: 'SOS тревога',
  undervoltageAlarm: 'аварийный сигнал о пониженном напряжении',
  overvoltageAlarm: 'аварийный сигнал о повышенном напряжении',
  OilChangeAlarm: 'Сигнализации замена масла',
  accDetection: 'Обнаружение зажигания',
  PositiveAndNegativeDetection: 'Положительное и отрицательное обнаружение',
  alermValue: 'Значение сигнала тревоги',
  bufferValue: 'Значение буфера',
  timeZoneDifference: 'Разница в часовых поясах ',
  meridianEast: 'Меридиан Восточный',
  meridianWest: 'Западный Меридиан',
  max12hour: "Не может быть более 12 часов",

  trackDownload: 'Скачать трек',
  download: 'Скачать',
  multiReset: 'Массовый сброс',
  resetSuccess: 'Сбросить успех',
  multiResetTips: 'Советы： После сброса такие данные, как время активации устройства и трек, будут очищены, а состояние устройства не включено.',
  point: 'Точка',
  myplace: 'Мое место',
  addPoint: 'Добавить точку',
  error10018: 'недостаточно точек импорта',
  error110:'Объект не существует',
  error109:'Превышен максимальный лимит',
  error20013:'Тип устройства не существует',
  error90001:'Серийный номер типа устройства не может быть пустым',
  error20003:'Имей не может быть пустым',
  inputName: 'Пожалуйста, введите имя',
  virtualAccount: 'Виртуальная учетная запись',
  createTime: 'Создать время',
  permission: 'Разрешение',
  permissionRange: 'компетентность',
  canChange: 'Изменяемая функция',
  fotbidPassword: 'Изменить пароль',
  virtualAccountTipsText: 'При создании виртуальной учетной записи это псевдоним учетной записи текущего авторизованного дилера. Вы можете установить разрешения для виртуальной учетной записи. Чтобы создать виртуальную учетную запись для конечного пользователя, вы можете сначала изменить тип конечного пользователя на посредника, затем войти в систему с этим посредником, создать виртуальную учетную запись, а затем изменить тип обратно на конечного пользователя.',
  noOperationPermission: 'Виртуальная учетная запись не имеет разрешения на работу',
  number: 'Номер',
  rangeSetting: 'Scope Setting',
  setting: 'Настройки',
  // 1.6.1
  duration: 'продолжительность',
  voltageSta: 'Статистика напряжения',
  voltageAnalysis: 'Анализ напряжения',
  voltageEchart: 'Лист анализа напряжения',
  platformAlarm: 'Тревога платформы',
  platformAlarm1: 'телефон',
  platformAndPhone: 'телефон+Тревога платформы',
  smsAndplatformAlarm: 'SMS + Тревога платформы',
  smsAndplatformAlarm1: 'SMS',
  smsAndplatformAlarmandPhone: 'Тревога платформы + SMS + телефон',
  smsAndplatformAlarmandPhone1: 'SMS + телефон',
  more_speed: 'Km',
  attribute: 'Атрибут',
  profession: 'Профессиональный',
  locationPoint: 'Точка местоположения',
  openPlatform: 'Открытый API',
  experience: 'демонстрация',
  onlyViewMonitor: 'Только просмотр монитора',
  inputAccountOrUserName: 'Пожалуйста, введите номер учетной записи или имя пользователя',
  noDeviceTips: 'Не нашел нужную информацию об оборудовании, проверь клиентов',
  noUserTips: 'Не нашел нужную информацию пользователя, проверьте оборудование',
  clickHere: 'нажмите здесь',
  pointIntervalSelect: 'Интервал отслеживания точки',
  payment: 'Платеж',
  pleaceClick: 'Клик',
  paymentSaveTips: 'Совет по безопасности. Пожалуйста, подтвердите с истекшим сроком действия ссылки',
  fuelAlarmValue: 'Аварийное значение количества масла',
  fuelConsumption: 'Расход масла',
  client: 'Пользователь',
  create: 'Добавить',
  importPoint: 'Карта импорта',
  general: 'Карта импорта',
  lifelong: 'на протяжении всей жизни',
  renewalCard: 'Обновление карты',
  settingFuelFirst: 'Пожалуйста, установите значение тревоги количества масла перед отправкой команды!',
  overSpeedSetting: 'Установка разноса',
  kmPerHour: 'km/h',
  times: 'Таймс',
  total: 'Итого',
  primary: 'Главный',
  minor: 'Заместитель',
  unActiveTips: 'Устройство не активировано и не может быть использовано.',
  arrearsTips: 'Устройство имеет задолженность и не может использовать эту функцию',
  loading: 'Загрузка данных ...',
  expirationReminder: 'напоминание',
  projectName: 'Имя проекта',
  expireDate: 'Срок годности',
  changePwdTips: 'Ваш пароль слишком прост, существует угроза безопасности. Пожалуйста, измените свой пароль как можно скорее',
  pwdCheckTips1: 'Предложения 6-20 букв, цифр или символов',
  pwdCheckTips2: 'Введенный вами пароль слишком прост',
  pwdCheckTips3: 'Ваш пароль может быть более сложным',
  pwdCheckTips4: 'Ваш пароль в безопасности',
  pwdLevel1: 'Простой',
  pwdLevel2: 'Средний',
  pwdLevel3: 'Безопасный',
  comfirmChangePwd: 'Подтвердить изменение пароля',
  notSetYet: 'Не установлено в данный момент',
  liter: 'Литровый',
  arrearageDayTips: 'дня',
  todayExpire: 'Сегодня',
  forgotPwd: 'Забыли пароль?',
  forgotPwdTips: 'Пожалуйста, свяжитесь с продавцом, чтобы изменить свой пароль.',
  //1.6.7
  commonProblem: 'Общая проблема',
  instructions: 'инструкции',
  webInstructions: 'WEB инструкции',
  appInstructions: 'APP инструкции',
  acceptAlarmNtification: 'Уведомление о тревоге',
  alarmPeriod: 'Период тревоги',
  whiteDay: 'День',
  blackNight: 'Ночь',
  allDay: 'Весь день',
  alarmEmail: 'Тревога',
  muchEmailTips: "Можно ввести несколько почтовых ящиков, разделенных символом ';'",
  newsCenter: 'Центр уведомлений',
  allNews: 'Все',
  unReadNews: 'Непрочитанный',
  readNews: 'Читать',
  allTypeNews: 'Типы уведомлений',
  alarmInformation: 'Информация о тревоге',
  titleContent: 'заглавие',
  markRead: 'Отметить прочитанным',
  allRead: 'Все читают',
  allDelete: 'Удалить все',
  selectFirst: 'Выберите Сначала!',
  updateFail: 'Не удалось обновить!',
  ifAllReadTips: 'Все читают？',
  ifAllDeleteTips: 'Удалить все?',
  stationInfo: 'Информация о станции',
  phone: 'Телефон',
  //1.6.72
  plsSelectTime: 'Пожалуйста, выберите время!',
  customerNotFound: 'Не могу найти клиента',
  Postalcode: 'расположение',
  accWarning: 'ACC сигнализация',
  canInputMultiPhone: 'Вы можете ввести несколько номеров мобильных телефонов, используйте отдельно;',
  noLocationInfo: 'Информация о местонахождении устройства отсутствует.',
  //1.6.9
  fenceName: 'имя забора',
  fenceManage: 'Управление забором',
  circular: 'круг',
  polygon: 'полигон',
  allFence: 'Все заборы',
  shape: 'форма',
  stationNews: 'Сообщение сайта',
  phonePlaceholder: 'Вы можете ввести несколько номеров, разделенных знаком«, »',
  addressPlaceholder: 'Пожалуйста, введите адрес и почтовый индекс в порядке, разделенном ","',
  isUnbind: 'Хотите отсоединить»',
  alarmCar: 'Сигнализация автомобиля',
  alarmAddress: 'местоположение тревоги',
  chooseAtLeastOneTime: 'Выберите хотя бы один раз',
  alarmMessage: 'Нет подробностей этой информации о тревоге',
  navigatorBack: 'вернуться к начальнику',
  timeOverMessage: 'Срок действия пользователя не может быть больше срока действия платформы',
  //1.7.0
  userTypeStr: 'Тип пользователя',
  newAdd: 'новый',
  findAll: 'общий',
  findStr: 'Соответствующие данные',
  customColumn: 'Настроить',
  updatePswErr: 'Не удалось обновить пароль',
  professionalUser: 'Профессиональный пользователь или нет?',
  confirmStr: 'подтвердить',
  inputTargetCustomer: 'Введите целевой клиент',
  superiorUser: 'Улучшенный пользователь',
  speedReport: 'Отчет о скорости',
  createAccount: 'Создать аккаунт',
  push: 'нажим',
  searchCreateStr: 'Он будет управлять учетной записью и перенести устройство в эту учетную запись.',
  allowIMEI: 'Разрешить вход в IMEI',
  defaultPswTip: 'Пароль по умолчанию - последние 6 цифр IMEI',
  createAccountTip: 'Успешно создан аккаунт и передано устройство',
  showAll: 'Показать все',
  bingmap: 'Bing Map',
  areaZoom: 'Увеличить',
  areaZoomReduction: 'Восстановить зум',
  reduction: 'снижение',
  saveImg: 'Сохранить как изображение',
  fleetFence: 'Флот забор',
  alarmToSub: 'Уведомление о тревоге',
  bikeFence: 'Велосипедный забор',
  delGroupTip: 'Удалить не удалось, пожалуйста, сначала удалите пункты.',
  isExporting: 'Экспорт ...',
  addressResolution: 'Разрешение адреса ...',
  simNOTip: 'Номер SIM-карты может быть только номером',
  unArrowServiceTip: 'Время истечения срока действия пользователя для следующих устройств превышает время истечения срока действия платформы, пожалуйста, выберите заново. Номер устройства:',
  platformAlarmandPhone: 'Платформа Сигнализация + Тел',
  openLightAlarm: 'Датчик света открытого света',
  closeLightAlarm: 'Датчик тревоги ближнего света',
  ACCAlarm: 'ACC сигнализация',
  translateError: 'Передача не удалась, целевой пользователь не имеет разрешения',
  distanceTip: 'Нажмите OK, дважды щелкните, чтобы закончить',
  workMode: 'Рабочий режим',
  workModeType: '0: нормальный режим; 1 Интеллектуальный режим сна; 2 Режим глубокого сна',
  clickToStreetMap: 'Нажмите для открытия карты',
  current: 'ток',
  remarkTip: 'Примечание: не продавайте карточки разных типов одновременно',
  searchRes: 'Результат поиска',
  updateIcon: 'Изменить значок',
  youHaveALarmInfo: 'У вас есть предупреждение',
  moveInterval: 'Интервал тренировки',
  staticInterval: 'Интервал времени бездействия',
  notSupportTraffic: 'Coming soon.',
  ignite:'Acc ON',
  flameout:'Acc OFF',
  generateRenewalPointSuc:'Успешно создавать точки обновления',
  noGPSsignal:'Не позиционируется',
  imeiErr2:'Пожалуйста, введите хотя бы последние 6 цифр номера imei',
  searchCreateStr2:'Это позволит создать учетную запись и перенести это устройство на имя учетной записи.',
  addUser:'Добавить пользователя',
  alarmTemperature:'Значение аварийной температуры',
  highTemperatureAlarm:'Сигнализация высокой температуры',
  lowTemperatureAlarm:'Сигнализация низкой температуры',
  temperatureTip:'Пожалуйста, введите значение температуры！',
  locMode:'режим позиционирования',
  imeiInput:'Пожалуйста, введите номер IMEI',
  noResult:'Нет подходящих результатов',
  noAddressKey:'Адрес не доступен',
  deviceGroup: 'Управлять Группой',
  shareManage: 'Доля управления ',
  lastPosition: 'Последняя позиция',
  defaultGroup: 'Группа по умолчанию',
  tankShape: 'Форма топливного бака',
  standard: 'стандарт',
  oval: 'овальный',
  irregular: 'нерегулярный',
  //1.8.4
  inputAddressOrLoc:'Пожалуйста, введите адрес / широту и долготу',
  inputGroupName:'Имя входной группы',
  lock:'замок',
  shareHistory:'Поделиться историей воспроизведения',
  tomorrow:'завтра',
  threeDay:'3 дня',
  shareSuccess:'Поделитесь ссылкой на успех',
  effective:'эффективный',
  lapse:'инвалид',
  copyShareLink:'Копировать ссылку успешно',
  openStr:'НА',
  closeStr:'Выключено',
  linkError:'Ошибка ссылки',
  inputUserName:'Введите имя пользователя',
  barCodeStatistics:'статистика штрих-кода',
  barCode:'Штрих-код',
  sweepCodeTime:'Время сканирования',
  workModeType2: '1 Интеллектуальный режим сна ； 2 Режим глубокого сна ； 3 Режим дистанционного переключения',
  remoteSwitchMode: 'Режим удаленного переключения',
   saleTime :'Дата продажи',
   onlineTime :'Онлайн время',
   dayMileage:'Пробег сегодня',
   imeiNum:'Номер IMEI',
   overSpeedValue:'Значение превышения скорости',
   shareNoOpen:'Поделиться ссылкой не включено',
   addTo2:'клиент',
   overDue: 'Истекло',
     openInterface: 'Открытый интерфейс',
     privacyPolicy: 'Политика конфиденциальности',
    serviceTerm: 'Условия использования',
     importError: 'Не удалось добавить, номер устройства (IMEI) должен быть 15-значным числом',
     importResult: 'Результаты импорта',
     totalNum: 'общий',
     successInfo: 'успех',
     errorInfo: 'Нормально',
     repeatImei: 'IMEI repeat',
includeAccount: 'субсчет',
formatError:'бесформенный',
importErrorInfo:'Пожалуйста, введите 15-значный номер IMEI',
totalMileage: 'Общий пробег',
     totalOverSpeed: 'Общее превышение скорости (раз)',
     totalStop: 'Total stop (times)',
     totalOil: 'Total oil',
    timeChoose: 'Выбор времени',
    intervalTime: 'Интервал времени',
    default: 'По умолчанию',
     idleSpeedStatics: 'Статистика холостого хода',
     offlineStatistics: 'Офлайн-статистика',
     idleSpeed: 'Скорость холостого хода',
     idleSpeedTimeTip1: 'Простой не может быть пустым',
     idleSpeedTimeTip2: 'Время простоя должно быть положительным целым числом',
    averageSpeed: 'Средняя скорость',
     averageOil: 'Средний расход топлива',
     oilImgTitle: 'Диаграмма анализа нефти',
     oilChangeDetail: 'Детали замены топлива',
machineNameError:"Имя устройства не может содержать специальные символы (/ ')",
remarkError:'Информация примечания не может превышать 50 слов',
defineColumnTip:'Проверьте до 12 пунктов',
pswCheckTip: 'Предложение представляет собой комбинацию из 6-20 цифр, букв и символов',
     chooseGroup: 'Пожалуйста, выберите группу',
     chooseAgain: 'Вы можете запрашивать данные только за последние шесть месяцев, пожалуйста, выберите снова！',
     noDataTip: 'Нет данных！',
     noMachineNameError: 'Пожалуйста, выберите устройство',
     loginAccountError: 'Учетная запись не может содержать 15 цифр！',
     includeExpire: 'срок действия которого истекает',
groupNameTip: 'Имя группы не может быть пустым',
    outageTips:'Вы уверены, что масло отрезали?',
    powerSupplyTips:'Вы уверены, что восстановить нефть?',
    centerPhoneTips:'Пожалуйста, введите номер',
    centerPhoneLenTips:'Пожалуйста, введите 8-20 цифр',
    passworldillegal: "Есть недопустимые символы",
    // 2.0.0 POI，权限版本
  singleAdd:'одиночное добавление',
  batchImport:'Пакетный импорт',
  name:'имя',
  icon:'значок',
  defaultGroup:'группа по умолчанию',
  remark:'Примечание',
  uploadFile:'Загрузить файл',
  exampleDownload:'пример загрузки',
  uploadFiles:'загрузить файл',
  poiTips1:'Вы можете импортировать POI, загрузив файл Excel со связанной информацией. Пожалуйста, следуйте формату примера, чтобы подготовить файл',
  poiTips2:'Имя: Обязательно, не более 32 знаков',
  poiTips3:'Значок: обязательно, введите 1,2,3,4',
  poiTips4:'широта：обязательно',
  poiTips5:'долгота：обязательно',
  poiTips6:'Название группы: Необязательно, не более 32 символов. Если имя группы не указано, точка POI принадлежит группе по умолчанию. Если заполненное имя группы согласуется с именем созданной группы, точка POI принадлежит созданной группе. Название группы не создано, система добавит группу',
  poiTips7:'Замечания: необязательно, не более 50 символов',
  // 权限相关
  roleLimit: 'Разрешения на роль',
  operateLog: 'Журнал работы',
  sysAccountManage: 'Аккаунт органа власти',
  rolen: 'Роли',
  rolename: 'Имя роли',
  addRole: 'Новая роль',
  editRole: 'Изменить роль',
  deleteRole: 'Удалить роль',
  delRoleTip: 'Вы уверены, что хотите удалить эту роль?',
  delAccountTip: 'Вы уверены, что хотите удалить эту учетную запись?',
  limitconfig: 'Профиль прав',
  newAccountTip1: 'Учетная запись авторизации аналогична старой виртуальной учетной записи и является дополнительной учетной записью администратора. Администраторы могут создавать привилегированные учетные записи и назначать разные роли привилегированным учетным записям, чтобы разные учетные записи могли видеть различное содержимое и операции на платформе.',
  newAccountTip2: 'Процесс создания учетной записи разрешения:',
  newAccountTip31: '1. На странице управления ролями',
  newAccountTip32: 'Новая роль',
  newAccountTip33: ', И настроить разрешения для роли;',
  newAccountTip4: '2. На странице управления учетной записью центра создайте новую учетную запись и назначьте роли учетной записи.',
  newRoleTip1: 'Администраторы могут создавать роли и настраивать различные разрешения на операции для разных ролей в соответствии с бизнес-потребностями в разных сценариях.',
  newRoleTip2: 'Например, настройте, есть ли у финансовой роли разрешение на обнаружение и мониторинг, есть ли у нее разрешение на добавление клиентов, есть ли у нее разрешение на изменение информации об устройстве и т. Д.',
  "refuelrate": "Скорость заправки",
  "refuellimit": "Когда увеличение количества масла в минуту больше xxxxL и меньше xxxxL, это считается заправкой.",
  "refueltip": "Максимальная скорость заправки не должна быть меньше минимальной!",
  viewLimitConf: 'Просмотр настроек разрешений',
  viewLimit: 'Просмотр разрешений',
  newSysAcc: 'Новая системная учетная запись',
  editSysAcc: 'Изменить учетную запись разрешения',
  virtualAcc: 'Виртуальный счет',
  oriVirtualAcc: 'Исходный виртуальный счет',
  virtualTip: 'Модуль виртуальной учетной записи был обновлен до модуля системной учетной записи, пожалуйста, создайте новую системную учетную запись',
  operaTime: 'Рабочее время',
  ipaddr: 'айпи адрес',
  businessType: 'Вид бизнеса',
  params: 'Параметр запроса',
  operateType: 'Тип операции',
  uAcc: 'учетная запись пользователя',
  uName: 'имя пользователя',
  uType: 'тип тип пользователя',
  logDetail: 'Детали журнала',
  delAccount: 'Удалить аккаунт',
  modifyTime: 'Изменить время',
  unbindlimit: 'Невозможно создать электронный забор одним щелчком мыши без соответствующих разрешений для устройства!',
  setSmsTip: 'Если вы настроили SMS-уведомление, вам нужно сначала включить уведомление платформы; если доставка уведомления платформы прошла успешно, SMS-уведомление не прошло успешно, вам необходимо снова включить SMS-уведомление',
  cusSetComTip: 'Отказ от ответственности: риск, связанный с пользовательскими инструкциями, не имеет ничего общего с платформой.',
  cusSetComPas: 'Пожалуйста, введите пароль текущей учетной записи',
  cusSetComDes1: 'Пользовательские инструкции поддерживают только онлайн-инструкции.',
  cusSetComDes2: 'Если устройство не отвечает в течение двух минут после отправки команды, процесс завершается, и состояние команды оценивается как отсутствие ответа.',
  cueSetComoffline: 'Устройство не отвечает, и отправка специальной команды не удалась!',
  fbType: 'Тип обратной связи',
  fbType1: 'Консультативный',
  fbType2: 'Неисправность',
  fbType3: 'Пользовательский опыт',
  fbType4: 'Предложения по новым функциям',
  fbType5: 'разное',
  upload: 'Загрузить',
  uploadImg: 'загрузить изображение',
  uploadType: 'Пожалуйста, загрузите файлы типа .jpg .png .gif .jpeg',
  uploadSize: 'Размер загружаемого файла не может превышать 3 МБ.',
  fbManager: 'Управление обратной связью',
  blManager: 'Управление объявлениями',
  fbUploadTip: 'Выберите тип отзыва',
  menuPlatform: "Новости платформы",
  menuFeedback: "Обратная связь",
  menuBulletin: "Объявление платформы",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "Поведение при вождении",
  BtyjdfghtwsrgGHFEEGRDAF: "Быстрое ускорение",
  BtyuwyfgrWERERRTHDAsdDF: "Быстрое замедление",
  Be2562h253grgsHHJDbRDAF: "Резкий поворот",
  celTemperature:'Температура по Цельсию'
}
// 权限tree
lg.limits = {
  "ACC_statistics": "Статистика Зажигания",
  "Account_Home": "Домой",
  "Add": "новый",
  "Add_POI": "Добавить POI",
  "Add_customer": "Добавить пользователей",
  "Add_device_group": "Добавить группу устройств",
  "Add_fence": "Добавить забор",
  "Add_sharing_track": "Добавить трек для обмена",
  "Add_system_account": "Новая учетная запись разрешения",
  "Alarm_details": "Детали будильника",
  "Alarm_message": "Сообщение о тревоге",
  "Alarm_overview": "Обзор тревоги",
  "Alarm_statistics": "Отчет о тревоге",
  "All_news": "Все",
  "Associated_equipment": "Объединенное устройство",
  "Available_points": "баланс",
  "Barcode_statistics": "статистика штрих-кода",
  "Batch_Import": "Пакетный импорт",
  "Batch_renewal": "возобновлять",
  "Batch_reset": "Массовый сброс",
  "Bulk_sales": "продаж",
  "Call_the_police": "Тревога",
  "Customer_details": "Детали клиента",
  "Customer_transfer": "'Переместить Пользователя",
  "Delete_POI": "Удалить POI",
  "Delete_account": "Удалить аккаунт",
  "Delete_customer": "Удалить пользователей",
  "Delete_device": "Удалить устройство",
  "Delete_device_group": "Удалить группу устройств",
  "Delete_fence": "Удалить геозону",
  "Delete_role": "Удалить роль",
  "Device_List": "Список устройств",
  "Device_grouping": "Управлять Группой",
  "Device_transfer": "Перенос устройства",
  "Due_reminder": "напоминание",
  "Edit_details": "Редактировать детали",
  "Equipment_management": "Устройства",
  "My_clinet": "Устройства",
  "Fence": "Гео-зона",
  "Fence_management": "Управление забором",
  "Generate": "генерировать",
  "Generate_lead-in_points": "Создать новую карту",
  "Generate_renewal_points": "Создать оновлённую карту",
  "Have_read": "Очистить",
  "Idle_speed_statistics": "Статистика холостого хода",
  "Import": "Импортировать",
  "Import_Device": "Добавить новый IMEI",
  "Industry_Statistics": "Отраслевая статистика",
  "Location_monitoring": "Монитор",
  "Log_management": "Управление журналом",
  "Mark_read": "Отметить прочитанным",
  "Menu_management": "Управление меню",
  "Message_Center": "Центр уведомлений",
  "Mileage_statistics": "'Отчет о пробеге",
  "Modify_POI": "Изменить POI",
  "Modify_device_details": "Изменить сведения об устройстве",
  "Modify_device_group": "Изменить группу устройств",
  "Modify_role": "Изменить роль",
  "Modify_sharing_track": "Изменить дорожку обмена",
  "Modify_user_expiration": "Пользовательское пакетное изменение истекает",
  "More": "Подробнее",
  "My_client": "Бизнес",
  "New_role": "Новая роль",
  "New_users": "Добавить пользователя",
  "Oil_statistics": "Статистика топлива",
  "POI_management": "Управление POI",
  "Points_record": "История карт",
  "Push": "нажим",
  "Quick_sale": "Быстрая продажа",
  "Renew": "Возобновить",
  "Replay": "Воспроизведение",
  "Role_management": "Управление ролями",
  "Run_overview": "Перемещение Обзор",
  "Running_statistics": "Перемещение Обзор",
  "Sales_equipment": "Устройство Для Продажи",
  "Set_expiration_reminder": "Установить напоминание об истечении срока",
  "Share_track": "Поделиться историей воспроизведения",
  "Sharing_management": "Доля управления",
  "Speeding_detailed_list": "Pазноса",
  "Statistical_report": "Отчеты",
  "Stay_detailed_list": "Oставаться",
  "System_account_management": "Аккаунт органа власти",
  "Temperature_statistics": "Статистика температуры",
  "Transfer": "Перемещение пользователя",
  "Transfer_group": "Трансферная группа",
  "Transfer_point": "Создать новую карту",
  "Transfer_renewal_point": "Востановленная карта",
  "Trip_statistics": "Отчет о поездке",
  "Unlink": "Отменить связь",
  "View": "Вид",
  "View_POI": "Просмотр POI",
  "View_device_group": "Посмотреть группу устройств",
  "View_fence": "Проверить ограждение",
  "View_role": "Посмотреть роль",
  "View_sharing_track": "Посмотреть дорожку обмена",
  "Virtual_account": "Виртуальный счет",
  "Voltage_analysis": "Анализ напряжения",
  "Voltage_statistics": "Статистика напряжения",
  "batch_deletion": "Пакетное Удаление",
  "change_Password": "Изменить пароль",
  "delete": "Удалить",
  "edit": "Редактировать",
  "instruction": "Команды Устройств",
  "modify": "Обновить",
  "monitor": "Монитор",
  "my_account": "Главная",
  "reset_Password": "Сбросить пароль",
  "share_it": "Общее расположение",
  "sub_user": "Субсчёт",
  "track": "Отслеживание",
  "Custom_Order": "Индивидуальная инструкция",
  "GeoKey_Manager": "Управление GeoKey",
  "GeoKey_Update": "модифицировать",
  "GeoKey_Delete": "удалять",
  "GeoKey_Add": "добавить в",
  "GeoKey_View": "Посмотреть",
  "feedback_manager": "Управление обратной связью",
  "feedback_list": "Посмотреть",
  "feedback_handle": "Обработка обратной связи",
  "proclamat_manager": "Управление объявлениями",
  "proclamat_manager_list": "Посмотреть объявление",
  "proclamat_manager_update": "Объявление о модификации",
  "proclamat_manager_delete": "Удалить объявление",
  "proclamat_manager_save": "Новое объявление",
  "device_update_batch_model": "Пакетное изменение модели устройства"
}




// 问题文档的内容
lg.questionDocumentArr = [
  ['Q: Индикатор не горит после установки проводного устройства и находится в автономном режиме.', 'О: После выключения автомобиля используйте электрическую ручку и универсальный счетчик, чтобы измерить, соответствует ли напряжение подключенной линии диапазону напряжения GPS-трекера, как правило, 9-36В. <br/>Меры предосторожности при подключении: персонал, занимающийся установкой и монтажом, должен разбираться в автомобильной линии и обладать определенной практической способностью избежать повреждения вашего автомобиля в результате неправильного подключения'],
  ['Q: Проводное устройство или беспроводное устройство слежения в режиме реального времени, телефонный звонок или устройство фоновой загрузки IoT в автономном режиме', 'O：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Отправьте текстовое сообщение для перезагрузки, подождите несколько минут, чтобы увидеть, если он в сети. Как правило, отправьте RESET #, пожалуйста, свяжитесь с дилером, чтобы определить. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Соединение с сетью нестабильно. Пожалуйста, переместите автомобиль в зону с хорошим сигналом. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 3. После выполнения описанных выше шагов вы не смогли выйти в Интернет. Вам необходимо связаться с оператором мобильной связи, чтобы проверить, не является ли карта ненормальной.'],
  ['Q: Устройство не подключено к пакетам в начале и конце месяца.', 'О: Пожалуйста, проверьте, есть ли задолженность по карте. Если это задолженность, пожалуйста, вовремя пополните ее и возобновите использование.'],
  ['Q: Автомобиль за рулем, позиция GPS онлайн не обновляется.', 'O：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Проводное устройство может отправить текстовое сообщение STATUS #, чтобы проверить состояние приема спутникового сигнала.См. GPS: поиск спутника - это спутниковый сигнал, который был в поиске, в этой ситуации необходимо проверить место установки, установлен ли он в соответствии с инструкциями. Лицевой стороной вверх нет металлической крышки сверху. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Отправьте SMS STATUS #, статус возврата GPS: OFF., Пожалуйста, отправьте FACTORY # еще раз, после получения ответа OK, подождите 5 минут, чтобы увидеть, есть ли какие-либо обновления.<br/>&nbsp;&nbsp;&nbsp;&nbsp; 3. В соответствии с вышеуказанными двумя способами, неисправность не может быть устранена. Пожалуйста, свяжитесь с продавцом для ремонта.'],
  ['Q: Почему зарядная платформа долго заряжается, показывая, что она не полная?', 'О: Отображение мощности платформы основано на информации, возвращаемой устройством для анализа данных для определения текущей мощности устройства. В некоторых особых случаях может появиться решение для ошибки отображения мощности: <br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Данные питания устройства и данные позиционирования устройства загружаются вместе. Если аккумулятор заряжается в течение длительного времени, питание не изменилось. пожалуйста：①Переместите ваше устройство на 100-300 метров, чтобы обновить информацию о местоположении устройства, чтобы его данные об аккумуляторе и данные о местоположении могли быть переданы обратно на платформу вместе для обновления дисплея аккумулятора. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Определите, является ли питание полным в соответствии с изменением индикатора питания (в качестве примера взят S15). Шаги операции следующие: ①После зарядки в течение 8-10 часов индикатор питания загорится желтым и зеленым. После отсоединения зарядного кабеля вставьте кабель для зарядки, и индикатор питания снова станет желтым и зеленым в течение 15 минут. См. Руководство для других моделей. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 3, зарядка в течение длительного времени также полна электричества, эта ситуация может быть напряжение зарядного штекера ниже, чем 1A, пожалуйста, зарядите 5V, 1A зарядную головку в течение 8-10 часов.'],
  ['Q: Поддержка команды отключения питания GPS выдана успешно, почему автомобиль все еще не сломан?', 'O:После успешного выполнения инструкции по отключению питания оборудование должно быть выполнено при следующих условиях: <br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Убедитесь в правильности подключения оборудования и следуйте схеме подключения, приведенной в руководстве. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2, оборудование работает нормально, находится в статичном состоянии или в движении, имеет положение, не находится в автономном режиме, и скорость транспортного средства не превышает 20 километров, если транспортное средство находится в автономном режиме, не установлено или скорость транспортного средства превышает 20 километров, даже если команда питания отключена Доставка прошла успешно, и терминал не будет выполнен.'],
  ['Q：Первые три года беспроводные продукты установлены, устройство отображения не позиционировано или не подключено.', 'O：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Включите переключатель, чтобы проверить, мигает ли индикатор. Например, желтый и зеленый индикаторы S18 мигают в одно и то же время, как обычно, и мигает в сигнале поиска. Устройство не горит. (Статус разных моделей будет разным. Пожалуйста, обратитесь к руководству для других моделей). <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Индикатор мигает не на линии. Если сигнал включен в плохом положении, пожалуйста, получите сигнал в хорошем месте. Зона хорошего сигнала не находится на линии, вы можете выключить ее на 1 минуту, переустановить карту и запустить тест.'],
  ['Q：Кабельное изделие устанавливается впервые, а устройство отображения не позиционируется.', 'O：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Проверьте, нормально ли работает индикатор состояния GPS терминала. Проверьте состояние индикатора в соответствии с инструкциями разных моделей.<br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. Если индикатор не горит, устройство не может нормально работать. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 3, (зеленый желтый) индикатор карты не горит, выключите и заново установите карту, а затем включите, чтобы увидеть нормальный свет. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 4. Определите, не задан ли номер SIM-карты в устройстве, и работает ли функция доступа в Интернет по GPRS. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 5. В месте, где находится оборудование, например в нижней комнате, туннеле и т. Д., Где сигнал слабый, нет сети GSM. Пожалуйста, отправляйтесь в место, где покрытие GPRS хорошее. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 6, положение позиционера не должно быть слишком закрытым, не иметь металлических предметов, насколько это возможно в положении установки автомобиля. В противном случае это влияет на прием сигнала. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 7, нормальная загрузка, остановка в зоне сигнала не в сети, вы можете повторно выполнить команду линии, чтобы проверить, является ли интерфейс IP и сеть связи карты нормальной.'],
]
lg.webOptDoc = 'Скоро будет...';
lg.appOptDoc = 'Скоро будет...';
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push('<tr>');
html.push('<td class="cmdLabel" width="60">Пароль:</td>');
html.push('<td>Query terminal password</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">ТЕЛЕФОН:</td>');
html.push('<td>Запрос терминала встроенной SIM-карты</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">ПОЛЬЗОВАТЕЛЬ:</td>');
html.push('<td>Запрос номера телефона владельца </td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">СКОРОСТЬ:</td>');
html.push('<td>Запрос значения ограничения скорости </td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">Частота:</td>');
html.push('<td>Запросить частоту отслеживания, единица измерения - секунды</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">ПРОСЛЕДИТЬ:</td>');
html.push('<td>Запрос, включено ли отслеживание.1:включить,0:выключить</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">РАДИУС:</td>');
html.push('<td>Запросить диапазон тревоги о нелегальной перемещения, Единица измерения: метр</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIB:</td>');
html.push('<td>Запросить, включена ли SMS-сигнализация, 1: включить, 0: отключить');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBL:</td>');
html.push('<td>Запрос чувствительности вибрации от 0 до 15,0 - самая высокая чувствительность, слишком высокая - ложная тревога, 15 - самая низкая чувствительность</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push('<td>Запросить, включена ли сигнализация вызова, 1: включить, 0: отключить');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push('<td>Запросить, включен ли дрейф GPS-фильтра, 1: включить, 0: отключить, если при включении противоугонное устройство перейдет в стационарное состояние без вибрации в течение 5 минут, и отфильтровать весь дрейф GPS</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push('<td>Query whether sleep function is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the sleeping mode with no vibration occurs within 30 minutes,it will close GPS function and save power </td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">POF:</td>');
html.push('<td>Запросить, включена ли сигнализация отключения питания, 1: включена, 0: отключена</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">GPS:</td>');
html.push('<td>Запросите уровень сигнала спутника, например, 2300 1223 3431 ??? всего 12 наборов из четырех цифр 2300 означает: уровень сигнала от спутника номер 23 равен 0,1223 означает: уровень сигнала от спутника номер 12 равен 23</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VBAT:</td>');
html.push('<td>Запрос напряжения аккумулятора, напряжения зарядного порта Ток зарядки Например: VBAT = 3713300: 4960750: 303500 Указывает, что напряжение аккумулятора составляет 3713300 мкВ, 3,71 В, приложенное к напряжению зарядки на микросхеме 4,96 В, ток зарядки 303 мА.</td>');
html.push('</tr>');
html.push('</table>');
lg.queryparamhelp = html.join("");


// ???????

html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push('<tr>');
html.push('<td class="cmdLabel" width="60">Пароль:</td>');
html.push('<td>Установите пароль терминала, который состоит всего из 6 цифр</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">ТЕЛЕФОН:</td>');
html.push('<td>Установить номер SIM терминала</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">ПОЛЬЗОВАТЕЛЬ:</td>');
html.push('<td>Установите количество владельцев мобильных телефонов</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">СКОРОСТЬ:</td>');
html.push('<td>Установите значение ограничения скорости,0-300</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">FREQ:</td>');
html.push('<td>Установите сообщаемую частоту при включении слежения, Единица измерения: секунда</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">СЛЕЖЕНИЕ:</td>');
html.push('<td>Настройка открытия трека, 1: открыть, 0: закрыть</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">РАДИУС:</td>');
html.push('<td>Настройка диапазона сигнализации о незаконном перемещении, единица измерения: метр</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIB:</td>');
html.push('<td>Настройка, включена ли SMS-тревога, 1: включена, 0: выключена');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBL:</td>');
html.push('<td>Настройка чувствительности вибрации от 0 до 15,0-самая высокая чувствительность, слишком высокая может быть ложная тревога, 15-самая низкая чувствительность</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">Виброзвонок:</td>');
html.push('<td>Установить, является ли сигнал тревоги включен,1:включить,0:выключить');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push('<td>Установите, включен ли дрейф GPS-фильтра, 1: включите, 0: отключите, если при включении противоугонное устройство перейдет в стационарное состояние без вибрации в течение 5 минут, и отфильтрует весь дрейф GPS</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">СОН:</td>');
html.push('<td>Установите, включена ли функция ожидания, 1: включить, 0: отключить, если вы включите противоугонное устройство, то перейдете в режим ожидания без вибрации в течение 30 минут, выключит функцию GPS и сэкономит электроэнергию</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td class="cmdLabel">POF:</td>');
html.push('<td>Установите, включена ли сигнализация отключения питания, 1: включена, 0: отключена</td>');
html.push('</tr>');
html.push('</table>');
lg.setparamhelp = html.join("");

//????

html = [];
html.push('<div id="bulkAdds_treeDiv" class="легкая панель" ' +

  'style="Z-индекс: 999;position:absolute;left:189px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td  style="text-align: right"><span style="color:red;">*</span>клиент:</td>');
html.push('<td>');
html.push('<input class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' + 'bulkAdds_treeDiv' + ',' + 'bulkAdds_seller' + ')" style="width:250px;height:28px;">');
html.push('<input type="hidden" id="bulkAdds_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td  style="text-align: right"><span style="color:red;">*</span>Платформа Связи:</td>');
html.push('<td>');
html.push('<input id="ba_platformTime" class="easyui-validatebox textbox" style="width:250px;height:28px">');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Модель:</td>');
html.push('<td>');
html.push('<span class="select_box">' +

  '<span class="select_txt"></span>' +

  '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +

  '<div class="option" style="">' +

  '<div class="searchDeviceBox">' +

  '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +

  '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +

  '</div>' +

  '<div id="deviceList"></div>' +

  '</div>' +

  '</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>');
html.push('<td>');
html.push('<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<a id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>');
lg.bulkAdds = html.join('');
//????

html = [];
html.push('<div id="bulkRenew_treeDiv" class="easyui-panel" style="z-index: 999;position:absolute;left:138px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> ');
html.push('</div>');
html.push('<form id="bs_form">');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Add device:</td>');
html.push('<td>');
html.push('<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('<span class="re_addNumBox">ток：<span id="account_re_addNum">0</span>');
html.push('</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<tr>');
html.push('<td style="text-align:right;"><span style="color:red">*</span>Type</td>');
html.push('<td>');
html.push('<input type="radio" name="red_cardType"');
html.push('class="easyui-validatebox" value="3" checked><label>годовой</label></input>');
html.push('<input type="radio" name="red_cardType" style="margin-left:15px;" ');
html.push('class="easyui-validatebox" value="4"><label>пожизненный</label></input>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right">Quantity</td>');
html.push('<td>');
html.push('<input id="red_deductPoint" class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">');
html.push('</td>');
html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>User Due:</td>');
// html.push('<td>');
// html.push('<input id="red_serviceTime" class="easyui-validatebox textbox" style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Remark</td>');
html.push('<td>');
html.push('<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="re_renewMachines" title="'+lg.renew+'" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="re_reset" title="'+lg.reset+'"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('</form>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="account_re_machineList" style="width:500px;"></table>');
html.push('</div>');
lg.bulkRenew = html.join('');
//?????myAccount

html = [];
html.push('<div id="bulkSales_treeDiv" class="easyui-panel" style="z-index: 999;position:absolute;left:134px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> ');
html.push('</div>');
html.push('<form id="bs_form">');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Customer:</td>');
html.push('<td>');
html.push('<input class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' + 'bulkSales_treeDiv' + ',' + 'bulkSales_seller' + ')" style="width:250px;height:28px;">');
html.push('<input type="hidden" id="bulkSales_userId" >');
html.push('<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>');
html.push('</td>');
html.push('</tr>');
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>User Due:</td>');
// html.push('<td>');
// html.push('<input id="bs_serviceTime" class="easyui-validatebox textbox" style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>');
html.push('<td>');
html.push('<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="'+lg.addTo+'" style="cursor:pointer"></img>');
html.push('<span class="bs_addNumBox">ток：<span id="account_bs_addNum">0</span>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="bs_sellMachines" title="'+lg.sell+'"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="bs_reset" class="swd-gray-btn" title="'+lg.reset+'"  style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('</form>');
lg.bulkSales = html.join('');
//????,???


html = [];
html.push('<div id="blukTransfer_treeDiv" class="easyui-panel" style="z-index: 999;position:absolute;left:152px;top:171px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>target client:</td>');
html.push('<td>');
html.push('<input class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
html.push('<input type="hidden" id="bulkTransfer_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>');
html.push('<td>');
html.push('<a id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >Batch Add</a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push('</div>');
html.push('<a id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Move</a>');
html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)" >Cancel</a>');
lg.bulkTransfer = html.join('');
//????2,myClient

html = [];
html.push('<div id="blukTransfer_treeDiv" class="easyui-panel" style="z-index: 999;position:absolute;left:142px;top:83px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>target client:</td>');
html.push('<td>');
html.push('<input class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">');
html.push('<input type="hidden" id="bulkTransfer_userId" >');
html.push('</td>');
html.push('<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>');
html.push('<td>');
html.push('<img  id="bt_addMachines" style="cursor:pointer" title="'+lg.addTo+'" src="../../images/main/myAccount/add3.png" />');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push('</div>');
html.push('<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');
lg.bulkTransfer2 = html.join('');
//??????

html = [];
html.push('<div id="blukTransferUser_treeDiv" class="easyui-panel" style="z-index: 999;position:absolute;left:141px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> ');
html.push('<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> ');
html.push('</div>');
html.push('<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">');
html.push('<tr>');
html.push('<td style="text-align: right"><span style="color:red;">*</span>target client:</td>');
html.push('<td>');
html.push('<input class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">');
html.push('<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>');
html.push('</td>');
html.push('<td></td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push('</td>');
html.push('</tr>');
html.push('<tr>');
html.push('<td>');
html.push('</td>');
html.push('<td>');
html.push('<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>');
html.push('<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>');
html.push('</td>');
html.push('</tr>');
html.push('</table>');
lg.bulkTransferUser = html.join('');

window.lg = lg











