var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
var lg = {
  //波斯语
  //common
  user_guide: 'راهنمای کاربر',
  remoteSwitch: "سوئیچ از راه دور",
  pageTitle: "بلافاصله سکوی سرویس جهانی موقعیت مکانی را قرار دهید ،",
  description:
    "موقعیت یابی فوری ("+site+") مبتنی بر جدیدترین علم و فناوری صنعت است.طبقپردازش داده های بزرگ توزیع شده ，متعهد به ارائه خدمات با موقعیت مکانی هوشمند در ابر ، موقعیت برتر جهان استبستر خدمات را تنظیم کنید.",
  pageLang: "فارسی",
  inputCountTips: "لطفا یک حساب کاربری / IMEI ر",
  inputPasswordTips: "ل",
  appDownload: "دانلود مشتری",
  siteName: "موقعیت فعلی",
  rememberPassword: "به یاد داشته باشید رمز عبور",
  forgetPassword: 'رمز عبور را فراموش کرده ام',
  noToken: " لطفا رمز عبور را منتقل کنید",
  loginFirst: "لطفا ابتدا وارد شوید",
  move: " ورزش",
  stop: "هنوز هم",
  query: " پرس و جو",
  imeiQuery: "IMEI شماره پرس و جو،",
  delete: "حذف",
  update: " اصلاح کنید",
  cancel: "لغو",
  soft: "شماره سریال",
  more: "بیشتر",
  useful:'مفید',
  useless:'بی فایده',
  about:'About',
  replyFeedback:'بازخورد درباره "$"',
  edit: " ویرایش کنید",
  add: " افزایش",
  addTo: "اضافه کردن",
  addDevice: "اضافه کردن دستگاه",
  machineName: "نام دستگاه",
  searchDevice: "تجهیزات جستجو",
  date: " تاریخ",
  LatestUpdate: " سیگنال",
  engine: "ACC",
  locTime: " زمان موقعیت",
  locType: "نوع هدفگذاری",
  startLoc: "شروع موقعیت",
  endLoc: "موقعیت پایانی",
  address: " آدرس",
  noAddressTips: "نمی توانید اطلاعات آدرس را بدست آورید",
  lonlat: "طول و عرض جغرافیایی",
  carNO: "شماره پلاک",
  imei: "شماره تجهیزات (IMEI)",
  IMEI: "IMEI",
  simNO: "شماره سیم کارت",
  activeTime: " زمان فعال سازی",
  expireTime: "زمان انقضا",
  acceptSubordinateAlarm: "هشدار زیر را قبول کنید",
  acceptAlarmTips1: "بررسی شده است",
  acceptAlarmTips2: " شما می توانید تمام آلارم تجهیزات مشتری کمتر دریافت ",
  speed: "سرعت",
  y: "سال",
  M: "ماه",
  d: "روز",
  h: " زمان",
  min: " دقیقه",
  s: "دومین",
  _year: "سال",
  _month: "ماه",
  _day: "روز",
  _hour: " زمان",
  _minute: " دقیقه",
  _second: "دومین",
  confirm: " تعیین کنید",
  yes: " بله",
  car: "وسیله نقلیه",
  not: " نه",
  m: "برنج",
  account: "شماره حساب",
  psw: " رمز عبور",
  save: " ذخیره کنید",
  operator: " عملیات",
  queryNoData: "هیچ داده ای مورد پرسش قرار نگرفت",
  name: "نام",
  type: " مدل",
  open: " باز کردن",
  close: " بستن",
  send: " ارسال کنید",
  alarm: " هشدار",
  alarmSetting: " تنظیم زنگ هشدار",
  look: " مشاهده",
  tailAfter: " ردیابی",
  history: " پخش",
  dir: "سرصفحه",
  locStatus: "وضعیت موقعیت",
  machineTypeText: " مدل",
  carUser: " صاحب",
  machine: "دستگاه ها",
  unknowMachineType: " مدل نامشخص",
  noCommandRecord: " این دستگاه دستورالعمل ندارد",
  type1: " تایپ کنید",
  role: "تایپ کنید",
  roles: "تایپ کنید",
  timeType: " نوع زمان",
  moveSpeed: " سرعت در حال اجرا",
  signal: "سیگنال",
  loc: "موقعیت",
  wiretype: "تایپ کنید",
  wire: "سیم",
  wireless: "بی سیم",
  expire: "منقضی شده است",
  hour: "ساعت",
  hourTo: "ساعت به",
  remark: "یادداشت ها",
  remarkInfo: "یادداشت ها",
  noPriviledges: "این حساب دارای هیچ حقوق عملی نیست",
  commandNoOpen: "تابع دستور فعلی دستگاه هنوز استفاده نشده است.",
  choseDelelePhone: "لطفا شمارهی که میخواهید اول را حذف کنید.",
  streetView: "نمای خیابان",
  wrongFormat: "خطا در قالب ورودی",
  inputFiexd: "لطفا یک شماره ثابت وارد کنید",
  serialNumberStart: "لطفا یک شماره شروع می شود حتی اعداد را وارد کنید",
  serialNumberEnd: "لطفا شماره پایان را وارد کنید تا متوالی شود",
  clickSearchFirst: "لطفا ابتدا بر روی شماره دستگاه جستجو کلیک کنید",
  isDeleteDevice: "پس از حذف دستگاه را نمی توان ترمیم اگر حذف شده است؟",
  //پلت فرم کد خطا اشاره کرد
  errorTips: "عملیات شکست خورده با کد خطا است",
  error10003: 'رمز اشتباه',
  error90010: 'دستگاه آفلاین است و ارسال دستور سفارشی انجام نشد!',
  error70003: 'مقدار کنترل از راه دور نمی تواند خالی باشد',
  error70006: 'از دستورالعمل پشتیبانی نمی کند یا حق ندارد',
  error20001: 'شناسه خودرو نمی تواند خالی باشد',
  error20012: 'خودرو فعال نشده است',
  error10012: "خطای رمز عبور قدیمی",
  error10017: "حذف نشد، لطفا ابتدا کاربر زیر را حذف کنید",
  error10023: "حذف انجام نشد، کاربر دارای یک دستگاه است",
  error20008: "افزودن ناموفق، شماره IMEI در حال حاضر وجود دارد",
  error20006: "لطفا شماره دستگاه 15 رقمی را وارد کنید",
  error10019: "خطای قالب تلفن",
  error10024: "فروش را تکرار نکنید",
  error120003: "لینک به پایان رسیده منقضی شده است",
  error10025: "اطلاعات دستگاه اصلاح شده نباید خالی باشد",
  error2010: "لطفا فایل را آپلود کنید",
  error20002: "شماره IMEI موجود نیست",
  error10081: " تعداد کافی از کارت های تجدید پذیر",
  error10082: 'برای دستگاه مادام العمر نیازی به شارژ مجدد نیست',
  error3000: 'نقش به حساب سیستم اختصاص داده شده است و قابل حذف نیست',
  error103: 'حساب غیرفعال شده است ، لطفا با ارائه دهنده خدمات خود تماس بگیرید',
  error124: 'نمی تواند خودش عمل کند',
  // فرود مرتبط است
  logining: "وارد شوید ... ",
  login: "ورود",
  userEmpty: " نام کاربری نمیتواند خالی باشد",
  pswEmpty: "رمز عبور نمی تواند خالی باشد",
  prompt: " نکات",
  accountOrPswError: "حساب یا رمز عبور نادرست",
  UserNameAlreadyExist: " حساب کاربری در حال حاضر موجود است",
  noQualified: "هیچ اطلاعات واجد شرایط",
  //main.js
  systemName: 'سیستم نظارت را بلافاصله "قرار دهید',
  navTitle_user: ["سیستم های موقعیت یاب ", " actuals ", " مدیر دستگاه،"],
  navTitle_dealer: ["حساب من", "موکل من", "نظارت بر پلت فرم،", "عملیات بیشتر"],
  exitStytem: "خروج",
  user: "کاربر",
  UserCenter: "کاربر",
  alarmInfo: " هشدار",
  confirmExit: " آیا مطمئن هستید که از سیستم خارج شده اید؟",
  errorMsg: " دلیل خطا: ",
  logintimeout: " ورود به سیستم به پایان رسیده است، لطفا دوباره وارد شوید",
  clearAlarm: "پاک کردن",
  clear: "خالی",
  searchbtn: "جستجوی کاربر",
  print: "چاپ",
  export: "صادرات",
  // بخش بازخورد
  feedback: "بازخورد",
  feedback_sublime: "ثبت نام کنید",
  alerttitle: "عنوان نمیتواند خالی باشد",
  alertcontent: "بازخورد نمی تواند خالی باشد!",
  submitfail: "ارائه نشد",
  saveSuccess: "با موفقیت ذخیره شد",
  submitsuccess:
    "با موفقیت ارسال شد ما بازخورد شما را در اسرع وقت پردازش می کنیم ~",
  adviceTitle: "عنوان",
  adviceTitle_p: "سوال و نظر عنوان",
  adviceContent: " سوالات و نظرات",
  adviceContent_p:
    " به طور خلاصه سوالات و نظراتی را که می خواهید برای بازخورد توضیح دهید، شرح دهید و ما همچنان به شما برای بهبود آن ادامه خواهیم داد.",
  contact: "اطلاعات تماس",
  contact_p: "تلفن یا ایمیل خود را پر کنید",
  //monitor.js

  myMachine: "تجهیزات",
  all: "همه",
  online: "آنلاین",
  offline: "آفلاین",
  unUse: "استفاده نشده",
  group: "گروه بندی",
  moveGruop: " حرکت به",
  arrearage: "عقب افتادگی",
  noStatus: "بی بی سی",
  inputMachineName: "لطفا نام دستگاه / IMEI را وارد کنید",
  defaultGroup: "گروه بندی پیش فرض",
  offlineLessOneDay: " آفلاین <1 روز",
  demoUserForbid: "کاربران تجربه نمیتوانند از این ویژگی استفاده کنند",
  shareTrack: " به اشتراک بگذارید",
  shareName: "نام توزیع",
  liveShare: "به اشتراک گذاری آهنگ در زمان واقعی",
  expiration: " زمان موثر",
  getShareLink: "ایجاد لینک سهم",
  copy: "کپی کنید",
  copySuccess: "کپی موفق",
  enlarge: "بزرگنمایی",
  shareExpired: "لینک به پایان رسیده است",
  LinkFailure: "به اشتراک گذاشتن لینک باز شکست خورده است",
  inputShareName: "لطفا نام سهم را وارد کنید",
  inputValid: " لطفا زمان معتبر را وارد کنید",
  //statistics.js
  runOverview: " مرور عملیات",
  runSta: "آمار عملیاتی",
  mileageSta: "آمار مسافت پیموده شده",
  tripSta: "آمار سفر",
  overSpeedDetail: " لیست سرعت",
  stopDetail: "فهرست اقامت",
  alarmSta: " آمار هشدار",
  alarmOverview: "مرورگر هشدار",
  alarmDetail: "لیست هشدار",
  shortcutQuery: "پرس و جو سریع",
  today: "امروز",
  yesterday: " دیروز",
  lastWeek: " هفته گذشته",
  thisWeek: " این هفته",
  thisMonth: "این ماه",
  lastMonth: "ماه گذشته",
  mileageNum: " مسافت پیموده شده (کیلومتر)",
  overSpeedNum: " سرعت (کیلومتر / ساعت)",
  overSpeed: " سرعت بخشیدن",
  stopTimes: "اقامت (بار)",
  searchMachine: "تجهیزات",
  speedNum: "سرعت (کیلومتر / ساعت)",
  querying: "پرسیدن",
  stopTime: "زمان اقامت",
  HisToryStopTime: "ماندن",
  clickLookLoc: "برای دیدن آدرس کلیک کنید",
  lookLoc: "مشاهده موقعیت مکانی",
  noData: "بدون اطلاعات",
  alarmTime: "زمان زنگ هشدار",
  vibrationLevel: "سطح ارتعاش",
  vibrationWay: "حالت زنگ هشدار",
  acc: "ACC",
  accStatistics: "آمار ACC",
  accType: ["همه کشورها، ACC آتش، ACC flameout"],
  accstatus: ["باز، نزدیک"],
  openAccQuery: "پرس و جو ACC",
  runtime: "در حال اجرا",
  // تغییر صفحه مانیتورینگ
  run: "رانندگی",
  speed: "سرعت",
  // مدیریت تجهیزات
  machineManage: "مدیریت تجهیزات",
  deviceTable: "هدف من",
  status: "دولت",
  havaExpired: "منقضی شده است",
  expiredIn60: "ظرف 60 روز منقضی میشود",
  expiredIn7: "ظرف 7 روز منقضی می شود",
  normal: " عادی",
  allMachine: "تمام تجهیزات",
  allMachine1: "تمام تجهیزات",
  expiredIn7Machine: "7 روز منقضی شده است",
  expiredIn60Machine: " 60 روز منقضی شده است",
  havaExpiredMachine: " دستگاه منقضی شده",

  //history.js
  replay: "بازی",
  replaytitle: "  پخش",
  choseDate: " زمان را انتخاب کنید",
  from: "از",
  to: " به",
  startTime: " زمان شروع",
  endTime: " پایان زمان",
  pause: "تعلیق",
  slow: " آهسته",
  mid: "متوسط",
  fast: " سریع",
  startTimeMsg: "شما زمان شروع را انتخاب نکرده اید",
  endTimeMsg: "شما یک زمان پایان را انتخاب نکرده اید",
  smallEnd:
    " زمان پایان شما وارد شده کمتر از زمان شروع است، لطفا دوباره انتخاب کنید!",
  bigInterval: " فاصله زمانی که وارد می کنید نباید بیش از 31 روز باشد!",
  trackisempty: " مسیر در این دوره زمانی خالی است",
  longitude: "طول جغرافیایی",
  latitude: " عرض جغرافیایی",
  direction: " جهت",
  stopMark: " نشانه توقف",
  setStopTimes: [
    {
      text: "1 دقیقه",
      value: "1",
    },
    {
      text: "2 دقیقه",
      value: "2",
    },
    {
      text: "3 دقیقه",
      value: "3",
    },
    {
      text: "5 دقیقه",
      value: "5",
    },
    {
      text: "10 دقیقه",
      value: "10",
    },
    {
      text: "15 دقیقه",
      value: "15",
    },
    {
      text: "20 دقیقه",
      value: "20",
    },
    {
      text: "30 دقیقه",
      value: "30",
    },
    {
      text: "45 دقیقه",
      value: "45",
    },
    {
      text: "1 ساعت",
      value: "60",
    },
    {
      text: "6 ساعت",
      value: "360",
    },
    {
      text: "12 ساعت",
      value: "720",
    },
  ],
  filterDrift: "راندگی فیلتر",
  userType: [
    "مدیر، فروشنده، کاربر، تدارکات، اجاره، کاربر خودرو، کنترل خطر، حرفه ای",
  ],
  userTypeArr: [
    "مدیر، فروشنده، کاربر، تدارکات، اجاره، کاربر خودرو، کنترل خطر، حرفه ای",
  ],
  machineType: {
    '0':'نوع ناشناخته',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '83':'S03A',
    '84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: [
    "زنگ نامشخص ",
    " زنگ هشدار لرزش ",
    " زنگ خاموش ",
    " زنگ باتری کم ",
    " کمک به SOS ",
    " زنگ سرعت ",
    " یک زنگ نرده ",
    " زنگ جابجایی ",
    " زنگ باتری کم خارجی از پلیس منطقه ",
    " زنگ پیاده کردن ",
    " زنگ حساس به نور ",
    " زنگ القای مغناطیسی ",
    " زنگ رشوه دادن ",
    " زنگ بلوتوث ",
    " سیگنال به ماسک زنگ ",
    " شبه ایستگاه های پایه زنگ ",
    " نامزد زنگ نوار ",
    " زنگ نامزد نوار",
    "یک زنگ نرده",
    " درب زنگ باز ",
    " خستگی راننده ",
    " به دو نقطه اتهام ",
    " دو نقطه اتهام ",
    " دو نقطه اتهام اقامت ",
    " آنلاین ترمینال ",
    " نامزد زنگ نوار ",
    " یک زنگ نرده ",
    " نامزد زنگ نوار ",
    " یک زنگ نرده ",
    " زنگ نفت",
    "زنگ برخورد",
  ],
  alarmTypeNew:  {
    '40': "دزدگیر درجه حرارت بالا",
    '45': "دزدگیر درجه حرارت پایین",
    '50': "هشدار بیش از حد",
    '55': " هشدار ناخوشایند",
    '60': 'دزدگیر پارکینگ'
  },
  alarmNotificationType: [
    { type: "زنگ لرزش", value: 1 },
    { type: " زنگ هشدار برق", value: 2 },
    { type: "زنگ باتری کم", value: 3 },
    { type: "SOS برای کمک", value: 4 },
    { type: "هشدار سرعت", value: 5 },
    // {type:' زنگ حصار',value:6},
    { type: "هشدار جابجایی", value: 7 },
    { type: " نامزد زنگ نوار", value: 8 },
    { type: "خارج از زنگ منطقه", value: 9 },
    { type: "زنگ زدن", value: 10 },
    { type: "زنگ نور", value: 11 },

    { type: "هشدار تامر", value: 13 },

    { type: "زنگ محافظ سیگنال", value: 15 },
    { type: " هشدار ایستگاه پایه ای", value: 16 },
    // {type:' زنگ حیاط ورودی (تصمیم پلت فرم)',value:17},
    // {type:'زنگ ورودی ورودی (تصمیم ترمینال)',value:18},
    // {type:'زنگ حصار',value:19},

    { type: " رانندگی خستگی", value: 21 },
    { type: " دو امتیاز را وارد کنید", value: 22 },
    { type: " دو امتیاز", value: 23 },
    { type: "دو نقطه طولانی", value: 24 },
    { type: " ترمینال آفلاین", value: 25 },
    // {type:' زنگ ورودی ورودی (کنترل آب و هوا)',value:26},
    // {type:'زنگ حصار (کنترل آب و هوا)',value:27}
    { type: " زنگ ورودی حصار", value: 26 },
    { type: " زنگ حصار", value: 27 },
  ],
  alarmTypeText: "نوع زنگ هشدار",
  alarmNotification: " تنظیمات را فشار دهید",
  pointType: [
    "غیر هدفمند ",
    "GPS",
    "Beidou",
    " محل ایستگاه پایه ",
    " موقعیت WIFI از ",
  ],

  cardType: [
    "نوع ناشناس ",
    " نقطه در سال واردات، ",
    " نقطه واردات مادام العمر ",
    " در کارت ",
    " کارت مادام العمر، ",
  ],
  //  جنوب شرقی و شمال غربی
  directarray: ["شرق", "جنوب", "غرب", "شمال"],
  // زمینه جهت
  directionarray: [
    "شمالی",
    "شمال",
    "شرق",
    "جنوب شرق",
    "جنوب",
    " جنوب غربی ",
    " به دلیل غرب ",
    " شمال غربی ",
  ],
  // روش موقعیت یاب
  pointedarray: [
    " غیر هدفمند ",
    " از GPS ",
    " پایه ",
    " محل ایستگاه پایه ",
    " هدف قرار دادن WIFI ",
  ],

  //mapمرتبط
  ruler: "دامنه",
  distance: "اطلاعات ترافیکی",
  baidumap: "نقشه بایو",
  map: "نقشه",
  satellite: "ماهواره",
  ThreeDimensional: "سه بعدی",
  baidusatellite: "ماهواره Baidu",
  googlemap: "نقشه های گوگل",
  googlesatellite: " ماهواره Google",
  fullscreen: "صفحه کامل",
  noBaidumapStreetView: "محل فعلی نقشه Baidu بدون نمایش خیابان",
  noGooglemapStreetView: "موقعیت فعلی در Google Maps بدون نمای خیابان",
  exitStreetView: "خروج از نمای خیابان",
  draw: "قرعه کشی",
  finish: " کامل",
  unknown: "ناشناخته",
  realTimeTailAfter: " ردیابی زمان واقعی",
  trackReply: "پیگیری پخش",
  afterRefresh: " پس از طراوت",
  rightClickEnd: " راست، شعاع",
  rightClickEndGoogle: "راست پایین - شعاع",

  //treeمرتبط
  currentUserMachineCount: "تعداد فعلی دستگاه های کاربر",
  childUserMachineCount: "شامل تعداد کل دستگاه های زیر کاربر است",

  // پنجره مرتبط است

  electronicFence: " حصار الکترونیک",
  drawTrack: " ردیابی آهنگ",
  showOrHide: "نمایش / پنهان کردن",
  showDeviceName: "نام دستگاه",
  circleCustom: "گرد سفارشی",
  circle200m: " دور 200 متر",
  polygonCustom: "سفارشیسازی چندگانه",
  drawPolygon: " رسم چند ضلعی",
  drawCircle: "یک دایره بنویسید",
  radiusMin100:
    "حداقل شعاع حصار کشیده شده توسط r 20 متر است. لطفا تجدید نظر کنید. شعاع حصار فعلی:",
  showAllFences: "نمایش همه نرده ها",
  lookEF: "نمایش حصار",
  noEF: "هیچ حصار الکترونیک پیدا نشد!",
  hideEF: "حصار پنهان",
  blockUpEF: " حیاط را غیرفعال کنید",
  deleteEF: " حصار را پاک کن",
  isStartUsing: "آیا برای فعال کردن",
  startUsing: "فعال کردن",
  stopUsing: " غیرفعال کردن",
  nowEFrange: "محدوده حصار فعلی",
  enableSucess: " موفق به فعال شدن",
  unableSucess: " غیرفعال شده با موفقیت",
  sureDeleteMorgage: "اطمینان حاصل کنید که نقطه دوم را حذف کنید",
  enterMorgageName: "لطفا نام پست دوم را وارد کنید",
  openMorgagelongStayAlarm: " دومین زنگ هشدار طولانی را باز کنید",
  openMorgageinOutAlarm: " زنگ ورودی و خروجی را باز کنید",
  setEFSuccess: "حصار را با موفقیت تنظیم کرده و محدوده حصار را فعال کنید",
  setElectronicFence: "حصار الکترونیکی را راه اندازی کنید",
  drawFence: "طراحی یک حصار الکترونیکی",
  drawMorgagePoint: " قرعه کشی دو امتیاز",
  customFence: "نرده سفارشی",
  enterFenceTips: " ناهار سفارشی را وارد کنید",
  leaveFenceTips: "ناهار سفارشی را ببافید",
  inputFenceName: " لطفا نام حصار را وارد کنید",
  relation: "انجمن",
  relationDevice: "دستگاه همراه",
  unRelation: " مرتبط نیست",
  hadRelation: " وابسته",
  quickRelation: " یک کلیک انجمن",
  cancelRelation: " لغو پیوند",
  relationSuccess: "انجمن موفقیت آمیز",
  cancelRelationSuccess: "جدا کردن با موفقیت",
  relationFail: "عدم موفقیت انجمن",
  deviceList: " لیست دستگاه",
  isDeleteFence: "حیاط را حذف کنید",
  choseRelationDeviceFirst:
    "لطفا دستگاهی را که میخواهید برای اولین بار ارتباط برقرار کنید را انتخاب کنید",
  choseCancelRelationDeviceFirst:
    "لطفا دستگاهی را که میخواهید اولین بار آن را لغو کنید، انتخاب کنید",
  selectOneTips: "لطفا حداقل یک روش هشدار را انتخاب کنید",
  radius: " شعاع",
  //صفحه دوم را تنظیم کنید
  setMortgagePoint: "دو نقطه را تنظیم کنید",

  circleMortage: "دو نقطه دور",
  polygonMorgage: "دو نقطه چند ضلعی",
  morgageSet: "دو شرط بندی تعیین شده است",
  operatePrompt: " عملیات سریع",
  startDrawing: " برای شروع نقاشی کلیک کنید",
  drawingtip1:
    "کلیک راست موس را برای شروع رسم کنید، برای پایان دادن به نقاشی دوبار کلیک کنید",
  drawingtip2: "کلیک راست کرده و بکشید تا نقاشی شروع شود",

  /************************************************/
  endTrace: "پخش آهنگ به پایان رسیده است ",
  travelMileage: " مسافت پیموده شده ",
  /************************************************/
  myAccount: ' حساب من "،',
  serviceProvide: "پشتیبانی",
  completeInfo: " لطفا اطلاعات زیر را تکمیل کنید، مانند فرد مخاطب، شماره تلفن.",
  clientName: " نام مشتری",
  loginAccount: " حساب کاربری ورود",
  linkMan: " تماس بگیرید",
  linkPhone: " تلفن",
  clientNameEmpty: "نام مشتری نمی تواند خالی باشد!",
  updateSuccess: " به روز شده با موفقیت!",
  /************************************************/
  oldPsw: "رمز عبور قدیمی",
  newPsw: "رمز عبور جدید",
  confirmPsw: "تایید رمز عبور",
  pswNoSame: " ورودی نامنسجم کلمه عبور",
  pswUpdateSuccess: " گذرواژه با موفقیت اصلاح شد!",
  email: " صندوق پستی",
  oldPwdWarn: "لطفا رمز عبور قدیمی را وارد کنید",
  newPwdWarn: " لطفا یک رمز عبور جدید وارد کنید",
  pwdConfirmWarn: "لطفا رمز عبور جدید را تایید کنید",
  /************************************************/
  //مونتاژ پاپ آپ سفارشی
  resetPswFailure: "برای بازنشانی گذرواژه نتوانستیم",
  notification: "بلافاصله",
  isResetPsw_a: " میخواهید‘",
  isResetPsw_b: "’ تنظیم مجدد",
  pwsResetSuccess_a: "دارای‘",
  pwsResetSuccess_b: "’ رمز عبور به 123456 باز گردید",
  /************************************************/
  machineSearch: "جستجوی دستگاه",
  search: "جستجو",
  clientRelation: "رابطه مشتری",
  machineDetail: "جزئیات",
  machineDetail2: "جزئیات دستگاه",
  machineCtrl: "دستورالعمل",
  transfer: "انتقال",
  belongCustom: " مشتری",
  addImeiFirst: "لطفا اول IMEI را وارد کنید",
  addUserFirst: " لطفا اول IMEI را وارد کنید",
  transferSuccess: "لطفا ابتدا مشتریان را اضافه کنید",
  multiAdd: "اضافه کردن به دسته",
  multiImport: "واردات دسته ای",
  multiRenew: "تجدید دسته",
  //批量修改设备begin
  editDevice:'مدل دستگاه را اصلاح کنید',
  deviceAfter: 'مدل دستگاه (پس از اصلاح)',
  editDeviceTips:'لطفاً تأیید کنید دستگاهی که باید اصلاح شود همان مدل است و فعال نیست!',
  pleaseChoseDevice: 'لطفاً ابتدا دستگاهی را انتخاب کنید که اصلاح شود!',
  editResult:'ویرایش نتیجه',
  successCount:'دستگاه با موفقیت اصلاح شد:',
  failCount:'دستگاه های خراب:',
  //批量修改设备end
  multiDelete: "حذف فله",
  canNotAddImei: " IMEI وجود ندارد و نمی تواند به لیست اضافه شود",
  importTime: " زمان واردات",
  loginName: "نام کاربری",
  platformDue: "پلت فرم منقضی می شود",
  machinePhone: " شماره سیم کارت",
  userDue: "کاربر منقضی شده است",
  overSpeedAlarm: "آیکون سرعت بخشیدن",
  changeIcon: "جایگزین نماد",
  dealerNote: " اظهارات فروشنده",
  noUserDue: " لطفا زمان انقضا کاربر را وارد کنید",
  phoneLengththan3: " طول تلفن باید بیش از 3 باشد",
  serialNumberInput: "ورودی شماره سریال",

  /************************************************/
  sending: "ارسال دستورالعمل ..... لطفا صبر کنید ...",
  sendFailure: " ارسال موفق شد",
  ctrlName: "نام آموزش",
  interval: " فاصله زمانی",
  intervalError: " خطای قالب بندی فاصله",
  currectInterval: "لطفاً فاصله زمانی صحیحی را وارد کنید!",
  intervalLimit: "محدوده فاصله زمانی 10-720، واحد (دقیقه) را تنظیم کنید",
  intervalLimit2: "محدوده فاصله زمانی را از 10 تا 5400 در ثانیه تنظیم کنید",
  intervalLimit3: "محدوده فاصله زمانی 5 تا 1440 را در واحد (دقیقه)",
  intervalLimit4: "محدوده فاصله زمانی 3-999 را در ثانیه تنظیم کنید",
  intervalLimit5:
    "محدوده فاصله زمانی را از 10 تا 10800 در واحد (ثانیه) تنظیم کنید",
  intervalLimit1:
    "محدوده فاصله زمانی 1-999، واحد (دقیقه) 000 را تنظیم کنید، یعنی حالت بازگشت زمان را خاموش کنید.",
  intervalLimit6: "محدوده فاصله زمانی 1-65535 را در ثانیه تنظیم کنید",
  intervalLimit7: " فاصله زمانی فاصله از 1 تا 999999 در واحد (ثانیه)",
  intervalLimit8:
    " محدوده فاصله زمانی 0 تا 255 را تنظیم کنید، 0 به معنای بستن است",
  intervalLimit9:
    "محدوده فاصله زمانی را از 3 تا 10800 در واحد (ثانیه) تنظیم کنید",
  intervalLimit10: "محدوده زمانی فاصله 3-86400 را در عرض چند ثانیه تنظیم کنید",
  intervalLimit11:
    "محدوده زمانی فاصله 180-86400 را در عرض چند ثانیه تنظیم کنید",
    intervalLimit22:
    "محدوده زمانی فاصله 60-86400 را در عرض چند ثانیه تنظیم کنید",
  intervalLimit23:
    "محدوده فاصله زمانی 5-60 ، (دوم)",
  intervalLimit24:
    "محدوده فاصله زمانی 10-86400 ، (دوم)",
  intervalLimit25: "محدوده فاصله زمانی 5 تا 43200 را در واحد (دقیقه)",
  intervalLimit12:
    "محدوده فاصله زمانی را از 10 تا 60 در واحد (ثانیه) تنظیم کنید",
  intervalLimit13:
    "محدوده بازه زمانی 1-24 (به معنی 1-24 ساعت) یا 101-107 (به معنی 1-7 روز)",
  intervalLimit14:
    "محدوده بازه زمانی 10-3600 ، واحد (دوم) ؛ پیش فرض: 10 ثانیه ،",
  intervalLimit15: "محدوده بازه زمانی 180-86400 ، واحد (دوم) ؛ پیش فرض: 3600s",
  intervalLimit16: "محدوده بازه زمانی 1-72 ، واحد (ساعت) ؛ پیش فرض: 24 ساعت",
  intervalLimit17: "دامنه تنظیم دما -127-127 ، واحد (° C)",
  intervalLimit18: "تنظیم فاصله زمانی 5-18000 ، واحد (دوم)",
  intervalLimit19: "محدوده بازه زمانی 10-300 ، واحد (دوم)",
  intervalLimit20: "تنظیم فاصله زمانی 5-399 ، واحد (ثانیه)",
  intervalLimit21: "تنظیم فاصله زمانی 5-300 ، واحد (ثانیه)",
  noInterval: "لطفا فاصله زمانی را وارد کنید",
  intervalTips:
    "نیاز به خاموش کردن حالت ردیابی، لطفا زمان زنگ هشدار را تنظیم کنید",
  phoneMonitorTips:
    "پس از فرمان ارسال می شود، دستگاه به طور فعال شماره تماس برگشت را برای نظارت انتخاب می کند.",
  time1: "زمان 1",
  time2: "زمان 2",
  time3: "زمان 3",
  time4: "زمان 4",
  time5: "زمان 5",
  intervalNum: " فاصله (دقیقه)",
  sun: " یکشنبه",
  mon: " دوشنبه",
  tue: " سه شنبه",
  wed: "چهارشنبه",
  thu: " پنجشنبه",
  fri: "جمعه",
  sat: "شنبه",
  awakenTime: "بیدار شدن",
  centerPhone: " شماره مرکز",
  inputCenterPhone: "لطفا شماره مرکز را وارد کنید",
  phone1: " شماره یک",
  phone2: "شماره دو",
  phone3: "شماره سه",
  phone4: "شماره چهارم",
  phone5: " شماره پنج",
  inputPhone: " لطفا شماره را وارد کنید",
  offlineCtrl:
    "فرمان آفلاین ذخیره شده است و پس از اینکه دستگاه در حالت آنلاین، فرمان آنلاین به طور خودکار ارسال می شود.",
  terNotSupport: " ترمینال پشتیبانی نمی کند",
  terReplyFail: " پاسخ ترمینال ناموفق بود",
  machineInfo: "اطلاعات دستگاه",

  /************************************************/
  alarmTypeScreen: "غربالگری نوع هشدار",
  allRead: " همه خواندن",
  read: "خواندن",
  noAlarmInfo: " هیچ اطلاعات هشدار قابل تعویض وجود ندارد",
  alarmTip: "نکته: برای فیلتر کردن این نوع اطلاعات هشدار، برداشت کنید",

  /************************************************/
  updatePsw: "رمز عبور",
  resetPsw: " بازنشانی گذرواژه",

  /************************************************/
  multiSell: " فروش توده ای",
  sell: "فروش",
  sellSuccess: " فروش موفق",
  modifySuccess: " با موفقیت اصلاح شد",
  modifyFail: " اصلاح انجام نشد",
  multiTransfer: " انتقال دسته",
  multiUserExpires: " اصلاح انقضای کاربر",
  batchModifying: "ویرایش دسته ای",
  userTransfer: " انتقال",
  machineRemark: "یادداشت ها",
  sendCtrl: " ارسال دستورالعمل",
  ctrl: " دستورالعمل",
  ctrlLog: " رکورد آموزش",
  ctrlLogTips: " رکورد آموزش",
  s06Ctrls: [
    "قطع برق از راه دور",
    "بازیابی نفت و برق از راه دور",
    "موقعیت پرس و جو",
  ],
  ctrlType: " نام آموزش",
  resInfo: " پیام پاسخ",
  resTime: "زمان پاسخ",
  ctrlSendTime: "ارسال زمان",
  // csv آپلود فایل آپلود
  choseCsv: " لطفا فایل CSV را انتخاب کنید",
  choseFile: "فایل را انتخاب کنید",
  submit: "ثبت نام کنید",
  targeDevice: "دستگاه هدف",
  csvTips_1: " 1، فایل اکسل را به عنوان فرمت csv ذخیره کنید",
  csvTips_2: "2. فایل CSV را وارد سیستم کنید.。",
  importExplain: " دستورالعمل واردات:",
  fileDemo: "مثال فرمت فایل",

  // جدید
  sendType: " ارسال نوع",
  onlineCtrl: "آموزش آنلاین",
  offCtrl: "آموزش آفلاین",
  resStatus: [
    "فرستاده نشده، منقضی شده، تحویل داده شده، موفق، شکست خورده، بدون پاسخ",
  ],
  /************************************************/
  addSubordinateClient: "یک کاربر زیر را اضافه کنید",
  noSubordinateClient: " هیچ کاربر تابع",
  superiorCustomerEmpty: "لطفا یک مشتری برتر انتخاب کنید",
  noCustomerName: "لطفا نام مشتری را وارد کنید",
  noLoginAccount: " لطفا یک حساب کاربری وارد کنید",
  noPsw: "لطفا رمز عبور خود را وارد کنید",
  noConfirmPsw: "لطفا رمز عبور تأیید را وارد کنید",
  pswNotAtypism: " گذرواژه وارد شده و رمز عبور تأیید ناپذیر است!",
  addSuccess: "با موفقیت اضافه شد",
  superiorCustomer: "مشتری برتر",
  addVerticalImei: "لطفا شماره IME را در یک ستون عمودی وارد کنید",
  noImei: "IMEI وجود ندارد و نمی تواند به لیست اضافه شود",
  addImei_curr: "لطفا شماره IMEI را وارد کنید، در حال حاضر",
  no: " یکی",
  aRowAImei: "IMEI را در یک خط وارد کنید",

  /*
   * dealer  ترجمه رابط شروع می شود
   *
   * */
  //main.js
  imeiOrUserEmpty: "شماره دستگاه (IMEI) / نام مشتری نمیتواند خالی باشد!",
  accountEmpty: "حساب نمیتواند خالی باشد",
  queryNoUser: " کاربر درخواست نشده است",
  queryNoIMEI: " شماره IMEI مورد پرسش قرار نگرفت",
  imeiOrClientOrAccount: "شماره تجهیزات (IMEI) / نام مشتری / شماره حساب",
  dueSoon: " زود گذر",
  recentlyOffline: "اخیرا آفلاین",
  choseSellDeviceFirst:
    "لطفا دستگاه مورد نظر خود را برای اولین بار به فروش برسانید",
  choseDeviceFirst:
    "لطفا دستگاهی را که میخواهید برای اولین بار انتقال آن را انتخاب کنید انتخاب کنید",
  choseDeviceExpiresFirst:
    "لطفا دستگاه مورد نظر خود را برای اولین بار تغییر دهید!",
  choseRenewDeviceFirst:
    "لطفا دستگاه مورد نظر خود را برای اولین بار تمدید کنید",
  choseDeleteDeviceFirst:
    "لطفا دستگاهی را که میخواهید اول حذف کنید را انتخاب کنید",
  choseClientFirst:
    "لطفا مشتری را که می خواهید برای اولین بار انتقال را انتخاب کنید!",

  //myClient.js
  clientList: " لیست مشتری",
  accountInfo: "اطلاعات حساب",
  machineCount: "تعداد دستگاه ها",
  stock: " خرید",
  inventory: "موجودی",
  subordinateClient: " کاربر زیرمجموعه",
  datum: " اطلاعات",
  monitor: " نظارت",
  dueMachineInfo: " اطلاعات دستگاه منقضی شده",
  haveExpired: "منقضی شده است",
  timeRange: ["7ظرف 7 روز", "30 روز", "60 روز", " 7-30 روز", "30-60 روز"],
  offlineMachineInfo: "اطلاعات دستگاه آفلاین",
  timeRange1: [
    "در عرض 1 ساعت",
    "1 روز",
    "7 روز",
    "30 روز",
    "60 روز",
    "60 روز یا بیشتر",
    "1 ساعت - 1 روز",
    "1-7 روز",
    "7-30 روز",
    "30-60 روز",
  ],
  offlineTime: " زمان آفلاین",
  includeSubordinateClient: " شامل کاربران تابع",

  stopMachineInfo: " اطلاعات دستگاه ثابت",
  stopTime1: " زمان ثابت",
  unUseMachineInfo: "اطلاعات دستگاه فعال نیست",
  unUseMachineCount: " تعداد دستگاه های فعال نشده است",

  sellTime: " زمان فروش",
  detail: " تفصیلی",
  manageDevice: " تفصیلی",
  details: " جزئیات",
  deleteSuccess: " حذف شده با موفقیت!",
  deleteFail: " حذف نشد",
  renewalLink: " لینک تجدید",
  deleteGroupTips: " این که آیا یک گروه را حذف کنید",
  addGroup: " اضافه کردن گروه",
  jurisdictionRange: "محدوده قدرت: تابع قابل تغییر",
  machineSellTransfer: "انتقال تجهیزات تجهیزات",
  monitorMachineGroup: " گروه بندی نظارت بر دستگاه",
  jurisdictionArr: [
    "مدیریت مشتری، مدیریت پیام، تنظیم حصار، اطلاعات هشدار، مدیریت حساب های مجازی، صدور دستورالعمل",
  ],
  confrimDelSim: "اطمینان حاصل کنید که شماره سیم کارت را حذف کنید:",

  // منوی راست کلیک کنید
  sellDevice: " تجهیزات فروش",
  addClient: " اضافه کردن مشتریان جدید",
  deleteClient: "حذف مشتری",
  resetPassword: " بازنشانی گذرواژه",
  transferClient: " انتقال مشتری",
  ifDeleteClient: "پاک کردن",

  //myAccount
  myWorkPlace: "میز کار",
  availablePoints: "نقاط موجود",
  yearCard: "کارت سال",
  lifetimeOfCard: " کارت مادام العمر",
  oneyear: " یک سال",
  lifetime: " طول عمر",
  oneyearPoint: "نقطه واردات یک ساله",
  commonImportPoint: " نقطه واردات معمول",
  lifetimeImportPoint: " نقطه واردات طول عمر",
  myServiceProvide: " ارائه دهنده خدمات",
  moreOperator: " عملیات بیشتر",
  dueMachine: "دستگاه منقضی شده",
  offlineMachine: "دستگاه آفلاین",
  quickSell: "فروش سریع",
  sellTo: " فروش به",
  machineBelong: "تجهیزات متعلق به",
  reset: "تنظیم مجدد",
  targetCustomer: "مشتری هدف",
  common_lifetimeImport: "نقطه وارد کردن عادی (0)، نقطه واردات عمر (0)",
  cardType1: "نوع اشتراک",
  credit: "نقاط شارژ",
  generateImportPoint: " تولید نقاط واردات",
  generateImportPointSuc: " نقطه واردات را با موفقیت تولید کنید",
  generateImportPointFail: " نمرات واردات ناکام شد",
  year_lifeTimeCard: " (0)، کارت Lifetime (0)",
  generateRenewPoint: "ایجاد نقطه تجدید",
  transferTo: "انتقال به",
  transferPoint: " نقاط انتقال",
  transferRenewPoint: "کارت تمدید انتقال",
  pointHistoryRecord: "رکورد نقطه",
  newGeneration: "نسل جدید",
  operatorType: " نوع عملیات",
  consume: "مصرف",
  give: " بیا",
  income: " درآمد",
  pay: "هزینه ها",
  imeiErr: "برای دستگاه مورد نظر، شماره IMEI باید حداقل 6 رقم آخر باشد!",
  accountFirstPage: "صفحه اصلی حساب",

  /*
   * dealer  پایان ترجمه رابط
   *
   * */
  // صفحه 1.4.8 کنترل باد کنترل بخشی
  finrisk: " کنترل ریسک مالی",
  attention: "توجه",
  cancelattention: "لغو شدن",
  poweroff: "خاموش",
  inout: " داخل و خارج",
  inoutEF: "داخل و خارج از حصار",
  longstay: "اقامت دوم",
  secsetting: " تنظیم دو نقطه",
  EFsetting: " تنظیم نرده",
  polygonFence: " حصار الکترونیک چند ضلعی",
  cycleFence: "حصار الکترونیکی گرد",
  haveBeenSetFence: " حصار الکترونیکی تنظیم شده است",
  haveBeenSetPoint: " دو شرط بندی تعیین شده است",
  drawingFailed: " نقاشی شکست خورد، لطفا تجدید نظر کنید",
  inoutdot: "ورود و خروج",
  eleStatistics: "آمار برق",
  noData: "بدون اطلاعات",
  // لیست ورود و خروج
  accountbe: "حساب",
  SMtype: "نوع دو نقطه",
  SMname: " نام پست دوم",
  time: "زمان",
  position: "محل سکونت",
  lastele: "باقی مانده",
  statisticTime: " زمان آماری",
  searchalarmType: [
    "همه، آفلاین، خاموش، در داخل و خارج از حصار، در داخل و خارج از دو، دو اقامت طولانی",
  ],
  remarks: ["شرط دوم، شرکت تضمین، نقطه انحلال، بازار معا"],
  focusOnly: "فقط تمرکز کن",
  // [?]توضیحات
  interpretSignal: "سیگنال: آخرین بار که دستگاه با پلت فرم ارتباط برقرار کرد",
  interpretPosition: "موقعیت: آخرین موقعیت  موقعیت ماهواره ای دستگاه",
  interpretAll:
    "دستگاه آنلاین موقع ایستادن نیست، اما همچنان با پلت فرم ارتباط برقرار خواهد کرد",

  autoRecord: "ضبط خودکار",
  /******************************************************تنظیم دستورالعمل برای شروع**********************************8*/
  setCtrl: {
    text: " دستورالعمل تنظیم",
    value: "",
  },
  moreCtrl: {
    text: 'دستورالعمل های بیشتر',
    value: ''
  },
  sc_openTraceModel: {
    text: "حالت ردیابی را روشن کنید",
    value: "0",
  },
  sc_closeTraceModel: {
    text: " خاموش کردن حالت ردیابی",
    value: "1",
  },
  sc_setSleepTime: {
    text: " زمان خواب را تنظیم کنید",
    value: "2",
  },
  sc_setAwakenTime: {
    text: " تنظیم زمان بیداری",
    value: "3",
  },
  sc_setDismantleAlarm: {
    text: "تنظیم زنگ هشدار",
    value: "4",
  },
  sc_setSMSC: {
    text: " شماره مرکز را افزایش دهید",
    value: "5",
  },
  sc_delSMSC: {
    text: " شماره مرکز را حذف کنید",
    value: "6",
  },
  sc_setSOS: {
    text: "اضافه کردن SOS",
    value: "7",
  },
  sc_delSOS: {
    text: "حذف SOS",
    value: "8",
  },
  sc_restartTheInstruction: {
    text: "راه اندازی مجدد فرمان",
    value: "9",
  },
  sc_uploadTime: {
    text: "تنظیم فاصله آپلود",
    value: "10",
  },
  /* تنظیم زمان بیدار شدن از زنگ هشدار
     زمان تنظیم زمان بازگشت
      تنظیم زنگ هشدار
     هفته حالت روشن و خاموش*/
  sc_setAlarmClock: {
    text: " ساعت زنگ هشدار را تنظیم کنید",
    value: "11",
  },
  sc_setTimingRebackTime: {
    text: " زمان بازگشت بهنگام را تنظیم کنید",
    value: "12",
  },
  sc_openWeekMode: {
    text: "حالت هفته را روشن کنید",
    value: "13",
  },
  sc_closeWeekMode: {
    text: " حالت هفته را خاموش کنید",
    value: "14",
  },
  sc_powerSaverMode: {
    text: " حالت بازگشت زمان را تنظیم کنید",
    value: "15",
  },
  sc_carCatchingMode: {
    text: "تنظیم حالت تعقیب",
    value: "16",
  },
  sc_closeDismantlingAlarm: {
    text: " تنظیمات زنگ خطر را غیرفعال کنید",
    value: "17",
  },
  sc_openDismantlingAlarm: {
    text: "تنظیمات زنگ تامر را روشن کنید",
    value: "18",
  },
  sc_VibrationAlarm: {
    text: " زنگ ارتعاش را تنظیم کنید",
    value: "19",
  },
  sc_timeZone: {
    text: "تنظیم منطقه زمانی",
    value: "20",
  },
  sc_phoneMonitor: {
    text: "نظارت تلفن",
    value: "21",
  },
  sc_stopCarSetting: {
    text: "تنظیم پارکینگ",
    value: "22",
  },
  sc_bindAlarmNumber: {
    text: "شماره زنگ هشدار",
    value: "23",
  },
  sc_bindPowerAlarm: {
    text: " زنگ هشدار برق",
    value: "24",
  },
  sc_fatigueDrivingSetting: {
    text: "راه اندازی خستگی رانندگی",
    value: "25",
  },
  sc_peripheralSetting: {
    text: " تنظیمات محیطی",
    value: "26",
  },
  sc_SMSAlarmSetting: {
    text: "تنظیم زنگ SMS",
    value: "27",
  },
  sc_autoRecordSetting: {
    text: "تنظیمات ضبط خودکار",
    value: "28",
  },
  sc_monitorCallback: {
    text: " مانیتور پاسخ تماس",
    value: "29",
  },
  sc_recordCtrl: {
    text: " دستورالعمل ضبط",
    value: "30",
  },
  sc_unbindAlarmNumber: {
    text: "شماره زنگ هشدار را لغو کنید",
    value: "31",
  },
  sc_alarmSensitivitySetting: {
    text: " زنگ لرزش تنظیمات SMS",
    value: "32",
  },
  sc_alarmSMSsettings: {
    text: " تنظیمات زنگ تلفن لرزش",
    value: "33",
  },
  sc_alarmCallSettings: {
    text: "زنگ هشدار برق را روشن کنید",
    value: "34",
  },
  sc_openFailureAlarmSetting: {
    text: " زنگ هشدار برق را روشن کنید",
    value: "35",
  },
  sc_restoreFactory: {
    text: " بازگرداندن کارخانه",
    value: "36",
  },
  sc_openVibrationAlarm: {
    text: " زنگ ارتعاش را روشن کنید",
    value: "37",
  },
  sc_closeVibrationAlarm: {
    text: "زنگ زنگ لرزش را خاموش کنید",
    value: "38",
  },
  sc_closeFailureAlarmSetting: {
    text: " خاموش کردن زنگ هشدار برق",
    value: "39",
  },
  sc_feulAlarm: {
    text: "تنظیم مقدار زنگ مقدار روغن",
    value: "40",
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: "حالت صرفه جویی در مصرف برق",
    value: "41",
  },
  sc_sleepMode: {
    text: "حالت خواب",
    value: "42",
  },
  sc_alarmMode: {
    text: "حالت هشدار",
    value: "43",
  },
  sc_weekMode: {
    text: "حالت هفته",
    value: "44",
  },
  sc_monitorNumberSetting: {
    text: "نظارت بر تنظیم شماره",
    value: "45",
  },
  sc_singlePositionSetting: {
    text: "حالت موقعیت یگانه",
    value: "46",
  },
  sc_timingworkSetting: {
    text: "حالت به موقع",
    value: "47",
  },
  sc_openLightAlarm: {
    text: "زنگ سنسور نور باز",
    value: "48",
  },
  sc_closeLightAlarm: {
    text: "هشدار حسگر نور را ببندید",
    value: "49",
  },
  sc_workModeSetting: {
    text: "تنظیم حالت کار",
    value: "50",
  },
  sc_timingOnAndOffMachine: {
    text: "تنظیم زمان تنظیم سوئیچ",
    value: "51",
  },
  sc_setRealTimeTrackMode: {
    text: "حالت تعقیب در زمان واقعی را تنظیم کنید",
    value: "52",
  },
  sc_setClockMode: {
    text: "حالت زنگ را تنظیم کنید",
    value: "53",
  },
  sc_openTemperatureAlarm: {
    text: "زنگ دما را روشن کنید",
    value: "54",
  },
  sc_closeTemperatureAlarm: {
    text: "Désactiver l'alarme de température",
    value: "55",
  },
  sc_timingPostbackSetting: {
    text: "تنظیمات زمانبندی برگشت",
    value: "56",
  },
  sc_remoteBoot: {
    text: "بوت از راه دور",
    value: "57",
  },
  sc_smartTrack: {
    text: "بوت از راه دور",
    value: "58",
  },
  sc_cancelSmartTrack: {
    text: "ردیابی هوشمند را لغو کنید",
    value: "59",
  },
  sc_cancelAlarm: {
    text: "زنگ را لغو کنید",
    value: "60",
  },
  sc_smartPowerSavingMode: { 
    text:"تنظیم حالت صرفه جویی در مصرف انرژی هوشمند",
    value:"61"
  },
  sc_monitorSetting: {
    text: "مانیتور کنید",
    value: '62'
  },
  // 指令重构新增翻译
  sc_timedReturnMode: {
    text: 'حالت بازگشت به موقع',
    value: '100'
    },
    sc_operatingMode: {
        text: ' حالت عملیاتی',
        value: '101'
    },
    sc_realTimeMode : {
        text: 'حالت موقعیت یابی در زمان واقعی',
        value: '102'
    },
    sc_alarmMode : {
        text: 'حالت هشدار',
        value: '103'
    },
    sc_weekMode : {
        text: 'حالت هفته',
        value: '104'
    },
    sc_antidemolitionAlarm : {
        text: 'زنگ ضد انهدام',
        value: '105'
    },
    sc_vibrationAlarm : {
        text: 'زنگ لرزش',
        value: '106'
    },
    sc_monitoringNumber : {
        text: 'نظارت بر مدیریت شماره',
        value: '107'
    },
    sc_queryMonitoring : {
        text: 'شماره نظارت پرس و جو',
        value: '108'
    },
    sc_electricityControl : {
        text: 'کنترل روغن و برق',
        value: '109'
    },
    sc_SOSnumber : {
        text: 'مدیریت شماره SOS',
        value: '110'
    },
    sc_SleepCommand : {
      text: 'دستور خواب',
      value: '201'
    },
    sc_RadiusCommand : {
        text: 'شعاع جابجایی',
        value: '202'
    },
    sc_punchTimeMode:{
        text:'打卡模式',
        value:'203'  
    },
    sc_intervelMode:{
        text:'时间段模式',
        value:'204'  
    },
    sc_activeGPS:{
        text:'激活GPS',
        value:'205'  
    },
    sc_lowPowerAlert: {
        text: 'یادآوری باتری کم',
        value: '206'
    },
    sc_SOSAlert: {
        text: 'SOS报警',
        value: '207'
    },
    mc_cuscom : {
        text: 'دستورالعمل سفارشی',
        value: '1'
    },
    NormalTrack: 'حالت ردیابی عادی',
    listeningToNumber:'آیا مطمئن هستید که می خواهید شماره نظارت  را بررسی کنید؟',
    versionNumber:'آیا مطمئن هستید که می خواهید شماره نسخه را بررسی کنید؟',
    longitudeAndLatitudeInformation:'آیا مطمئن هستید که می خواهید اطلاعات طول و عرض جغرافیایی را بررسی کنید؟',
    equipmentStatus:'آیا مطمئن هستید که می خواهید وضعیت  را بررسی کنید؟',
    public_parameter:'آیا مطمئن هستید که می خواهید پارامترهای را بررسی ',
    GPRS_parameter:' آیا مطمئن هستید که می خواهید پارامترهای GPRS از  را بررسی کنید؟',
    deviceName: 'آیا مطمئن هستید که می خواهید دستگاه  را رول کنید؟',
    SMS_alert:' آیا مطمئن هستید که می خواهید زنگ یادآوری پیام کوتاه  را بررسی کنید؟',
    theBindingNumber:'آیا مطمئن هستید که می خواهید شماره صحافیرا بررسی کنید؟',
    intervalTimeRange:'تنظیم فاصله زمانی 001-999 ، واحد (دقیقه)',
    pleaseChoose:'لطفا انتخاب کنید',
    RealTimeCarChase:'آیا مطمئن هستید که می خواهید این دستگاه را در حالت تعقیب اتومبیل در زمان واقعی تنظیم کنید؟',
    inputPhoneNumber: "لطفا شماره تلفن را وارد کنید",
    inputCorPhoneNumber: "لطفا شماره تلفن صحیحی را وارد کنید",
    autoCallPhone: "نکته: پس از اجرای موفقیت آمیز دستور ، ترمینال به طور خودکار شماره تنظیم شده را شماره گیری می کند",
    limitTheNumberOfCellPhoneNumbers1:'این فرمان حداکثر 5 شماره تلفن همراه را پشتیبانی می کند',
    limitTheNumberOfCellPhoneNumbers2:' این فرمان حداکثر 3 شماره تلفن همراه را پشتیبانی می کند',
    equipmentTorestart:' مطمئن هستید که می خواهید این دستگاه را مجدداً راه اندازی کنید',
    remindTheWay:'راه تذکر',
    alarmWakeUpTime:'زمان بیدار شدن از خواب',
    alarmWakeUpTime1:'زمان بیدار شدن از خواب 1',
    alarmWakeUpTime2:'زمان بیدار شدن از خواب 2',
    alarmWakeUpTime3:'زمان بیدار شدن از خواب 3',
    alarmWakeUpTime4:'زمان بیدار شدن از خواب 4',
    sensitivityLevel:'لطفاً سطح حساسیت را انتخاب کنید',
    parking_time:' زمان پارکینگ',
    selectWorkingMode:'لطفاً حالت کار را انتخاب کنید',
    Alarm_value:'مقدار هشدار',
    Buffer_value:' مقدار بافر',
    gqg_disconnect:'قطع شدن',
    gqg_turnOn:'روشن کن',
    Return_interval:'اصله بازگشت',
    gq_startTime:' زمان شروع',
    gq_restingTime:'زمان استراحت',
    gq_Eastern:'منطقه زمانی شرقی',
    gq_Western:'منطقه زمانی غربی',

    gq_driver:'دزدگیر رانندگی خستگی',
    gq_deviceName:'آیا مطمئن هستید که می خواهید این دستگاه را رول کنید؟',
    gq_noteAlarm:'آیا مطمئن هستید که می خواهید زنگ یادآوری پیامک را بررسی کنید؟',
    gq_restoreOriginal:'آیا مطمئن هستید که می خواهید این تجهیزات را به کارخانه اصلی بازیابی کنید؟',
    gq_normalMode:'حالت عادی',
    gq_IntelligentsleepMode:'حالت خواب هوشمند',
    gq_DeepsleepMode:'حالت خواب عمیق',
    gq_RemotebootMode:'حالت بوت از راه دور',
    gq_IntelligentsleepModeTips:'یا مطمئن هستید که می خواهید روی حالت خواب هوشمند تنظیم کنید',
    gq_DeepsleepModeTips:'آیا مطمئن هستید که می خواهید حالت خواب عمیق تنظیم کنید',
    gq_RemotebootModeTips:' مطمئن هستید که می خواهید روی حالت راه انداز راه دور تنظیم کنید',
    gq_normalModeTips:'آیا مطمئن هستید که می خواهید در حالت عادی تنظیم کنید ،',
    gq_sleepModeTips:'آیا مطمئن هستید که می خواهید این دستگاه را در حالت خواب تنظیم کنید؟',
    gq_Locatethereturnmode:'حالت بازگشت به موقعیت',
    gq_regularWorkingHours:'دوره کاری به پایان رسیده',
    gq_AlarmType:{
        text: 'نوع زنگ هشدار',
        value: '111'
    },
    IssuedbyThePrompt:'دستور صادر شده است ، لطفا منتظر جواب دستگاه باشید',
    platformToinform:'اعلان بستر های نرم افزاری',
    gq_shortNote:'اطلاع رسانی پیام کوتاه', 
  /************فرمان بومی**********************/
  closeDismantlingAlarm: "تنظیمات زنگ خطر را غیرفعال کنید",
  openDismantlingAlarm: " تنظیمات زنگ تامر را روشن کنید",
  closeTimingRebackMode: " تنظیم حالت بازگشت بهنگام را خاموش کنید",
  minute: "دقیقه",
  timingrebackModeSetting: "حالت بازگشت زمان را تنظیم کنید:",
  setWakeupTime: "تنظیم زمان بیداری",
  weekModeSetting: "حالت هفته را تنظیم کنید:",
  closeWeekMode: " تنظیم حالت روز تنظیم را خاموش کنید",
  setRealtimeTrackMode: " حالت تعقیب در حالت واقعی را تنظیم کنید",
  fortification: "غنی سازی",
  disarming: " خلع سلاح",
  settimingrebackmodeinterval: "فاصله زمانبندی حالت بازگشت زمان را تنظیم کنید:",
  oilCutCommand: "فرآیند برش نفت",
  restoreOilCommand: " بازیابی دستورالعمل های نفت و گاز",
  turnNnTheVehiclesPower: " قدرت خودرو را فعال کنید",
  turnOffTehVehiclesPower: " قدرت خودرو را خاموش کنید",
  implementBrakes: "ترمز اجرا",
  dissolveBrakes: " ترمزها را آزاد کنید",
  openVoiceMonitorSlarm: " صدای زنگ را فعال کنید",
  closeVoiceMonitorAlarm: " صدای زنگ را خاموش کنید",
  openCarSearchingMode: "حالت ماشین را روشن کنید",
  closeCarSearchingMode: "حالت خودرو را خاموش کنید",
  unrecognizedCommand: " قادر به تشخیص دستورالعمل نیست",
  commandSendSuccess: "تبریک می گویم، دستور اجرای دستگاه موفق است!",
  /******************************************** پایان فرمان نصب**************************************************/

  /******************************************** فرمان پرس آغاز می شود**************************************************/
  queryCtrl: {
    text: " دستورالعمل پرس و جو",
    value: "",
  },
  /*پرس و جو تنظیم پارامتر*/
  qc_softwareVersion: {
    text: "نسخه نرم افزار درخواستی",
    value: "1",
  },
  qc_latlngInfo: {
    text: "اطلاعات عرض جغرافیایی و طول جغرافیایی",
    value: "2",
  },
  qc_locationHref: {
    text: " پیکربندی پارامتر پرس و جو",
    value: "3",
  },
  qc_status: {
    text: "وضعیت پرس و جو",
    value: "4",
  },
  qc_gprs_param: {
    text: " درخواست پارامترهای GPRS",
    value: "5",
  },
  qc_name_param: {
    text: " نام",
    value: "6",
  },
  qc_SMSReminderAlarm_param: {
    text: " پیام زنگ هشدار یادآوری پرس و جو",
    value: "7",
  },
  qc_bindNumber_param: {
    text: " شماره مربوط به درخواست",
    value: "8",
  },

  /********************************************پایان فرمان پرس و جو**************************************************/

  /******************************************* فرمان کنترل شروع می شود***************************************************/

  controlCtrl: {
    text: " فرمان کنترل شروع می شود",
    value: "",
  },
  cc_offOilElectric: {
    text: "دستورالعمل کنترل",
    value: "1",
  },
  cc_recoveryOilElectricity: {
    text: "بازیافت نفت و گاز",
    value: "2",
  },
  cc_factorySettings: {
    text: " تنظیم مجدد کارخانه",
    value: "4",
  },
  cc_fortify: {
    text: " غنی سازی",
    value: "75",
  },
  cc_disarming: {
    text: " خلع سلاح",
    value: "76",
  },
  cc_brokenOil: {
    text: " آموزش برش روغن",
    value: "7",
  },
  cc_RecoveryOil: {
    text: "مدار روغن بازیابی",
    value: "8",
  },

  /*******************************************پایان فرمان کنترل***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: "کیلو متر",
  mileage: "مسافت پیموده شده",
  importMachine: "وارد کردن دستگاه",
  transferImportPoint: "انتقال اشتراک جدید",
  machineType1: "مدل تجهیزات",
  confirmIMEI: "لطفا شماره و مدل IMEI قبل از عملیات را تایید کنید.",
  renew: " هزینه بازپرداخت",
  deductPointNum: " نقاط تخفیف",
  renewSuccess: "تجدید موفقیت!",
  wireType: [
    {
      text: " سیم",
      value: false,
    },
    {
      text: "بی سیم",
      value: true,
    },
  ],
  vibrationWays: [
    {
      text: " پلت فرم",
      value: 0,
    },
    {
      text: "پلت فرم + اس ام اس",
      value: 1,
    },
    {
      text: "پلت فرم + SMS + تلفن",
      value: 2,
    },
  ],
  addMachineType: [
    {
      text: "S06",
      value: "3",
    },
    //  SO6 زیر سطح --- شروع -----
    {
      text: "GT06",
      value: "8",
    },
    {
      text: "S08V",
      value: "9",
    },
    {
      text: "S01",
      value: "10",
    },
    {
      text: "S01T",
      value: "11",
    },
    {
      text: "S116",
      value: "12",
    },
    {
      text: "S119",
      value: "13",
    },
    {
      text: "TR06",
      value: "14",
    },
    {
      text: "GT06N",
      value: "15",
    },
    {
      text: "S101",
      value: "16",
    },
    {
      text: "S101T",
      value: "17",
    },
    {
      text: "S06U",
      value: "18",
    },
    {
      text: "S112U",
      value: "19",
    },
    {
      text: "S112B",
      value: "20",
    },
    // کودک SO6 ---- پایان
    {
      text: "S15",
      value: "1",
    },
    {
      text: "S05",
      value: "2",
    },
    {
      text: "SW06",
      value: "4",
    },
    {
      text: "S001",
      value: "5",
    },
    {
      text: "S08",
      value: "6",
    },
    {
      text: "S09",
      value: "7",
    },
  ],

  /*
   * 2018-02-02
   * */
  maploadfail: "نقشه فعلی بارگیری نشد. آیا به نقشه دیگری تغییر کرده اید؟",

  /*
    2018-03-06فرمان جدید کنترل
    * */
  cc_openPower: {
    text: " روشن کردن وسیله نقلیه",
    value: "7",
  },
  cc_closePower: {
    text: " قدرت خودرو را خاموش کنید",
    value: "8",
  },
  cc_openBrake: {
    text: " ترمز خودرو را روشن کنید",
    value: "9",
  },
  cc_closeBrake: {
    text: " ترمز خودرو را خاموش کنید",
    value: "10",
  },
  cc_openAlmrmvoice: {
    text: " زنگ خودرو را روشن کنید",
    value: "11",
  },
  cc_closeAlmrmvoice: {
    text: "زنگ خودرو را خاموش کنید",
    value: "12",
  },
  /*2018-03-06 فرمان جدید کنترل
   * */
  cc_openFindCar: {
    text: "ماشین را روشن کنید تا یک ماشین پیدا کنید",
    value: "13",
  },
  cc_closeFindCar: {
    text: "ماشین را خاموش کنید",
    value: "14",
  },

  /*2018-03-19
   * */
  EF: "حصار",

  /*
    2018-03-29，زمینه تمدید شده
    * */
  exData: ["برق، ولتاژ، مقدار روغن، درجه حرارت","مقاومت"],

  /*
    2018-04-10
    * */
  notSta: " موقعیت ایستگاه پایه، که در آمار موجود نیست ",

  // آمار کسر نفت
  fuelSetting: "تنظیم مقدار روغن",
  mianFuelTank: "مخزن سوخت اصلی",
  auxiliaryTank: "مخزن سوخت دوم",
  maximum: " حداکثر",
  minimum: " حداقل مقدار",
  FullTankFuel: "حجم کامل مخزن",
  fuelMinValue: 'حجم سوخت نمی تواند کمتر از 10L باشد',
  standardSetting: " تنظیم استاندارد",
  emptyBoxMax: " جعبه خالی حداکثر",
  fullBoxMax: "حداکثر جعبه کامل ",
  fuelStatistics: "آمار کسر نفت",
  settingSuccess: "راه اندازی موفق",
  settingFail: "راه اندازی نشد",
  pleaseInput: " لطفا وارد شوید",
  fuelTimes: "بار سوخت گیری",
  fuelTotal: "کل سوخت رسانی",
  refuelingTime: 'زمان سوخت گیری',
  fuelDate: " زمان سوختگیری",
  fuel: "مقدار روغن",
  fuelChange: "تغییر مقدار روغن",
  feulTable: "جدول تجزیه و تحلیل روغن",
  addFullFilter: "لطفا فیلتر کامل را اضافه کنید",
  enterIntNum: "لطفا یک عدد صحیح مثبت وارد کنید",
  // آمار دما
  tempSta: "آمار دما",
  tempTable: " جدول تجزیه و تحلیل دما",
  industrySta: "آمار صنعت",
  temperature: "دما",
  temperature1: "1دما",
  temperature2: "2دما",
  temperature3: "3دما",
  tempRange: "محدوده دما",
  tempSetting:'تنظیم دما',
  tempSensor:'سنسور دما',
  tempAlert:'پس از خاموش شدن سنسور دما ، داده دما را دریافت نمی کند!',
  phoneNumber: " شماره تلفن همراه",
  sosAlarm: "زنگ SOS",
  undervoltageAlarm: " هشدار ناخوشایند",
  overvoltageAlarm: "هشدار بیش از حد",
  OilChangeAlarm: "مقدار زنگ خطر تغذیه",
  accDetection: "تشخیص ACC",
  PositiveAndNegativeDetection: "تشخیص مثبت و منفی",
  alermValue: "مقدار زنگ هشدار",
  bufferValue: " مقدار بافر",
  timeZoneDifference: " تفاوت زمان منطقه",
  meridianEast: "مریدین شرقی",
  meridianWest: " مریدین غرب",
  max12hour: "تفاوت ورودی نمی تواند بیش از 12 ساعت باشد",

  trackDownload: "دانلود آهنگ",
  download: " دانلود",
  multiReset: " بازنشانی فله",
  resetSuccess: " با موفقیت تنظیم مجدد",
  multiResetTips:
    "داده های تست مانند زمان فعال شدن دستگاه و آهنگ بعد از تنظیم مجدد پاک می شود و وضعیت دستگاه به صورت آنلاین یا آفلاین تنظیم مجدد می شود.",
  point: "نقطه",
  myplace: " محل من",
  addPoint: "اضافه کردن نقطه",
  error10018: " تعداد ناکافی نقاط واردات",
  error110:'شی وجود ندارد',
  error109:'حداکثر حد مجاز',
  error20013:'نوع دستگاه وجود ندارد',
  error90001:'شماره سریال نوع دستگاه نمی تواند خالی باشد',
  error20003:'ایمی نمی تواند خالی باشد',
  inputName: " لطفا یک نام را وارد کنید",
  virtualAccount: "حساب مجازی",
  createTime: " زمان ایجاد",
  permission: " مجوز",
  permissionRange: " محدوده اقتدار",
  canChange: " تابع قابل اصلاح",
  fotbidPassword: "تغییر رمز عبور",
  virtualAccountTipsText:
    " هنگام ایجاد یک حساب مجازی، آن یک حساب نام مستعار حساب فروشنده فعلی ثبت شده است و می تواند مجوز برای حساب مجازی را تنظیم کند.",
  noOperationPermission: " حساب مجازی دارای هیچ حقوق عملی نیست",
  number: "شماره",
  rangeSetting: " تنظیم محدوده",
  setting: " تنظیم",
  // 1.6.1
  duration: " مدت زمان",
  voltageSta: "آمار ولتاژ",
  voltageAnalysis: " تجزیه و تحلیل ولتاژ",
  voltageEchart: " جدول تجزیه و تحلیل ولتاژ",
  platformAlarm: "زنگ پلت فرم",
  platformAlarm1:" تلفن",
  platformAndPhone: 'تلفن+زنگ تلفن',
  smsAndplatformAlarm: "SMS + زنگ تلفن",
  smsAndplatformAlarm1: "SMS",
  smsAndplatformAlarmandPhone: " زنگ پلت فرم + SMS + تلفن",
  smsAndplatformAlarmandPhone1: "SMS + تلفن",
  more_speed: " سرعت",
  attribute: " مشخصه",
  profession: " حرفه ای",
  locationPoint: "نقطه پایانی",
  openPlatform: " پلت فرم باز",
  experience: "من می خواهم تجربه کنم",
  onlyViewMonitor: " تنها نظارت را می توان مشاهده کرد",

  // 1.6.3
  inputAccountOrUserName: "لطفا یک شماره حساب کاربری یا نام کاربری وارد کنید",
  noDeviceTips:
    " اطلاعات تجهیزات مربوطه را پیدا نکردید، نقطه مشتری را بررسی کنید",
  noUserTips:
    "اطلاعات کاربر مربوطه را پیدا نکردید، امتیازات تجهیزات را بررسی کنید",
  clickHere: "اینجا",
  // 1.6.4
  pointIntervalSelect: " فاصله نقطه پیگیری",
  payment: " پرداخت",
  pleaceClick: " لطفا کلیک کنید",
  paymentSaveTips:
    "نکته ایمنی: لطفا با تأیید سرویس دهنده اعتبار لینک را تأیید کنید",
  //1.6.4 اضافه شده است
  fuelAlarmValue: "مقدار زنگ مقدار نفت",
  fuelConsumption: " مصرف سوخت",
  client: " مشتری",
  create: "جدید",
  importPoint: " نقطه واردات",
  general: " رایج است",
  lifelong: " طول عمر",
  renewalCard: "تمدید اشتراک",
  settingFuelFirst:
    "لطفا قبل از ارسال فرمان مقدار زنگ مقدار مقدار نفت را تنظیم کنید!",
  overSpeedSetting: "تنظیم سرعت",
  kmPerHour: "کیلومتر / ساعت",
  times: " بارها",
  total: " کل",
  primary: "پروردگار",
  minor: "معاون",
  unActiveTips: "این دستگاه فعال نیست و نمی تواند مورد استفاده قرار گیرد.",
  arrearsTips:
    " این دستگاه دارای عقب افتادگی است و نمی تواند از این ویژگی استفاده کند",
  //1.6.5
  loading: "بارگیری اطلاعات ....",
  expirationReminder: "یادآوری تاریخ انقضا",
  projectName: "نام پروژه",
  expireDate: "زمان انقضا",
  changePwdTips:
    "رمز عبور شما بسیار ساده است و یک خطر امنیتی وجود دارد. لطفا رمز عبور خود را بلافاصله تغییر دهید.",
  pwdCheckTips1: "پیشنهادات 6 تا 20 حرف، شماره یا نماد است",
  pwdCheckTips2: " گذرواژه وارد شده خیلی ضعیف است",
  pwdCheckTips3: " رمز عبور شما می تواند پیچیده تر باشد.",
  pwdCheckTips4: "رمز عبور شما امن است.",
  pwdLevel1: "ضعیف",
  pwdLevel2: "متوسط",
  pwdLevel3: "قوی",
  comfirmChangePwd: "تعیین رمز عبور برای تغییر",
  notSetYet: " هنوز مشخص نشده است",
  // 1.6.6
  liter: "ل",
  arrearageDayTips: " با توجه به روزها",
  todayExpire: "با توجه به امروز",
  forgotPwd: " رمز عبور خود را فراموش کرده اید؟",
  forgotPwdTips: "برای تغییر رمز عبور خود لطفا با فروشنده تماس بگیرید.",
  //1.6.7
  commonProblem: " سوالات متداول",
  instructions: "دستورالعمل ها",
  webInstructions: "دستورالعمل عملیات WEB",
  appInstructions: "دستورالعمل عملیات APP",
  acceptAlarmNtification: "دریافت اخطار هشدار دهنده",
  alarmPeriod: "دوره هشدار",
  whiteDay: "روزی",
  blackNight: "شب تاریک",
  allDay: "تمام روز",
  alarmEmail: "نامه هشدار دهنده",
  muchEmailTips:
    "صندوق پستی چندگانه را می توان وارد کرد، توسط نماد '؛' جدا شده است.",
  newsCenter: "مرکز پیام",
  allNews: "همه اخبار",
  unReadNews: "پیام خوانده نشده",
  readNews: "پیام را بخوانید",
  allTypeNews: "همه انواع پیام",
  alarmInformation: "اطلاعات هشدار",
  titleContent: "محتوای عنوان",
  markRead: "علامت گذاری شده به عنوان خوانده شده",
  allRead: "همه خوانده شده",
  allDelete: "همه را حذف کنید",
  selectFirst: "لطفا آن را قبل از ادامه انتخاب کنید!",
  updateFail: "بروزرسانی انجام نشد!",
  ifAllReadTips: "آیا قرار است همه بخوانند؟",
  ifAllDeleteTips: "همه حذف شده اند؟",
  stationInfo: "اطلاعات ایستگاه",
  phone: "تلفن همراه",
  //1.6.72
  plsSelectTime: "لطفا زمان را انتخاب کنید!",
  customerNotFound: "مشتری را پیدا نمی کنید",
  //1.6.8
  Postalcode: "محل سکونت",
  accWarning: "زنگ باز ACC",
  canInputMultiPhone:
    "لطفاً چندین شماره موبایل را وارد کنید ، لطفاً استفاده کنید ؛ جداگانه",
  noLocationInfo: "دستگاه هنوز اطلاعات مربوط به مکان را ندارد.",
  //1.6.9
  fenceName: "نام حصار",
  fenceManage: "مدیریت نرده",
  circular: "دایره",
  polygon: "چند ضلعی",
  allFence: "همه نرده ها",
  shape: "شکل",
  stationNews: "پیام سایت",
  phonePlaceholder:
    'می توانید چندین شماره را وارد کنید ، که با "" ، جدا شده اند ',
  addressPlaceholder: "لطفا آدرس و کد پستی را به ترتیب وارد کنید ،",
  isUnbind: "آیا می خواهید ارتباط برقرار کنید",
  alarmCar: "خودرو زنگ دار",
  alarmAddress: "مکان هشدار",
  chooseAtLeastOneTime: "حداقل یک بار انتخاب کنید",
  alarmMessage: "جزئیات این اطلاعات زنگ هشدار وجود ندارد",
  navigatorBack: "بازگشت به برتر",
  timeOverMessage: "زمان انقضا کاربر نمی تواند بیشتر از زمان انقضا بستر باشد",
  //1.7.0
  userTypeStr: "نوع کاربر",
  newAdd: "جدید",
  findAll: "کل",
  findStr: " تطبیق داده ها",
  customColumn: "سفارشی سازی",
  updatePswErr: "گذرواژه به روزرسانی شد",
  professionalUser: "کاربر حرفه ای یا نه؟",
  confirmStr: "تأیید کنید",
  inputTargetCustomer: "مشتری هدف ورودی",
  superiorUser: "حساب سرپرست",
  speedReport: "گزارش سرعت",
  createAccount: "ایجاد حساب کاربری",
  push: "فشار",
  searchCreateStr:
    "یک حساب کاربری خواهد کرد و دستگاه را به این حساب منتقل می کند.",
  allowIMEI: "اجازه ورود به IMEI",
  defaultPswTip: "گذرواژه پیش فرض آخرین 6 رقم IMEI است",
  createAccountTip: "ایجاد حساب کاربری و انتقال دستگاه موفقیت آمیز است",
  showAll: "نمایش همه",
  bingmap: "نقشه بینگ",
  areaZoom: "بزرگنمایی",
  areaZoomReduction: "بازیابی زوم",
  reduction: "کاهش",
  saveImg: "به عنوان تصویر ذخیره کنید",
  fleetFence: "نرده ناوگان",
  alarmToSub: "اعلان هشدار",
  bikeFence: "نرده دوچرخه",
  delGroupTip: "حذف نشد ، لطفاً ابتدا امتیازات را حذف کنید!",
  isExporting: "در حال صادرات ...",
  addressResolution: "وضوح آدرس ...",
  simNOTip: "شماره سیم کارت فقط می تواند یک عدد باشد",
  unArrowServiceTip:
    "زمان انقضا کاربر برای دستگاه های زیر بیشتر از زمان انقضا پلتفرم است ، لطفا مجدداً انتخاب کنید. شماره دستگاه:",
  platformAlarmandPhone: "هشدار بستر های نرم افزاری + تلفن",
  openLightAlarm: "زنگ سنسور نور باز",
  closeLightAlarm: "هشدار حسگر نور را ببندید",
  ACCAlarm: "هشدار ACC",
  translateError: "انتقال انجام نشد ، کاربر هدف اجازه ای ندارد",
  distanceTip: "OK را بزنید ، دوبار کلیک کنید تا تمام شود",
  workMode: "حالت کار",
  workModeType: "0: حالت عادی؛ 1 حالت خواب هوشمند؛ 2 حالت خواب عمیق",
  clickToStreetMap: "برای باز کردن نقشه نمای خیابان کلیک کنید",
  current: "جریان",
  remarkTip: "توجه: کارتهای بسته های مختلف را همزمان بفروشید",
  searchRes: "نتیجه جستجو",
  updateIcon: "نماد تغییر دهید",
  youHaveALarmInfo: "شما یک پیام هشدار دارید",
  moveInterval: "فاصله ورزش",
  staticInterval: "فاصله زمانی عدم فعالیت",
  notSupportTraffic: "Coming soon.",
  ignite: "Acc ON",
  flameout: "خاموش",
  generateRenewalPointSuc: "نقاط تجدید را با موفقیت ایجاد کنید",
  noGPSsignal: "موقعیتی نیست",
  imeiErr2: "لطفا حداقل 6 رقم آخر از شماره imei را وارد کنید",
  searchCreateStr2:
    "این عمل یک حساب کاربری ایجاد کرده و این دستگاه را به نام حساب منتقل می کند",
  addUser: "افزایش کاربر",
  alarmTemperature: "مقدار درجه حرارت هشدار",
  highTemperatureAlarm: "دزدگیر درجه حرارت بالا",
  lowTemperatureAlarm: "دزدگیر درجه حرارت پایین",
  temperatureTip: "لطفاً یک مقدار دما وارد کنید",
  locMode: "حالت موقعیت یابی",
  imeiInput: "لطفا شماره IMEI را وارد کنید",
  noResult: "هیچ نتیجه مطابق وجود ندارد",
  noAddressKey: "به موقع قادر به دریافت اطلاعات آدرس نیست",
  deviceGroup: "گروه بندی",
  shareManage: "اشتراک گذاری",
  lastPosition: "آخرین موقعیت",
  defaultGroup: "پیش فرض",
  tankShape: "شکل مخزن",
  standard: "استاندارد",
  oval: "بیضی",
  irregular: "بی رویه",
  //1.8.4
  inputAddressOrLoc: "آدرس / طول و عرض جغرافیایی را وارد کنید",
  inputGroupName: "نام گروه را وارد کنید",
  lock: "قفل کردن",
  shareHistory: "اشتراک گذاری",
  tomorrow: "فردا",
  threeDay: "سه روز",
  shareSuccess: "پیوند اشتراک را با موفقیت ایجاد کنید",
  effective: "تاثیر گذار",
  lapse: "سپری شدن",
  copyShareLink: "پیوند اشتراک را کپی کنید",
  openStr: "باز کن",
  closeStr: 'خاموش',
  linkError: "این پیوند اشتراک به پایان رسیده است",
  inputUserName: "لطفاً نام مشتری را وارد کنید",
  barCodeStatistics: "آمار بارکد",
  barCode: "بارکد",
  sweepCodeTime: "زمان اسکن",
  workModeType2:
    '1 حالت خواب هوشمند. 2 حالت خواب عمیق. 3 حالت روشن یا خاموش کردن از راه دور "،',
  remoteSwitchMode: "حالت سوئیچ از راه دور",
  saleTime: "تاریخ فروش",
  onlineTime: "تاریخ عرضه",
  dayMileage: "مسافت پیموده شده امروز",
  imeiNum: "IMEI",
  overSpeedValue: "آستانه واژگون",
  shareNoOpen: "پیوند اشتراک فعال نیست",
  addTo2: "اضافه کردن به",
  overDue: "منقضی شده",
  openInterface: "رابط کاربری باز",
  privacyPolicy: "سیاست حفظ حریم خصوصی",
  serviceTerm: "شرایط خدمات",
  importError: "افزودن نشد ، شماره دستگاه (IMEI) باید یک عدد 15 رقمی باشد",
  importResult: "نتایج واردات",
  totalNum: "جمع",
  successInfo: "موفقیت",
  errorInfo: "شکست",
  repeatImei: "IMEI تکرار کنید",
  includeAccount: "حساب زیر",
  formatError: "ناقص",
  importErrorInfo: "لطفاً شماره IMEI 15 رقمی را وارد کنید",
  totalMileage: "کل مسافت پیموده شده",
  totalOverSpeed: "کل overspeed (بار)",
  totalStop: "کل توقف (زمان)",
  totalOil: "کل روغن",
  timeChoose: "انتخاب زمان",
  intervalTime: "زمان بازه",
  default: "پیش فرض",
  idleSpeedStatics: "آمار سرعت کار",
  offlineStatistics: 'آمار آفلاین'  ,
  idleSpeed: "سرعت کار",
  idleSpeedTimeTip1: "زمان بیکار نمی تواند خالی باشد",
  idleSpeedTimeTip2: "زمان کار باید عدد صحیح مثبت باشد",
  averageSpeed: "سرعت متوسط",
  averageOil: "متوسط مصرف سوخت",
  oilImgTitle: "نمودار تجزیه و تحلیل روغن",
  oilChangeDetail: "جزئیات تغییر سوخت",
  machineNameError: "نام دستگاه نمی تواند حاوی نمادهای ویژه باشد (/ ')",
  remarkError: "اطلاعات اظهارات نمی توانند بیش از 50 کلمه باشند",
  defineColumnTip: "تا 12 مورد را بررسی کنید",
  pswCheckTip: "پیشنهاد ترکیبی از 6-20 رقم ، حروف و نمادها است",
  chooseGroup: "لطفاً یک گروه را انتخاب کنید",
  chooseAgain:
    "شما فقط می توانید داده ها را برای شش ماه گذشته پرس و جو کنید ، لطفا دوباره select را انتخاب کنید ",
  noDataTip: "داده ای وجود ندارد",
  noMachineNameError: "لطفاً دستگاهی را انتخاب کنید",
  loginAccountError: "حساب ورود نمی تواند 15 رقم be باشد ",
  includeExpire: "که منقضی می شود",
  groupNameTip: "نام گروه نمی تواند خالی باشد",
  outageTips: "مطمئن هستید روغن قطع شده است؟",
  powerSupplyTips: "مطمئن هستید روغن را بازیابی می کنید؟",
  centerPhoneTips: "لطفا شماره را وارد کنید",
  centerPhoneLenTips: "لطفاً 8-20 رقم را وارد کنید",
  passworldillegal: "شخصیت های غیرقانونی وجود دارد",
  // 2.0.0 POI，权限版本
  singleAdd:'اضافه کردن تنها',
  batchImport:'واردات دسته ای',
  name:'نام',
  icon:'نماد',
  defaultGroup:'گروه پیش فرض',
  remark:'یادداشت برداری',
  uploadFile:'پرونده را بارگذاری کنید',
  exampleDownload:'بارگیری به عنوان مثال',
  uploadFiles:'بارگذاری پرونده',
  poiTips1:'شما می توانید POI را با بارگذاری فایل اکسل با اطلاعات مربوطه وارد کنید. لطفاً برای تهیه پرونده از قالب مثال پیروی کنید',
  poiTips2:'نام: مورد نیاز ، بیش از 32 کاراکتر است',
  poiTips3:' آیکون: لازم است ، 1،2،3،4 وارد کنید',
  poiTips4:'عرض جغرافیایی - الزامی است',
  poiTips5:' طول - لازم است',
  poiTips6:'نام گروه: اختیاری ، بیش از 32 کاراکتر نیست. اگر نام گروه پر نشده باشد ، نقطه POI به گروه پیش فرض تعلق دارد. اگر نام گروه پر شده با نام گروه ایجاد شده مطابقت داشته باشد ، نقطه POI متعلق به گروه ایجاد شده است. نام گروه ایجاد نشده است ، سیستم گروه را اضافه می کند',
  poiTips7:'مشاهدات: اختیاری ، بیش از 50 حرف نیست',
  // 权限相关
  roleLimit: 'مجوزهای نقش',
  operateLog: 'مدیریت حساب سیستم',
  sysAccountManage: 'حساب مرجع',
  rolen: 'نقش ها',
  rolename: 'نام نقش',
  addRole: 'نقش جدید',
  editRole: 'نقش را ویرایش کنید',
  deleteRole: 'حذف نقش',
  delRoleTip: 'آیا مطمئن هستید که می خواهید این نقش را حذف کنید؟',
  delAccountTip: 'آیا مطمئن هستید که می خواهید این حساب را حذف کنید؟',
  limitconfig: 'مشخصات حقوق',
  newAccountTip1: 'حساب autority مشابه حساب مجازی قدیمی است و زیر حساب سرپرست است. سرپرستان می توانند حساب های اقتدار ایجاد کرده و نقش های مختلفی را به حساب های اقتدار اختصاص دهند ، بنابراین حساب های مختلف می توانند محتوا و عملکردهای مختلف را در سیستم عامل مشاهده کنند.',
  newAccountTip2: 'روند ایجاد یک حساب مجوز:',
  newAccountTip31: '1. در صفحه مدیریت نقش ،',
  newAccountTip32: 'نقش جدید',
  newAccountTip33: '، و مجوزها را برای نقش پیکربندی کنید.',
  newAccountTip4: '2. در صفحه مدیریت حساب autority ، یک حساب autority جدید ایجاد کرده و نقشها را به حساب اختصاص دهید.',
  newRoleTip1: 'سرپرستان می توانند نقش ها را ایجاد کرده و مجوزهای مختلف عملکرد را برای نقش های مختلف پیکربندی کنند تا در سناریوهای مختلف نیازهای کسب و کار را برآورده کنند.',
  newRoleTip2: 'به عنوان مثال ، پیکربندی کنید که آیا نقش مالی اجازه مکان یابی و نظارت را دارد ، آیا اجازه افزودن مشتری را دارد ، آیا اجازه تغییر اطلاعات دستگاه و غیره را دارد.',
  "refuelrate": "میزان سوخت گیری",
  "refuellimit": "وقتی افزایش روغن در دقیقه بیشتر از xxxxL و کمتر از xxxxL باشد ، به عنوان سوخت گیری در نظر گرفته می شود.",
  "refueltip": "حداکثر میزان سوخت گیری نباید از حداقل میزان سوخت گیری کمتر باشد!",
  viewLimitConf: 'مشاهده تنظیمات مجوز',
  viewLimit: 'مشاهده مجوزها',
  newSysAcc: 'حساب سیستم جدید',
  editSysAcc: 'حساب مجوز را ویرایش کنید',
  virtualAcc: 'گزارش عملیات',
  oriVirtualAcc: 'حساب مجازی اصلی',
  virtualTip: 'ماژول حساب مجازی به ماژول حساب سیستم ارتقا یافته است ، لطفاً یک حساب سیستم جدید ایجاد کنید',
  operaTime: 'زمان کار',
  ipaddr: 'آدرس آی پی',
  businessType: 'نوع کسب و کار',
  params: 'درخواست پارامتر',
  operateType: 'نوع عملیات',
  uAcc: 'حساب کاربری',
  uName: 'نام کاربری',
  uType: 'نوع کاربر',
  logDetail: 'جزئیات ورود به سیستم',
  delAccount: 'حذف حساب کاربری',
  modifyTime: 'زمان را تغییر دهید',
  unbindlimit: 'بدون مجوز دستگاه مرتبط نمی توانید حصار الکترونیکی با یک کلیک ایجاد کنید!',
  setSmsTip: 'اگر اعلان پیامکی را تنظیم کنید ، ابتدا باید اعلان پلت فرم را روشن کنید ؛ اگر تحویل اعلان سیستم عامل موفقیت آمیز است ، اعلان پیام کوتاه موفقیت آمیز نیست ، باید اعلان پیامکی را دوباره روشن کنید',
  cusSetComTip: 'سلب مسئولیت: خطر ناشی از دستورالعمل های سفارشی هیچ ارتباطی با سیستم عامل ندارد',
  cusSetComPas: 'لطفاً رمز ورود به سیستم حساب ورود به سیستم را وارد کنید',
  cusSetComDes1: 'دستورالعمل های سفارشی فقط از دستورالعمل های آنلاین پشتیبانی می کنند.',
  cusSetComDes2: 'اگر دستگاه ظرف دو دقیقه پس از ارسال دستور پاسخ ندهد ، روند کار خاتمه می یابد و وضعیت دستور به عنوان عدم پاسخ ارزیابی می شود.',
  cueSetComoffline: 'دستگاه پاسخ نمی دهد و ارسال دستور سفارشی ناموفق است!',
  fbType: 'نوع بازخورد',
  fbType1: 'مشورتی',
  fbType2: 'اشکال در عملکرد',
  fbType3: 'تجربه ی کاربر',
  fbType4: 'پیشنهادات ویژگی جدید',
  fbType5: 'دیگر',
  upload: 'بارگذاری',
  uploadImg: 'بارگذاری تصویر',
  uploadType: 'لطفاً پرونده هایی از نوع .jpg .png .jpeg .gif را بارگذاری کنید',
  uploadSize: 'پرونده بارگذاری نمی تواند بزرگتر از 3M باشد',
  fbManager: 'مدیریت بازخورد',
  blManager: 'مدیریت اعلامیه',
  fbUploadTip: 'لطفاً نوع بازخورد را انتخاب کنید',
  menuPlatform: "اخبار سکو",
  menuFeedback: "بازخورد",
  menuBulletin: "اعلامیه سکو",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "رفتار رانندگی",
  BtyjdfghtwsrgGHFEEGRDAF: "شتاب سریع",
  BtyuwyfgrWERERRTHDAsdDF: "شتاب سریع",
  Be2562h253grgsHHJDbRDAF: "گردش تند",
  celTemperature:'دمای سانتیگراد'
};
// 权限tree
lg.limits = {
  "ACC_statistics": "آمار ACC",
  "Account_Home": "صفحه اصلی حساب",
  "Add": "جدید",
  "Add_POI": "POI اضافه کنید",
  "Add_customer": "اضافه کردن مشتریان جدید",
  "Add_device_group": "گروه دستگاه را اضافه کنید",
  "Add_fence": "حصار اضافه کنید",
  "Add_sharing_track": "آهنگ اشتراک گذاری را اضافه کنید",
  "Add_system_account": "حساب مجوز جدید",
  "Alarm_details": "لیست هشدار",
  "Alarm_message": "پیام هشدار",
  "Alarm_overview": "مرورگر هشدار",
  "Alarm_statistics": "آمار هشدار",
  "All_news": "همه اخبار",
  "Associated_equipment": "دستگاه همراه",
  "Available_points": "نقاط موجود",
  "Barcode_statistics": "آمار بارکد",
  "Batch_Import": "واردات دسته ای",
  "Batch_renewal": "تجدید دسته",
  "Batch_reset": "بازنشانی فله",
  "Bulk_sales": " فروش توده ای",
  "Call_the_police": " هشدار",
  "Customer_details": "اطلاعات مشتری",
  "Customer_transfer": "انتقال مشتری",
  "Delete_POI": "POI را حذف کنید",
  "Delete_account": "حذف حساب کاربری",
  "Delete_customer": "حذف مشتری",
  "Delete_device": "حذف دستگاه",
  "Delete_device_group": "گروه دستگاه را حذف کنید",
  "Delete_fence": " حصار را پاک کن",
  "Delete_role": "حذف نقش",
  "Device_List": " لیست دستگاه",
  "Device_grouping": "گروه بندی",
  "Device_transfer": "انتقال دستگاه",
  "Due_reminder": "یادآوری تاریخ انقضا",
  "Edit_details": "جزئیات ویرایش",
  "Equipment_management": "مدیریت تجهیزات",
  "My_clinet": "مدیریت تجهیزات",
  "Fence": "حصار",
  "Fence_management": "مدیریت نرده",
  "Generate": "تولید می کنند",
  "Generate_lead-in_points": " تولید نقاط واردات",
  "Generate_renewal_points": "ایجاد نقطه تجدید",
  "Have_read": "پاک کردن",
  "Idle_speed_statistics": "آمار سرعت کار",
  "Import": "وارد كردن",
  "Import_Device": "وارد کردن دستگاه",
  "Industry_Statistics": "آمار صنعت",
  "Location_monitoring": "نظارت بر مکان",
  "Log_management": "مدیریت ورود به سیستم",
  "Mark_read": "علامت گذاری شده به عنوان خوانده شده",
  "Menu_management": "مدیریت منو",
  "Message_Center": "مرکز پیام",
  "Mileage_statistics": "آمار مسافت پیموده شده",
  "Modify_POI": "POI را تغییر دهید",
  "Modify_device_details": "جزئیات دستگاه را تغییر دهید",
  "Modify_device_group": "گروه دستگاه را اصلاح کنید",
  "Modify_role": "تغییر نقش",
  "Modify_sharing_track": "مسیر اشتراک گذاری را تغییر دهید",
  "Modify_user_expiration": " اصلاح انقضای کاربر",
  "More": "بیشتر",
  "My_client": "مشتری من",
  "New_role": "نقش جدید",
  "New_users": "افزایش کاربر",
  "Oil_statistics": "آمار کسر نفت",
  "POI_management": "مدیریت POI",
  "Points_record": "رکورد نقطه",
  "Push": "فشار",
  "Quick_sale": "فروش سریع",
  "Renew": " هزینه بازپرداخت",
  "Replay": "  پخش",
  "Role_management": "مدیریت نقش",
  "Run_overview": " مرور عملیات",
  "Running_statistics": "آمار عملیاتی",
  "Sales_equipment": " تجهیزات فروش",
  "Set_expiration_reminder": "یادآوری انقضا را تنظیم کنید",
  "Share_track": "اشتراک گذاری",
  "Sharing_management": "اشتراک گذاری",
  "Speeding_detailed_list": " لیست سرعت",
  "Statistical_report": "گزارش آماری",
  "Stay_detailed_list": "فهرست اقامت",
  "System_account_management": "حساب مرجع",
  "Temperature_statistics": "آمار دما",
  "Transfer": " انتقال",
  "Transfer_group": "گروه انتقال",
  "Transfer_point": "انتقال اشتراک جدید",
  "Transfer_renewal_point": "کارت تمدید انتقال",
  "Trip_statistics": "آمار سفر",
  "Unlink": "قطع پیوند",
  "View": " مشاهده",
  "View_POI": "مشاهده POI",
  "View_device_group": "مشاهده گروه دستگاه",
  "View_fence": "نمایش حصار",
  "View_role": "مشاهده نقش",
  "View_sharing_track": "مشاهده مسیر اشتراک گذاری",
  "Virtual_account": "گزارش عملیات",
  "Voltage_analysis": "تجزیه و تحلیل ولتاژ",
  "Voltage_statistics": "آمار ولتاژ",
  "batch_deletion": "حذف فله",
  "change_Password": "تغییر رمز عبور",
  "delete": "حذف",
  "edit": " ویرایش کنید",
  "instruction": "دستورالعمل",
  "modify": " اصلاح کنید",
  "monitor": "نظارت",
  "my_account": "حساب من",
  "reset_Password": "بازنشانی گذرواژه",
  "share_it": " به اشتراک بگذارید",
  "sub_user": " کاربر زیرمجموعه",
  "track": " ردیابی",
  "Custom_Order": "دستورالعمل سفارشی",
  "GeoKey_Manager": "مدیریت GeoKey",
  "GeoKey_Update": "تغییر",
  "GeoKey_Delete": "حذف",
  "GeoKey_Add": "اضافه کردن به",
  "GeoKey_View": "چشم انداز",
  "feedback_manager": "مدیریت بازخورد",
  "feedback_list": "چشم انداز",
  "feedback_handle": "در حال پردازش بازخورد",
  "proclamat_manager": "مدیریت اعلامیه",
  "proclamat_manager_list": "مشاهده اعلامیه",
  "proclamat_manager_update": "اعلامیه اصلاح",
  "proclamat_manager_delete": "اعلامیه را حذف کنید",
  "proclamat_manager_save": "اطلاعیه جدید",
  "device_update_batch_model": "مدل دستگاه را تغییر دهید دسته ای"
}
// محتوای سند مسئله
lg.questionDocumentArr = [
  [
    "س: چراغ نشانگر پس از نصب دستگاه سیم کشی خاموش است و به صورت آفلاین است.",
    "پاسخ: لطفا بخار را خاموش کنیدبعد از ماشین ، از قلم برقی و متر جهانی استفاده کنید تا اندازه گیری ولتاژ خط خودرو متصل به دامنه ولتاژ ردیاب GPS را ببینید.معمولاً 9-36 ولت.<br/>موارد احتیاطی در مورد سیم کشی: پرسنل نصب و سیم کشی باید درک خط اتومبیل را داشته باشند.وتوانایی خاصی در این کار داشته باشید تا از صدمه زدن به ماشین خود ناشی از سیم کشی نادرست جلوگیری کنید.",
  ],
  [
    "س: دستگاههای سیمی یا دستگاههای ردیابی در زمان واقعی بی سیم ، تماسهای تلفنی یا بوت پس زمینه IoTدستگاه وضعیت آفلاین است",
    "پاسخ:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. ارسال پیام متنی برای راه اندازی مجدد ، مشاهدهچند دقیقه برای دیدن اینکه آیا آنلاین است یا خیر.به طور کلی برای تعیین اطلاعات ، شماره RESET # ارسال کنید لطفا با فروشنده تماس بگیرید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.اتصال شبکه ناپایدار است ، لطفاً اتومبیل را به نامه منتقل کنیدنه منطقه خوب<br/>&nbsp;&nbsp;&nbsp;&nbsp;3.پس از مراحل فوق ، نتوانسته استبه صورت آنلاین بروید و با اپراتور تلفن همراه تماس بگیرید تا ببینید کارت غیر طبیعی است یا خیر.",
  ],
  [
    "س: این دستگاه در آغاز و پایان ماه در دسته ها آفلاین است.",
    "پاسخ: لطفاً کارت بازپرداخت را بررسی کنید.اگر معوقات است ، لطفاً آن را به موقع شارژ کرده و استفاده از آن را از سر بگیرید.",
  ],
  [
    "س: ماشین در حال رانندگی است ، موقعیت آنلاین GPS به روز نشده است.",
    "پاسخ:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1.تجهیزات سیم کشی می توانند پیام های متنی STATUS # را برای مشاهده حروف ماهواره ای ارسال کنندوضعیت دریافت شماره ，GPS را مشاهده کنید: جستجوی ماهواره سیگنال ماهواره ای همیشه در جستجوی است，در این حالت ، لازم است که محل نصب و اینکه آیا مطابق دستورالعمل نصب شده است را بررسی کنید.رو به رو，هیچ پوشش فلزی در قسمت بالا وجود ندارد.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.ارسال پیام متنی STATUS #，وضعیت بازگشت GPS است: خاموش ، لطفاً دوباره کارخانه شماره # را ارسال کنید .پس از دریافت پاسخ خوب ، نگاه 5 دقیقه ای را مشاهده کنید.آیا به روز رسانی وجود دارد<br/>&nbsp;&nbsp;&nbsp;&nbsp;3.با توجه به دو روش فوق ، گسل قابل رفع نیست ، لطفا برای تعمیر با فروشنده تماس بگیرید.",
  ],
  [
    "س: چرا سکوی شارژ مدت طولانی است که هنوز هم نشان می دهد که کامل نیست؟",
    "پاسخ: صفحه نمایش قدرت پلتفرم براساس اطلاعاتی است که توسط دستگاه به منظور ارایه تحلیل داده برای تعیین توان فعلی دستگاه ساخته شده است.<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、داده قدرت دستگاه به همراه داده های موقعیت یابی دستگاه بارگذاری می شود. اگر باتری مدت طولانی شارژ شود ، برق تغییر نکرده است.لطفاً: 1 دستگاه خود را بیاورید تا 100-300 متر حرکت کند تا اطلاعات موقعیت مکانی دستگاه را به روز کند تا داده های برق و مکان آن بتواند دوباره به سکوی تغذیه شود تا صفحه باتری را تازه کند.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、با توجه به تغییر چراغ نشانگر قدرت برای تعیین اینکه آیا آن به طور کامل شارژ شده است (به عنوان نمونه S15 را بگیرید) ، مراحل کار به شرح زیر است:1 بار را 8-10 ساعت شارژ کنید ، سپس چراغ نشانگر برق به رنگ زرد و سبز روشن می شود.بعد از وصل کردن کابل شارژ ، کابل شارژ را وارد کنید.در مدت 15 دقیقه ، نشانگر قدرت به زرد و سبز تبدیل می شود تا کاملاً شارژ شود .برای سایر مدل ها ، لطفاً به دفترچه راهنما مراجعه کنید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3、شارژ برای مدت طولانی نیز پر است ، این وضعیت ممکن است به این صورت باشد که ولتاژ پلاگین شارژ از 1A پایین تر است ، لطفاً باتری را با 5 ولت ، 1A برای 8-10 ساعت شارژ کنید.",
  ],
  [
    "س: فرمان قطع برق GPS با موفقیت صادر شده است؟ چرا هنوز خودرو خراب نشده است؟",
    "پاسخ: پس از موفقیت آمیز فرمان خاموش ، تجهیزات باید تحت شرایط زیر اجرا شوند:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、اطمینان حاصل کنید که سیم کشی تجهیزات صحیح است و نمودار سیم کشی دفترچه راهنما را دنبال کنید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、تجهیزات به طور عادی کار می کنند ، در حالت ایستاده یا حالت رانندگی قرار دارند ، دارای موقعیتی هستند ، آفلاین نیستند و سرعت خودرو از 20 کیلومتر در ساعت تجاوز نمی کند.<br/>اگر وسیله نقلیه آفلاین باشد ، موقعیت یابی نشده باشد یا سرعت خودرو از 20 کیلومتر در ساعت فراتر رود ، حتی اگر فرمان قطع برق با موفقیت صادر شود ، ترمینال اجرا نمی شود.",
  ],
  [
    "س: سه سال از محصولات بی سیم برای اولین بار نصب شده است ، دستگاه صفحه نمایش قرار نمی گیرد و یا بصورت آنلاین است.",
    "پاسخ:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、سوئیچ را روشن کنید تا ببینید نور چراغ نشانگر چشمک می زند؟ به عنوان مثال ، نشانگرهای زرد و سبز S18 همزمان با حالت عادی چشمک می زنند و چراغ چشمک زن در سیگنال جستجو است. دستگاه روشن نیست.(وضعیت مدل های مختلف متفاوت خواهد بود. لطفا برای سایر مدل ها به دفترچه راهنما مراجعه کنید)<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、چراغ نشانگر به صورت آنلاین نمی رود. اگر سیگنال در وضعیت ضعیف روشن شده است ، لطفاً سیگنال را در منطقه خوبی دریافت کنید.منطقه خوب سیگنال در خط نیست ، می توانید 1 دقیقه خاموش کنید ، کارت را مجددا نصب کنید و سپس تست را شروع کنید.",
  ],
  [
    "س: محصول کابل برای اولین بار نصب شده است و دستگاه صفحه نمایش قرار نمی گیرد.",
    "پاسخ:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、مشاهده کنید که آیا شاخص وضعیت GPS پایانه عادی است یا خیر؟ وضعیت نشانگر را مطابق کتابچه راهنمای مدل های مختلف بررسی کنید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、اگر چراغ نشانگر خاموش باشد ، دستگاه به درستی تغذیه نمی شود.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3、(سبز و زرد) نشانگر کارت روشن نیست. اگر کارت خاموش است ، کارت را دوباره نصب کنید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;4、اطمینان حاصل کنید که شماره سیم کارت در دستگاه معوق نیست و عملکرد دسترسی به اینترنت GPRS طبیعی است.<br/>&nbsp;&nbsp;&nbsp;&nbsp;5、در محلی که تجهیزات در آن قرار دارد ، مانند فضای پایین ، تونل و غیره ، شبکه GSM وجود ندارد ، جایی که سیگنال ضعیف است ، لطفاً به محلی که پوشش GPRS مناسب است ، سوار شوید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;6、موقعیت یاب را خیلی محکم نصب نکنید ، اشیاء فلزی نداشته باشید و تا جای ممکن آن را در ماشین نصب کنید.در غیر این صورت بر دریافت سیگنال تأثیر می گذارد.<br/>&nbsp;&nbsp;&nbsp;&nbsp;7、به طور معمول روشن ، متوقف کردن در منطقه خوب سیگنال به صورت آنلاین نیست ، می توانید دستور خط را دوباره صادر کنید تا بررسی کنید که آیا رابط IP و شبکه پیوند کارت نرمال است یا خیر.",
  ],
  [
    "س: چراغ نشانگر پس از نصب دستگاه سیم کشی خاموش است و به صورت آفلاین است.",
    "راه حل:<br/>&nbsp;&nbsp;&nbsp;&nbsp;بعد از خاموش کردن ماشین ، از قلم برقی و کنتور جهانی برای اندازه گیری اینکه ولتاژ خط ماشین وصل شده مطابق با دامنه ولتاژ ردیاب GPS است ، به طور کلی 9-36 ولت است استفاده کنید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;اقدامات احتیاطی سیم کشی:<br/>&nbsp;&nbsp;&nbsp;&nbsp;پرسنل نصب و سیم کشی باید درک درستی از خط اتومبیل داشته و از قابلیت دستی خاصی برخوردار باشند تا از آسیب رسیدن به ماشین شما ناشی از سیم کشی نادرست جلوگیری شود.",
  ],
  [
    "س: دستگاه سیمی یا دستگاه ردیابی در زمان واقعی بی سیم ، تماس تلفنی یا دستگاه بوت پس زمینه IoT به صورت آفلاین",
    "جواب بده：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1.برای راه اندازی مجدد یک پیام متنی بفرستید ، چند دقیقه مشاهده کنید تا ببینید آنلاین است.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2اتصال شبکه ناپایدار است ، لطفاً ماشین را به قسمت سیگنال منتقل کنید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3.پس از مراحل فوق ، شما نتوانستید بصورت آنلاین بروید ، برای بررسی اینکه آیا کارت غیر طبیعی است ، باید با اپراتور تلفن همراه تماس بگیرید.",
  ],
  [
    'س: این تجهیزات در آغاز و پایان ماه آنلاین نیست. "،" راه حل:لطفاً اگر کارت عقب افتاده است ، لطفاً به موقع آن را شارژ کرده و استفاده از آن را از سر بگیرید.',
  ],
  [
    "س: ماشین در حال رانندگی است ، موقعیت آنلاین GPS به روز نشده است.",
    "راه حل:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1.دستگاه سیم کشی می تواند یک پیام متنی STATUS # ارسال کند تا وضعیت دریافت سیگنال ماهواره ای را بررسی کند و GPS را مشاهده کند:searching satelliteیعنی سیگنال ماهواره همیشه در حال جستجو است در این حالت لازم است که وضعیت نصب را بررسی کنید و اینکه طبق دستورالعمل نصب شده است یا خیر.رو به بالا ، هیچ پوشش فلزی در قسمت بالا وجود ندارد.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.发短信STATUS#，返回状态是GPS：OFF，请再发FACTORY#，收到回复OK后，观察5分钟看位置是否有更新。<br/>&nbsp;&nbsp;&nbsp;&nbsp;3.按照以上2种方法还不能排除故障，请联系卖家返修。",
  ],
  [
    "س: چرا سکوی شارژ مدت طولانی است که هنوز هم نشان می دهد که کامل نیست؟",
    "نمایشگر قدرت پلتفرم مبتنی بر اطلاعاتی است که از دستگاه دریافت می شود تا بتواند تجزیه و تحلیل داده ها را برای تعیین توان فعلی دستگاه تعیین کند.در بعضی موارد خاص ، خطای نمایش قدرت رخ می دهد.<br/>راه حل:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1、اگر داده باتری دستگاه با داده های موقعیت یابی دستگاه بارگذاری می شود ، اگر باتری از مدت طولانی شارژ شدن تغییر نکرده است ، لطفاً: 1 دستگاه خود را بیاورید تا 100-300 متر حرکت کند.اطلاعات موقعیت مکانی دستگاه به روز شده است ، به این ترتیب داده های برق و داده های مکان می توانند دوباره به سیستم عامل تغذیه شوند تا تجدید نمایشگر قدرت باشد.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2、با توجه به تغییر چراغ نشانگر قدرت برای تعیین اینکه آیا آن به طور کامل شارژ شده است (به عنوان نمونه S15 را بگیرید) ، مراحل کار به شرح زیر است:1 بار را 8-10 ساعت شارژ کنید ، سپس چراغ نشانگر برق به رنگ زرد و سبز روشن می شود.بعد از وصل کردن کابل شارژ ، کابل شارژ را وارد کنید.در مدت 15 دقیقه ، نشانگر قدرت به زرد و سبز تبدیل می شود تا کاملاً شارژ شود .برای سایر مدل ها ، لطفاً به دفترچه راهنما مراجعه کنید.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3、شارژ برای مدت طولانی نیز پر از برق است ، این وضعیت ممکن است به این صورت باشد که ولتاژ پلاگین شارژ از 1A پایین تر است ، لطفاً باتری را با 5 ولت ، 1A برای 8-10 ساعت شارژ کنید.",
  ],
];
lg.webOptDoc =
  "<h1>一、مراحل حساب کاربر را اضافه کنید</h1>" +
  "<ol>" +
  "<li>" +
  "1. در مرورگر ، پلت فرم ما را وارد کنید https://www.gpsnow.net ، وارد حساب فروشنده شوید تا وارد رابط کاربری سیستم عامل شوید." +
  "</li>" +
  "<li>" +
  '2. پس از ورود به سیستم با موفقیت ، صفحه اصلی این سکو را مشاهده خواهید کرد.در نوار پیمایش در بالا (مطابق شکل زیر) روی "مشتری من" کلیک کنید.”。' +
  "</li>" +
  "<li>" +
  "3. مشتری را در لیست کلیه مشتریان انتخاب کنید ، و سپس افزودن را انتخاب کنید تا رابط کاربری جدید فرعی (مانند تصویر زیر) ظاهر شود." +
  '<img src="../../lib/document/cn/img/1.png"/>' +
  "</li>" +
  "<li>" +
  "4. نوع کاربر را با توجه به نیاز کاربر اختصاص دهید (برای نمایش اطلاعات بیشتر در مورد دستگاه پروژه حساس به دما روغن S208 ، استفاده از مجوز حرفه ای را بررسی کنید)." +
  "</li>" +
  "<li>" +
  "5. نام مشتری می تواند با حساب کاربری یا همان حساب مغایرت داشته باشد." +
  "</li>" +
  "<li>" +
  "6. پس از وارد کردن نقطه اطلاعات ، صفحه نمایش با موفقیت کامل می شود." +
  "</li>" +
  "<li>" +
  '7. پس از ورود به سیستم با موفقیت ، صفحه اصلی این سکو را مشاهده خواهید کرد.در نوار پیمایش در بالا (مطابق شکل زیر) روی "مشتری من" کلیک کنید.”' +
  "</li>" +
  "<li>" +
  '8. پس از ورود به سیستم با موفقیت ، صفحه اصلی این سکو را مشاهده خواهید کرد.در نوار پیمایش در بالا (مطابق شکل زیر) روی "مشتری من" کلیک کنید.”' +
  "</li>" +
  "</ol>" +
  "<h1>二、مرحله تجهیزات فروش</h1>" +
  "<ol>" +
  "<li>" +
  "1. حساب کاربری فرعی را در کادر جستجو وارد کنید (مطابق شکل زیر)" +
  "</li>" +
  "<li>" +
  "2. حساب را در سمت چپ انتخاب کرده و دکمه سمت راست ماوس ظاهر می شود." +
  '<img src="../../lib/document/cn/img/2.png"/>' +
  "</li>" +
  "<li>" +
  "3. شماره IME یک یا یک دسته را وارد کنید و شماره IMEI دسته ای را وارد کنید." +
  '<img src="../../lib/document/cn/img/3.png"/>' +
  "</li>" +
  "<li>" +
  "4. برای تأیید نکته را وارد کنید ، سپس روی ارسال کلیک کنید ، سیستم فروش را به موفقیت انجام می دهد." +
  "</li>" +
  "</ol>" +
  "<h1>三、برای ورود به سیستم ، یک حساب کاربری جدید برای کاربر ارسال کنید.</h1>" +
  "<ol>" +
  "<li>" +
  "1. مرورگر https://www.gpsnow.net را برای ورود به رابط ورود به سیستم و وارد کردن رمز ورود (مانند تصویر زیر) باز می کند。" +
  "</li>" +
  "<li>" +
  "2. به مشتریان راهنمایی کنید تا از کد QR در زیر ، هفته آینده برنامه Android یا IOS استفاده کنند." +
  "</li>" +
  "<li>" +
  "3. در مرحله اول اکانت و رمز عبور را ایجاد کنید و برای ورود به سیستم کاربر فرعی آن را ارسال کنید." +
  "</li>" +
  '<img src="../../lib/document/cn/img/4.png"/>' +
  '<img src="../../lib/document/cn/img/5.png"/>' +
  "<li>" +
  "صفحه نمایش صفحه نظارت وب بعد از ورود" +
  "</li>" +
  "<li>" +
  "کاربر دستگاه را در اینجا مانیتور می کند ، آمار دستگاه و اطلاعات زنگ را بررسی می کند ، جزئیات دستگاه را بررسی می کند و داده و رمز ورود خود را تغییر می دهد." +
  "</li>" +
  "</ol>";
lg.appOptDoc = "با ما همراه باشید ...";
// راهنما پارامتر پرس و جو
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>کلمه عبور ترمینال</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>شماره سیم کارت داخلی ترمینال را جستجو کنید</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>شماره موبایل مالک را بررسی کنید</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push("<td>مقدار حداکثر سرعت زنگ نمایش داده شد</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>تعداد دفعات گزارش پس از پیگیری ، در چند ثانیه پرس و جو کنید</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push(
  "<td>پرس و جو برای فعال کردن ردیابی ، 1 برای فعال کردن ردیابی ، 0 خاموش کردن ردیابی است</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push(
  "<td>دامنه داوری زنگ تغییر غیرقانونی را پرس و جو کنید ، واحد متر است</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>پرس و جو برای فعال کردن زنگ پیام لرزش ، 1 برای خاموش کردن 0 فعال است"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>حساسیت به ارتعاش پرس و جو 0 ~ 15 ، 0 بالاترین حساسیت است ، بسیار زیاد ممکن است مثبت نادرست باشد ، 15 کمترین حساسیت</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>پرس و جو برای فعال کردن زنگ تلفن لرزش ، 1 برای خاموش کردن 0 فعال است"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>بررسی کنید که عملکرد GPS رانش فیلتر فعال شده است 1 برای خاموش کردن 0 باید روشن شود اگر روشن باشد دستگاه ضد سرقت طی 5 دقیقه لرزش نخواهد داشت ، سپس وارد حالت استاتیک شوید و تمام نقاط رانش GPS را فیلتر کنید.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>بررسی کنید که عملکرد خواب فعال است یا خیر ، زنگ خاموش است اگر زنگ در 30 دقیقه لرزش نداشته باشد ، وارد حالت خواب می شود و قطع GPS را خاموش می کند.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>پرس و جو برای خاموش کردن عملکرد زنگ خطای برق ، 1 این است که 0 را خاموش کنید</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">GPS:</td>');
html.push(
  "<tdبرای دریافت شماره و شدت ماهواره دریافت شده توسط GPS ، به عنوان مثال: 2300 1223 3431 را جستجو کنید. در کل 12 گروه چهار رقمی ، 2300 نشان می دهد که قدرت سیگنال ماهواره ای از دریافت عدد 23 برابر 00 است ، و 1223 نشان می دهد که قدرت سیگنال ماهواره ای از عدد 12 دریافت شده 23 است</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VBAT:</td>');
html.push(
  "<td>ولتاژ باتری پرس و جو ، ولتاژ رابط شارژ ، جریان شارژ ، به عنوان مثال: VBAT = 3713300: 4960750: 303500 یعنی ولتاژ باتری 3713300uV است ، 3.71 ولت ، ولتاژ شارژ 4.96 ولت ، جریان شارژ 303mA است</td>"
);
html.push("</tr>");
html.push("</table>");
lg.queryparamhelp = html.join("");

// با تنظیم پارامترها راهنمایی کنید
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push(
  "<td>رمز عبور پایانه را تنظیم کنید ، رمز عبور فقط 6 رقم می تواند باشد</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>شماره سیم کارت را در ترمینال تنظیم کنید</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>شماره موبایل مالک را تنظیم کنید</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push(
  "<td>مقدار محدودیت سرعت زنگ هشدار بزرگ را تنظیم کنید ، دامنه باید بین 0 ~ 300 باشد</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>پس از روشن شدن ردیابی ، ثانیه ها فرکانس گزارش را تنظیم کنید.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push(
  "<td>تنظیم فعال کردن ردیابی ، 1 برای روشن کردن ردیابی ، 0 برای خاموش کردن ردیابی است</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push(
  "<td>محدوده داوری زنگ تغییر غیرقانونی را تنظیم کنید ، واحد متر است</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>تنظیم کنید که آیا زنگ پیام لرزش را فعال کنید ، 1 را فعال کنید تا 0 را خاموش کنید"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>تنظیم حساسیت لرزش 0 ~ 15 ، 0 بالاترین حساسیت است ، بسیار زیاد ممکن است مثبت نادرست باشد ، 15 کمترین حساسیت</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>تنظیم کنید که زنگ لرزش تلفن را فعال کنید ، 1 روشن است 0 خاموش است"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>تنظیم کنید که آیا عملکرد عملکرد رانش فیلتر GPS را فعال کنید ، 1 این است که 0 را خاموش کنید ، اگر روشن باشد ، زنگ هشدار ظرف 5 دقیقه لرزش نخواهد داشت ، سپس وارد حالت استاتیک شوید ، تمام نقاط رانش GPS را فیلتر کنید.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>تنظیم کنید که آیا برای فعال کردن عملکرد خواب، 1 روشن است برای خاموش کردن 0، اگر آن روشن است</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>تنظیم کنید که آیا برای فعال کردن عملکرد خواب، 1 روشن است برای خاموش کردن 0، اگر آن روشن است</td>"
);
html.push("</tr>");
html.push("</table>");
lg.setparamhelp = html.join("");

//اضافه کردن به دسته
html = [];
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox" ' +
    'style="z-index: 999;position:absolute;left:211px;top:88px;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافه کردن به</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkAdds_treeDiv" +
    "," +
    "bulkAdds_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>پلت فرم منقضی می شود</td>'
);
html.push("<td>");
html.push(
  '<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>مدل تجهیزات</td>'
);
html.push("<td>");
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    "</div>" +
    '<div id="deviceList"></div>' +
    "</div>" +
    "</span>"
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافه کردن دستگاه</td>'
);
html.push("<td>");
html.push(
  '<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push(
  '<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>'
);
lg.bulkAdds = html.join("");

//تجدید دسته
html = [];
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافه کردن دستگاه</td>'
);
html.push("<td>");
html.push(
  '<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="re_addNumBox">جریان：<span id="account_re_addNum">0</span>'
);
html.push("</span>");
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<tr>");
html.push(
  '<td style="text-align:right;"><span style="color:red">*</span>نوع کارت</td>'
);
html.push("<td>");
html.push('<input  type="radio" name="red_cardType"');
html.push(
  'class="easyui-validatebox"  value="3" checked><label>一年</label></input>'
);
html.push(
  '<input  type="radio" name="red_cardType" style="margin-left:15px;" '
);
html.push(
  'class="easyui-validatebox" value="4"><label>طول عمر</label></input>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td style="text-align: right">نقاط تخفیف</td>');
html.push("<td>");
html.push(
  '<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>کاربر منقضی شده است</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>یادداشت ها</td>'
);
html.push("<td>");
html.push(
  '<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkRenew = html.join("");

// //فروش توده ای，myAccount
html = [];
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel treePulldownBox"  style="z-index: 999;position:absolute;left:176px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>فروش به</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkSales_treeDiv" +
    "," +
    "bulkSales_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push(
  '<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>کاربر منقضی شده است</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافه کردن دستگاه:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="bs_addNumBox">جریان：<span id="account_bs_addNum">0</span>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="bs_reset" class="swd-gray-btn" title="' +
    lg.reset +
    '"  style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkSales = html.join("");

//انتقال دسته 1، جعبه پاپ آپ，弹出框
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:127px;top:172px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>مشتری هدف</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافه کردن</td>'
);
html.push("<td>");
html.push(
  '<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >اضافه کردن به دسته</a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >انتقال</a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)">لغو</a>'
);

lg.bulkTransfer = html.join("");

//انتقال دسته 2,myClient
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:117px;top:84px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>مشتری هدف</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>اضافه کردن دستگاه:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bt_addMachines" style="cursor:pointer" title="' +
    lg.addTo +
    '" src="../../images/main/myAccount/add3.png" />'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);

lg.bulkTransfer2 = html.join("");

//کاربران به طور عمده انتقال داده
html = [];
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:135px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>مشتری هدف</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">'
);
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("<td></td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");

lg.bulkTransferUser = html.join("");
window.lg = lg