var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
    site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
// 英语
var lg = {
  //common common
  user_guide: 'User Guide',
  remoteSwitch: "Remote Switch",
  pageTitle:
    "WhatsGPS Global Tracking System|Vehicle GPS Tracker|3G Tracker|mini 4G Tracker|GPSNow|Car Locator",
  description:
    site+" is dedicated to providing users with intelligent cloud location services. It is the world's leading location service platform.",
  pageLang: "English",
  inputCountTips: "Account/IMEI",
  inputPasswordTips: "Password",
  appDownload: "APP Download",
  rememberPassword: "Remember Me",
  forgetPassword: 'Forgot Password',
  siteName: "WhatsGPS",
  noToken: " Please send token",
  loginFirst: "Login First",
  move: "Move",
  stop: "Static",
  query: "Check",
  imeiQuery: "IMEI",
  delete: "Delete",
  update: "Update",
  cancel: "Cancel",
  soft: "No.",
  more: "More",
  edit: "Edit",
  useful:'helpful',
  useless:'unhelpful',
  about:'About',
  replyFeedback:'Feedback on "$"',
  add: "Add",
  addTo: "Add",
  addDevice: "Add Device",
  machineName: "Device Name",
  searchDevice: "Device",
  date: "Datetime",
  LatestUpdate: "Update",
  engine: "ACC",
  locTime: "GPS time",
  locType: "Location type",
  startLoc: "Start Location",
  endLoc: "End Location",
  address: "Address",
  noAddressTips: "Unable to get address information",
  lonlat: "Latitude and Longitude",
  carNO: "Plate No.",
  imei: "Device No.(IMEI)",
  IMEI: "IMEI",
  simNO: "SIM Card",
  activeTime: "Activated Time",
  expireTime: "Expired Time",
  acceptSubordinateAlarm: "Accept Subordinate Alarm",
  acceptAlarmTips1: "If selected,",
  acceptAlarmTips2: "you will receive alarm messages from all sub-accounts",
  speed: "Speed",
  y: "Years",
  M: "Months",
  d: "Days",
  h: "Hours",
  min: "Minutes",
  s: "Seconds",
  _year: "y",
  _month: "m",
  _day: "d",
  _hour: "h",
  _minute: "m",
  _second: "s",
  confirm: "Confirm",
  yes: "Yes",
  car: "car",
  not: "No",
  m: "Meters",
  account: "Account",
  psw: "Password",
  save: "Save",
  operator: "Operate",
  queryNoData: "Check No Data",
  name: "Name",
  type: "Model",
  open: "On",
  close: "Off",
  send: "Send",
  alarm: "Alerts",
  alarmSetting: "Alarm Settings",
  look: "View",
  tailAfter: "Tracking",
  history: "Playback",
  dir: "Dir",
  locStatus: "Location Status",
  machineTypeText: "Model",
  carUser: "Car User",
  machine: "Target",
  unknowMachineType: "Unknow Machine",
  noCommandRecord: "Commands not serve for this device",
  type1: "Type",
  role: "Role",
  roles: "Roles",
  timeType: "Time type",
  moveSpeed: "Move Speed",
  signal: "Signal",
  loc: "Position",
  wiretype: "Type",
  wire: "wired",
  wireless: "wireless",
  expire: "Expired",
  hour: "Hour",
  hourTo: "Hour to",
  remark: "Remark",
  remarkInfo: "Remark",
  noPriviledges: "The account has no-operation privileges",
  commandNoOpen: "The current device command is not yet open for use",
  choseDelelePhone: "Please select the number to delete first",
  streetView: "Streetview",
  wrongFormat: "Input format error",
  inputFiexd: "Input fixed number",
  serialNumberStart: "The first number of consecutive IMEI",
  serialNumberEnd: "The last number of consecutive IMEI",
  clickSearchFirst: "Please click search device first!",
  isDeleteDevice:
    "The device cannot be restored after being deleted. Is it deleted?",
  //平台错误代码提示
  errorTips: "Operation failed with error code：",
  error10003: 'Password error',
  error90010: 'The device is offline,customized command failed!',
  error70003: 'The remote control value cannot be empty',
  error70006: 'Does not support or has no right to send the command',
  error20001: 'Vehicle ID cannot be empty',
  error20012: 'Vehicle is not activated',
  error10012: "Old password error",
  error10017: "Delete failed, please delete sub user!",
  error10023: "Delete failed, the user has device",
  error20008: "Add failed, IMEI already exists",
  error20006: "Please enter a IMEI of length 15",
  error10019: "Wrong format of contact phone",
  error10024: "Do not repeat sales",
  error120003: "Share links disabled",
  error10025: "The modified device information cannot be empty",
  error2010: "Please upload the file",
  error20002: "No IMEI number exists",
  error10081: "Insufficient number of renewal cards",
  error10082: 'No need to recharge for lifelong device',
  error3000: 'The role has been assigned to the system account and cannot be deleted',
  error103: 'The account has been suspended, please contact your provider',
  error124: 'Cannot operate on itself',
  // 登陆相关 login.js
  logining: "Logining...",
  login: "Login",
  userEmpty: "User empty",
  pswEmpty: "Password empty",
  prompt: "Reminder",
  accountOrPswError: "Account or password error",
  UserNameAlreadyExist: "The login account has already existed",
  noQualified: "There is no qualified information",
  //main.js
  systemName: "WhatsGPS",
  navTitle_user: ["Monitor", "Report", "Device"],
  navTitle_dealer: ["Home", "Business", "Monitor", "More Operations"],
  exitStytem: "Exit",
  user: "User",
  UserCenter: "User",
  alarmInfo: "Alarm",
  confirmExit: "Confirm Exit?",
  errorMsg: "Error Message: ",
  logintimeout: "Login time out!",
  clearAlarm: "Clear",
  clear: "Clear",
  searchbtn: "User",
  print: "Print",
  export: "Export",
  //feedback
  feedback: "Feedback",
  feedback_sublime: "submit",
  alerttitle: "The title can not be blank!",
  alertcontent: "Feedback cannot be empty!",
  submitfail: "Submission Failed！",
  saveSuccess: "Save success！",
  submitsuccess:
    "Submitted successfully! We will process your feedback as soon as possible~",
  adviceTitle: "Title",
  adviceTitle_p: "Question and opinion title",
  adviceContent: "Questions and opinions",
  adviceContent_p:
    "Briefly describe the questions and comments you want to feedback, and we will continue to improve for you.",
  contact: "Contact Information",
  contact_p: "Fill in your phone or email",
  //monitor.js
  myMachine: "Device",
  all: "All",
  online: "Online",
  offline: "Offline",
  unUse: "Unused",
  group: "Group",
  moveGruop: "Move To Group",
  arrearage: "Arrearage",
  noStatus: "No Status",
  inputMachineName: "Target/IMEI",
  defaultGroup: "Default",
  offlineLessOneDay: "Offline<One Day",
  demoUserForbid: "Experience users cannot use this feature",
  shareTrack: "Share Location",
  shareName: "Name",
  liveShare: "Real-time Track Sharing",
  expiration: "Expiration Time",
  getShareLink: "Create Link",
  copy: "Copy",
  copySuccess: "Successful Copy!",
  enlarge: "Enlarge",
  shareExpired: "Sharing Link Expired",
  LinkFailure: "Link Open Failed",
  inputShareName: "Input A Share Name",
  inputValid: "Please enter the correct failure time",
  //statistics.js
  runOverview: "Operation Statistics",
  runSta: "Operation Statistics",
  mileageSta: "Mileage Report",
  tripSta: "Trip Report",
  overSpeedDetail: "Overspeed Details",
  stopDetail: "Parking Details",
  alarmSta: "Alarm Report",
  alarmOverview: "Alarm Overview",
  alarmDetail: "Alarm Details",
  shortcutQuery: "Quick Check",
  today: "Today",
  yesterday: "Yesterday",
  lastWeek: "Last Week",
  thisWeek: "This Week",
  thisMonth: "This Month",
  lastMonth: "Last Month",
  mileageNum: "Mileage(Km)",
  overSpeedNum: "Overspeed(km/h)",
  overSpeed: "Overspeed",
  stopTimes: "Parking(Times)",
  searchMachine: "Search",
  speedNum: "Speed(km/h)",
  querying: "Querying",
  stopTime: "Static Time",
  HisToryStopTime: "Static Time",
  clickLookLoc: "Click to view address",
  lookLoc: "Check Location",
  noData: "No data",
  alarmTime: "Alarm Time",
  vibrationLevel: "Vibration Level",
  vibrationWay: "Alarm Type",
  acc: "ACC",
  accStatistics: "ACC Statistics",
  accType: ["All", "ACC On", "ACC Off"],
  accstatus: ["on", "off"],
  openAccQuery: "ACC Check",
  runtime: "Running Time",
  //监控页面修改
  run: "Driving",
  speed: "Speed",
  //设备管理
  machineManage: "Devices",
  deviceTable: "My Targets",
  status: "Status",
  havaExpired: "Have Expired",
  expiredIn60: "Expired In 60Days",
  expiredIn7: "Expired in 7Days",
  normal: "Nomal",
  allMachine: "All",
  allMachine1: "All Devices",
  expiredIn7Machine: "Expire In 7 Days",
  expiredIn60Machine: "Expire In 60 Days",
  havaExpiredMachine: "Expired Targets",

  //history.js
  replay: "Play",
  replaytitle: "  Playback",
  choseDate: "Select Date",
  from: "From",
  to: "To",
  startTime: "Start Time",
  endTime: "End Time",
  pause: "Pause",
  slow: "Slow",
  mid: "Mid",
  fast: "Fast",
  startTimeMsg: "Start Time Msg",
  endTimeMsg: "End Time Msg",
  smallEnd: "End time can not be earlier than start time. ",
  bigInterval: "Time interval less than 31 days!",
  trackisempty: "No Data",
  longitude: "Longitude",
  latitude: "Latitude",
  direction: "Direction",
  stopMark: "Parking Marker",
  setStopTimes: [
    {
      text: "1 Minute",
      value: "1",
    },
    {
      text: "2 Minute",
      value: "2",
    },
    {
      text: "3 Minute",
      value: "3",
    },
    {
      text: "5 Minute",
      value: "5",
    },
    {
      text: "10 Minute",
      value: "10",
    },
    {
      text: "15 Minute",
      value: "15",
    },
    {
      text: "20 Minute",
      value: "20",
    },
    {
      text: "30 Minute",
      value: "30",
    },
    {
      text: "45 Minute",
      value: "45",
    },
    {
      text: "1 Hours",
      value: "60",
    },
    {
      text: "6 Hours",
      value: "360",
    },
    {
      text: "12 Hours",
      value: "720",
    },
  ],
  filterDrift: "Remove Drift Location",
  userType: [
    "Admin",
    "Distributor",
    "User",
    "Logistics",
    "Rental",
    "Car user",
    "Risk Control",
    "Profession",
  ],
  userTypeArr: [
    "Admin",
    "Distributor",
    "End User",
    "Logistics",
    "Rental",
    "Car user",
    "Profession",
  ],
  machineType: {
    '0': 'Machine Type',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '83':'S03A',
    '84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: [
    "Alarm Type",
    "Vibration Alarm",
    "Power off Alarm",
    "Low Battery Alarm",
    "SOS Alarm",
    "Overspeed Alarm",
    "Geofence Out Alarm",
    "Displacement Alarm",
    "External Low Battery Alarm",
    "Out Of Area Alarm",
    "Disassemble Alarm",
    "Light Sensing Alarm",
    "Magnetic Sensing Alarm",
    "Dismantal Alarm",
    "Bluetooth Alarm",
    "Signal Shielding Alarm",
    "False Base Station Alarm",
    "Geofence In Alarm",
    "Geofence In Alarm",
    "Geofence Out Alarm",
    "Door-Open Alarm",
    "Fatigue Driving",
    "Entry Mortgage Point",
    "Exit Mortgage Point",
    "Mortgage Point Parking",
    "Terminal Offline",
    "Geofence In Alarm",
    "Geofence Out Alarm",
    "Geofence In Alarm",
    "Geofence Out Alarm",
    "Fuel Alarm",
    "ACC ON Alarm",
    "ACC OFF Alarm",
    "Collision alarm",
  ],
  alarmTypeNew:  {
    '40': 'Hochtemperaturalarm',
    '45': 'Niedrigtemperaturalarm',
    '50': 'overvoltage Alarm',
    '55': 'undervoltage Alarm',
    '60': 'Parking alarm'
  },
  alarmNotificationType: [
    { type: "Vibration Alarm", value: 1 },
    { type: "Power Off Alarm", value: 2 },
    { type: "Low Battery Alarm", value: 3 },
    { type: "SOS Alarm", value: 4 },
    { type: "Overspeed Alarm", value: 5 },
    // {type:'Geofence out Alarm',value:6},
    { type: "Displacement Alarm", value: 7 },
    { type: "External Low Battery Alarm", value: 8 },
    { type: "Out Of Area Alarm", value: 9 },
    { type: "Disassemble Alarm", value: 10 },
    { type: "Light Sensing Alarm", value: 11 },
    { type: "Dismantal Alarm", value: 13 },
    { type: "Signal Shielding Alarm", value: 15 },
    { type: "False Base Station Alarm", value: 16 },
    // {type:'Geofence in Alarm',value:17},
    // {type:'Geofence in Alarm',value:18},
    // {type:'Geofence out Alarm',value:19},
    { type: "Fatigue Driving", value: 21 },
    { type: "Entry Mortgage Point", value: 22 },
    { type: "Exit Mortgage Point", value: 23 },
    { type: "Mortgage Point Parking", value: 24 },
    { type: "Terminal Offline", value: 25 },
    // {type:'Geofence in Alarm (Risk control)',value:26},
    // {type:'Geofence out Alarm(Risk control)',value:27},
    { type: "Geofence In Alarm", value: 26 },
    { type: "Geofence Out Alarm", value: 27 },
    { type: "Fuel Alarm", value: 30 },
    { type: "ACC ON Alarm", value: 31 },
    { type: "ACC OFF Alarm", value: 32 },
    { type: "Collision alarm", value: 33 },
  ],
  alarmTypeText: "Alarm Type",
  alarmNotification: "Notification",
  pointType: [
    "point Type",
    "Satellite Location",
    "Compass Location",
    "LBS Location",
    "WIFI Location",
  ],

  cardType: [
    "Type",
    "Import Card",
    "Import card  to lifelong",
    "Annua card",
    "Lifelong Card",
  ],
  // 东南西北
  directarray: ["East", "South", "West", "North"],
  // 方向字段
  directionarray: [
    "Due North",
    "Northeast",
    "Due East",
    "Southeast",
    "Due South",
    "Southwest",
    "Due West",
    "Northwest",
  ],
  // 定位方式
  pointedarray: ["Undefined", "GPS", "LAC", "LAC Location", "WIFI Location"],

  //map Relevant
  ruler: "Ruler",
  distance: "Traffic",
  baidumap: "Baidu Map",
  map: "Map",
  satellite: "Satellite",
  ThreeDimensional: "3D",
  baidusatellite: "Baidu Satellite",
  googlemap: "Google Map",
  googlesatellite: "Google Satellite",
  fullscreen: "Full Screen",
  noBaidumapStreetView: "Current Location on Baidu Maps without Street View",
  noGooglemapStreetView: "Current Location on Google Maps without Street View",
  exitStreetView: "Exit Street View",
  draw: "Draw",
  finish: "Finish",
  unknown: "Unknow",
  realTimeTailAfter: "Real Time Tracking",
  trackReply: "History Playback",
  afterRefresh: "Refresh After",
  rightClickEnd: "Right Click End,Radius：",
  rightClickEndGoogle: "Right Click End------------------------Radius：",

  //tree Relevant
  currentUserMachineCount: "current User Machine Count",
  childUserMachineCount: "child User Machine Count",

  //Window relevant

  electronicFence: "Geofence",
  drawTrack: "Set Geofence",
  showOrHide: "Show/Hide",
  showDeviceName: "Target Name",
  circleCustom: "User Defined",
  circle200m: "Radius 200 meters",
  polygonCustom: "Polygon Definition",
  drawPolygon: "Draw A Polygon",
  drawCircle: "Draw A Circle",
  radiusMin100:
    "The drawn fence radius is at least 20 meters. Please redraw it. Current fence radius:",
  showAllFences: "Show All Fences",
  lookEF: "Check Fence",
  noEF: "No geofence data detected ",
  hideEF: "Hide Geofence",
  blockUpEF: "Close Fence",
  deleteEF: "Delete Fence",
  isStartUsing: "Whether to enable",
  startUsing: "Enable",
  stopUsing: "Disable",
  nowEFrange: "Current Fence Range",
  enableSucess: "Enable Succefful",
  unableSucess: "Disable Succefful",
  sureDeleteMorgage: "Delete morgage point",
  enterMorgageName: "Enter morgage point name",
  openMorgagelongStayAlarm: "Start Mortgage Point Parking",
  openMorgageinOutAlarm: "Start Entry and exit Mortgage Point",
  setEFSuccess: "Set geofence successfully",
  setElectronicFence: "Set Electronic Fence",
  drawFence: "Draw fence",
  drawMorgagePoint: "Draw Morgage Point",
  customFence: "Fence customization",
  enterFenceTips: "Entering the alarm",
  leaveFenceTips: "Leave the alarm",
  inputFenceName: "Pls input fence name",
  relation: "Bundle",
  relationDevice: "Associate Device",
  unRelation: "Not Associated",
  hadRelation: "Associated Already",
  quickRelation: "One-click Bundle",
  cancelRelation: "One-click Cancel",
  relationSuccess: "Successful Bundle",
  cancelRelationSuccess: "Cancel Success",
  relationFail: "Cancel Failed",
  deviceList: "Device List",
  isDeleteFence: "Whether to delete fence",
  choseRelationDeviceFirst: "Pls first select the device to be associated!",
  choseCancelRelationDeviceFirst:
    "Pls first select the device to be unassociated!",
  selectOneTips: "Please select at least one alarm method",
  radius: "Radius",
  //设置二押点页面
  setMortgagePoint: "Set mortgage point",

  circleMortage: "Circle mortgage point",
  polygonMorgage: "Polygon mortgage point",
  morgageSet: "Mortgage point set already",
  operatePrompt: "Operate prompt",
  startDrawing: "Click to start drawing",
  drawingtip1: "Left mouse click to start drawing, double click to end drawing",
  drawingtip2: "Left mouse click and drag to start drawing",

  /************************************************/
  endTrace: "End Trace",
  travelMileage: "Driving Mileage",
  /************************************************/
  myAccount: "Account",
  serviceProvide: "Service Provider",
  completeInfo: "Complete Info, such as contacts, phone number",
  clientName: "Customer Name",
  loginAccount: "Login Account",
  linkMan: "Contact",
  linkPhone: "Tel/Mob",
  clientNameEmpty: "Client name empty!",
  updateSuccess: "Update Success!",
  /************************************************/
  oldPsw: "Old password",
  newPsw: "New password",
  confirmPsw: "Confirm Password",
  pswNoSame: "Password no same",
  pswUpdateSuccess: "Password update success!",
  email: "Email",
  oldPwdWarn: "Please enter a old password",
  newPwdWarn: "Please enter a new password",
  pwdConfirmWarn: "Please confirm new password",
  /************************************************/
  //Custom popup components
  resetPswFailure: "Rest password failed",
  notification: "Notification",
  isResetPsw_a: "Are you sure to reset the ",
  isResetPsw_b: "`s password?",
  pwsResetSuccess_a: "Already reset the ",
  pwsResetSuccess_b: "`s password to 123456",
  /************************************************/
  machineSearch: "Device Info",
  search: "Search",
  clientRelation: "Client Relation",
  machineDetail: "Details",
  machineDetail2: "Device Details",
  machineCtrl: "Commands",
  transfer: "Move",
  belongCustom: "Membership",
  addImeiFirst: "Add IMEI First!",
  addUserFirst: "Add User First!",
  transferSuccess: "Move Success!",
  multiAdd: "Batch Add",
  multiImport: "Batch Import",
  multiRenew: "Batch Renew",
  //批量修改设备begin
  editDevice:'Modify device model',
  deviceAfter: 'Device model (after modification)',
  editDeviceTips:'Please confirm the device to be modified is the same model and is not active!',
  pleaseChoseDevice: 'Please select the device to be modified first!',
  editResult:'Edit result',
  successCount:'Successfully modified device:',
  failCount:'Failed devices:',
  //批量修改设备end
  multiDelete: "Batch Delete",
  canNotAddImei: "IMEI does not exist,can not add IMEI",
  importTime: "Import Time",
  loginName: "Account",
  platformDue: "Platform Due",
  machinePhone: "SIM Card",
  userDue: "User Due",
  overSpeedAlarm: "Overspeed Alarm",
  changeIcon: "Icon",
  dealerNote: "Remark",
  noUserDue: "No User Due",
  phoneLengththan3: "Phone length must be greater than 3",
  serialNumberInput: "Serial number input",

  /************************************************/
  sending: "Sending.....Please wait...",
  sendFailure: "Send failure",
  ctrlName: "Name",
  interval: "Time interval",
  intervalError: "Interval format error",
  currectInterval: "Please enter the correct time interval!",
  intervalLimit: "Time interval range 10-720,(Minute)",
  intervalLimit2: "Time interval range 10-5400,(Second)",
  intervalLimit3: "Time interval range 5-1440,(Minute)",
  intervalLimit4: "Time interval range 3-999,(Second)",
  intervalLimit5: "Time interval range 10-10800,(Second)",
  intervalLimit1:
    "Time interval range 1-999,(Minute).",
  intervalLimit6: "Time interval range 1-65535,(Second)",
  intervalLimit7: "Time interval range 1-999999,(Second)",
  intervalLimit8: "Time interval range 0-255, 0 means to close",
  intervalLimit9: "Time interval range 3-10800,(Second)",
  intervalLimit10: "Time interval range 3-86400,(Second)",
  intervalLimit11: "Time interval range 180-86400,(Second)",
  intervalLimit22: "Time interval range 60-86400,(Second)",
  intervalLimit23: "Time interval range 5-60,(Second)",
  intervalLimit24: "Time interval range 10-86400,(Second)",
  intervalLimit25: "Time interval range 5-43200,(Minute)",
  intervalLimit12: "Time interval range 10-60,(Second)",
  intervalLimit13:
    "Set interval time range 1-24 (means 1-24 hours) or 101-107 (means 1-7 days)",
  intervalLimit14:
    "Set the interval time range10-3600，Unit (seconds); default：10s",
  intervalLimit15:
    "Set the interval time range180-86400，Unit (seconds); default：3600s",
  intervalLimit16:
    "Set the interval time range1-72，Unit (Hours); default：24h",
  intervalLimit17: "Einstelltemperaturbereich -127-127, Einheit (° C)",
  intervalLimit18: "Set interval time range 5-18000, unit (second)",
  intervalLimit19: "Set interval time range 10-300, unit (second)",
  intervalLimit20: "Set interval time range 5-399, unit (seconds)",
  intervalLimit21: "Set interval time range 5-300, unit (seconds)",
  noInterval: "Please input interval time!",
  intervalTips: "Turn off tracking mode by setting the alarm wake-up time",
  phoneMonitorTips:
    "After the command is sent, the device will actively dial the callback number to implement monitoring.",
  time1: "Time1",
  time2: "Time2",
  time3: "Time3",
  time4: "Time4",
  time5: "Time5",
  intervalNum: "Interval(minutes)",
  sun: "Sun.",
  mon: "Mon.",
  tue: "Tue.",
  wed: "Wed.",
  thu: "Thu.",
  fri: "Fri.",
  sat: "Sat.",
  awakenTime: "Awaken Time",
  centerPhone: "Center Phone",
  inputCenterPhone: "Input Center Phone!",
  phone1: "No.1",
  phone2: "No.2",
  phone3: "No.3",
  phone4: "No.4",
  phone5: "No.5",
  inputPhone: "Input phone",
  offlineCtrl:
    "Offline ctrl，Offline instructions will be automatically sent to the device after the device is online",
  terNotSupport: "Terminal Not Support",
  terReplyFail: "Terminal response failed",
  machineInfo: "Device Info",

  /************************************************/
  alarmTypeScreen: "Alarm type screen",
  allRead: "All read",
  read: "Read",
  noAlarmInfo: "No clearable alarm information",
  alarmTip: "Tip : Filter this type of alarm information by unchecked",

  /************************************************/
  updatePsw: "Password",
  resetPsw: "Reset Password",

  /************************************************/
  multiSell: "Batch Sale",
  sell: "Sales",
  sellSuccess: "Sell Success!",
  modifySuccess: "Modify success",
  modifyFail: "Modify failure",
  multiTransfer: "Batch Move",
  multiUserExpires: "Batch Modify Expiry Date",
  batchModifying: "Batch Modify",
  userTransfer: "Move",
  machineRemark: "Remark",
  sendCtrl: "Send Command",
  ctrl: "Command",
  ctrlLog: "Command History",
  ctrlLogTips: "Command History",
  s06Ctrls: ["Stop Engine", "Restore Engine", "Check Location"],
  ctrlType: "Command Name",
  resInfo: "Result",
  resTime: "Reponse Time",
  ctrlSendTime: "Send Time",
  // csv文件导入上传
  choseCsv: "Select The CSV File",
  choseFile: "Select The File",
  submit: "Submit",
  targeDevice: "The Target Device",
  csvTips_1: "1.Save the excel file as CSV format",
  csvTips_2: "2.Import the CSV file into the system",
  importExplain: "Import Instructions:",
  fileDemo: "File Format Example",

  // add
  sendType: "Type",
  onlineCtrl: "Online Command",
  offCtrl: "Offline Command",
  resStatus: [
    "Not sent",
    "Invalid",
    "Has Been Issued",
    "Execute Success",
    "Execution Failed",
    "No Answer",
  ],
  /************************************************/
  addSubordinateClient: "Add Sub-account",
  noSubordinateClient: "No Sub-account",
  superiorCustomerEmpty: "Choose Superior",
  noCustomerName: "Customer Name",
  noLoginAccount: "User",
  noPsw: "Password",
  noConfirmPsw: "Confirm Password",
  pswNotAtypism: "Password Not Atypism!",
  addSuccess: "Added successfully",
  superiorCustomer: "Superior",
  addVerticalImei: "Add Vertical IMEI",
  noImei: "No IMEI",
  addImei_curr: "Input IMEI Number",
  no: "Unit",
  aRowAImei: "One IMEI For One Line",

  /*
   * dealer  Beginning of interface translation
   *
   * */
  //main.js
  imeiOrUserEmpty: "IMEI Or User Empty!",
  accountEmpty: "IMEI/Name/Account is required",
  queryNoUser: "Check NoUser",
  queryNoIMEI: "Check No IMEI",
  imeiOrClientOrAccount: "IMEI/Name/Account",
  dueSoon: "To Expire",
  recentlyOffline: "Offline",
  choseSellDeviceFirst: "Please select the devices to sell first!",
  choseDeviceFirst: "Please select the device to move first!",
  choseDeviceExpiresFirst: "Please select the devices to modify first!",
  choseRenewDeviceFirst: "Please select the device to renew first!",
  choseDeleteDeviceFirst: "Please select the device to delete first!",
  choseClientFirst: "Please select the customer to move first!",

  //myClient.js
  clientList: "Customers",
  accountInfo: "Account Information",
  machineCount: "Device Amount",
  stock: "Total",
  inventory: "Stock",
  subordinateClient: "Sub-account",
  datum: "Information",
  monitor: "Monitor",
  dueMachineInfo: "Expiration",
  haveExpired: "Expired",
  timeRange: [
    "within 7days",
    "within 30days",
    "within 60days",
    "within 7-30days",
    "within 30-60days",
  ],
  offlineMachineInfo: "offline devices information",
  timeRange1: [
    "within an hour",
    "within a day",
    "within 7 days",
    "within a month",
    "within 60 days",
    "Over 60 days",
    "Between 1hour to 7days",
    "Between 1 day to 7days",
    "Between 7days to 30days",
    "Between 30 days to 60 days",
  ],
  offlineTime: "Range",
  includeSubordinateClient: "Sub-account",

  stopMachineInfo: "Static Information",
  stopTime1: "Static Time",
  unUseMachineInfo: "Unactive Information",
  unUseMachineCount: "Unactive Amount",

  sellTime: "Sell Time",
  detail: "Detail",
  manageDevice: "Manage Device",
  details: "Details",
  deleteSuccess: "Delete Success!",
  deleteFail: "Delete failure!",
  renewalLink: "Renewal Link",
  deleteGroupTips: "Whether to delete a group",
  addGroup: "Add group",
  jurisdictionRange: "Modify functions",
  machineSellTransfer: "Machine Sell",
  monitorMachineGroup: "Monitor",
  jurisdictionArr: [
    "Client manage",
    "Message managet",
    "GEO set",
    "Alarm manage",
    "Virtual account manage",
    "Dispatch instruction",
  ],
  confrimDelSim: "Confirm delete SIM card:",

  // 右键菜单
  sellDevice: "Sell Device",
  addClient: "Add User",
  deleteClient: "Delete User",
  resetPassword: "Reset Password",
  transferClient: "Move User",
  ifDeleteClient: "Delete",

  //myAccount
  myWorkPlace: "My Account",
  availablePoints: "Balance",
  yearCard: "Annual Card",
  lifetimeOfCard: "Lifelong Card",
  oneyear: "Import",
  lifeyear: "Annual",
  lifetime: "Lifelong",
  commonImportPoint: "Import Card",
  lifetimeImportPoint: "Import Card to lifelong",
  myServiceProvide: "Supplier",
  moreOperator: "More Operation",
  dueMachine: "Expired Device",
  offlineMachine: "Offline Device",
  quickSell: "Quick Sale",
  sellTo: "Target",
  machineBelong: "Belong To",
  reset: "Reset",
  targetCustomer: "Target User",
  common_lifetimeImport: "Anual Card(0),Lifelong Card(0)",
  cardType1: "Type",
  credit: "Quantity",
  generateImportPoint: "Create Import Card",
  generateImportPointSuc: "Import Card Successful!",
  generateImportPointFail: "Import Card Fail!",
  year_lifeTimeCard: "Import Card(0),Import Card To Lifelong(0)",
  generateRenewPoint: "Create Renew Card",
  transferTo: "Move",
  transferPoint: "Quantity",
  transferRenewPoint: "Move Renew Card",
  pointHistoryRecord: "Card History",
  newGeneration: "New",
  operatorType: "Operation",
  consume: "Consume",
  give: "Give",
  income: "Income",
  pay: "Expenditure",
  imeiErr: "Pls input last 6 numbers of IMEI!",
  accountFirstPage: "Home",

  /*
   * dealer  End of interface translation
   *
   * */
  // 1.4.8 risk control
  finrisk: "Financial Risk Control",
  attention: "Subscribe",
  cancelattention: "Delete Subscribe",
  poweroff: "Power Off",
  inout: "In And Out",
  inoutEF: "In And Out Of The Fence",
  longstay: "Mortgage Parking",
  secsetting: "Mortgage Point Setting ",
  EFsetting: "Geofence Setting",
  polygonFence: "Polygon Fence",
  cycleFence: "Cycle Fence",
  haveBeenSetFence: "Have Been Set Fence",
  haveBeenSetPoint: "Have Been Set Mortgage Point",
  drawingFailed: "Drawing Failed. Please Redraw!",
  inoutdot: "In And Out",
  eleStatistics: "Electricity Statistics",
  noData: "No Data",
  // 进出二押点表格
  accountbe: "Account",
  SMtype: "Mortgage Point Type",
  SMname: "Mortgage Point Name",
  time: "Time",
  position: "Position",
  lastele: "Remaining Battery",
  statisticTime: "Statistics Date",
  searchalarmType: [
    "All",
    "Offline",
    "Power Off",
    "In And Out Fence",
    "In And Out",
    "Mortgage Parking",
  ],
  remarks: [
    "Mortgage Point",
    "Guarantee Company",
    "Disassemble Point",
    "Second-hand Market",
  ],
  focusOnly: "Focus Only",

  autoRecord: "Automatic Recording",
  /******************************************************set command start**********************************8*/
  setCtrl: {
    text: "Set Command",
    value: "",
  },
  moreCtrl: {
    text: 'More instructions',
    value: ''
  },
  sc_openTraceModel: {
    text: "Set Trace Model",
    value: "0",
  },
  sc_closeTraceModel: {
    text: "Close Trace Model",
    value: "1",
  },
  sc_setSleepTime: {
    text: "Set Sleep Time",
    value: "2",
  },
  sc_setAwakenTime: {
    text: "Set Awaken Time",
    value: "3",
  },
  sc_setDismantleAlarm: {
    text: "Set Dismantle Alarm",
    value: "4",
  },
  sc_setSMSC: {
    text: "Center Number",
    value: "5",
  },
  sc_delSMSC: {
    text: "Delete SMSC",
    value: "6",
  },
  sc_setSOS: {
    text: "Add SOS",
    value: "7",
  },
  sc_delSOS: {
    text: "Delete SOS",
    value: "8",
  },
  sc_restartTheInstruction: {
    text: "Reset",
    value: "9",
  },
  sc_uploadTime: {
    text: "Set Upload Interval",
    value: "10",
  },
  /*Alarm clock time setting
     Timing reback time setting
     Dismantle alarm setting
     Week mode open close*/
  sc_setAlarmClock: {
    text: "Set Alarm Clock",
    value: "11",
  },
  sc_setTimingRebackTime: {
    text: "Set Timing Reback Time",
    value: "12",
  },
  sc_openWeekMode: {
    text: "Open Week Mode",
    value: "13",
  },
  sc_closeWeekMode: {
    text: "Close Week Mode",
    value: "14",
  },

  sc_powerSaverMode: {
    text: "Upload Interval",
    value: "15",
  },
  sc_carCatchingMode: {
    text: "Tracking Mode",
    value: "16",
  },
  sc_closeDismantlingAlarm: {
    text: "Close Dismantling Alarm",
    value: "17",
  },
  sc_openDismantlingAlarm: {
    text: "Open Dismantling Alarm",
    value: "18",
  },
  sc_VibrationAlarm: {
    text: "set Vibration Alarm",
    value: "19",
  },
  sc_timeZone: {
    text: "Time Zone Setting",
    value: "20",
  },
  sc_phoneMonitor: {
    text: "Telephone Listening",
    value: "21",
  },
  sc_stopCarSetting: {
    text: "Parking Settings",
    value: "22",
  },
  sc_bindAlarmNumber: {
    text: "Binding The Alarm Number",
    value: "23",
  },
  sc_bindPowerAlarm: {
    text: "Power Failure Alarm",
    value: "24",
  },
  sc_fatigueDrivingSetting: {
    text: "Fatigue Driving Setup",
    value: "25",
  },
  sc_peripheralSetting: {
    text: "Peripheral Settings",
    value: "26",
  },
  sc_SMSAlarmSetting: {
    text: "Set SMS Alert",
    value: "27",
  },
  sc_autoRecordSetting: {
    text: "Automatic Recording Settings",
    value: "28",
  },
  sc_monitorCallback: {
    text: "Listen Callback",
    value: "29",
  },
  sc_recordCtrl: {
    text: "Recording Instructions",
    value: "30",
  },
  sc_unbindAlarmNumber: {
    text: "Unbind Alarm Number",
    value: "31",
  },
  sc_alarmSensitivitySetting: {
    text: "Vibration Alarm Sensitivity Setting",
    value: "32",
  },
  sc_alarmSMSsettings: {
    text: "Vibration Alarm SMS Settings",
    value: "33",
  },
  sc_alarmCallSettings: {
    text: "Vibration Alarm Call Settings",
    value: "34",
  },
  sc_openFailureAlarmSetting: {
    text: "Turn On Power Failure Alarm",
    value: "35",
  },
  sc_restoreFactory: {
    text: "Restore Factory",
    value: "36",
  },
  sc_openVibrationAlarm: {
    text: "Turn On The Vibration Alarm",
    value: "37",
  },
  sc_closeVibrationAlarm: {
    text: "Turn Off The Vibration Alarm",
    value: "38",
  },
  sc_closeFailureAlarmSetting: {
    text: " Turn Off The Power Failure Alarm",
    value: "39",
  },
  sc_feulAlarm: {
    text: "Fuel Alarm Setting",
    value: "40",
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: "Power saving mode",
    value: "41",
  },
  sc_sleepMode: {
    text: "Sleep mode",
    value: "42",
  },
  sc_alarmMode: {
    text: "Alarm mode",
    value: "43",
  },
  sc_weekMode: {
    text: "Week mode",
    value: "44",
  },
  sc_monitorNumberSetting: {
    text: "Monitor number stetting",
    value: "45",
  },
  sc_singlePositionSetting: {
    text: "Single positioning mode",
    value: "46",
  },
  sc_timingworkSetting: {
    text: "Timing mode of work",
    value: "47",
  },
  sc_openLightAlarm: {
    text: "Open light Sensor Alarm",
    value: "48",
  },
  sc_closeLightAlarm: {
    text: "Close light Sensor Alarm",
    value: "49",
  },
  sc_workModeSetting: {
    text: "Working Mode Setting",
    value: "50",
  },
  sc_timingOnAndOffMachine: {
    text: "Timing Switch Setting",
    value: "51",
  },
  sc_setRealTimeTrackMode: {
    text: "Set real-time tracking mode",
    value: "52",
  },
  sc_setClockMode: {
    text: "Set alarm mode",
    value: "53",
  },
  sc_openTemperatureAlarm: {
    text: "Turn on temperature alarm",
    value: "54",
  },
  sc_closeTemperatureAlarm: {
    text: "Close temperature alarm",
    value: "55",
  },
  sc_timingPostbackSetting: {
    text: "Timing postback settings",
    value: "56",
  },
  sc_remoteBoot: {
    text: "Remote boot",
    value: "57",
  },
  sc_smartTrack: {
    text: "Smart tracking",
    value: "58",
  },
  sc_cancelSmartTrack: {
    text: "Cancel smart tracking",
    value: "59",
  },
  sc_cancelAlarm: {
    text: "Cancel alarm",
    value: "60",
  },
  sc_smartPowerSavingMode: {
    text: "Set Smart Power Saving Mode",
    value: "61",
  },
  sc_monitorSetting: {
    text: "Monitor",
    value: '62'
  },
  
   // 指令重构新增翻译
   sc_timedReturnMode: {
    text: 'Timed return mode',
    value: '100'
    },
    sc_operatingMode: {
        text: 'Operating mode',
        value: '101'
    },
    sc_realTimeMode : {
        text: 'Real-time positioning mode',
        value: '102'
    },
    sc_alarmMode : {
        text: 'Alarm mode',
        value: '103'
    },
    sc_weekMode : {
        text: 'Week mode',
        value: '104'
    },
    sc_antidemolitionAlarm : {
        text: 'Anti-demolition alarm',
        value: '105'
    },
    sc_vibrationAlarm : {
        text: 'Vibration alarm',
        value: '106'
    },
    sc_monitoringNumber : {
        text: 'Monitoring number management',
        value: '107'
    },
    sc_queryMonitoring : {
        text: 'Query monitoring number',
        value: '108'
    },
    sc_electricityControl : {
        text: 'Oil and electricity control',
        value: '109'
    },
    sc_SOSnumber : {
        text: 'SOS number management',
        value: '110'
    },
    sc_SleepCommand : {
      text: 'Sleep command',
      value: '201'
    },
    sc_punchTimeMode:{
        text:'打卡模式',
        value:'203'  
    },
    sc_intervelMode:{
        text:'时间段模式',
        value:'204'  
    },
    sc_activeGPS:{
        text:'激活GPS',
        value:'205'  
    },
    sc_lowPowerAlert: {
        text: 'Low Battery Reminder',
        value: '206'
    },
    sc_SOSAlert: {
        text: 'SOS报警',
        value: '207'
    },
    sc_RadiusCommand : {
        text: 'Displacement radius',
        value: '202'
    },
    mc_cuscom : {
      text: 'Custom command',
      value: '1'
    },
    NormalTrack: 'Normal tracking mode',
    listeningToNumber:'Are you sure you want to check the monitoring number?',
    versionNumber:'Are you sure you want to check the version number?',
    longitudeAndLatitudeInformation:'Are you sure you want to check the latitude and longitude information?',
    equipmentStatus:'Are you sure you want to check the status?',
    public_parameter:'Are you sure you want to check the parameters？',
    GPRS_parameter:'Are you sure you want to check the GPRS parameters?',
    deviceName: 'Are you sure you want to roll device？',
    SMS_alert:'Are you sure you want to check SMS reminder alarm?',
    theBindingNumber:'Are you sure you want to check the binding number ？',
    intervalTimeRange:'Setting interval time range is 001-999, unit (minute)',
    pleaseChoose:'Please choose',
    RealTimeCarChase:'Are you sure you want to set this device to real-time car chase mode?',
    inputPhoneNumber: "Please enter the phone number",
    inputCorPhoneNumber: "Please enter the correct phone number",
    autoCallPhone: "Tip: After the command is executed successfully, the terminal will automatically dial the set number",
    limitTheNumberOfCellPhoneNumbers1:'This command supports up to 5 mobile phone numbers',
    limitTheNumberOfCellPhoneNumbers2:'This command supports up to 3 mobile phone numbers',
    equipmentTorestart:'Are you sure you want to restart this device?',
    remindTheWay:'Way of reminding',
    alarmWakeUpTime:'Alarm wake-up time',
    alarmWakeUpTime1:'Alarm wake-up time 1',
    alarmWakeUpTime2:'Alarm wake-up time 2',
    alarmWakeUpTime3:'Alarm wake-up time 3',
    alarmWakeUpTime4:'Alarm wake-up time 4',
    sensitivityLevel:'Please select sensitivity level',
    parking_time:'Parking time',
    selectWorkingMode:'Please select working mode',
    Alarm_value:'Alarm value',
    Buffer_value:'Buffer value',
    gqg_disconnect:'disconnect',
    gqg_turnOn:'Turn on',
    Return_interval:'Return interval',
    gq_startTime:'Start time',
    gq_restingTime:'Resting time',
    gq_Eastern:'Eastern time zone',
    gq_Western:'Western time zone',

    gq_driver:'Fatigue driving alarm',
    gq_deviceName:'Are you sure you want to roll this device?',
    gq_noteAlarm:'Are you sure you want to check the SMS reminder alarm?',
    gq_restoreOriginal:'Are you sure you want to restore this equipment to the original factory?',
    gq_normalMode:'Normal mode',
    gq_IntelligentsleepMode:'Smart sleep mode',
    gq_DeepsleepMode:'Deep sleep mode',
    gq_RemotebootMode:'Remote boot mode',
    gq_IntelligentsleepModeTips:'Are you sure you want to set to smart sleep mode',
    gq_DeepsleepModeTips:'Are you sure you want to set to deep sleep mode',
    gq_RemotebootModeTips:'Are you sure you want to set to remote boot mode',
    gq_normalModeTips:'Are you sure you want to set to normal mode',
    gq_sleepModeTips:'Are you sure you want to set this device to sleep mode',
    gq_Locatethereturnmode:'Positioning return mode',
    gq_regularWorkingHours:'Timed working period',
    gq_AlarmType:{
        text: 'Alarm Type',
        value: '111'
    },
    IssuedbyThePrompt:'The command has been issued, please wait for the device to respond',
    platformToinform:'Platform notification',
    gq_shortNote:'SMS notification', 
  /************指令白话文**********************/
  closeDismantlingAlarm: "Close Dismantling Alarm",
  openDismantlingAlarm: "Open Dismantling Alarm",
  closeTimingRebackMode: "Close Timing Reback Mode",
  minute: "Mintues",
  timingrebackModeSetting: "Upload Interval:",
  setWakeupTime: "Set Wake-up Time:",
  weekModeSetting: "Week Mode Setting:",
  closeWeekMode: "Close Week Mode",
  setRealtimeTrackMode: "Set Teal-time Track Mode",
  fortification: "Fortification",
  disarming: "Disarming",
  settimingrebackmodeinterval: "Set Timing Reback Mode Interval:",
  oilCutCommand: "Fuel Cut Command",
  restoreOilCommand: "Restore Fuel Command",
  turnNnTheVehiclesPower: "Turn On The Vehicles Power",
  turnOffTehVehiclesPower: "Turn Off Teh Vehicles Power",
  implementBrakes: "Implement Brakes",
  dissolveBrakes: "Dissolve Brakes",
  openVoiceMonitorSlarm: "Open Voice Monitor Alarm",
  closeVoiceMonitorAlarm: "Close Voice Monitor Alarm",
  openCarSearchingMode: "Open Car Searching Mode",
  closeCarSearchingMode: "Close Car Searching Mode",
  unrecognizedCommand: "Unrecognized Command",
  commandSendSuccess:
    "Congratulations! The Device Executed The Command Successfully!",

  /********************************************设置指令结束**************************************************/

  /********************************************查询指令开始**************************************************/
  queryCtrl: {
    text: "Check Command",
    value: "",
  },
  /*参数设置查询*/
  qc_softwareVersion: {
    text: "Check Software Version",
    value: "1",
  },
  qc_latlngInfo: {
    text: "Check Latitude And Longitude",
    value: "2",
  },
  qc_locationHref: {
    text: "Check Parameter Configuration",
    value: "3",
  },
  qc_status: {
    text: "Check Status",
    value: "4",
  },
  qc_gprs_param: {
    text: "Check GPRS Param",
    value: "5",
  },
  qc_name_param: {
    text: "Roll Call",
    value: "6",
  },
  qc_SMSReminderAlarm_param: {
    text: "Check SMS Reminder Alarm",
    value: "7",
  },
  qc_bindNumber_param: {
    text: "Check Binding Number",
    value: "8",
  },

  /********************************************查询指令结束**************************************************/

  /*******************************************控制指令开始***************************************************/

  controlCtrl: {
    text: "Control Command",
    value: "",
  },
  cc_offOilElectric: {
    text: "stop Engine",
    value: "1",
  },
  cc_recoveryOilElectricity: {
    text: "Restore Engine",
    value: "2",
  },
  cc_factorySettings: {
    text: "Factory Setting",
    value: "4",
  },
  cc_fortify: {
    text: "Fortify",
    value: "75",
  },
  cc_disarming: {
    text: "Disarming",
    value: "76",
  },
  cc_brokenOil: {
    text: "Fuel Cut Instruction",
    value: "7",
  },
  cc_RecoveryOil: {
    text: "Recovery Fuel Circuit",
    value: "8",
  },

  /*******************************************控制指令结束***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: "KM",
  mileage: "Mileage",
  importMachine: "Add New IMEI",
  transferImportPoint: "Move Import Card",
  machineType1: "Model",
  confirmIMEI:
    "Please make sure the validity of IMEI No. and Model Name before importing,as the operation is irrevocable.",
  renew: "Renew",
  deductPointNum: "Quantity",
  renewSuccess: "Renew Success!",
  wireType: [
    {
      text: "Wired",
      value: false,
    },
    {
      text: "Wireless",
      value: true,
    },
  ],
  vibrationWays: [
    {
      text: "Platform",
      value: 0,
    },
    {
      text: "Platform+Message",
      value: 1,
    },
    {
      text: "Platform+Message+Phone",
      value: 2,
    },
  ],
  addMachineType: [
    {
      text: "S06",
      value: "3",
    },
    // SO6子级-----start-----------
    {
      text: "GT06",
      value: "8",
    },
    {
      text: "S08V",
      value: "9",
    },
    {
      text: "S01",
      value: "10",
    },
    {
      text: "S01T",
      value: "11",
    },
    {
      text: "S116",
      value: "12",
    },
    {
      text: "S119",
      value: "13",
    },
    {
      text: "TR06",
      value: "14",
    },
    {
      text: "GT06N",
      value: "15",
    },
    {
      text: "S101",
      value: "16",
    },
    {
      text: "S101T",
      value: "17",
    },
    {
      text: "S06U",
      value: "18",
    },
    {
      text: "S112U",
      value: "19",
    },
    {
      text: "S112B",
      value: "20",
    },
    // SO6子级-----end-----------
    {
      text: "S15/S02F",
      value: "1",
    },
    {
      text: "S05",
      value: "2",
    },
    {
      text: "SW06",
      value: "4",
    },
    {
      text: "S001",
      value: "5",
    },
    {
      text: "S08",
      value: "6",
    },
    {
      text: "S09",
      value: "7",
    },
  ],

  /*
   * 2018-02-02
   * */
  maploadfail:
    "Sorry,Load current map failed,Do you want to switch to another map?",

  /*
    2018-03-06新增 控制指令
    * */
  cc_openPower: {
    text: "Open Vehicle Power",
    value: "7",
  },
  cc_closePower: {
    text: "Colse Vehicle Power",
    value: "8",
  },
  cc_openBrake: {
    text: "Open Brake",
    value: "9",
  },
  cc_closeBrake: {
    text: "Close Brake",
    value: "10",
  },
  cc_openAlmrmvoice: {
    text: "Open Alarm",
    value: "11",
  },
  cc_closeAlmrmvoice: {
    text: "Close Alarm",
    value: "12",
  },
  /*2018-03-06新增 控制指令
   * */
  cc_openFindCar: {
    text: "Open Vehicle Search",
    value: "13",
  },
  cc_closeFindCar: {
    text: "Close Vehicle Search",
    value: "14",
  },
  /*2018-03-19
   * */
  EF: "GeoFence",

  /*
    2018-03-29，扩展字段
    * */
  exData: ["Power", "Voltage", "Fuel", "Temperature", "Resistance"],

  /*
    2018-04-10
    * */
  notSta: "LBS not included in Statistics",

  // 油量统计
  fuelSetting: "Fuel Setting",
  mianFuelTank: "Main Fuel Tank",
  auxiliaryTank: "Auxiliary tank",
  maximum: "Max",
  minimum: "Min",
  FullTankFuel: "Full Tank Fuel",
  fuelMinValue: 'The fuel volume cannot be less than 10L',
  standardSetting: "Standard Setting",
  emptyBoxMax: "Max of Empty",
  fullBoxMax: "Max of Full",
  fuelStatistics: "Fuel Statistics",
  settingSuccess: "Successed",
  settingFail: "Failed",
  pleaseInput: "Please input",
  fuelTimes: "Fuel Times",
  fuelTotal: "Fuel Total",
  refuelingTime: 'Refueling Time',
  fuelDate: "Fuel Date",
  fuel: "Fuel",
  fuelChange: "Fuel Change",
  feulTable: "Fuel Analysis Table",
  addFullFilter: "Please add the full filter",
  enterIntNum: "Please enter a positive integer",
  // 温度统计
  tempSta: "Temperature Statistics",
  tempTable: "Temperature Analysis Table",
  industrySta: "Industry Statistics",
  temperature: "Temperature",
  temperature1: "Temperature1",
  temperature2: "Temperature2",
  temperature3: "Temperature3",
  tempRange: "Temperature Range",
  tempSetting:'Temperature setting',
  tempSensor:'Temperature sensor',
  tempAlert:'After the temperature sensor is turned off, it will not receive temperature data!',
  phoneNumber: "Mobile Number",
  sosAlarm: "SOS Alarm",
  undervoltageAlarm: "undervoltage Alarm",
  overvoltageAlarm: "overvoltage Alarm",
  OilChangeAlarm: "Fuel Change Alarm",
  accDetection: "ACC detection",
  PositiveAndNegativeDetection: "Positive and negative detection",
  alermValue: "Alarm value",
  bufferValue: "Buffer value",
  timeZoneDifference: "Time zone difference",
  meridianEast: "Meridian East",
  meridianWest: "Meridian West",
  max12hour: "Can't be more than 12 hours",
  trackDownload: "Track Download",
  download: "Download",
  multiReset: "Bulk Reset",
  resetSuccess: "Reset Success",
  multiResetTips:
    "Test data such as device activation time and track will be cleared after reset, device status is reset to not enabled online or offline.",
  point: "Point",
  myplace: "My Place",
  addPoint: "Add a point",
  error10018: "The number of import points is insufficient",
  error110:'Object does not exist',
  error109:'Maximum limit exceeded',
  error20013:'Device type does not exist',
  error90001:'Device type serial number cannot be empty',
  error20003:'Imei cannot be empty',
  inputName: "Please enter a name",
  virtualAccount: "Virtual Account",
  createTime: "Create Time",
  permission: "Permission",
  permissionRange: "Permission Range",
  canChange: "Modifiable Function",
  fotbidPassword: "Change Password",
  virtualAccountTipsText:
    "When creating a virtual account, it is the alias account of the currently logged-in dealer account. You can set permissions for the virtual account. To create a virtual account for an User, you can first change the User type to a reseller, then log in with that reseller, create a virtual account, and then change the type back to the User.",
  noOperationPermission: "Virtual account has no operation permission",
  number: "Number",
  rangeSetting: "Scope Setting",
  setting: "Settings",
  // 1.6.1
  duration: "Duration",
  voltageSta: "Voltage Statistics",
  voltageAnalysis: "Voltage Analysis",
  voltageEchart: "Voltage analysis sheet",
  platformAlarm: "Platform Alarm",
  platformAlarm1: "Telephone",
  platformAndPhone: 'Telephone+Platform Alarm',
  smsAndplatformAlarm: "SMS+Platform Alarm",
  smsAndplatformAlarm1: "SMS",
  smsAndplatformAlarmandPhone: "Platform Alarm+SMS+Telephone",
  smsAndplatformAlarmandPhone1: "SMS+Telephone",
  more_speed: "Km",
  attribute: "Attribute",
  profession: "Professional",
  locationPoint: "Location Point",
  openPlatform: "Open API",
  experience: "Demo",
  onlyViewMonitor: "Only view Moniter",
  inputAccountOrUserName: "Please enter an account number or username",
  noDeviceTips: "No result of target information,search customer",
  // noUserTips: 'Did not find relevant user information, check equipment',
  noUserTips: "No result of customer information,search target",
  clickHere: "click here",
  pointIntervalSelect: "Track point interval",
  payment: "Payment",
  pleaceClick: "Click",
  paymentSaveTips:
    "Safety Tip: Please confirm with the expired the validity of the link",
  fuelAlarmValue: "Fuel quantity alarm value",
  fuelConsumption: "Fuel Consumption",
  client: "User",
  create: "Add",
  importPoint: "Import Card",
  general: "Import Card",
  lifelong: "Lifelong",
  renewalCard: "Renewal Card",
  settingFuelFirst:
    "Please set the fuel quantity alarm value before sending command!",
  overSpeedSetting: "Overspeed setting",
  kmPerHour: "km/h",
  times: "Times",
  total: "Total",
  primary: "Main",
  minor: "Vice",
  unActiveTips: "Device not activated.",
  arrearsTips: "The device is in arrears and cannot use this feature",
  loading: "Data Loading...",
  expirationReminder: "Expiration",
  projectName: "Project Name",
  expireDate: "Expiration date",
  changePwdTips:
    "Your password is too simple,security risk existed.Pls change your password asap",
  pwdCheckTips1: "Suggestions are 6-20 letters,numbers or symbols",
  pwdCheckTips2: "The password you entered is too simple",
  pwdCheckTips3: "Your password can be more complex",
  pwdCheckTips4: "Your password is secure",
  pwdLevel1: "Simple",
  pwdLevel2: "Middle",
  pwdLevel3: "Secure",
  comfirmChangePwd: "Confirm revise password",
  notSetYet: "Not set at the moment",
  // liter: 'Liter',
  liter: "L",
  arrearageDayTips: "Days due",
  todayExpire: "Due today",
  forgotPwd: "Forgot password?",
  forgotPwdTips: "Please Contact Seller To Modify Password",
  //1.6.7
  commonProblem: "FAQ",
  instructions: "Instructions",
  webInstructions: "WEB Instructions",
  appInstructions: "APP Instructions",
  acceptAlarmNtification: "Alarm Notification",
  alarmPeriod: "Alarm Period",
  whiteDay: "Day",
  blackNight: "Night",
  allDay: "Whole Day",
  alarmEmail: "Alarm Mail",
  muchEmailTips: "Support mutiple emails,separated by semicolons",
  newsCenter: "Notification Center",
  allNews: "All",
  unReadNews: "Unread",
  readNews: "Read",
  allTypeNews: "Notification Types",
  alarmInformation: "Alarm Information",
  titleContent: "Title",
  markRead: "Mark Read",
  allRead: "All read",
  allDelete: "Delete All",
  selectFirst: "Select First!",
  updateFail: "Update Failed!",
  ifAllReadTips: "All read？",
  ifAllDeleteTips: "Delete All？",
  stationInfo: "Station information",
  phone: "Phone",
  //1.6.72
  plsSelectTime: "Please choose time!",
  customerNotFound: "Can't find the customer",
  Postalcode: "Location",
  accWarning: "ACC Alarm",
  canInputMultiPhone:
    "You can enter multiple mobile numbers, please use; separate",
  noLocationInfo: "The device has no location information yet.",
  //1.6.9
  fenceName: "Fence name",
  fenceManage: "Fence management",
  circular: "Round",
  polygon: "Polygon",
  allFence: "All fence",
  shape: "shape",
  stationNews: "Station news",
  phonePlaceholder: 'Multiple numbers can be entered, separated by ","',
  addressPlaceholder:
    'Please enter the address and postal code in order, separated by ","',
  isUnbind: "Whether to unlink",
  alarmCar: "Alarm vehicle",
  alarmAddress: "Alarm location",
  chooseAtLeastOneTime: "Choose at least one time",
  alarmMessage: "No alarm details information",
  navigatorBack: "Return to superior",
  timeOverMessage: "User due cannot be greater than platform due",
  // 1.7.0
  newAdd: "New",
  findAll: "Total",
  findStr: " Matching Data",
  customColumn: "Customize",
  updatePswErr: "Updated Password Failed",
  professionalUser: "Professional User Or Not?",
  confirmStr: "Confirm",
  inputTargetCustomer: "Input Target Customer",
  superiorUser: "Superior User",
  speedReport: "Speed",
  createAccount: "Create Account",
  push: "Notification",
  searchCreateStr:
    "It will operate an account and transfer the device to this account.",
  allowIMEI: "Allow IMEI Login",
  defaultPswTip: "Default password is last 6 digits of IMEI",
  createAccountTip: "Created account and transferred device successful",
  showAll: "Show All",
  bingmap: "Bing Map",
  areaZoom: "Zoom",
  areaZoomReduction: "Restore zoom",
  reduction: "Reduction",
  saveImg: "Save as image",
  fleetFence: "Fleet Fence",
  alarmToSub: "Alarm notification",
  bikeFence: "Bike Fence",
  delGroupTip: "Delete failed, please delete interest point first",
  isExporting: "Exporting...",
  addressResolution: "Address resolution...",
  simNOTip: "Only Numbers",
  unArrowServiceTip:
    "Platform due is less than User due, Pleast select again, IMEI as Below: ",
  platformAlarmandPhone: "Platform Alarm+Tel",
  openLightAlarm: "Open light Sensor Alarm",
  closeLightAlarm: "Close light Sensor Alarm",
  ACCAlarm: "ACC Alarm",
  translateError: "Failed, no permission for user",
  distanceTip: "Click to confirm location,  double-click to end",
  workMode: "Working Mode",
  workModeType: "0: Normal Mode; 1 Intelligent Sleep Mode; 2 Deep Sleep Mode",
  clickToStreetMap: "Click to open the street view map",
  current: "Total",
  remarkTip: "note：Do not sell cards of different types at the same time",
  searchRes: "search results",
  updateIcon: "Change icon",
  youHaveALarmInfo: "You have an alert message",
  moveInterval: "movment interval",
  staticInterval: " static Interval",
  notSupportTraffic: "Coming soon.",
  ignite: "On",
  flameout: "Off",
  generateRenewalPointSuc: "Generate renewal points successfully",
  noGPSsignal: "Not positioned",
  imeiErr2: "Please enter at least last 6 digits of the imei number",
  searchCreateStr2: "This operation will create an account and transfer the device to this account at the same time",
  addUser: "Add User",
  alarmTemperature: "Alarmtemperaturwert",
  highTemperatureAlarm: "Hochtemperaturalarm",
  lowTemperatureAlarm: "Niedrigtemperaturalarm",
  temperatureTip: "Bitte geben Sie einen Temperaturwert ein！",
  locMode: "Positioning mode",
  imeiInput: "Please enter IMEI number",
  noResult: "No matching results",
  noAddressKey: "Temporarily unable to get address information",

  deviceGroup: "Manage Group",
  shareManage: "Share management ",
  lastPosition: "Last Position",
  defaultGroup: "Defaul Group",
  tankShape: "Fuel tank shape",
  standard: "Standard",
  oval: "oval",
  irregular: "Irregular",
  //1.8.4
  inputAddressOrLoc: "Please enter address / latitude and longitude",
  inputGroupName: "Input Group Name",
  lock: "lock",
  shareHistory: "Share history playback",
  tomorrow: "tomorrow",
  threeDay: "3 days",
  shareSuccess: "Share link success",
  effective: "effective",
  lapse: "invalid",
  copyShareLink: "Copy link success",
  openStr: "ON",
  closeStr: 'OFF',
  linkError: "Link error",
  inputUserName: "Input user name",
  barCodeStatistics: "Barcode Statistics",
  barCode: "Bar Icode",
  sweepCodeTime: "Scan time",
  workModeType2: "1 Smart sleep mode；2 Deep sleep mode；3 Remote switch mode",
  remoteSwitchMode: "Remote switch mode",
  saleTime: "Sale Date",
  onlineTime: "Online time",
  dayMileage: "Today mileage",
  imeiNum: "IMEI number",
  overSpeedValue: "Overspeed value",
  shareNoOpen: "Share link is not enabled",
  addTo2: "Customer",
  overDue: "Expired",
  openInterface: "Open API",
  privacyPolicy:'Privacy Policy',
  serviceTerm:'Terms Of Service',
  importError:
    "Failed to add, the device number (IMEI) must be a 15-digit number!",
  importResult: "Import results",
  totalNum: "total",
  successInfo: "success",
  errorInfo: "Fail",
  repeatImei: "IMEI repeat",
  includeAccount: "sub-account",
  formatError: "Fomat error",
  importErrorInfo: "Please enter 15-digit IMEI number",
  totalMileage: "totalMileage",
  totalOverSpeed: "Total overspeed (times)",
  totalStop: "Total stop(times)",
  totalOil: "Total oil",
  timeChoose: "Time selection",
  intervalTime: "Interval time",
  default: "Default",
  idleSpeedStatics: "Idle speed statistics",
  offlineStatistics:'Offline Statistics',
  idleSpeed: "Idle speed ",
  idleSpeedTimeTip1: "Idle time cannot be empty",
  idleSpeedTimeTip2: "Idle time must be a positive integer",
  averageSpeed: "Average speed",
  averageOil: "Average fuel consumption",
  oilImgTitle: "Oil analysis chart",
  oilChangeDetail: "Fuel change details",
  machineNameError: "The device name cannot contain special symbols( / ' )",
  remarkError: "Remark information cannot exceed 50 words",
  defineColumnTip: "Check up to 12 items",
  pswCheckTip:
    "The suggestion is a combination of 6-20 digits, letters and symbols",
  chooseGroup: "Please select a group",
  chooseAgain:
    "You can only query the data for the past six months, please select again！",
  noDataTip: "No data！",
  noMachineNameError: "Please select a device！",
  loginAccountError: "Login account cannot be 15 digits！",
  includeExpire: "Which expires",
  groupNameTip: "Group name cannot be empty！",
  outageTips: "Are you sure oil cut off?",
  powerSupplyTips: "Are you sure restore oil?",
  centerPhoneTips: "Please enter the number",
  centerPhoneLenTips: "Please enter 8-20 digits",
  passworldillegal: "There are illegal characters",
  // 2.0.0 POI，权限版本
  singleAdd:'Single add',
  batchImport:'Batch Import',
  name:'Name',
  icon:'Icon',
  defaultGroup:'Default group',
  remark:'Remark',
  uploadFile:'Upload file',
  exampleDownload:'Example download',
  uploadFiles:'Upload file',
  poiTips1:'You can import POI by uploading Excel file with related information. Please follow the format of example to prepare the file',
  poiTips2:'Name:Required, no more than 32 characters',
  poiTips3:'Icon: required, enter 1,2,3,4',
  poiTips4:'Latitude：Required',
  poiTips5:'Longitude：Required',
  poiTips6:'Group name: Optional, no more than 32 characters. If the group name is not filled in, the POI point belongs to the default group. If the filled group name is consistent with the created group name, the POI point belongs to the created group. The name of the group has not been created, the system will add the group',
  poiTips7:'Remarks: Optional, no more than 50 characters',
  // 权限相关
  roleLimit: 'Role Permissions',
  operateLog: 'Operation log',
  sysAccountManage: 'Authority account',
  rolen: 'Roles',
  rolename: 'Role Name',
  addRole: 'New role',
  editRole: 'Edit role',
  deleteRole: 'Delete Role',
  delRoleTip: 'Delete this Role?',
  delAccountTip: 'Delect account?',
  limitconfig: 'Permission settings',
  newAccountTip1: 'The authority  account is similar to the old virtual account, which is the sub-account of the administrator.Administrators can create authority accounts and assign different roles to authority accounts, so that different accounts can see different contents and operations on the platform.',
  newAccountTip2: 'Process of creating authority account:',
  newAccountTip31: '1. On the role management page, ',
  newAccountTip32: 'create a new role',
  newAccountTip33: ' and configure permissions for the role',
  newAccountTip4: '2. On the authority account management page, create a new authority account and assign roles to the account.',
  newRoleTip1: 'Administrators can create roles and configure different operation permissions for different roles to meet business needs in different scenarios.',
  newRoleTip2: 'For example, configure whether a financial role has the permission to locate and monitor, whether it has the permission to add customers, whether it has the permission to modify device information, etc.',
  "refuelrate": "Refueling rate",
  "refuellimit": "When the increase of oil per minute is greater than xxxxL and less than xxxxL, it is regarded as refueling.",
  "refueltip": "The maximum refueling rate must not be less than the minimum refueling rate!",
  viewLimitConf: 'View permission settings',
  viewLimit: 'View permissions',
  newSysAcc: 'New system account',
  editSysAcc: 'Edit authority  account',
  virtualAcc: 'Virtual account',
  oriVirtualAcc: 'Original virtual account',
  virtualTip: 'The virtual account module has been upgraded to a system account module, please create a new system account',
  operaTime: 'Operating time',
  ipaddr: 'IP address',
  businessType: 'Business Type',
  params: 'Request parameter',
  operateType: 'Operation Type',
  uAcc: 'User Account',
  uName: 'User Name',
  uType: 'User Type',
  logDetail: 'Log details',
  delAccount: 'Delete account',
  modifyTime: 'Modify Time',
  unbindlimit: 'Cannot create Geo-fence without associated device permissions!',
  setSmsTip: 'Please turn on the SMS notification before set up the SMS notification,if the SMS notification is not successful,please set again',
  cusSetComTip: 'Disclaimer: The risk brought by custom instructions has nothing to do with the platform',
  cusSetComPas: 'Please enter the password of the current login account',
  cusSetComDes1: 'Custom instructions only support online instructions.',
  cusSetComDes2: 'If the device does not respond within two minutes of sending the command, the process will be terminated.It is judged that the command status is no response.',
  cueSetComoffline: 'The device does not respond, and customized command failed!',
  fbType: 'Feedback type',
  fbType1: 'Counseling',
  fbType2: 'Malfunction',
  fbType3: 'User experience',
  fbType4: 'New feature suggestions',
  fbType5: 'Others',
  upload: 'Upload',
  uploadImg: 'Upload Image',
  uploadType: 'Please upload files of type .jpg .png .jpeg .gif',
  uploadSize: 'Upload file cannot be larger than 3M',
  fbManager: 'Feedback management',
  blManager: 'Announcement management',
  fbUploadTip: 'Please select feedback type',
  menuPlatform: "Platform news",
  menuFeedback: "Feedback",
  menuBulletin: "Platform announcement",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "Driving Behavior",
  BtyjdfghtwsrgGHFEEGRDAF: "Rapid Acceleration",
  BtyuwyfgrWERERRTHDAsdDF: "Rapid Deceleration",
  Be2562h253grgsHHJDbRDAF: "Sharp Turn",
  celTemperature:'Celsius Temperature'
};
// 权限tree
lg.limits = {
  "ACC_statistics": "ACC Statistics",
  "Account_Home": "Home",
  "Add": "New",
  "Add_POI": "Add  POI",
  "Add_customer": "Add User",
  "Add_device_group": "Add device group",
  "Add_fence": "Add Geo-Fence",
  "Add_sharing_track": "Add sharing track",
  "Add_system_account": "New authority account",
  "Alarm_details": "Alarm Details",
  "Alarm_message": "Alarm message",
  "Alarm_overview": "Alarm Overview",
  "Alarm_statistics": "Alarm Report",
  "All_news": "All",
  "Associated_equipment": "Associate Device",
  "Available_points": "Balance",
  "Barcode_statistics": "Barcode Statistics",
  "Batch_Import": "Batch Import",
  "Batch_renewal": "Batch Renew",
  "Batch_reset": "Bulk Reset",
  "Bulk_sales": "Batch Sale",
  "Call_the_police": "Alerts",
  "Customer_details": "Customer Details",
  "Customer_transfer": "Move User",
  "Delete_POI": "Delete  POI",
  "Delete_account": "Delete account",
  "Delete_customer": "Delete User",
  "Delete_device": "Delete Device",
  "Delete_device_group": "Delete device group",
  "Delete_fence": "Delete Fence",
  "Delete_role": "Delete Role",
  "Device_List": "Device List",
  "Device_grouping": "Manage Group",
  "Device_transfer": "Move Device",
  "Due_reminder": "Expiration",
  "Edit_details": "Edit details",
  "Equipment_management": "Devices",
  "My_clinet": "Devices",
  "Fence": "GeoFence",
  "Fence_management": "Fence management",
  "Generate": "Create",
  "Generate_lead-in_points": "Create Import Card",
  "Generate_renewal_points": "Create Renew Card",
  "Have_read": "Clear",
  "Idle_speed_statistics": "Idle speed statistics",
  "Import": "Importar",
  "Import_Device": "Add New IMEI",
  "Industry_Statistics": "Industry Statistics",
  "Location_monitoring": "Monitor",
  "Log_management": "Log management",
  "Mark_read": "Mark Read",
  "Menu_management": "Menu management",
  "Message_Center": "Notification Center",
  "Mileage_statistics": "Mileage Report",
  "Modify_POI": "Modify POI",
  "Modify_device_details": "Modify",
  "Modify_device_group": "Modify device group",
  "Modify_role": "Modify role",
  "Modify_sharing_track": "Modify sharing track",
  "Modify_user_expiration": "Batch Modify Expiry Date",
  "More": "More",
  "My_client": "Business",
  "New_role": "New role",
  "New_users": "Add User",
  "Oil_statistics": "Fuel Statistics",
  "POI_management": "POI management",
  "Points_record": "Card History",
  "Push": "Notification",
  "Quick_sale": "Quick Sale",
  "Renew": "Renew",
  "Replay": "Playback",
  "Role_management": "Role Managerment",
  "Run_overview": "Operation Statistics",
  "Running_statistics": "Operation Statistics",
  "Sales_equipment": "Sell Device",
  "Set_expiration_reminder": "Expiration",
  "Share_track": "Share history playback",
  "Sharing_management": "Share management",
  "Speeding_detailed_list": "Overspeed Details",
  "Statistical_report": "Report",
  "Stay_detailed_list": "Parking Details",
  "System_account_management": "Authority account",
  "Temperature_statistics": "Temperature Statistics",
  "Transfer": "Move",
  "Transfer_group": "Move Group",
  "Transfer_point": "Move Import Card",
  "Transfer_renewal_point": "Move Renew Card",
  "Trip_statistics": "Trip Report",
  "Unlink": "Cancel Geo-Fence",
  "View": "View",
  "View_POI": "View POI",
  "View_device_group": "View device group",
  "View_fence": "Check Fence",
  "View_role": "View role",
  "View_sharing_track": "View sharing track",
  "Virtual_account": "Virtual account",
  "Voltage_analysis": "Voltage Analysis",
  "Voltage_statistics": "Voltage Statistics",
  "batch_deletion": "Batch Delete",
  "change_Password": "Change Password",
  "delete": "Delete",
  "edit": "Edit",
  "instruction": "Commands",
  "modify": "Update",
  "monitor": "Monitor",
  "my_account": "Home",
  "reset_Password": "Reset Password",
  "share_it": "Share Location",
  "sub_user": "Sub-account",
  "track": "Tracking",
  "Custom_Order": "Custom command",
  "GeoKey_Manager": "GeoKey Managerment",
  "GeoKey_Update": "Update",
  "GeoKey_Delete": "Delete",
  "GeoKey_Add": "Add",
  "GeoKey_View": "View",
  "feedback_manager": "Feedback management",
  "feedback_list": "View",
  "feedback_handle": "Processing feedback",
  "proclamat_manager": "Announcement management",
  "proclamat_manager_list": "View announcement",
  "proclamat_manager_update": "Modification announcement",
  "proclamat_manager_delete": "Delete announcement",
  "proclamat_manager_save": "New announcement",
  "device_update_batch_model": "Modify device model",
}
// 问题文档的内容
lg.questionDocumentArr = [
  [
    "Q: After the wiring device is installed, the indicator light is off, device is offline",
    "A: After you turn off the car, using the electric pen and the universal meter to measure whether the voltage of the connected car line meets the GPS tracker voltage range, which is generally 9-36V.<br/>Wiring precautions: The installation wiring person need to have an understanding of the car line and have certain hands-on ability to avoid damage to your car caused by improper wiring.",
  ],

  [
    "Q: Wired devices or wireless real-time tracking devices, phone call is available or IOT card background boot status, device is offline",
    "2. A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Send a text message to restart and wait for a few minutes to see if it is online. Generally send RESET#. Please contact the dealer for details.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2.The network connection is unstable, please move the car to the good signal area. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 3. After the above steps, you have not been able to go online, you need to contact the mobile operator to check if the card is abnormal.",
  ],

  [
    "Q: The equipment is batch offline at the beginning and end of the month",
    "A: Please check if the card is in arrears. If it is arrears, please recharge it in time and resume using it.",
  ],

  [
    "Q: The car is driving, but the GPS online position is not updated.",
    "2. A：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Wiring device can send SMS STATUS# to check the satellite signal receiving status. If you see GPS:searching satellite, which means that the satellite signal has been in the search, this situation needs to check the installation location, whether it is installed according to the instructions. Face up, there is no metal cover on the top. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2.Send SMS STATUS#, return status is GPS: OFF, please send FACTORY# again, after receiving the reply OK, observing 5 minutes to see if the position is updated. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 3.According to the above 2 methods, you can't eliminate the fault. Please contact the seller for repair.",
  ],

  [
    "Q: Why the charging platform has been charged for a long time, it still shows that it is not full?",
    "A: The platform power display is based on the information feedback from the device to do a data analysis to determine the current power of the device. In some special cases, the power display error will occur.   Solutions: <br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. The device power data and the device positioning data are uploaded together. If the battery doesn’t change for a long time, please: ①Bring your device to move 100-300 meters to update the device's location information. Battery data and location data can be feedback together to the platform to refresh the battery display. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2、According to the change of the power indicator, determine whether it is fully charged (take S15 as an example). The operation steps are as follows: ①charge 8-10 hours, then the power indicator turns yellow green colour, after unplugging the charging line, then inserting the charging cable. Within 15 minutes, the power indicator will turn yellow green colour, which means full power. Please refer to the manual for other models.<br/>&nbsp;&nbsp;&nbsp;&nbsp; 3、Charging for a long time is also not full of electricity, this situation maybe bacause the charging plug voltage is lower than 1A, please use the voltage 5V, 1A charging head to charge 8-10 hours",
  ],

  [
    "Q: The GPS power cut off command has been successfully issued. Why the vehicle's oil and electricity is still not broken?",
    "A:After the power-off command is issued successfully, the equipment must be powered off in the following conditions:  <br/>&nbsp;&nbsp;&nbsp;&nbsp; 1、Ensure that the equipment is wired correctly and follow the wiring diagram of the manual. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2、The equipment works normally, is in a static or driving state, has positioning, is not offline, and the vehicle speed does not exceed 20 kilometers; <br/>If the vehicle is offline, not positioned or the vehicle speed exceeds 20 kilometers, even if the power cut command is delivered successfully, The terminal will not be executed.",
  ],

  [
    "Q: Three years of wireless product is installed firstly, the device displays not located or online",
    "A：<br/>&nbsp;&nbsp;&nbsp;&nbsp; 1. Turn on the switch to observe whether the indicator light is blinking. For example, the S18 yellow and green indicators flash quickly at the same time as normal, and flash slowly is in searching signal, and not flash means device is broken. (The status of different models will be different. Please refer to the manual for other models.) <br/>&nbsp;&nbsp;&nbsp;&nbsp;2、2. The indicator light flash but offline. If the signal is in poor position, please get the signal in the good signal area. If in the signal good area is not on the line, you can shut down for 1 minute, re-install the card and then start the test.",
  ],

  [
    "Q: The wired product is installed for the first time, and the device display is not positioned",
    "A：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Observe whether the GPS status indicator of the terminal is normal. Check the status of the indicator according to the instructions of different models. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 2. If the indicator light is off, the device is not able to be powered normally, then Change the place to take the power test<br/>&nbsp;&nbsp;&nbsp;&nbsp; 3、(green yellow) card indicator is not lit, power off and re-install the card, and then power on to see the normal light is normal. <br/>&nbsp;&nbsp;&nbsp;&nbsp;4. Make sure the SIM card number in the device is not in arrears, and the GPRS Internet access function is normal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;  5. There is no GSM network in the place where the equipment is located, such as the bottom room, tunnel, etc., where the signal is weak, please drive to the place where GPRS coverage is good. <br/>&nbsp;&nbsp;&nbsp;&nbsp;6, The position of the positioner should not be too closed, do not have metal objects, as far as possible in the car's installation position. Otherwise it affects signal reception. <br/>&nbsp;&nbsp;&nbsp;&nbsp; 7, Normal boot, stop in the signal good area is not online, you can re-issue the line command to check whether the IP interface and card link network is normal.",
  ],
];
lg.webOptDoc = "Coming soon...";
lg.appOptDoc = "Coming soon...";
// 查询参数的帮助
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Check Terminal Password</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>Check Terminal Built-in SIM Card</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Check Owner Phone Number</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push("<td>Check The Value Of Speed Limit</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push("<td>Check The Frequency Of Tracking,The Unit Is Seconds</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push("<td>Check Whether Tracking Is Enabled.1:Enable,0:Disable</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push("<td>Check The Illegal Migration Alarm Range,Unit: Meter</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push("<td>Check whether SMS alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Check vibration sensitivity is 0 to 15,0 is the highest sensitivity, too high may be false alarm, 15 is the lowest sensitivity</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push("<td>Check whether call alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Check whether GPS filter drift is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the stationary state with no vibration occurs within 5 minutes,and filter all GPS drift</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Check whether sleep function is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the sleeping mode with no vibration occurs within 30 minutes,it will close GPS function and save power </td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Check whether the power off alarm is enabled,1:enbale,0:disabled</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">GPS:</td>');
html.push(
  "<td>Check the satellite signal strength,For example：2300 1223 3431 。。。 a total of 12 sets of four-digit,2300 means: The signal strength from Number 23 satellite is 0,1223 means: The signal strength from Number 12 satellite is 23</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VBAT:</td>');
html.push(
  "<td>Check battery voltage, charging port voltage Charge current For example: VBAT = 3713300:4960750:303500 Indicates that the battery voltage is 3713300uV 3.71v Applied to the charging voltage on the chip 4.96V,Charging current 303mA</td>"
);
html.push("</tr>");
html.push("</table>");
lg.queryparamhelp = html.join("");

// 设置参数的帮助
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Set terminal password,which is only 6 digits</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PHONE:</td>');
html.push("<td>Set SIM number of the terminal</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">USER:</td>');
html.push("<td>Set the number of mobile phone owners</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SPEED:</td>');
html.push("<td>Set the speed limit value,0-300</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Set up the reported frequency when turn on tracking,Unit:secend</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TRACE:</td>');
html.push("<td>Set up whether open the track,1:open,0:close</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RADIUS:</td>');
html.push("<td>Set up the illegal migration alarm range,Unit: meter</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push("<td>Set up whether SMS alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Set up vibration sensitivity of 0 to 15,0 is the highest sensitivity, too high may be false alarm, 15 is the lowest sensitivity</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push("<td>Set up whether call alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Set up whether GPS filter drift is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the stationary state with no vibration occurs within 5 minutes,and filter all GPS drift</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">SLEEP:</td>');
html.push(
  "<td>Set up whether sleep function is enabled,1:enable,0:disable,if you turn on the anti-theft device will enter the sleeping mode with no vibration occurs within 30 minutes,it will close GPS function and save power</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Set up whether the power off alarm is enabled,1:enbale,0:disabled</td>"
);
html.push("</tr>");
html.push("</table>");
lg.setparamhelp = html.join("");

//批量添加
html = [];
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox"  ' +
    'style="z-index: 999;position:absolute;left:146px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Customer:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkAdds_treeDiv" +
    "," +
    "bulkAdds_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Platform Due:</td>'
);
html.push("<td>");
html.push(
  '<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Model:</td>'
);
html.push("<td>");
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    "</div>" +
    '<div id="deviceList"></div>' +
    "</div>" +
    "</span>"
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>'
);
html.push("<td>");
html.push(
  '<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push(
  '<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>'
);
lg.bulkAdds = html.join("");

//批量续费
html = [];
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:92px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Add Device:</td>'
);
html.push("<td>");
html.push(
  '<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="re_addNumBox">Total：<span id="account_re_addNum">0</span>'
);
html.push("</span>");
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<tr>");
html.push(
  '<td style="text-align:right;"><span style="color:red">*</span>Type</td>'
);
html.push("<td>");
html.push('<input  type="radio" name="red_cardType"');
html.push(
  'class="easyui-validatebox"  value="3" checked><label>Annual</label></input>'
);
html.push(
  '<input  type="radio" name="red_cardType" style="margin-left:15px;" '
);
html.push(
  'class="easyui-validatebox" value="4"><label>Lifelong</label></input>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td style="text-align: right">Quantity</td>');
html.push("<td>");
html.push(
  '<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>User Due:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Remark</td>'
);
html.push("<td>");
html.push(
  '<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkRenew = html.join("");

//批量销售，myAccount
html = [];
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:126px;top:85px;border: 1px solid #ccc !important;height:200px;width:250px;"> '
);
html.push('<ul style="height:200px;" class="ztree" id="bulkSales_tree"></ul> ');
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Customer:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkSales_treeDiv" +
    "," +
    "bulkSales_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push(
  '<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>User Due:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="bs_addNumBox">Total：<span id="account_bs_addNum">0</span>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="bs_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");

lg.bulkSales = html.join("");

//批量转移1，弹出框
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:152px;top:171px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Target User:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>'
);
html.push("<td>");
html.push(
  '<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >Batch Add</a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Move</a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)" >Cancel</a>'
);

lg.bulkTransfer = html.join("");

//批量转移2,myClient
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:142px;top:83px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Target User:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Additional:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bt_addMachines" style="cursor:pointer" title="' +
    lg.addTo +
    '" src="../../images/main/myAccount/add3.png" />'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);

lg.bulkTransfer2 = html.join("");

//批量转移用户
html = [];
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:132px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Target User:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">'
);
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("<td></td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");

lg.bulkTransferUser = html.join("");
window.lg = lg