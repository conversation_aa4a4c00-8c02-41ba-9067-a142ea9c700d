var host = window.location.host;
var site = 'WhatsGPS'; //默认显示立即定位的登录样式
if (host.indexOf('emboard.cn') != -1) { //大车嘀定制页emboard.cn
  site = 'EMBOARD'
} else if (host.indexOf('www.justrack.com') != -1) {//马来西亚justrack定制页my.justrack
    site = 'JusTrack'
} else if (host.indexOf('bngps.top') != -1) {  //台湾客户定制页bngps.top
    site = 'BNGPS'
} else if (host.indexOf('justrackevo.com') != -1) {  ////马来西亚evo定制页evo
    site = 'JusTrack'
} else if (host.indexOf('fleet.ownmake.co.uk') != -1) {  ////fleet.ownmake.co.uk
    site = 'FleetTrack'
} else if (host.indexOf('ytrack.my') != -1) {  ////马来西亚定制页ytrack.my
    site = 'YTRACK'                  
} else if (host.indexOf('justrackai.com') != -1) {  ////马来西亚定制页justrackai.com
    site = 'JusTrack'
} else if (host.indexOf('s3dz') != -1) {  ////马来西亚定制页justrackai.com
    site = 'S3'
} else if (host.indexOf('camino-tech') != -1) {  ////barry定制页camino-tech.com
    site = 'CAMINO'
} else if (host.indexOf('geolock.sn') != -1) {  ////geolock.sn                  
    site = 'GEOLOCK'
}else if (host.indexOf('countertrack') != -1) {  ////countertrack.com
    site = 'COUNTERTRACK'
}else if (host.indexOf('prevatechgps') != -1) {  ////countertrack.com
    site = 'Prevatech'
}else if (host.indexOf('forcegps') != -1) {  ////countertrack.com
  site = 'Forcegps'
}
//法语
var lg = {
  //common common
  user_guide: "Mode d'emploi",
  remoteSwitch : "Commutateur à distance",
  pageTitle:
    "WhatsGPS Global Tracking System|Vehicle GPS Tracker|3G Tracker|mini 4G Tracker|GPSNow|Car Locator",
  description:
    site+" is dedicated to providing users with intelligent cloud location services. It is the world's leading location service platform.",
  pageLang: "Français",
  inputCountTips: "Compte/IMEI",
  inputPasswordTips: "Mot de passe",
  appDownload: "APP Télécharger",
  rememberPassword: "Souviens-toi de moi",
  forgetPassword: 'Mot de passe oublié',
  siteName: "WhatsGPS",
  noToken: "Envoyez un jeton SVP",
  loginFirst: "S'enregistrer d'abord",
  move: "En Mvmt",
  stop: "Statique",
  query: "Valider",
  imeiQuery: "IMEI",
  delete: "Effacer",
  update: "Mettre a jour",
  cancel: "Annuler",
  soft: "N ° ",
  more: "Plus",
  useful:'utile',
  useless:'peu serviable',
  about:'About',
  replyFeedback:'Commentaires sur "$"',
  edit: "Editer",
  add: "Ajouter",
  addTo: "Ajouter",
  addDevice: "Ajouter un appareil", //en
  machineName: "Nom du périphérique",
  searchDevice: "Dispositif",
  date: "Date heure",
  LatestUpdate: "Signaux",
  engine: "ACC",
  locTime: "Heure GPS",
  locType: "Type d'emplacement",
  startLoc: "Lieu de départ",
  endLoc: "Lieu d'arriver",
  address: "Adresse",
  noAddressTips: "Impossible d'obtenir les informations d'adresse",
  lonlat: "Latitude et longitude",
  carNO: "Numéro de plaque",
  imei: "IMEI",
  IMEI: "IMEI",
  simNO: "Carte SIM",
  activeTime: "Temps activé",
  expireTime: "temps expiré",
  acceptSubordinateAlarm: "Acceptez L’alarme Subordonnée",
  acceptAlarmTips1: "Après vérification, utilisateur",
  acceptAlarmTips2:
    "Je reçois des informations sur tous les clients subordonnés",
  speed: "Vitesse",
  y: "Années",
  M: "Mois",
  d: "Jours",
  h: "Heures",
  min: "Minutes",
  s: "Secondes",
  _year: "y",
  _month: "m",
  _day: "d",
  _hour: "h",
  _minute: "m",
  _second: "s",
  confirm: "Confirmer",
  yes: "Oui",
  car: "Voiture",
  not: "Non",
  m: "Meters",
  account: "Compte",
  psw: "Mot de passe",
  save: "Sauvegarder",
  operator: "Exploiter",
  queryNoData: "Interroger aucune donnée",
  name: "Nom",
  type: "Modèle",
  open: "Ouvrir",
  close: "Fermer",
  send: "Envoyer",
  alarm: "Alarme",
  alarmSetting: "Paramètres d'alarme",
  look: "Vue",
  tailAfter: "Suivi",
  history: "La lecture",
  dir: "Direction",
  locStatus: "état d'emplacement",
  machineTypeText: "Modèle",
  carUser: "Utilisateur du vehicule",
  machine: "Cible",
  unknowMachineType: "Machine inconnue",
  noCommandRecord: "Commands not serve for this device",
  type1: "Type",
  role: "Rôle",
  roles: "Les rôles",
  timeType: "Type de temps",
  moveSpeed: "Vitesse de déplacement",
  signal: "Signaux",
  loc: "Emplacement",
  wiretype: "Types de",
  wire: "Ligne",
  wireless: "Sans fil",
  expire: "Expiré",
  hour: "Heure",
  hourTo: "Heure à",
  remark: "Remarques",
  remarkInfo: "Remarque",
  noPriviledges: "Le compte a des privilèges sans opération",
  commandNoOpen:
    "La commande actuelle du périphérique n'est pas encore ouverte pour utilisation",
  choseDelelePhone: "Veuillez choisir le numéro pour supprimer le premier",
  streetView: "Tview œuvrant",
  wrongFormat: "Erreur de format d'entrée",
  inputFiexd: "Entrer un numéro fixe",
  serialNumberStart: "Le premier numéro d'IMEI consécutif",
  serialNumberEnd: "Le dernier numéro de IMEI consécutif",
  clickSearchFirst:
    "S'il vous plaît cliquez d'abord sur le dispositif de recherche!",
  isDeleteDevice:
    "Le dispositif ne peut pas être ravivé après être effacé. Il est supprimé?",
  //平台错误代码提示
  errorTips: "L'opération a échoué avec code d'erreur:",
  error10003: 'Mauvais mot de passe',
  error90010: "L'appareil est hors ligne et l'envoi de la commande personnalisée a échoué!",
  error70003: 'La valeur de la télécommande ne peut pas être vide',
  error70006: "Ne prend pas en charge ou n'a pas le droit d'émettre l'instruction",
  error20001: "L'ID du véhicule ne peut pas être vide",
  error20012: "Le véhicule n'est pas activé",
  error10012: "Erreur Ancien mot de passe",
  error10017:
    "Échec de la suppression, s'il vous plaît supprimer le sous utilisateur!",
  error10023: "Échec de la suppression, l'utilisateur a un dispositif",
  error20008: "Échec  d'ajout, IMEI existant",
  error20006: "S'il vous plaît entrer un IMEI de longueur 15",
  error10019: "Mauvais format du numero de téléphone",
  error10024: "Do not repeat sales", //en
  error120003: "Impossible de partager les liens",
  error10025: "L’information sur le dispositif modifié ne doit pas être vide",
  error2010: "Je vous en prie, transmettez le dossier",
  error20002: "Pas de numéro IMEI",
  error10081: "Nombre insuffisant de cartes de renouvellement",
  error10082: 'Pas besoin de recharger pour un appareil à vie',
  error3000: 'Le rôle a été attribué au compte système et ne peut pas être supprimé',
  error103: 'Le compte a été désactivé, veuillez contacter votre fournisseur de services',
  error124: 'Ne peut pas fonctionner sur lui-même',
  // 登陆相关 login.js
  logining: "Connexion en cours...",
  login: "Connexion",
  userEmpty: "Utilisateur vide",
  pswEmpty: "Mot de passe vide",
  prompt: "Rappel",
  accountOrPswError: "Erreur de nom d'utilisateur ou de mot de passe",
  UserNameAlreadyExist: "Le Nom d'utilisateur existe déjà",
  noQualified: "Il n’y a pas de qualification",
  //main.js
  systemName: "WhatsGPS",
  navTitle_user: ["Surveiller", "Rapport", "Dispositif"],
  navTitle_dealer: ["Accueil", "Business", "Surveiller", "Plus d'opérations"],
  exitStytem: "Quitter",
  user: "Utilisateur",
  UserCenter: "Centre utilisateur",
  alarmInfo: "Alarme",
  confirmExit: "confirmer la déconnexion?",
  errorMsg: "Message d'erreur:",
  logintimeout: "Délai de connexion!",
  clearAlarm: "Vider",
  clear: "Les",
  searchbtn: "Utilisateur",
  print: "Imprimer",
  export: "Exportation",
  //feedback
  feedback: "Rétroaction",
  feedback_sublime: "soumettre",
  alerttitle: "Le titre ne peut pas être vide!",
  alertcontent: "Les commentaires ne peuvent pas être vides!",
  submitfail: "Échec de sauvegarde!",
  saveSuccess: "Sauvegarde Reussi",
  submitsuccess:
    "Soumis avec succès! Nous traiterons vos commentaires dès que possible ~",
  adviceTitle: "Titre",
  adviceTitle_p: "Titre de la question et de l'opinion",
  adviceContent: "Questions et opinions",
  adviceContent_p:
    "Décrivez brièvement les questions et les commentaires que vous souhaitez commenter et nous Allons les améliorer.",
  contact: "Informations de contact",
  contact_p: "Remplissez votre téléphone ou email",
  //monitor.js
  myMachine: "Dispositif",
  all: "Tous",
  online: "En ligne",
  offline: "hors ligne",
  unUse: "Inutilisé",
  group: "Groupe",
  moveGruop: "Déplacer vers le groupe",
  arrearage: "Arriéré",
  noStatus: "Pas de statut",
  inputMachineName: "Cible/IMEI",
  defaultGroup: "Groupe par défaut",
  offlineLessOneDay: "Hors ligne <un jour",
  demoUserForbid:
    "Les utilisateurs expérimentés ne peuvent pas utiliser cette fonctionnalité",
  shareTrack: "Partager",
  shareName: "Nom",
  liveShare: "Real-time track sharing",
  expiration: "Expiration time",
  getShareLink: "Créer un lien",
  copy: "Copier",
  copySuccess: "Copie réussie!",
  enlarge: "Agrandir",
  shareExpired: "Le lien partagé a expiré",
  LinkFailure: "Echec d'ouverture du lien",
  inputShareName: "Entrez un nom de partage",
  inputValid: "Veuillez entrer à l’heure de la correction",
  //statistics.js
  runOverview: "Vue d'ensemble de déplacement",
  runSta: "Vue d'ensemble de déplacement",
  mileageSta: "Rapport de kilométrage",
  tripSta: "Rapport de voyage",
  overSpeedDetail: "Détails de survitesse",
  stopDetail: "Détails de stationnement",
  alarmSta: "Rapport d'alarme",
  alarmOverview: "Vue d'ensemble de l'alarme",
  alarmDetail: "Détails de l'alarme",
  shortcutQuery: "Vérification rapide",
  today: "Aujourd'hui",
  yesterday: "Hier",
  lastWeek: "Semaine dernière",
  thisWeek: "Cette semaine",
  thisMonth: "Ce mois-ci",
  lastMonth: "Mois dernier",
  mileageNum: "Kilométrage",
  overSpeedNum: "Survitesse (km / h)",
  overSpeed: "Survitesse",
  stopTimes: "Stationnment (Fois)",
  searchMachine: "Chercher",
  speedNum: "Vitesse (km / h)",
  querying: "Interroger",
  stopTime: "Temps statique",
  HisToryStopTime: "Static Time",
  clickLookLoc: "Cliquez pour voir l'adresse",
  lookLoc: "Requête de l'emplacement",
  noData: "Pas de données",
  alarmTime: "Heure d'alarme",
  vibrationLevel: "Niveau de vibration",
  vibrationWay: "Type d'alarme",
  acc: "ACC",
  accStatistics: "Statistiques ACC",
  accType: ["Tous", "ACC ouvert", "ACC fermer"],
  accstatus: ["Allumé", "Eteint"],
  openAccQuery: "Open ACC query",
  runtime: "Durée de parcours",
  //监控页面修改
  run: "Travel",
  speed: "Speed",
  //设备管理
  machineManage: "Dispositifs",
  deviceTable: "Mes Dispostifs",
  status: "Statut",
  havaExpired: "Ont expiré",
  expiredIn60: "Expire dans 60",
  expiredIn7: "Expire dans 7",
  normal: "Nomal",
  allMachine: "Tous",
  allMachine1: "Tous les dispositifs",
  expiredIn7Machine: "Expire dans 7 jours",
  expiredIn60Machine: "Expire dans 60 jours",
  havaExpiredMachine: "Cibles expirées",

  //history.js
  replay: "Lire",
  replaytitle: "La lecture",
  choseDate: "Sélectionner une date",
  from: "Du",
  to: "au",
  startTime: "à partir de",
  endTime: "Jusqu'à",
  pause: "Pause",
  slow: "Lent",
  mid: "Mi",
  fast: "Rapide",
  startTimeMsg: "Heure de début msg",
  endTimeMsg: "Heure de fin msg",
  smallEnd:
    "Heure de fin plus petit que l'heure de départ, veuillez choisir à nouveau!",
  bigInterval: "Intervalle doit etre moins de 31 jours!",
  trackisempty: "Piste vide",
  longitude: "Longitude",
  latitude: "Latitude",
  direction: "Direction",
  stopMark: " Marqueur de stationnement ",
  setStopTimes: [
    {
      text: "1 minute",
      value: "1",
    },
    {
      text: "2 minutes",
      value: "2",
    },
    {
      text: "3 minutes",
      value: "3",
    },
    {
      text: "5 minutes",
      value: "5",
    },
    {
      text: "10 minutes",
      value: "10",
    },
    {
      text: "15 minutes",
      value: "15",
    },
    {
      text: "20 minutes",
      value: "20",
    },
    {
      text: "30 minutes",
      value: "30",
    },
    {
      text: "45 minutes",
      value: "45",
    },
    {
      text: "1 heure",
      value: "60",
    },
    {
      text: "6 heures",
      value: "360",
    },
    {
      text: "12 heures",
      value: "720",
    },
  ],
  filterDrift: "Supprimer l'emplacement de la déviation",
  userType: [
    "Admin",
    "Vendeur",
    "Utilisateur final",
    "Logistique",
    "Location",
    "Utilisateur du vehicule",
    "Contrôle des risques",
    "Professionnel",
  ],
  userTypeArr: [
    "Admin",
    "Dealer",
    "End user",
    "Logistics",
    "Rental",
    "Car user",
    "Professionnel",
  ],
  machineType: {
    '0':'Type de machine',
    '1':'S15',
    '2':'S05',
    '93':'S05L',
    '94': 'S309',
    '95': 'S15L',
    '96':'S16L',
    '97':'S16LA',
    '98':'S16LB',
    '3':'S06',
    '4':'SW06',
    '5':'S001',
    '6':'S08',
    '7':'S09',
    '8':'GT06',
    '9':'S08V',
    '10':'S01',
    '11':'S01T',
    '12':'S116',
    '13':'S119',
    '14':'TR06',
    '15':'GT06N',
    '16':'S101',
    '17':'S101T',
    '18':'S06U',
    '19':'S112U',
    '20':'S112B',
    '21':'SA4',
    '22':'SA5',
    '23':'S208',
    '24':'S10',
    '25':'S101E',
    '26':'S709',
    '99':'S709L',
    '27':'S1028',
    '28':'S102T1',
    '29':'S288',
    '30':'S18',
    '31':'S03',
    '32':'S08S',
    '33':'S06E',
    '34':'S20',
    '35':'S100',
    '36':'S003',
    '37':'S003T',
    '38':'S701',
    '39':'S005',
    '40':'S11',
    '41':'T2A',
    '42':'S06L',
    '43':'S13',
    '86':'S13-B',
    '44':'GT800',
    '45':'S116M',
    '46':'S288G',
    '47':'S09L',
    '48':'S06A',
    '49':'S300',
    '50':'',
    '51':'GS03A',
    '52':'GS03B',
    '53':'GS05A',
    '54':'GS05B',
    '55':'S005T',
    '56':'AT6',
    '57':'GT02A',
    '58':'GT03C',
    '59':'S5E',
    '60':'S5L',
    '61':'S102L',
    '85':'S105L',
    '62':'TK103',
    '63':'TK303',
    '64':'ET300',
    '65':'S102A',
    '91':'S102A-D',
    '66':'S708',
    '67':'MT05A',
    '68':'S709N',
    '69':'',
    '70':'GS03C',
    '71':'GS03D',
    '72':'GS05C',
    '73':'GS05D',
    '74':'S116L',
    '75':'S102',
    '76':'S102T',
    '77':'S718',
    '78':'S19',
    '79':'S101A',
    '80':'VT03D',
    '81':'S5L-C',
    '82':'S710',
    '83':'S03A',
    '84':'C26',
    '87':'S102M',
    '88':'S101-B',
    '92':'LK720',
    '89':'S116-B',
    '90':'X3'
  },
  alarmType: [
    "Type d'alarme",
    "Alarme de vibration",
    "Alarme de coupure de courant",
    "Alarme de batterie faible",
    "Alarme SOS",
    "Alarme de survitesse",
    "Alarme de géo-clôture",
    "Alarme de déplacement",
    "Alarme de batterie externe faible",
    "Alarme hors zone",
    "Alarme de demontage",
    "Alarme de détection de lumière",
    "Alarme magnétique",
    "Démonter l'alarme",
    "Alarme Bluetooth",
    "Alarme de signalisation",
    "Alarme de station de fausse base",
    "Alarme dans zone Géo-clôture",
    "Alarme dans zone Géo-clôture",
    "Alarme Hors zone Géo-clôture",
    "Alarme porte ouverte",
    "Fatigue au volant",
    "Point d'hypothèque d'entrée",
    "Point d' hypothécaire de sortie",
    "Point hypothécaire",
    "Terminal déconnecté",
    "Alarme dans zone Géo-clôture",
    "Alarme Hors zone Géo-clôture",
    "Alarme dans zone Géo-clôture",
    "Alarme Hors zone Géo-clôture",
    "Alarme de carburant",
    "Alarme ACC ON",
    "Alarme ACC OFF",
    "Alarme de collision",
  ],
  alarmTypeNew:  {
    '40': "Alarme de température élevée",
    '45': "Alarme de basse température",
    '50': "Alarme de surtension",
    '55': "alarme de sous-tension",
    '60': 'Alarme de stationnement'
  },
  alarmNotificationType: [
    { type: "Vibration alarm", value: 1 },
    { type: "Power off alarm", value: 2 },
    { type: "Alarme de batterie faible", value: 3 },
    { type: "Alarme SOS", value: 4 },
    { type: "Alarme de survitesse", value: 5 },
    // {type:'Alarme de géo-clôture',value:6},
    { type: "Alarme de déplacement", value: 7 },
    { type: "Alarme de batterie externe faible", value: 8 },
    { type: "Alarme hors zone", value: 9 },
    { type: "Alarme de demontage", value: 10 },
    { type: "Alarme de détection de lumière", value: 11 },
    { type: "Démonter l'alarme", value: 13 },
    { type: "Alarme de signalisation", value: 15 },
    { type: "Alarme de station de fausse base", value: 16 },
    // {type:'Alarme dans zone Géo-clôture',value:17},
    // {type:'Alarme dans zone Géo-clôture',value:18},
    // {type:'Alarme Hors zone Géo-clôture',value:19},
    { type: "Fatigue au volant", value: 21 },
    { type: "Point d'hypothèque d'entrée", value: 22 },
    { type: "Point d' hypothécaire de sortie", value: 23 },
    { type: "Point hypothécaire", value: 24 },
    { type: "Terminal déconnecté", value: 25 },
    // {type:'Alarme dans zone Géo-clôture (Risk control)',value:26},
    // {type:'Alarme Hors zone Géo-clôture(Risk control)',value:27},
    { type: "Alarme dans zone Géo-clôture", value: 26 },
    { type: "Alarme Hors zone Géo-clôture", value: 27 },
    { type: "Alarme de carburant", value: 30 },
    { type: "Alarme ACC ON", value: 31 },
    { type: "Alarme ACC OFF", value: 32 },
    { type: "Alarme de collision", value: 33 },
  ],
  alarmTypeText: "Type d'alarme",
  alarmNotification: "Notification",
  pointType: [
    "Type de point",
    "Emplacement du satellite",
    "Emplacement de la boussole",
    "Emplacement LBS",
    "Emplacement WIFI",
  ],

  cardType: [
    "Type",
    "Nouvelle carte",
    "Nouvelle carte à vie",
    "Nouvelle carte",
    "Carte à vie",
  ],
  // 东南西北
  directarray: ["Est", "Sud", "Ouest", "Nord"],
  // 方向字段
  directionarray: [
    "Nord géographique",
    "Nord-est",
    "Est géographique",
    "Sud-est",
    "Sud géographique",
    "Sud-ouest",
    "Ouest géographique",
    "Nord-ouest",
  ],
  // 定位方式
  pointedarray: [
    "Indéfini",
    "GPS",
    "LAC",
    "Localisation de LAC",
    "Emplacement WIFI",
  ],

  //map Relevant
  ruler: "Règle",
  distance: "Circulation",
  baidumap: "Baidu Map",
  map: "Carte",
  satellite: "Satellite",
  ThreeDimensional: "3D",
  baidusatellite: "Satellite Baidu",
  googlemap: "Google Map",
  googlesatellite: "Satellite Google",
  fullscreen: "Plein écran",
  noBaidumapStreetView: "Emplacement actuel sur Baidu Maps sans Street View",
  noGooglemapStreetView: "Emplacement actuel sur Google Maps sans Street View",
  exitStreetView: "Quitter street view",
  draw: "Dessiner",
  finish: "Finir",
  unknown: "Inconnu",
  realTimeTailAfter: "Suivi en temps réel",
  trackReply: "Lecture de L'historique",
  afterRefresh: "Rafraîchir",
  rightClickEnd: "Clique droit sur FiN, rayon：",
  rightClickEndGoogle: "clic droit fin ------------------------ rayon ：",

  //tree Relevant
  currentUserMachineCount: "Nombre actuel de machines utilisateur",
  childUserMachineCount: "Nombre de machines utilisateur enfant",

  //Window relevant

  electronicFence: "Géo-clôture",
  drawTrack: "Définir Géo-clôture",
  showOrHide: "Afficher / Masquer",
  showDeviceName: "Afficher le nom de l'appareil",
  circleCustom: "Défini par l'utilisateur",
  circle200m: "Cercle 200m",
  polygonCustom: "Définir le polygone",
  drawPolygon: "Tire un polygon",
  drawCircle: "Tire un cercle",
  radiusMin100:
    "La limite de la clôture de la charka est d’au moins 20 mètres.Radius de la clôture actuelle:",
  showAllFences: "Montrez tous les fences",
  lookEF: "Vérifiez la géo-clôture",
  noEF: "Aucune donnée de géo-clôture détectée",
  hideEF: "Masquer la géo-clôture",
  blockUpEF: "Fermer la clôture",
  deleteEF: "Supprimer la clôture",
  isStartUsing: "Si pour activer",
  startUsing: "Activer",
  stopUsing: "Désactiver",
  nowEFrange: "Intervale de clôture actuelle",
  enableSucess: "Activer avec succès",
  unableSucess: "Désactivé avec succès",
  sureDeleteMorgage: "Supprimer le point hypothécaire",
  enterMorgageName: "Entrez le nom du point d'hypothèque",
  openMorgagelongStayAlarm: "Start Point hypothécaire",
  openMorgageinOutAlarm: "Commencer l'entrée et sortir du point d'hypothèque",
  setEFSuccess: "Géo-clôture defini avec succès",
  setElectronicFence: "Definir clôture électronique",
  drawFence: "Dessiner une clôture",
  drawMorgagePoint: "Dessiner un point hypothécaire",
  customFence: "Personnalisation de la clôture",
  enterFenceTips: "Entrer dans l'alarme",
  leaveFenceTips: "Laisser l'alarme",
  inputFenceName: "veuillez saisir le nom de la clôture",
  relation: "Lots",
  relationDevice: "Appareil associé",
  unRelation: "Non associé",
  hadRelation: "Déjà associé",
  quickRelation: "Ensemble en un clic",
  cancelRelation: "Annuler le lot",
  relationSuccess: "Bundle réussi",
  cancelRelationSuccess: "Annulation reussi",
  relationFail: "Echec Annulation",
  deviceList: "Liste des périphériques",
  isDeleteFence: "Si supprimer la clôture",
  choseRelationDeviceFirst:
    "Veuillez d’abord choisir le dispositif pour être associé!",
  choseCancelRelationDeviceFirst:
    "S’il vous plaît, choisissez d’abord le dispositif pour être inassocié!",
  selectOneTips: "Veuillez choisir au moins une méthode d’alarme",
  radius: "Rayon",
  //设置二押点页面
  setMortgagePoint: "Définir le point hypothéque",

  circleMortage: "Point hypothécaire Cercle",
  polygonMorgage: "Point hypothécaire polygone",
  morgageSet: "Point hypothécaire déjà défini",
  operatePrompt: "Operer Rapidement",
  startDrawing: "Cliquez pour commencer à dessiner",
  drawingtip1:
    "Clic gauche pour commencer à dessiner, double-cliquer pour terminer le dessin",
  drawingtip2: "Clic gauche et glisser pour commencer à dessiner",

  /************************************************/
  endTrace: "Fin de tracement",
  travelMileage: "Kilométrage du voyage",
  /************************************************/
  myAccount: "Compte",
  serviceProvide: "Fournisseur de services",
  completeInfo:
    "Completer les Informations, telles que contacts, numéro de téléphone",
  clientName: "Nom du client",
  loginAccount: "Compte",
  linkMan: "contacts",
  linkPhone: "Tel/Mob",
  clientNameEmpty: "Nom du client vide!",
  updateSuccess: "Mise a Jour avec Succès!",
  /************************************************/
  oldPsw: "Ancien mot de passe",
  newPsw: "Nouveau mot de passe",
  confirmPsw: "Confirmez le mot de passe",
  pswNoSame: "Mot de passe non identique",
  pswUpdateSuccess: "Mot de passe mise à jour avec succès!",
  email: "Email",
  oldPwdWarn: "Veuillez entrer l'ancien mot de passe",
  newPwdWarn: "Veuillez entrer un nouveau mot de passe",
  pwdConfirmWarn: "Veuillez confirmer le nouveau mot de passe",
  /************************************************/
  //Custom popup components
  resetPswFailure: "La réinitialisation du mot de passe a échoué",
  notification: "Notification",
  isResetPsw_a: "Etes-vous sûr de réinitialiser le",
  isResetPsw_b: "mot de passe?",
  pwsResetSuccess_a: "Déjà réinitialiser le",
  pwsResetSuccess_b: "mot de passe pour 123456",
  /************************************************/
  machineSearch: "Info de l'appareil",
  search: "Chercher",
  clientRelation: "Relation avec le client",
  machineDetail: "Les détails",
  machineDetail2: "Les détails",
  machineCtrl: "Commander",
  transfer: "Deplacer",
  belongCustom: "Appartient au client",
  addImeiFirst: "Ajoutez d'abord IMEI!",
  addUserFirst: "Ajouter le client d'abord!",
  transferSuccess: "Déplacement reussi!",
  multiAdd: "Ajout par lots",
  multiImport: "Importer",
  multiRenew: "Renouvellement de lot",
    //批量修改设备begin
  editDevice:"Modifier le modèle de l'appareil",
  deviceAfter: "Modèle d'appareil (après modification)",
  editDeviceTips:"Veuillez confirmer que l'appareil à modifier est le même modèle et n'est pas actif!",
  pleaseChoseDevice: "Veuillez d'abord sélectionner l'appareil à modifier!",
  editResult:"Modifier le résultat",
  successCount:"Appareil modifié avec succès:",
  failCount:"Appareils défectueux:",
  //批量修改设备end
  multiDelete: "Suppression par lots",
  canNotAddImei: "L'IMEI n'existe pas,il ne peut pas etre ajouter!",
  importTime: "Temps d'importation",
  loginName: "Compte",
  platformDue: "Plate-forme Dû",
  machinePhone: "Carte SIM",
  userDue: "Utilisateur dû",
  overSpeedAlarm: "Alarme de survitesse",
  changeIcon: "Icône",
  dealerNote: "Remarque",
  noUserDue: "Aucun utilisateur dû",
  phoneLengththan3: "La longueur du téléphone doit être supérieure à 3",
  serialNumberInput: "Entrée du numéro de série",

  /************************************************/
  sending: "Envoi en cours ..... veuillez patienter ...",
  sendFailure: "Send failure",
  ctrlName: "Prénom",
  interval: "Intervalle de temps",
  intervalError: "Interval format error",
  currectInterval: "Veuillez saisir le bon intervalle de temps !",
  intervalLimit: "Plage d'intervalle de temps 10-720, (minute)",
  intervalLimit2: "Plage d'intervalle de temps 10-5400, (seconde)",
  intervalLimit3: "Plage d'intervalle de temps 5-1440, (minute)",
  intervalLimit4: "Plage d'intervalle de temps 3-999, (seconde)",
  intervalLimit5: "Plage d'intervalle de temps 10-10800, (seconde)",
  intervalLimit1:
    "Plage d'intervalle de temps 1-999, (minute).",
  intervalLimit6: "Plage d'intervalle de temps 1-65535,(minute)",
  intervalLimit7: "Plage d'intervalle de temps 1-999999,(seconde)",
  intervalLimit8: "Plage d'intervalle de temps 0-255, 0 signifie fermer",
  intervalLimit9: "Plage d'intervalle de temps 3-10800, (seconde)",
  intervalLimit10: "Plage d'intervalle de temps 3-86400, (seconde)",
  intervalLimit11: "Plage d'intervalle de temps 180-86400, (seconde)",
  intervalLimit22: "Plage d'intervalle de temps 60-86400, (seconde)",
  intervalLimit23: "Plage d'intervalle de temps 5-60, (seconde)",
  intervalLimit24: "Plage d'intervalle de temps 10-86400, (seconde)",
  intervalLimit25: "Plage d'intervalle de temps 5-43200, (minute)",
  intervalLimit12: "Plage d'intervalle de temps 10-60, (seconde)",
  intervalLimit13:
    "Définir un intervalle de temps compris entre 1-24 (signifie 1-24 heures) ou 101-107 (signifie 1-7 jours)",
  intervalLimit14:
    "Définir la plage de temps d'intervalle 10-3600, unité(seconde); par défaut: 10 s",
  intervalLimit15:
    "Définir la plage de temps d'intervalle 180-86400, unité(seconde); par défaut: 3600s",
  intervalLimit16:
    "Définir la plage de temps d'intervalle 1-72, unité(heure); par défaut: 24 heures",
  intervalLimit17: "Plage de température de réglage -127-127, unité (° C)",
  intervalLimit18:
    "Définir la plage de temps d'intervalle 5-18000, unité (seconde)",
  intervalLimit19:
    "Définir la plage de temps d'intervalle 10-300, unité (seconde)",
  intervalLimit20:
    "Définir la plage de temps d'intervalle 5-399, unité (secondes)",
  intervalLimit21:
    "Définir la plage de temps d'intervalle 5-300, unité (secondes)",
  noInterval: "S'il vous plaît entrer le temps d'intervalle!",
  intervalTips: "Turn off tracking mode by setting the alarm wake-up time",
  phoneMonitorTips:
    "Une fois la commande envoyée, le périphérique composera activement le numéro de rappel à surveiller",
  time1: "Temps1",
  time2: "Temps2",
  time3: "Temps3",
  time4: "Temps4",
  time5: "Temps5",
  intervalNum: "Intervalle (minutes)",
  sun: "Dim.",
  mon: "Lun.",
  tue: "Mar.",
  wed: "Mer.",
  thu: "Jeu.",
  fri: "Ven.",
  sat: "Sam.",
  awakenTime: "Temps Éveiller",
  centerPhone: "Téléphone central",
  inputCenterPhone: "Saisir le téléphone du centre !",
  phone1: "Numéro 1",
  phone2: "Numéro 2",
  phone3: "Numéro 3",
  phone4: "Numéro 4",
  phone5: "Numéro 5",
  inputPhone: "Saisir le téléphone",
  offlineCtrl:
    "Ctrl hors ligne ， Les instructions hors ligne seront automatiquement envoyées à l'appareil une fois l'appareil en ligne.",
  terNotSupport: "Terminal non supporté",
  terReplyFail: "La réponse du terminal a échoué",
  machineInfo: "Info appareil",

  /************************************************/
  alarmTypeScreen: "Montrer type d'alarme",
  allRead: "Lire tout",
  read: "Lire",
  noAlarmInfo: "Aucune information d'alarme effaçable",
  alarmTip:
    "Conseil: Filtrez ce type d’informations d’alarme en les décochant.",

  /************************************************/
  updatePsw: "Mot de passe",
  resetPsw: "Réinitialiser le mot de passe",

  /************************************************/
  multiSell: "Vente par lots",
  sell: "Soumettre",
  sellSuccess: "Vente reussie !",
  modifySuccess: "Modifier le succès",
  modifyFail: "Modifier l'échec",
  multiTransfer: "Déplacement par lots",
  multiUserExpires: "Les utilisateurs lot modification expire", // en
  batchModifying: "Modification du lot",
  userTransfer: "Transfert",
  machineRemark: "Remarques",
  sendCtrl: "Envoyer la commande",
  ctrl: "Commande",
  ctrlLog: "Fiche d'instruction",
  ctrlLogTips: "Historique des commande",
  s06Ctrls: [
    "Arrêter le moteur",
    "Restaurer le moteur",
    "requête de Localisation",
  ],
  ctrlType: "Nom de la commande",
  resInfo: "Résultat",
  resTime: "Temps de réponse",
  ctrlSendTime: "Heure d'envoi",
  // csv文件导入上传
  choseCsv: "Sélectionnez le fichier CSV",
  choseFile: "Sélectionnez le dossier",
  submit: "Les ventes",
  targeDevice: "Le dispositif cible",
  csvTips_1: "1.Enregistrez le fichier excel en format CSV",
  csvTips_2: "2.Importez le fichier CSV dans le système",
  importExplain: "Instructions d'importation:",
  fileDemo: "Format fichier exemple",
  // add
  sendType: "Type",
  onlineCtrl: "Commande en ligne",
  offCtrl: "Commande hors ligne",
  resStatus: [
    "Non envoyé",
    "invalide",
    "a été publié",
    "Exécuter avec succès",
    "l'exécution a échoué",
    "pas de réponse",
  ],
  /************************************************/
  addSubordinateClient: "Ajouter un sous-compte",
  noSubordinateClient: "Pas de sous-compte",
  superiorCustomerEmpty: "Choisissez le supérieur",
  noCustomerName: "Nom du client",
  noLoginAccount: "Utilisateur",
  noPsw: "Mot de passe",
  noConfirmPsw: "Confirmez le mot de passe",
  pswNotAtypism: "Mot de passe pas correcte",
  addSuccess: "Ajout reussi",
  superiorCustomer: "Supérieur",
  addVerticalImei: "Ajouter un IMEI vertical",
  noImei: "Pas d'IMEI",
  addImei_curr: "Saisir le nombre d'IMEI",
  no: "Unité",
  aRowAImei: "Un IMEI pour une ligne",
  max12hour: "Ne peut pas être plus de 12 heures",

  /*
   * dealer  Beginning of interface translation
   *
   * */
  //main.js
  imeiOrUserEmpty: "IMEI ou utilisateur vide!",
  accountEmpty: "IMEI / Nom / Compte requis",
  queryNoUser: "requête NoUser",
  queryNoIMEI: "query No IMEI",
  imeiOrClientOrAccount: "IMEI / Nom / Compte",
  dueSoon: "Expirer",
  recentlyOffline: "Hors ligne",
  choseSellDeviceFirst:
    "Veuillez sélectionner le matériel à vendre en premier!",
  choseDeviceFirst:
    "Veuillez sélectionner le périphérique à transférer en premier!",
  choseDeviceExpiresFirst: "Please select the device to be modified first!", //en
  choseRenewDeviceFirst: "Please select the device to renew first!",
  choseDeleteDeviceFirst: "Sélectionnez d'abord le périphérique à supprimer!",
  choseClientFirst: "Veuillez sélectionner le client à déplacer en premier!",

  //myClient.js
  clientList: "Les clients",
  accountInfo: "Information sur le compte",
  machineCount: "matériel",
  stock: "Total",
  inventory: "Stock",
  subordinateClient: "Sous-compte",
  datum: "Information",
  monitor: "Moniteur",
  dueMachineInfo: "Expiration",
  haveExpired: "Expiré",
  timeRange: [
    "dans les 7 jours",
    "dans les 30 jours",
    "dans les 60 jours",
    "dans les 7-30days",
    "dans les 30-60days",
  ],
  offlineMachineInfo: "informations sur les périphériques hors ligne",
  timeRange1: [
    "dans l'heure",
    "dans la journée",
    "dans les 7 jours",
    "dans un mois",
    "dans les 60 jours",
    "Plus de 60 jours",
    "Entre 1 heure à 7 jours",
    "Entre 1 jour et 7 jours",
    "Entre 7 et 30 jours",
    "Entre 30 et 60 jours",
  ],
  offlineTime: "Intervalle",
  includeSubordinateClient: "Sous-compte",

  stopMachineInfo: "Information statique",
  stopTime1: "Temps statique",
  unUseMachineInfo: "Informations non activé",
  unUseMachineCount: "Montant inactif",

  sellTime: "Temps de vente",
  detail: "Détail",
  manageDevice: "Détaillé",
  details: "Détails",
  deleteSuccess: "Supprimer le succès!",
  deleteFail: "Supprimer l'échec!",
  renewalLink: "Lien de renouvellement",
  deleteGroupTips: "S'il faut supprimer un groupe",
  addGroup: "Ajouter un groupe",
  jurisdictionRange: "Modifier les fonctions",
  machineSellTransfer: "Machine à vendre",
  monitorMachineGroup: "Moniteur",
  jurisdictionArr: [
    "Gérer le client",
    "Gestionnaire de message",
    "Réglage GEO",
    "Gérer les Alarmes",
    "Gérer le compte virtuel",
    "Instruction d'expédition",
  ],
  confrimDelSim: "Confirmez la suppression de la carte SIM:",

  // 右键菜单
  sellDevice: "Vendre un appareil",
  addClient: "Ajouter un client",
  deleteClient: "Supprimer l'utilisateur",
  resetPassword: "Réinitialiser le mot de passe",
  transferClient: "Déplacer l'utilisateur",
  ifDeleteClient: "Effacer",

  //myAccount
  myWorkPlace: "Mon compte",
  availablePoints: "Balance",
  yearCard: "Carte annuelle",
  lifetimeOfCard: "Carte à vie",
  oneyear: "Annuelle",
  lifetime: "Toute la vie",
  commonImportPoint: "Nouvelle carte",
  lifetimeImportPoint: "Nouvelle carte à vie",
  myServiceProvide: "Fournisseur",
  moreOperator: "Plus d'opération",
  dueMachine: "Périphérique expiré",
  offlineMachine: "Appareil hors ligne",
  quickSell: "Vente rapide",
  sellTo: "Cible",
  machineBelong: "Appartenir à",
  reset: "Réinitialiser",
  targetCustomer: "Cibler un client",
  common_lifetimeImport: "Carte annuelle(0),Carte à vie(0)",
  cardType1: "Types",
  credit: "Quantité",
  generateImportPoint: "Créer une nouvelle carte",
  generateImportPointSuc: "Importation carte reussie!",
  generateImportPointFail: "Échec d'importation  de la carte",
  year_lifeTimeCard: "New Card(0),Nouvelle carte à vie(0)",
  generateRenewPoint: "Génération de points",
  transferTo: "Déplacer",
  transferPoint: "Quantité",
  transferRenewPoint: "Frais de renouvellement",
  pointHistoryRecord: "Historique de la carte",
  newGeneration: "Nouveau",
  operatorType: "Opération",
  consume: "Consommer",
  give: "Donner",
  income: "Revenu",
  pay: "Dépense",
  imeiErr: "Saisie des 6 derniers chiffres de IMEI!",
  accountFirstPage: "Accueil",

  /*
   * dealer  End of interface translation
   *
   * */
  // 1.4.8 risk control
  finrisk: "Contrôle des risques financiers",
  attention: "Souscrire",
  cancelattention: "Supprimer abonnement",
  poweroff: "Éteindre",
  inout: "Dedans et dehors",
  inoutEF: "Dans et hors de la clôture",
  longstay: "Séjour hypothécaire",
  secsetting: "Réglage du point hypothécaire",
  EFsetting: "Réglage de la clôture géographique",
  polygonFence: "Clôture polygonale",
  cycleFence: "Clôture de cycle",
  haveBeenSetFence: "clôture ont été mis",
  haveBeenSetPoint: "Ont été mis point hypothécaire",
  drawingFailed: "Le dessin a échoué. S'il vous plaît redessiner!",
  inoutdot: "Dedans et dehors",
  eleStatistics: "Statistiques d'électricité",
  noData: "Pas de données",
  // 进出二押点表格
  accountbe: "Compte",
  SMtype: "Type de point hypothécaire",
  SMname: "Nom du point hypothécaire",
  time: "Temps",
  position: "Position",
  lastele: "Batterie restante",
  statisticTime: "Date de la statistique",
  searchalarmType: [
    "Tous",
    "hors ligne",
    "éteindre",
    "Dedans et dehors de la clôture",
    "Dedans et dehors",
    "Séjour hypothécaire",
  ],
  remarks: [
    "Point hypothécaire",
    "Garantie de la Société ",
    "Point de démontage",
    "Marché d'occasion",
  ],
  focusOnly: "se concentrer seulement",

  autoRecord: "Enregistrement automatique",
  /******************************************************set command start**********************************8*/
  setCtrl: {
    text: "Définir la commande",
    value: "",
  },
  moreCtrl: {
    text: "Plus d'instructions",
    value: ''
  },
  sc_openTraceModel: {
    text: "Définir le modèle de la piste",
    value: "0",
  },
  sc_closeTraceModel: {
    text: "Fermer le modèle de la piste",
    value: "1",
  },
  sc_setSleepTime: {
    text: "Définir le temps de sommeil",
    value: "2",
  },
  sc_setAwakenTime: {
    text: "Réglez l'heure de réveil",
    value: "3",
  },
  sc_setDismantleAlarm: {
    text: "Définir l'alarme de démantèlement",
    value: "4",
  },
  sc_setSMSC: {
    text: "Numéro du centre",
    value: "5",
  },
  sc_delSMSC: {
    text: "Supprimer SMSC",
    value: "6",
  },
  sc_setSOS: {
    text: "Ajouter SOS",
    value: "7",
  },
  sc_delSOS: {
    text: "Supprimer SOS",
    value: "8",
  },
  sc_restartTheInstruction: {
    text: "Réinitialiser",
    value: "9",
  },
  sc_uploadTime: {
    text: "Définir l'intervalle de téléchargement",
    value: "10",
  },
  /*Alarm clock time setting
     Timing reback time setting
     Dismantle alarm setting
     Week mode open close*/
  sc_setAlarmClock: {
    text: "Régler le réveil",
    value: "11",
  },
  sc_setTimingRebackTime: {
    text: "Régler le temps de retour",
    value: "12",
  },
  sc_openWeekMode: {
    text: "Mode de debut de semaine",
    value: "13",
  },
  sc_closeWeekMode: {
    text: "Mode de fin de semaine",
    value: "14",
  },

  sc_powerSaverMode: {
    text: "Intervalle de téléchargement",
    value: "15",
  },
  sc_carCatchingMode: {
    text: "Mode de suivi",
    value: "16",
  },
  sc_closeDismantlingAlarm: {
    text: "Fermer l'alarme de démantèlement",
    value: "17",
  },
  sc_openDismantlingAlarm: {
    text: "Ouvrir l'alarme de démontage",
    value: "18",
  },
  sc_VibrationAlarm: {
    text: "régler l'alarme de vibration",
    value: "19",
  },
  sc_timeZone: {
    text: "Réglage du fuseau horaire",
    value: "20",
  },
  sc_phoneMonitor: {
    text: "écoute téléphonique",
    value: "21",
  },
  sc_stopCarSetting: {
    text: "Paramètres de stationnement",
    value: "22",
  },
  sc_bindAlarmNumber: {
    text: "Relier le numéro d'alarme",
    value: "23",
  },
  sc_bindPowerAlarm: {
    text: "Alarme de coupure de courant",
    value: "24",
  },
  sc_fatigueDrivingSetting: {
    text: "Configuration de conduite en fatigue",
    value: "25",
  },
  sc_peripheralSetting: {
    text: "paramètres périphériques",
    value: "26",
  },
  sc_SMSAlarmSetting: {
    text: "Définir l'alerte SMS",
    value: "27",
  },
  sc_autoRecordSetting: {
    text: "Paramètres d'enregistrement automatique",
    value: "28",
  },
  sc_monitorCallback: {
    text: "écouter le rappel",
    value: "29",
  },
  sc_recordCtrl: {
    text: "instructions d'enregistrement",
    value: "30",
  },
  sc_unbindAlarmNumber: {
    text: "Numéro d'alarme de reliure",
    value: "31",
  },
  sc_alarmSensitivitySetting: {
    text: "Réglage de la sensibilité de l'alarme de vibration",
    value: "32",
  },
  sc_alarmSMSsettings: {
    text: "Paramètres SMS de l'alarme de vibration",
    value: "33",
  },
  sc_alarmCallSettings: {
    text: "Paramètres d'appel alarme vibration",
    value: "34",
  },
  sc_openFailureAlarmSetting: {
    text: "Désactiver l'alarme de panne d'alimentation",
    value: "35",
  },
  sc_restoreFactory: {
    text: "Restaurer l'usine",
    value: "36",
  },
  sc_openVibrationAlarm: {
    text: "Activer l'alarme de vibration",
    value: "37",
  },
  sc_closeVibrationAlarm: {
    text: "Désactiver l'alarme de vibration",
    value: "38",
  },
  sc_closeFailureAlarmSetting: {
    text: "éteindre l'alarme de coupure de courant",
    value: "39",
  },
  sc_feulAlarm: {
    text: "Réglage de l'alarme de carburant",
    value: "40",
  },
  //1.6.72
  sc_PowerSavingMode: {
    text: "Mode économie d'énergie",
    value: "41",
  },
  sc_sleepMode: {
    text: "Mode veille",
    value: "42",
  },
  sc_alarmMode: {
    text: "Mode d'alarme",
    value: "43",
  },
  sc_weekMode: {
    text: "Mode semaine",
    value: "44",
  },
  sc_monitorNumberSetting: {
    text: "Réglage du numéro de moniteur",
    value: "45",
  },
  sc_singlePositionSetting: {
    text: "Mode de positionnement unique",
    value: "46",
  },
  sc_timingworkSetting: {
    text: "Mode de travail",
    value: "47",
  },
  sc_openLightAlarm: {
    text: "Alarme de capteur de lumière ouverte",
    value: "48",
  },
  sc_closeLightAlarm: {
    text: "Alarme de capteur de lumière fermer",
    value: "49",
  },
  sc_workModeSetting: {
    text: "Réglage du mode de travail",
    value: "50",
  },
  sc_timingOnAndOffMachine: {
    text: "Réglage du commutateur de synchronisation",
    value: "51",
  },
  sc_setRealTimeTrackMode: {
    text: "Définir le mode de poursuite en temps réel",
    value: "52",
  },
  sc_setClockMode: {
    text: "Définir le mode d'alarme",
    value: "53",
  },
  sc_openTemperatureAlarm: {
    text: "Activer l'alarme de température",
    value: "54",
  },
  sc_closeTemperatureAlarm: {
    text: "Désactiver l'alarme de température",
    value: "55",
  },
  sc_timingPostbackSetting: {
    text: "Paramètres de synchronisation de la publication",
    value: "56",
  },
  sc_remoteBoot: {
    text: "Démarrage à distance",
    value: "57",
  },
  sc_smartTrack: {
    text: "Suivi intelligent",
    value: "58",
  },
  sc_cancelSmartTrack: {
    text: "Annuler le suivi intelligent",
    value: "59",
  },
  sc_cancelAlarm: {
    text: "Annuler l'alarme",
    value: "60",
  },
  sc_smartPowerSavingMode: {
    text: "Définir le mode d'économie d'énergie intelligent",
    value: "61",
  },
  sc_monitorSetting: {
    text: "Surveiller",
    value: '62'
  },
   // 指令重构新增翻译
   sc_timedReturnMode: {
    text: 'Mode retour temporisé',
    value: '100'
  },
  sc_operatingMode: {
      text: 'Mode de fonctionnement',
      value: '101'
  },
  sc_realTimeMode : {
      text: 'Mode de positionnement en temps réel',
      value: '102'
  },
  sc_alarmMode : {
      text: 'Mode d"alarme',
      value: '103'
  },
  sc_weekMode : {
      text: 'Mode semaine',
      value: '104'
  },
  sc_antidemolitionAlarm : {
      text: 'Alarme anti-démolition',
      value: '105'
  },
  sc_vibrationAlarm : {
      text: 'Alarme de vibration',
      value: '106'
  },
  sc_monitoringNumber : {
      text: 'Gestion des numéros de surveillance',
      value: '107'
  },
  sc_queryMonitoring : {
      text: 'Numéro de contrôle de la requête',
      value: '108'
  },
  sc_electricityControl : {
      text: 'Contrôle de l"huile et de l"électricité',
      value: '109'
  },
  sc_SOSnumber : {
      text: 'Gestion des numéros SOS',
      value: '110'
  },
  sc_SleepCommand : {
    text: 'Commande de sommeil',
    value: '201'
  },
  sc_RadiusCommand : {
      text: 'Rayon de déplacement',
      value: '202'
  },
    sc_punchTimeMode:{
        text:'打卡模式',
        value:'203'  
    },
    sc_intervelMode:{
        text:'时间段模式',
        value:'204'  
    },
    sc_activeGPS:{
        text:'激活GPS',
        value:'205'  
    },
    sc_lowPowerAlert: {
        text: 'Rappel de batterie faible',
        value: '206'
    },
    sc_SOSAlert: {
        text: 'SOS报警',
        value: '207'
    },
  mc_cuscom : {
      text: 'Instruction personnalisée',
      value: '1'
  },
  NormalTrack: 'Mode de suivi normal',
  listeningToNumber:'Voulez-vous vraiment vérifier le numéro de surveillance de ?',
  versionNumber:'Voulez-vous vraiment vérifier le numéro de version de?',
  longitudeAndLatitudeInformation:'Voulez-vous vraiment vérifier les informations de latitude et de longitude de?',
  equipmentStatus:'Voulez-vous vraiment vérifier l‘état de?',
  public_parameter:'Voulez-vous vraiment vérifier les paramètres？',
  GPRS_parameter:'Voulez-vous vraiment vérifier les paramètres GPRS？',
  deviceName: 'Voulez-vous vraiment rouler le périphérique？',
  SMS_alert:'Voulez-vous vraiment vérifier l"alarme de rappel SMS ?',
  theBindingNumber:'Voulez-vous vraiment vérifier le numéro de liaison de ?',
  intervalTimeRange:'La plage de temps d"intervalle de réglage est 001-999, unité (minute)',
  pleaseChoose:'Veuillez choisir',
  RealTimeCarChase: 'Voulez-vous vraiment régler cet appareil en mode poursuite en voiture en temps réel?',
  inputPhoneNumber : "Veuillez entrer le numéro de téléphone",
  inputCorPhoneNumber : "Veuillez saisir le bon numéro de téléphone",
  autoCallPhone : "Astuce : Une fois la commande exécutée avec succès, le terminal composera automatiquement le numéro défini",
  limitTheNumberOfCellPhoneNumbers1:'Cette commande prend en charge jusqu"à 5 numéros de téléphone portable',
  limitTheNumberOfCellPhoneNumbers2:'Cette commande prend en charge jusqu"à 3 numéros de téléphone portable',
  equipmentTorestart:'Voulez-vous vraiment redémarrer cet appareil',
  remindTheWay:'Manière de rappeler',
  alarmWakeUpTime:'Heure de réveil del"alarme',
  alarmWakeUpTime1:'Heure de réveil del"alarme 1',
  alarmWakeUpTime2:'Heure de réveil del"alarme 2',
  alarmWakeUpTime3:'Heure de réveil del"alarme 3',
  alarmWakeUpTime4:'Heure de réveil del"alarme 4',
  sensitivityLevel:'Veuillez sélectionner le niveau de sensibilité',
  parking_time:'Temps de stationnement',
  selectWorkingMode:'Veuillez sélectionner le mode de travail',
  Alarm_value:'Valeur d"alarme',
  Buffer_value:'Valeur du tampon',
  gqg_disconnect:'déconnecter',
  gqg_turnOn:'Allumez',
  Return_interval:'Intervalle de retour',
  gq_startTime:'Heure de début',
  gq_restingTime:'Temps de repos',
  gq_Eastern:'Fuseau horaire de l"Est',
  gq_Western:'Fuseau horaire occidental',

  gq_driver:'Alarme de conduite de fatigue',
  gq_deviceName:'Voulez-vous vraiment rouler cet appareil?',
  gq_noteAlarm:'Voulez-vous vraiment vérifier l"alarme de rappel SMS?',
  gq_restoreOriginal:'Voulez-vous vraiment restaurer cet équipement dans son usine d"origine?',
  gq_normalMode:'Mode normal',
  gq_IntelligentsleepMode:'Mode veille intelligent',
  gq_DeepsleepMode:'Mode sommeil profond',
  gq_RemotebootMode:'Mode de démarrage à distance',
  gq_IntelligentsleepModeTips:'Voulez-vous vraiment passer en mode veille intelligente',
  gq_DeepsleepModeTips:'Voulez-vous vraiment passer en mode veille prolongée',
  gq_RemotebootModeTips:'Voulez-vous vraiment passer en mode de démarrage à distance?',
  gq_normalModeTips:'Êtes-vous sûr de vouloir passer en mode normal',
  gq_sleepModeTips:'Voulez-vous vraiment mettre cet appareil en mode veille?',
  gq_Locatethereturnmode:'Mode retour de positionnement',
  gq_regularWorkingHours:'Période de travail chronométrée',
  gq_AlarmType:{
      text: "Type d'alarme",
      value: '111'
  },
  IssuedbyThePrompt:"La commande a été émise, veuillez attendre que l'appareil réponde",
  platformToinform:'Notification de plate-forme',
  gq_shortNote:'Notification par SMS', 
  /************指令白话文**********************/
  closeDismantlingAlarm: "Fermer l'alarme de démantèlement",
  openDismantlingAlarm: "Ouvrir l'alarme de démontage ",
  closeTimingRebackMode: "Fermer le mode de rappel de minutage",
  minute: "mintues",
  timingrebackModeSetting: "Intervalle de téléchargement:",
  setWakeupTime: "Définir l'heure de réveil:",
  weekModeSetting: "Réglage du mode semaine:",
  closeWeekMode: "Fermer le mode semaine",
  setRealtimeTrackMode: "Définir le mode de suivi en temps réel",
  fortification: "Fortification",
  disarming: "Désarmant",
  settimingrebackmodeinterval:
    "Définissez l’intervalle du mode de récupération de la minuterie:",
  oilCutCommand: "Commande de coupe de carburant",
  restoreOilCommand: "Restaurer la commande de carburant",
  turnNnTheVehiclesPower: "Allumer le véhicule",
  turnOffTehVehiclesPower: "Éteindre le véhicule",
  implementBrakes: "Mettre en place des freins",
  dissolveBrakes: "Dissoudre les freins",
  openVoiceMonitorSlarm: "Alarme vocale ouverte",
  closeVoiceMonitorAlarm: "Fermer l'alarme du moniteur vocal",
  openCarSearchingMode: "Mode de recherche de voiture ouverte",
  closeCarSearchingMode: "fermer le mode de recherche de voiture",
  unrecognizedCommand: "Commande non reconnue",
  commandSendSuccess: "Félicitations! Le dispositif de commande à exécuter!",

  /********************************************设置指令结束**************************************************/

  /********************************************查询指令开始**************************************************/
  queryCtrl: {
    text: "Commande de requête",
    value: "",
  },
  /*参数设置查询*/
  qc_softwareVersion: {
    text: "Requête du version du logiciel",
    value: "1",
  },
  qc_latlngInfo: {
    text: "Requête Latitude et longitude",
    value: "2",
  },
  qc_locationHref: {
    text: "Requête paramètre de configuration",
    value: "3",
  },
  qc_status: {
    text: "Requête du statut",
    value: "4",
  },
  qc_gprs_param: {
    text: "Requête GPRS param",
    value: "5",
  },
  qc_name_param: {
    text: " Nom",
    value: "6",
  },
  qc_SMSReminderAlarm_param: {
    text: "Alarme de rappel de requête SMS",
    value: "7",
  },
  qc_bindNumber_param: {
    text: "Numéro de liaison de la requête",
    value: "8",
  },

  /********************************************查询指令结束**************************************************/

  /*******************************************控制指令开始***************************************************/

  controlCtrl: {
    text: "Commande de contrôle",
    value: "",
  },
  cc_offOilElectric: {
    text: "Hors huile électrique",
    value: "1",
  },
  cc_recoveryOilElectricity: {
    text: "Récupération de l'électricité du carburant",
    value: "2",
  },
  cc_factorySettings: {
    text: "Réglage d'usine",
    value: "4",
  },
  cc_fortify: {
    text: "Fortifier",
    value: "75",
  },
  cc_disarming: {
    text: "Désarmant",
    value: "76",
  },
  cc_brokenOil: {
    text: "Instruction de coupe d'huile",
    value: "7",
  },
  cc_RecoveryOil: {
    text: "Circuit d'huile de récupération",
    value: "8",
  },

  /*******************************************控制指令结束***************************************************/

  /*
   * m--》min
   * 2018-01-23
   * */
  km: "KM",
  mileage: "Kilométrage",
  importMachine: "Ajouter un nouveau IMEI",
  transferImportPoint: "Déplacer une nouvelle carte",
  machineType1: "Modèle",
  confirmIMEI:
    "Veuillez vous assurer de la validité du numéro IMEI et du nom du modèle avant d'importer, car l'opération est irrévocable.",
  renew: "Renouveler",
  deductPointNum: "Quantité",
  renewSuccess: "Renouvelement  Reussi!",
  wireType: [
    {
      text: "câblé",
      value: false,
    },
    {
      text: "sans fil",
      value: true,
    },
  ],
  vibrationWays: [
    {
      text: "Plate-forme",
      value: 0,
    },
    {
      text: "Platform+Message",
      value: 1,
    },
    {
      text: "Plateforme + Message + Téléphone",
      value: 2,
    },
  ],
  addMachineType: [
    {
      text: "S06",
      value: "3",
    },
    // SO6子级-----start-----------
    {
      text: "GT06",
      value: "8",
    },
    {
      text: "S08V",
      value: "9",
    },
    {
      text: "S01",
      value: "10",
    },
    {
      text: "S01T",
      value: "11",
    },
    {
      text: "S116",
      value: "12",
    },
    {
      text: "S119",
      value: "13",
    },
    {
      text: "TR06",
      value: "14",
    },
    {
      text: "GT06N",
      value: "15",
    },
    {
      text: "S101",
      value: "16",
    },
    {
      text: "S101T",
      value: "17",
    },
    {
      text: "S06U",
      value: "18",
    },
    {
      text: "S112U",
      value: "19",
    },
    {
      text: "S112B",
      value: "20",
    },
    // SO6子级-----end-----------
    {
      text: "S15/S02F",
      value: "1",
    },
    {
      text: "S05",
      value: "2",
    },
    {
      text: "SW06",
      value: "4",
    },
    {
      text: "S001",
      value: "5",
    },
    {
      text: "S08",
      value: "6",
    },
    {
      text: "S09",
      value: "7",
    },
  ],

  /*
   * 2018-02-02
   * */
  maploadfail:
    "Sorry,Load current map failed,Do you want to switch to another map?",

  /*
    2018-03-06新增 控制指令
    * */
  cc_openPower: {
    text: "Ouvrir le contact du véhicule",
    value: "7",
  },
  cc_closePower: {
    text: "Fermer le contact du véhicule",
    value: "8",
  },
  cc_openBrake: {
    text: "Ouvrir frein",
    value: "9",
  },
  cc_closeBrake: {
    text: "Fermer Frein",
    value: "10",
  },
  cc_openAlmrmvoice: {
    text: "Ouvrir alarme",
    value: "11",
  },
  cc_closeAlmrmvoice: {
    text: "Fermer alarme",
    value: "12",
  },
  /*2018-03-06新增 控制指令
   * */
  cc_openFindCar: {
    text: "Ouvrir la recherche de vehicule",
    value: "13",
  },
  cc_closeFindCar: {
    text: "Fermer la recherche de vehicule",
    value: "14",
  },
  /*2018-03-19
   * */
  EF: "GeoFence",

  /*
    2018-03-29，扩展字段
    * */
  exData: ["Puissance", "Tension", "Le carburant", "température", "la résistance"],

  /*
    2018-04-10
    * */
  notSta: "LBS non inclus dans les statistiques",

  // 油量统计
  fuelSetting: "Le carburant fixant",
  mianFuelTank: "Le réservoir de carburant principal",
  auxiliaryTank: "Réservoir de carburant secondaire",
  maximum: "Max",
  minimum: "Min",
  FullTankFuel: "Le plein de carburant",
  fuelMinValue: 'Le volume de carburant ne peut pas être inférieur à 10L',
  standardSetting: "L'établissement de normes",
  emptyBoxMax: "Max de vide ",
  fullBoxMax: "Max of Full",
  fuelStatistics: "Statistiques de carburant",
  settingSuccess: "Successed",
  settingFail: "Pas",
  pleaseInput: "Veuillez entrer",
  fuelTimes: "Des moments de carburant",
  fuelTotal: "Le carburant total",
  refuelingTime: 'Temps de ravitaillement',
  fuelDate: "Date de carburant",
  fuel: "Le carburant",
  fuelChange: "Le changement de combustible",
  feulTable: "Tableau d'analyse du carburant",
  addFullFilter: "S'il vous plaît ajouter le filtre complet",
  enterIntNum: "S'il vous plaît entrer un entier positif",
  // 温度统计
  tempSta: "Statistiques de température",
  tempTable: "Table d'analyse de la température",
  industrySta: "Statistiques de l'industrie",
  temperature: "température",
  temperature1: "température1",
  temperature2: "température2",
  temperature3: "température3",
  tempRange: "Plage de température",
  tempSetting:'Réglage de la température',
  tempSensor:'Capteur de température',
  tempAlert:'Une fois le capteur de température éteint, il ne recevra pas de données de température!',
  phoneNumber: "Numéro de mobile",
  sosAlarm: "alarme SOS",
  undervoltageAlarm: "alarme de sous-tension",
  surtensionAlarm: "alarme de surtension",
  overvoltageAlarm: 'Alarme de surtension',
  OilChangeAlarm: "Alarme de changement d'huile",
  accDetection: "détection ACC",
  PositiveAndNegativeDetection: "Détection positive et négative",
  alermValue: "Valeur d'alarme",
  bufferValue: "Valeur tampon",
  timeZoneDifference: "Différence de fuseau horaire",
  meridianEast: "Meridian East",
  meridianWest: "Meridian West",
  max12hour: "La différence d'entrée ne peut être supérieure à 12 heures",
  trackDownload: "Piste de téléchargement",
  download: "Télécharger",
  multiReset: "Réinitialiser en vrac",
  resetSuccess: "Réinitialiser le succès",
  multiResetTips: "Conseils de réinitialisation multiple",
  point: "Point",
  myplace: "Ma place",
  addPoint: "Ajouter un point",
  error10018: "l'équilibre n'est pas suffisant",
  error110:"L'objet n'existe pas",
  error109:"Limite maximale dépassée",
  error20013:"Le type d'appareil n'existe pas",
  error90001:"Le numéro de série du type d'appareil ne peut pas être vide",
  error20003:"Imei ne peut pas être vide",
  inputName: "Nom d'entrée pls",
  virtualAccount: "Compte virtuel",
  createTime: "Créer du temps",
  permission: "Autorisation",
  permissionRange: "Plage de permission",
  canChange: "Les fonctions peuvent être changées",
  fotbidPassword: "Mot de passe",
  virtualAccountTipsText:
    "Lors de la création d'un compte virtuel, il s'agit d'un compte alias du compte revendeur actuellement enregistré, qui peut définir des autorisations pour le compte virtuel.",
  noOperationPermission: "Aucune autorisation",
  number: "Nombre",
  rangeSetting: "Réglage de la plage",
  setting: "Réglage",
  // 1.6.1
  duration: "Durée",
  voltageSta: "Statistiques de tension",
  voltageAnalysis: "Analyse de la tension",
  voltageEchart: "Tableau d’analyse de tension",
  platformAlarm: "Alerte de la plate-forme",
  platformAlarm1: "téléphone",
  platformAndPhone: 'téléphone+plate-forme d’alarme',
  smsAndplatformAlarm: "SMS+ plate-forme d’alarme",
  smsAndplatformAlarm1: "SMS",
  smsAndplatformAlarmandPhone: "Plate-forme alarme +SMS+ téléphone",
  smsAndplatformAlarmandPhone1: "SMS+ téléphone",
  more_speed: "Km",
  attribute: "Attribut",
  profession: "Professionnel",
  locationPoint: "Point d'ancrage",
  openPlatform: "API ouverte",
  experience: "Démo",
  onlyViewMonitor: "Voir seulement Moniteur",
  inputAccountOrUserName:
    "Veuillez entrer un numéro de compte ou un nom d'utilisateur",
  noDeviceTips:
    "Vous n'avez pas trouvé d'informations pertinentes sur l'équipement, vérifiez les clients",
  noUserTips:
    "Vous n'avez pas trouvé les informations utilisateur pertinentes, vérifiez l'équipement",
  clickHere: "Cliquez ici",
  pointIntervalSelect: "Track point interval",
  payment: "Paiement",
  pleaceClick: "Cliquez",
  paymentSaveTips:
    "Conseil de sécurité: Veuillez confirmer auprès du prestataire de service la validité du lien.",
  fuelAlarmValue: "Valeur d'alarme de quantité d'huile",
  fuelConsumption: "Consommation de carburant",
  client: "Client",
  create: "Créer",
  importPoint: "Nouvelle carte",
  general: "Commune",
  lifelong: "Carte à vie",
  renewalCard: "Carte de renouvellement",
  settingFuelFirst:
    "Réglez d'abord la valeur d'alarme de quantité d'huile avant d'envoyer la commande!",
  overSpeedSetting: "Réglage de la vitesse",
  kmPerHour: "km/h",
  times: "Fois",
  total: "Total",
  primary: "Seigneur",
  minor: "Adjoint",
  unActiveTips: "L'appareil n'est pas activé et ne peut pas être utilisé.",
  arrearsTips:
    "L'appareil est en retard et ne peut pas utiliser cette fonctionnalité",
  loading: "le chargement",
  expirationReminder: "Expiration rappel",
  projectName: "Nom du projet",
  expireDate: "Date d'expiration",
  changePwdTips:
    "Votre mot de passe est trop simple, il y a un risque de sécurité.",
  pwdCheckTips1: "Les suggestions sont 6-20 lettres, chiffres ou symboles",
  pwdCheckTips2: "Votre mot de passe est trop faible.",
  pwdCheckTips3: "Votre mot de passe peut être plus compliqué",
  pwdCheckTips4: "Votre mot de passe est sécurisé",
  pwdLevel1: "Faible",
  pwdLevel2: "Moyen",
  pwdLevel3: "Fort",
  comfirmChangePwd: "Confirmer le changement pwd",
  notSetYet: "Pas encore",
  liter: "Litre",
  arrearageDayTips: "Jours dus",
  todayExpire: "Due aujourd'hui",
  forgotPwd: "Mot de passe oublié?",
  forgotPwdTips:
    "Veuillez contacter le vendeur pour changer votre mot de passe",
  //1.6.7
  commonProblem: "Centre d'aide",
  instructions: "Instructions",
  webInstructions: "Instructions WEB",
  appInstructions: "Instructions APP",
  acceptAlarmNtification: "Notification d'alarme",
  alarmPeriod: "Période d'alarme",
  whiteDay: "Journée",
  blackNight: "Nuit",
  allDay: "Toute la journée",
  alarmEmail: "Courrier d'alarme",
  muchEmailTips:
    "Plusieurs boîtes aux lettres peuvent être entrées, séparées par le symbole ';'.",
  newsCenter: "Centre de notification",
  allNews: "Tout",
  unReadNews: "Non lu",
  readNews: "Lis",
  allTypeNews: "Types de notification",
  alarmInformation: "Informations d'alarme",
  titleContent: "Titre",
  markRead: "Marqué lu",
  allRead: "Tout lire",
  allDelete: "Supprimer tout",
  selectFirst: "Sélectionnez d'abord!",
  updateFail: "Mise à jour a échoué!",
  ifAllReadTips: "Tout lire?",
  ifAllDeleteTips: "Supprimer tout?",
  stationInfo: "Informations sur la gare",
  phone: "Téléphone",
  //1.6.72
  plsSelectTime: "S'il vous plaît choisir le temps!",
  customerNotFound: "Impossible de trouver le client",
  Postalcode: "Emplacement",
  accWarning: "Alarme ACC",
  canInputMultiPhone:
    "Vous pouvez entrer plusieurs numéros de téléphone, utilisez-les séparément.",
  noLocationInfo:
    "Le dispositif ne dispose pas d 'informations de localisation.",
  //1.6.9
  fenceName: "Nom de clôture",
  fenceManage: "Gestion de clôture",
  circular: "cercle",
  polygon: "Polygone",
  allFence: "Toutes les clôtures",
  shape: "Forme",
  stationNews: "Message du site",
  phonePlaceholder: 'Vous pouvez entrer plusieurs numéros, séparés par ","',
  addressPlaceholder:
    "S'il vous plaît entrez l'adresse et le code postal dans l'ordre, séparés par ','",
  isUnbind: "Voulez-vous dissocier",
  alarmCar: "véhicule d'alarme",
  alarmAddress: "Emplacement de l'alarme",
  chooseAtLeastOneTime: "Sélectionnez au moins une fois",
  alarmMessage: "Il n'y a pas de détails sur cette information d'alarme'",
  navigatorBack: "Retour au supérieur",
  timeOverMessage:
    "L'heure d'expiration de l'utilisateur ne peut pas être supérieure à l'heure d'expiration de la plateforme",
  //1.7.0
  userTypeStr: "Type d'utilisateur",
  newAdd: "Nouveau",
  findAll: "Total",
  findStr: "Données correspondantes",
  customColumn: "Personnaliser",
  updatePswErr: "Echec du mot de passe mis à jour",
  professionalUser: "Utilisateur professionnel ou pas?",
  confirmStr: "Confirmer",
  inputTargetCustomer: "Client cible en entrée",
  superiorUser: "Utilisateur supérieur",
  speedReport: "Rapport de vitesse",
  createAccount: "Créer un compte",
  push: "Pousser",
  searchCreateStr:
    "Il ouvrira un compte et transférera l'appareil sur ce compte.",
  allowIMEI: "Autoriser la connexion IMEI",
  defaultPswTip:
    "Le mot de passe par défaut est les 6 derniers chiffres de l'IMEI",
  createAccountTip: "Compte créé et appareil transféré avec succès",
  showAll: "Montrer tout",
  bingmap: "Bing Map",
  areaZoom: "Zoom",
  areaZoomReduction: "Restaurer le zoom",
  reduction: "Réduction",
  saveImg: "Enregistrer en tant qu'image",
  fleetFence: "Fleet Fence",
  alarmToSub: "Alarm notification",
  bikeFence: "Clôture de vélo",
  delGroupTip: "Supprimer l 'échec",
  isExporting: "Exporter...",
  addressResolution: "Analyse d 'adresse...",
  simNOTip: "Le numéro de carte SIM ne peut être qu'un numéro",
  unArrowServiceTip:
    "Le délai d'expiration de l'utilisateur pour les périphériques suivants est supérieur au délai d'expiration de la plate-forme, veuillez sélectionner à nouveau. Le numéro d'appareil est:",
  platformAlarmandPhone: "Plateforme Alarme + Tel",
  openLightAlarm: "Alarme de capteur de lumière ouverte",
  closeLightAlarm: "Alarme de capteur de lumière fermer",
  ACCAlarm: "Alarme ACC",
  translateError:
    "Le transfert a échoué, l'utilisateur cible n'a pas d'autorisation",
  distanceTip: "Cliquez sur OK, double-cliquez pour terminer",
  workMode: "Mode de fonctionnement",
  workModeType:
    "0: mode normal; 1 mode veille intelligent; 2 mode de sommeil profond",
  clickToStreetMap: "Carte de la rue ouverte",
  current: "Courant",
  remarkTip:
    "Remarque: Ne vendez pas de cartes de différents types de packages en même temps",
  searchRes: "Résultat de la recherche",
  updateIcon: "Changer d'icône",
  youHaveALarmInfo: "Vous avez un message d'alerte",
  moveInterval: "Intervalle d'exercice",
  staticInterval: "Intervalle de temps d'inactivité",
  notSupportTraffic: "Coming soon.",
  ignite: "Acc ON",
  flameout: "Acc OFF",
  generateRenewalPointSuc: "Générer des points de renouvellement avec succès",
  noGPSsignal: "Non positionné",
  imeiErr2: "Veuillez saisir au moins les 6 derniers chiffres du numéro imei",
  searchCreateStr2:
    "Cela va créer un compte et transférer cet appareil vers le nom du compte",
  addUser: "Ajouter un utilisateur",
  alarmTemperature: "Valeur de température d'alarme",
  highTemperatureAlarm: "Alarme de température élevée",
  lowTemperatureAlarm: "Alarme de basse température",
  temperatureTip: "Veuillez saisir une valeur de température！",
  locMode: "mode de positionnement",
  imeiInput: "Veuillez saisir le numéro IMEI",
  noResult: "Aucun résultat correspondant",
  noAddressKey: "Temporairement incapable d'obtenir des informations d'adresse",
  deviceGroup: "Gérer le groupe",
  shareManage: "Gestion des partages ",
  lastPosition: "Dernière position",
  defaultGroup: "Groupe par défaut",
  tankShape: "Forme du réservoir de carburant",
  standard: "la norme",
  oval: "ovale",
  irregular: "Irrégulier",
  //1.8.4
  inputAddressOrLoc: "Veuillez saisir l'adresse / la latitude et la longitude",
  inputGroupName: "Nom du groupe d'entrée",
  lock: "fermer à clé",
  shareHistory: "Partager la lecture de l'historique",
  tomorrow: "demain",
  threeDay: "3 jours",
  shareSuccess: "Partager le succès du lien",
  effective: "efficace",
  lapse: "invalide",
  copyShareLink: "Succès du lien de copie",
  openStr: "SUR",
  closeStr: 'DE',
  linkError: "Erreur de liaison",
  inputUserName: "Entrez le nom d'utilisateur",
  barCodeStatistics: "statistique de code à barres",
  barCode: "code à barre",
  sweepCodeTime: "Temps de balayage",
  workModeType2:
    "1 Mode veille intelligent ； 2 Mode veille profonde ； 3 Mode de commutation à distance",
  remoteSwitchMode: "Mode de commutation à distance",
  saleTime: "Date de vente",
  onlineTime: "Temps en ligne",
  dayMileage: "Kilométrage aujourd'hui",
  imeiNum: "Numéro IMEI",
  overSpeedValue: "Valeur de survitesse",
  shareNoOpen: "Le lien de partage n'est pas activé",
  addTo2: "Vente à",
  overDue: "Expiré",
  openInterface: "Interface ouverte",
  privacyPolicy: "Politique de confidentialité",
  serviceTerm: "Conditions d'utilisation",
  importError:
    "Échec de l'ajout, le numéro de périphérique (IMEI) doit être un nombre à 15 chiffres",
  importResult: "Importer les résultats",
  totalNum: "total",
  successInfo: "succès",
  errorInfo: "Échec",
  repeatImei: "IMEI repeat",
  includeAccount: "sous-compte",
  formatError: "Malformé",
  importErrorInfo: "Veuillez saisir un numéro IMEI à 15 chiffres",
  totalMileage: "Total Mileage",
  totalOverSpeed: "Survitesse totale (fois)",
  totalStop: "Arrêt total (fois)",
  totalOil: "Total oil",
  timeChoose: "Time selection",
  intervalTime: "Interval time",
  default: "Default",
  idleSpeedStatics: "Statistiques de vitesse de ralenti",
  offlineStatistics: 'Statistiques hors ligne',
  idleSpeed: "Ralenti",
  idleSpeedTimeTip1: "Le temps d'inactivité ne peut pas être vide",
  idleSpeedTimeTip2: "Le temps d'inactivité doit être un entier positif",
  averageSpeed: "Vitesse moyenne",
  averageOil: "Consommation moyenne de carburant",
  oilImgTitle: "Tableau d'analyse d'huile",
  oilChangeDetail: "Détails de changement de carburant",
  machineNameError:
    "Le nom du périphérique ne peut pas contenir de symboles spéciaux (/ ')",
  remarqueError: "Les informations de remarque ne peuvent pas dépasser 50 mots",
  defineColumnTip: "Vérifier jusqu'à 12 éléments",
  pswCheckTip:
    "La suggestion est une combinaison de 6 à 20 chiffres, lettres et symboles",
  chooseGroup: "Veuillez sélectionner un groupe",
  chooseAgain:
    "Vous ne pouvez interroger que les données des six derniers mois, veuillez sélectionner à nouveau！",
  noDataTip: "Aucune donnée！",
  noMachineNameError: "Veuillez sélectionner un périphérique！",
  loginAccountError:
    "Le compte de connexion ne peut pas comporter 15 chiffres！",
  includeExpire: "Qui expire",
  groupNameTip: "Le nom du groupe ne peut pas être vide！",
  outageTips: "Êtes-vous sûr que l'huile a été coupée?",
  powerSupplyTips: "Êtes-vous sûr de restaurer l'huile?",
  centerPhoneTips: "Veuillez saisir le numéro",
  centerPhoneLenTips: "Veuillez saisir 8 à 20 chiffres",
  passworldillegal: "Il y a des caractères non autorisés",
  // 2.0.0 POI，权限版本
  singleAdd:'ajout unique',
  batchImport:'importation par lots',
  name:'nom',
  icon:'icône',
  defaultGroup:'groupe par défaut',
  remark:'Remarque',
  uploadFile:'télécharger le fichier',
  exampleDownload:'Exemple de téléchargement',
  uploadFiles:'télécharger le fichier',
  poiTips1:"Vous pouvez importer des POI en téléchargeant un fichier Excel avec des informations connexes. Veuillez suivre le format de l'exemple pour préparer le fichier",
  poiTips2:'Nom: obligatoire, pas plus de 32 caractères',
  poiTips3:'Icon: obligatoire, entrez 1,2,3,4',
  poiTips4:'Latitude: obligatoire',
  poiTips5:'Longitude: obligatoire',
  poiTips6:"Nom du groupe: facultatif, pas plus de 32 caractères. Si le nom du groupe n'est pas renseigné, le point POI appartient au groupe par défaut. Si le nom du groupe rempli est cohérent avec le nom du groupe créé, le point POI appartient au groupe créé. Le nom du groupe n'a pas été créé, le système ajoutera le groupe",
  poiTips7:'Remarques: facultatif, pas plus de 50 caractères',
  // 权限相关
  roleLimit: 'Autorisations de rôle',
  operateLog: 'Journal des opérations',
  sysAccountManage: "Compte d'autorité",
  rolen: 'Rôles',
  rolename: 'Nom de rôle',
  addRole: 'Nouveau rôle',
  editRole: 'Modifier le rôle',
  deleteRole: 'Supprimer le rôle',
  delRoleTip: 'Voulez-vous vraiment supprimer ce rôle?',
  delAccountTip: 'Voulez-vous vraiment supprimer ce compte?',
  limitconfig: 'Profil des droits',
  newAccountTip1: "Le compte d'autorité est similaire à l'ancien compte virtuel et est le sous-compte de l'administrateur. Les administrateurs peuvent créer des comptes privilégiés et attribuer différents rôles aux comptes privilégiés afin que différents comptes puissent voir différents contenus et opérations sur la plateforme.",
  newAccountTip2: "Processus de création d'un compte d'autorisation:",
  newAccountTip31: '1. Sur la page de gestion des rôles,',
  newAccountTip32: 'Nouveau rôle',
  newAccountTip33: ', Et configurez les autorisations pour le rôle;',
  newAccountTip4: "2. Sur la page de gestion du compte d'autorité, créez un nouveau compte d'autorité et attribuez des rôles au compte.",
  newRoleTip1: "Les administrateurs peuvent créer des rôles et configurer différentes autorisations de fonctionnement pour différents rôles afin de répondre aux besoins de l'entreprise dans différents scénarios.",
  newRoleTip2: "Par exemple, configurez si un rôle financier a l'autorisation de localiser et de surveiller, s'il a l'autorisation d'ajouter des clients, s'il a l'autorisation de modifier les informations de l'appareil, etc.",
  "refuelrate": "Taux de ravitaillement",
  "refuellimit": "Lorsque l'augmentation d'huile par minute est supérieure à xxxxL et inférieure à xxxxL, elle est considérée comme un ravitaillement.",
  "refueltip": "Le taux de ravitaillement maximum ne doit pas être inférieur au taux de ravitaillement minimum!",
  viewLimitConf: "Afficher les paramètres d'autorisation",
  viewLimit: 'Afficher les autorisations',
  newSysAcc: 'Nouveau compte système',
  editSysAcc: "Modifier le compte d'autorisation",
  virtualAcc: 'Compte virtuel',
  oriVirtualAcc: "Compte virtuel d'origine",
  virtualTip: 'Le module de compte virtuel a été mis à niveau vers un module de compte système, veuillez créer un nouveau compte système',
  operaTime: 'Temps de fonctionnement',
  ipaddr: 'adresse IP',
  businessType: "Type d'entreprise",
  params: 'Paramètre de demande',
  operateType: "Type d'opération",
  uAcc: "compte d'utilisateur",
  uName: "Nom d'utilisateur",
  uType: "type d'utilisateur",
  logDetail: 'Détails du journal',
  delAccount: 'Supprimer le compte',
  modifyTime: "Modifier l'heure",
  unbindlimit: 'Impossible de créer une clôture électronique en un clic sans les autorisations de périphérique associées!',
  setSmsTip: "Si vous configurez la notification par SMS, vous devez d'abord activer la notification de plate-forme; si la livraison de la notification de plate-forme réussit, la notification par SMS échoue, vous devez réactiver la notification par SMS.",
  cusSetComTip: "Clause de non-responsabilité: le risque apporté par les instructions personnalisées n'a rien à voir avec la plate-forme",
  cusSetComPas: 'Veuillez saisir le mot de passe du compte de connexion actuel',
  cusSetComDes1: 'Les instructions personnalisées ne prennent en charge que les instructions en ligne.',
  cusSetComDes2: "Si le périphérique ne répond pas dans les deux minutes suivant l'envoi de la commande, le processus est interrompu et l'état de la commande est considéré comme aucune réponse.",
  cueSetComoffline: "L'appareil ne répond pas et l'envoi de la commande personnalisée a échoué!",
  fbType: 'Type de commentaire',
  fbType1: 'Consultatif',
  fbType2: 'Mauvais fonctionnement',
  fbType3: 'expérience utilisateur',
  fbType4: 'Suggestions de nouvelles fonctionnalités',
  fbType5: 'autre',
  upload: 'Télécharger',
  uploadImg: 'télécharger une image',
  uploadType: 'Veuillez télécharger des fichiers de type .jpg .png .jpeg .gif',
  uploadSize: 'Le fichier de téléchargement ne peut pas dépasser 3 M',
  fbManager: 'Gestion des commentaires',
  blManager: 'Gestion des annonces',
  fbUploadTip: 'Veuillez sélectionner le type de commentaire',
  menuPlatform: "Actualités de la plateforme",
  menuFeedback: "Retour d'information",
  menuBulletin: "Annonce de la plateforme",
  // 新增驾驶行为
  BdfhrwetASDFFEGGREGRDAF: "Comportement de conduite",
  BtyjdfghtwsrgGHFEEGRDAF: "Accélération rapide",
  BtyuwyfgrWERERRTHDAsdDF: "décélération rapide",
  Be2562h253grgsHHJDbRDAF: "virage brusque",
  celTemperature:'Température Celsius'
};
// 权限tree
lg.limits = {
  "ACC_statistics": "Statistiques ACC",
  "Account_Home": "Accueil",
  "Add": "Nouveau",
  "Add_POI": "Ajouter un POI",
  "Add_customer": "Ajouter un client",
  "Add_device_group": "Ajouter un groupe d'appareils",
  "Add_fence": "Ajouter une clôture",
  "Add_sharing_track": "Ajouter une piste de partage",
  "Add_system_account": "Nouveau compte d'autorisation",
  "Alarm_details": "Détails de l'alarme",
  "Alarm_message": "Message d'alarme",
  "Alarm_overview": "Vue d'ensemble de l'alarme",
  "Alarm_statistics": "Rapport d'alarme",
  "All_news": "Tout",
  "Associated_equipment": "Appareil associé",
  "Available_points": "Balance",
  "Barcode_statistics": "statistique de code à barres",
  "Batch_Import": "importation par lots",
  "Batch_renewal": "Renouvellement de lot",
  "Batch_reset": "Réinitialiser en vrac",
  "Bulk_sales": "Vente par lots",
  "Call_the_police": "Alarme",
  "Customer_details": "Détails du client",
  "Customer_transfer": "Déplacer l'utilisateur",
  "Delete_POI": "Supprimer le POI",
  "Delete_account": "Supprimer le compte",
  "Delete_customer": "Supprimer l'utilisateur",
  "Delete_device": "Supprimer l'appareil",
  "Delete_device_group": "Supprimer le groupe d'appareils",
  "Delete_fence": "Supprimer la clôture",
  "Delete_role": "Supprimer le rôle",
  "Device_List": "Liste des périphériques",
  "Device_grouping": "Gérer le groupe",
  "Device_transfer": "Transfert d'appareil",
  "Due_reminder": "Expiration rappel",
  "Edit_details": "Modifier les détails",
  "Equipment_management": "Dispositifs",
  "My_clinet": "Dispositifs",
  "Fence": "GeoFence",
  "Fence_management": "Gestion de clôture",
  "Generate": "produire",
  "Generate_lead-in_points": "Créer une nouvelle carte",
  "Generate_renewal_points": "Génération de points",
  "Have_read": "Vider",
  "Idle_speed_statistics": "Statistiques de vitesse de ralenti",
  "Import": "Importer",
  "Import_Device": "Ajouter un nouveau IMEI",
  "Industry_Statistics": "Statistiques de l'industrie",
  "Location_monitoring": "Surveillance de l'emplacement",
  "Log_management": "Gestion des journaux",
  "Mark_read": "Marqué lu",
  "Menu_management": "Gestion des menus",
  "Message_Center": "Centre de notification",
  "Mileage_statistics": "Rapport de kilométrage",
  "Modify_POI": "Modifier le POI",
  "Modify_device_details": "Modifier les détails de l'appareil",
  "Modify_device_group": "Modifier le groupe d'appareils",
  "Modify_role": "Modifier le rôle",
  "Modify_sharing_track": "Modifier la piste de partage",
  "Modify_user_expiration": "Les utilisateurs lot modification expire",
  "More": "Plus",
  "My_client": "Mon client",
  "New_role": "Nouveau rôle",
  "New_users": "Ajouter un utilisateur",
  "Oil_statistics": "Statistiques de carburant",
  "POI_management": "Gestion des POI",
  "Points_record": "Historique de la carte",
  "Push": "Pousser",
  "Quick_sale": "Vente rapide",
  "Renew": "Renouveler",
  "Replay": "La lecture",
  "Role_management": "Gestion des rôles",
  "Run_overview": "Vue d'ensemble de déplacement",
  "Running_statistics": "Vue d'ensemble de déplacement",
  "Sales_equipment": "Vendre un appareil",
  "Set_expiration_reminder": "Définir un rappel d'expiration",
  "Share_track": "Partager la lecture de l'historique",
  "Sharing_management": "Gestion des partages",
  "Speeding_detailed_list": "Détails de survitesse",
  "Statistical_report": "Rapport statistique",
  "Stay_detailed_list": "Détails de stationnement",
  "System_account_management": "Compte d'autorité",
  "Temperature_statistics": "Statistiques de température",
  "Transfer": "Transfert",
  "Transfer_group": "Groupe de transfert",
  "Transfer_point": "Déplacer une nouvelle carte",
  "Transfer_renewal_point": "Frais de renouvellement",
  "Trip_statistics": "Rapport de voyage",
  "Unlink": "Dissocier",
  "View": "Vue",
  "View_POI": "Afficher le POI",
  "View_device_group": "Afficher le groupe d'appareils",
  "View_fence": "Vérifiez la géo-clôture",
  "View_role": "Afficher le rôle",
  "View_sharing_track": "Afficher la piste de partage",
  "Virtual_account": "Compte virtuel",
  "Voltage_analysis": "Analyse de la tension",
  "Voltage_statistics": "Statistiques de tension",
  "batch_deletion": "Suppression par lots",
  "change_Password": "Mot de passe",
  "delete": "Effacer",
  "edit": "Editer",
  "instruction": "Commander",
  "modify": "Mettre a jour",
  "monitor": "Moniteur",
  "my_account": "Mon compte",
  "reset_Password": "Réinitialiser le mot de passe",
  "share_it": "Partager",
  "sub_user": "Sous-compte",
  "track": "Suivi",
  "Custom_Order": "Instruction personnalisée",
  "GeoKey_Manager": "Gestion GeoKey",
  "GeoKey_Update": "modifier",
  "GeoKey_Delete": "supprimer",
  "GeoKey_Add": "Ajouter à",
  "GeoKey_View": "Vue",
  "feedback_manager": "Gestion des commentaires",
  "feedback_list": "Vue",
  "feedback_handle": "Traitement des commentaires",
  "proclamat_manager": "Gestion des annonces",
  "proclamat_manager_list": "Afficher l'annonce",
  "proclamat_manager_update": "Annonce de modification",
  "proclamat_manager_delete": "Supprimer l'annonce",
  "proclamat_manager_save": "Nouvelle annonce",
  "device_update_batch_model": "Modifier le modèle de l'appareil par lots"
}

// 问题文档的内容
lg.questionDocumentArr = [
  [
    "Demander：Une fois le dispositif de cablage installé, le voyant est éteint et hors ligne.",
    "R: Après avoir éteint la voiture, utilisez le stylo électrique et le compteur universel pour déterminer si la tension de ligne de la voiture connectée est conforme à la plage de tension du tracker GPS est généralement comprise entre 9 et 36V.<br/>Précautions de cablage: le personnel chargé de l'installation et du cablage doit bien comprendre la ligne de la voiture et disposer de certaines capacités pratiques pour éviter que la voiture ne soit endommagée par un cablage incorrect.",
  ],
  [
    "Demander：Périphérique filaire ou périphérique de suivi sans fil en temps réel, appel téléphonique ou périphérique d'état d'initialisation en arrière-plan IoT hors connexion",
    "Répondre：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Envoyez un message texte pour le redémarrer, observez quelques minutes pour voir s'il est en ligne. En règle générale, envoyez le numéro de remise à zéro. Veuillez contacter le revendeur pour le déterminer.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. La connexion réseau est instable, déplacez le véhicule dans une zone de signal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Après avoir suivi les étapes ci-dessus, vous n'avez pas été en mesure de vous connecter. Vous devez contacter l'opérateur de téléphonie mobile pour vérifier si la carte est anormale.",
  ],
  [
    "Demander：Lot hors ligne au début et à la fin du mois",
    "R: Veuillez vérifier si la carte est en retard, si elle est en retard, rechargez-la à temps et reprenez l’utilisation.",
  ],
  [
    "Demander：La voiture roule, la position en ligne du GPS n’est pas mise à jour.",
    "Répondre：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Le dispositif de cablage peut envoyer SMS STATUS # pour vérifier l'état de réception du signal satellite, voir GPS: recherche satellite si le signal satellite a été recherché, cette situation nécessite de vérifier l'emplacement d'installation, s'il est installé conformément aux instructions. Face vers le haut, il n'y a pas de couverture métallique sur le dessus.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Envoyez SMS STATUS #, le statut de retour est GPS: OFF, veuillez renvoyer FACTORY #, après avoir re?u la réponse, observez 5 minutes pour voir s’il ya une mise à jour.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Selon les deux méthodes ci-dessus, le problème ne peut pas être éliminé. Veuillez contacter le vendeur pour le faire réparer.",
  ],
  [
    "Demander：Pourquoi la plate-forme de charge se charge-t-elle longtemps ou si l'écran n'est pas plein?",
    "R: L’affichage de l’alimentation de la plate-forme est basé sur les informations renvoyées par le périphérique afin de lui permettre d’analyser les données afin de déterminer sa puissance actuelle.<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Les données d'alimentation de l'appareil et les données de positionnement de l'appareil sont téléchargées ensemble Si la batterie n'a pas été changée depuis longtemps, veuillez: 1 Amenez votre appareil à une distance de 100 à 300 mètres pour mettre à jour les informations de localisation de l'appareil. Les données et les données de localisation peuvent être renvoyées ensemble vers la plate-forme pour constituer un rafra?chissement de l'affichage.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. En fonction du changement d'indicateur d'alimentation, déterminez si celui-ci est complètement chargé (prenons l'exemple de S15). Dans les 15 minutes qui suivent, le voyant d’alimentation devient jaune et vert au maximum, veuillez vous reporter au manuel pour les autres modèles.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, la charge pendant une longue période est également pleine d'électricité, cette situation peut être la tension de la prise de charge est inférieure à 1A, veuillez charger 5V, tête de charge 1A pendant 8-10 heures.",
  ],
  [
    "Demander: Soutenant la commande de puissance de coupure GPS émise avec succès, pourquoi la voiture n'est-elle toujours pas cassée?",
    "Réponse: Une fois la commande de mise hors tension émise avec succès, l'équipement doit être exécuté dans les conditions suivantes:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Assurez-vous que le cablage de l'équipement est correct et suivez le schéma de cablage du manuel.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2, l'équipement fonctionne normalement, est statique ou en état de conduite, dispose d'un positionnement, n'est pas hors ligne et la vitesse du véhicule ne dépasse pas 20 km;<br/>Si le véhicule est hors ligne, non positionné ou si la vitesse du véhicule dépasse 20 kilomètres à l'heure, le terminal ne s'exécutera pas, même si la commande de coupure de courant est émise avec succès.",
  ],
  [
    "Demander：Les trois premières années d'utilisation des produits sans fil sont installées, le périphérique d'affichage n'est pas positionné ou en ligne.",
    "Répondre：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Activez le commutateur pour vérifier si le voyant clignote.Par exemple, les indicateurs jaune et vert S18 clignotent en même temps que la normale et le voyant clignotant se trouve dans le signal de recherche.Le périphérique n'est pas allumé.(Le statut des différents modèles sera différent. Veuillez vous reporter au manuel pour les autres modèles)<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Le voyant ne clignote pas sur la ligne Si le signal est mal activé, placez le signal dans une bonne zone. La zone signal bonne n'est pas sur la ligne, vous pouvez éteindre pendant 1 minute, réinstaller la carte et ensuite lancer le test.",
  ],
  [
    "Demander：Le produit cable est installé pour la première fois et le périphérique d’affichage n’est pas positionné.",
    "Répondre：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Vérifiez si l'indicateur d'état GPS du terminal est normal. Vérifiez l'état de l'indicateur conformément aux instructions de différents modèles.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Si le voyant est éteint, l'appareil ne peut pas être alimenté normalement.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, le voyant de la carte (jaune vert) n'est pas allumé, éteignez et réinstallez la carte, puis allumez pour voir la lumière normale est normale.<br/>&nbsp;&nbsp;&nbsp;&nbsp;4. Déterminez si le numéro de carte SIM de l'appareil n'est pas en retard et si la fonction d'accès Internet GPRS est normale.<br/>&nbsp;&nbsp;&nbsp;&nbsp;5. Il n'y a pas de réseau GSM à l'endroit où se trouve l'équipement, tel que sous-sol, tunnel, etc., où le signal est faible, veuillez vous rendre à l'endroit où la couverture GPRS est bonne.<br/>&nbsp;&nbsp;&nbsp;&nbsp;6, la position du positionneur ne doit pas être trop fermée, ne pas avoir d'objets métalliques, autant que possible dans la position d'installation de la voiture. Sinon, cela affecte la réception du signal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;7, démarrage normal, arrêt dans la zone de signal bonne n'est pas en ligne, vous pouvez réémettre la commande de ligne pour vérifier si l'interface IP et le réseau de liaison de carte sont normaux.",
  ],
  [
    "Demander：Une fois le dispositif de cablage installé, le voyant est éteint et hors ligne.",
    "Solution:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Après avoir éteint la voiture, utilisez le stylo électrique et le compteur universel pour déterminer si la tension de ligne de la voiture connectée est conforme à la plage de tension du tracker GPS est généralement comprise entre 9 et 36V.<br/>&nbsp;&nbsp;&nbsp;&nbsp;Précautions de cablage:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Le personnel chargé de l’installation et du cablage doit bien comprendre la ligne de la voiture et avoir une certaine capacité pratique à éviter d’endommager votre voiture en raison d’un cablage incorrect.",
  ],
  [
    "Demander：Périphérique filaire ou périphérique de suivi sans fil en temps réel, appel téléphonique ou périphérique d'état d'initialisation en arrière-plan IoT hors connexion",
    "Répondre：<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Envoyez un message texte pour le redémarrer, observez quelques minutes pour voir s'il est en ligne. En règle générale, envoyez le numéro de remise à zéro. Veuillez contacter le revendeur pour le déterminer.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. La connexion réseau est instable, déplacez le véhicule dans une zone de signal.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Après avoir suivi les étapes ci-dessus, vous n'avez pas été en mesure de vous connecter. Vous devez contacter l'opérateur de téléphonie mobile pour vérifier si la carte est anormale.",
  ],
  [
    "Demander：Lot hors ligne au début et à la fin du mois",
    "Solution: Vérifiez si la carte est en retard, si elle est en retard, rechargez-la à temps et reprenez l’utilisation.",
  ],
  [
    "Demander：La voiture roule, la position en ligne du GPS n’est pas mise à jour.",
    "Solution:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Le dispositif de cablage peut envoyer SMS STATUS # pour vérifier l'état de réception du signal satellite, voir GPS: recherche satellite si le signal satellite a été recherché, cette situation nécessite de vérifier l'emplacement d'installation, s'il est installé conformément aux instructions. Face vers le haut, il n'y a pas de couverture métallique sur le dessus.<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. Envoyez SMS STATUS #, le statut de retour est GPS: OFF, veuillez renvoyer FACTORY #. Après avoir re?u la réponse, observez 5 minutes pour voir si la position est mise à jour.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3. Selon les deux méthodes ci-dessus, le problème ne peut pas être éliminé. Veuillez contacter le vendeur pour le faire réparer.",
  ],
  [
    "Demander：Pourquoi la plate-forme de charge se charge-t-elle longtemps ou si l'écran n'est pas plein?",
    "L’affichage de l’alimentation de la plate-forme est basé sur les informations renvoyées par l’appareil afin de permettre une analyse des données permettant de déterminer la puissance actuelle de l’appareil.<br/>Solution:<br/>&nbsp;&nbsp;&nbsp;&nbsp;1. Les données d'alimentation de l'appareil et les données de positionnement de l'appareil sont téléchargées ensemble Si la batterie n'a pas été changée depuis longtemps, veuillez: 1 Amenez votre appareil à une distance de 100 à 300 mètres pour mettre à jour les informations de localisation de l'appareil. Les données et les données de localisation peuvent être renvoyées ensemble vers la plate-forme pour constituer un rafra?chissement de l'affichage de puissance;<br/>&nbsp;&nbsp;&nbsp;&nbsp;2. En fonction du changement d'indicateur d'alimentation, déterminez si celui-ci est complètement chargé (prenons l'exemple de S15). Dans les 15 minutes qui suivent, le voyant d’alimentation devient jaune et vert au maximum, veuillez vous reporter au manuel pour les autres modèles.<br/>&nbsp;&nbsp;&nbsp;&nbsp;3, la charge pendant une longue période est également pleine d'électricité, cette situation peut être la tension de la prise de charge est inférieure à 1A, veuillez charger 5V, tête de charge 1A pendant 8-10 heures.",
  ],
];
lg.webOptDoc = "Arrive bientôt...";
lg.appOptDoc = "Arrive bientôt...";
// 查询参数的帮助
var html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push("<td>Requête du mot de passe du terminal</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TÉLÉPHONE:</td>');
html.push("<td>Interroger la carte SIM intégrée au terminal</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">UTILISATEUR:</td>');
html.push("<td>Numéro de téléphone du propriétaire de la requête</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">LA VITESSE:</td>');
html.push("<td>Interroger la valeur de la limite de vitesse</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push("<td>Interrogez la fréquence de suivi, l'unité est en secondes</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">PISTE:</td>');
html.push("<td>Demande si le suivi est activé.1: activé, 0: désactivé</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RAYON:</td>');
html.push(
  "<td>Interrogez la plage d'alarme de migration illégale, Unité: mètre</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push(
  "<td>Demander si l'alarme SMS est activée, 1: activer, 0: désactiver"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>La sensibilité de la requête aux vibrations est comprise entre 0 et 15,0 est la sensibilité la plus élevée, trop élevée peut être une fausse alarme, 15 est la sensibilité la plus faible</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>Demander si l'alarme d'appel est activée, 1: activer, 0: désactiver"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Demander si la dérive du filtre GPS est activée, 1: activer, 0: désactiver, si vous activez le dispositif antivol qui passe en mode stationnaire sans vibration dans les 5 minutes, et filtre toute la dérive GPS.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">EN VEILLE:</td>');
html.push(
  "<td>Demander si la fonction de veille est activée, 1: activer, 0: désactiver, si vous activez l'antivol qui passera en mode veille sans vibration dans les 30 minutes, il fermera la fonction GPS et économisera de l'énergie</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Demander si l'alarme d'arrêt est activée, 1: enbale, 0: désactivé</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">GPS:</td>');
html.push(
  "<td>Interrogez l’intensité du signal du satellite. Par exemple: 2300 1223 3431 12 séries de chiffres à 4 chiffres au total, 2300 signifie: L’intensité du signal du satellite numéro 23 est de 0,1223.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VBAT:</td>');
html.push(
  "<td>Interrogation de la tension de la batterie, tension du port de charge Courant de charge Par exemple: VBAT = 3713300: 4960750: 303500 Indique que la tension de la batterie est de 3713300uV 3.71v Appliquée à la tension de charge de la puce 4.96V, Courant de charge de 303mA</td>"
);
html.push("</tr>");
html.push("</table>");
lg.queryparamhelp = html.join("");

// 设置参数的帮助
html = [];
html.push('<table class="cmdTable" width="100%" border="0">');
html.push("<tr>");
html.push('<td class="cmdLabel" width="60">PSW:</td>');
html.push(
  "<td>Définir le mot de passe du terminal, qui ne comprend que 6 chiffres</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">TÉLÉPHONE:</td>');
html.push("<td>Définir le numéro SIM du terminal</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">UTILISATEUR:</td>');
html.push("<td>Définir le nombre de propriétaires de téléphones mobiles</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">LA VITESSE:</td>');
html.push("<td>Définir la valeur limite de vitesse, 0-300</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">FREQ:</td>');
html.push(
  "<td>Configurer la fréquence rapportée lors de l'activation du suivi, Unit: secend</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">Le Tracement:</td>');
html.push("<td>Configurer si ouvrir la piste, 1: ouvert, 0: fermer</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">RAYON:</td>');
html.push(
  "<td>Configurer la plage d'alarme de migration illégale, Unité: mètre</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIB:</td>');
html.push("<td>Set up whether SMS alarm is enabled,1:enable,0:disable");
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBL:</td>');
html.push(
  "<td>Réglez la sensibilité de vibration de 0 à 15,0 est la sensibilité la plus élevée, trop élevé peut être une fausse alarme, 15 est la sensibilité la plus basse</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBCALL:</td>');
html.push(
  "<td>Définir si l'alarme d'appel est activée, 1: activé, 0: désactivé"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">VIBGPS:</td>');
html.push(
  "<td>Définir si la dérive de filtre GPS est activée, 1: activer, 0: désactiver, si vous activez le dispositif antivol qui passe en mode stationnaire sans vibration dans les 5 minutes, et filtre toute la dérive GPS.</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">EN VEILLE:</td>');
html.push(
  "<td>Configurer si la fonction veille est activée, 1: activer, 0: désactiver, si vous activez le dispositif antivol qui passera en mode veille sans vibration dans les 30 minutes, il fermera la fonction GPS et économisera de l'énergie</td>"
);
html.push("</tr>");
html.push("<tr>");
html.push('<td class="cmdLabel">POF:</td>');
html.push(
  "<td>Définir si l'alarme d'extinction est activée, 1: enbale, 0: désactivé</td>"
);
html.push("</tr>");
html.push("</table>");
lg.setparamhelp = html.join("");

//批量添加
html = [];
html.push(
  '<div id="bulkAdds_treeDiv" class="easyui-panel treePulldownBox" ' +
    'style="z-index: 999;position:absolute;left:194px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkAdds_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Vente à:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkAdds_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkAdds_treeDiv" +
    "," +
    "bulkAdds_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkAdds_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="ba_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Platform Due:</td>'
);
html.push("<td>");
html.push(
  '<input  id="ba_platformTime" class="easyui-validatebox textbox"  style="width:250px;height:28px">'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Model:</td>'
);
html.push("<td>");
html.push(
  '<span class="select_box">' +
    '<span class="select_txt"></span>' +
    '<a id="select_open_icon" class="icon-arrow-down-6"><img src="../../images/map/down.png"></a>' +
    '<div class="option" style="">' +
    '<div class="searchDeviceBox">' +
    '<input type="text" value="" oninput="selectDeviceCpt.search(this)" class="searchDeviceInput">' +
    '<span class="clearIcon" onclick="selectDeviceCpt.clear()"><img src="../../images/map/close.png"></span>' +
    "</div>" +
    '<div id="deviceList"></div>' +
    "</div>" +
    "</span>"
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Ajouter un appareil:</td>'
);
html.push("<td>");
html.push(
  '<img  id="ba_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push(
  '<a  id="ba_submit" style="margin-left:70px;margin-top:10px;width:120px;" href="javascript:void(0)" ></a>'
);
lg.bulkAdds = html.join("");

//批量续费
html = [];
html.push(
  '<div id="bulkRenew_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:138px;top:90px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkRenew_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Ajouter un appareil:</td>'
);
html.push("<td>");
html.push(
  '<img  id="re_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="re_addNumBox">Courant：<span id="account_re_addNum">0</span>'
);
html.push("</span>");
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_re_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<tr>");
html.push(
  '<td style="text-align:right;"><span style="color:red">*</span>Type</td>'
);
html.push("<td>");
html.push('<input  type="radio" name="red_cardType"');
html.push(
  "class='easyui-validatebox'  value='3' checked><label>Annuel</label></input>"
);
html.push(
  '<input  type="radio" name="red_cardType" style="margin-left:15px;" '
);
html.push(
  'class="easyui-validatebox" value="4"><label>Toute la vie</label></input>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push('<td style="text-align: right">Points de déduction</td>');
html.push("<td>");
html.push(
  '<input  id="red_deductPoint"  class="easyui-validatebox textbox" readonly value="1" style="width:250px;height:25px">'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Utilisateur expiré:</td>');
// html.push('<td>');
// html.push('<input  id="red_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align:right;vertical-align:top;padding-top:1px"><span style="color:red">&nbsp;</span>Remarques</td>'
);
html.push("<td>");
html.push(
  '<textarea id="red_remark" class="easyui-validatebox textbox" rows="5" style="width:250px;"></textarea>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="re_renewMachines" title="' +
    lg.renew +
    '" class="swd-yellow-btn"  href="javascript:void(0)" ><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="re_reset" title="' +
    lg.reset +
    '"  class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");
lg.bulkRenew = html.join("");

//批量销售，myAccount
html = [];
html.push(
  '<div id="bulkSales_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:195px;top:88px;border: 1px solid #d6d6d6;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkSales_tree"></ul> '
);
html.push("</div>");
html.push('<form id="bs_form">');
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Vente à:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkSales_seller" autocomplete="off" onkeydown="MyAccount.searchUser(event,' +
    "bulkSales_treeDiv" +
    "," +
    "bulkSales_seller" +
    ')" style="width:250px;height:28px;">'
);
html.push('<input  type="hidden" id="bulkSales_userId" >');
html.push(
  '<a href="###" id="bs_sellTo" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("</tr>");
// html.push('<tr>');
// html.push('<td style="text-align: right"><span style="color:red;">*</span>Utilisateur expiré:</td>');
// html.push('<td>');
// html.push('<input  id="bs_serviceTime" class="easyui-validatebox textbox"  style="width:250px;height:25px">');
// html.push('</td>');
// html.push('</tr>');
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Ajouter un appareil:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bs_addMachines" src="../../images/main/myAccount/add3.png" title="' +
    lg.addTo +
    '" style="cursor:pointer"></img>'
);
html.push(
  '<span class="bs_addNumBox">Courant：<span id="account_bs_addNum">0</span>'
);
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="account_bs_machineList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bs_sellMachines" title="' +
    lg.sell +
    '"  class="swd-yellow-btn" style="margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="bs_reset" class="swd-gray-btn" title="' +
    lg.reset +
    '"  style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-reset" style="width:13px;height:13px;margin-left: 1px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push("</form>");

lg.bulkSales = html.join("");

//批量转移1，弹出框
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:152px;top:171px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Ajouter un appareil:</td>'
);
html.push("<td>");
html.push(
  '<a  id="bt_addMachines" class="swd-yellow-btn" href="javascript:void(0)" >Ajouter par lots</a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)" >Move</a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)" >Cancel</a>'
);

lg.bulkTransfer = html.join("");

//批量转移2,myClient
html = [];
html.push(
  '<div id="blukTransfer_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:142px;top:83px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransfer_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransfer_user" autocomplete="off" style="width:250px;height:25px;">'
);
html.push('<input  type="hidden" id="bulkTransfer_userId" >');
html.push("</td>");
html.push(
  '<td><a href="###" id="bt_transferTo" ><img src="../../images/main/myAccount/pulldown.png"></a></td>'
);
html.push("</tr>");
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>Ajouter un appareil:</td>'
);
html.push("<td>");
html.push(
  '<img  id="bt_addMachines" style="cursor:pointer" title="' +
    lg.addTo +
    '" src="../../images/main/myAccount/add3.png" />'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");
html.push('<div style="padding-left:30px;padding-top:10px;">');
html.push('<table id="bt_machineList" style="width:500px;"></table>');
html.push("</div>");
html.push(
  '<a  id="bt_transferMachines" class="swd-yellow-btn" style="margin-left:70px;margin-top:10px;" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtn" class="swd-gray-btn" style="margin-left:10px;margin-top:10px;" href="javascript:void(0)"><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);

lg.bulkTransfer2 = html.join("");

//批量转移用户
html = [];
html.push(
  '<div id="blukTransferUser_treeDiv" class="easyui-panel"  style="z-index: 999;position:absolute;left:141px;top:80px;border: 1px solid #d6d6d6 !important;height:200px;width:250px;"> '
);
html.push(
  '<ul style="border:1px;height:200px;" class="ztree" id="bulkTransferUser_tree"></ul> '
);
html.push("</div>");
html.push(
  '<table cellpadding="20" cellspacing="20" style="margin-left:10px;font-size: 14px;">'
);
html.push("<tr>");
html.push(
  '<td style="text-align: right"><span style="color:red;">*</span>target client:</td>'
);
html.push("<td>");
html.push(
  '<input  class="easyui-validatebox textbox" id="bulkTransferUser_user" autocomplete="off" style="width:250px;height:28px;">'
);
html.push(
  '<input  type="hidden" id="bulkTransferUser_userId" ><a href="###" id="bt_transferUserTo" style="margin-top: 7px;margin-left: -23px;vertical-align:middle;position: relative;" ><img src="../../images/main/myAccount/pulldown.png"></a>'
);
html.push("</td>");
html.push("<td></td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push('<table id="bt_userList" style="width:400px;"></table>');
html.push("</td>");
html.push("</tr>");
html.push("<tr>");
html.push("<td>");
html.push("</td>");
html.push("<td>");
html.push(
  '<a id="bt_transferUsers" class="swd-yellow-btn" style="margin-top:12px;margin-left:2px" href="javascript:void(0)"><div class="icon-confirm" style="width:13px;height:13px"></div></a>'
);
html.push(
  '<a id="cancelBtnUser" class="swd-gray-btn" style="margin-left:10px;" href="javascript:void(0)" ><div class="icon-cancel2" style="width:13px;height:13px;"></div></a>'
);
html.push("</td>");
html.push("</tr>");
html.push("</table>");

lg.bulkTransferUser = html.join("");
window.lg = lg