<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
    <style type="text/css">
      body,
      html {
        width: 100%;
        height: 100%;
        margin: 0;
        font-family: '微软雅黑';
      }
      #panorama {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
      }
      #result {
        width: 100%;
        font-size: 12px;
      }
    </style>
    <script type="text/javascript" src="../js/core/TUI-core.js"></script>
    <script type="text/javascript" src="../lib/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=7OrmYt0wctpOEcfFWG6Wc4Oh4HbV56GI&s=1"></script>
    <title>百度街景</title>
  </head>
  <body>
    <div id="panorama"></div>
  </body>
</html>
<script type="text/javascript">
  var map = new BMap.Map('panorama')

  map.addTileLayer(new BMap.PanoramaCoverageLayer())

  function getRequestParam(name) {
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
    var r = window.location.search.substr(1).match(reg)
    if (r != null) return decodeURI(r[2])
    else return null
  }

  $(document).ready(function() {
    var lng = getRequestParam('lng')
    var lat = getRequestParam('lat')
    var zoom = getRequestParam('zoom')
    // lng=120.320032
    // lat=31.589666
    map.centerAndZoom(new BMap.Point(lng, lat), 16)
    var point = new BMap.Point(lng, lat)
    var marker = new BMap.Marker(point) // 创建标注
    map.addOverlay(marker) // 将标注添加到地图中
    var panorama = new BMap.Panorama('panorama')
    panorama.setPov({ heading: -40, pitch: 6 })
    panorama.setPosition(new BMap.Point(lng, lat)) //根据经纬度坐标展示全景图
    var panoramaService = new BMap.PanoramaService()
    var stCtrl = new BMap.PanoramaControl() //构造全景控件
    stCtrl.setOffset(new BMap.Size(20, 20))
    map.addControl(stCtrl) //添加全景控件
    map.enableScrollWheelZoom() //启用滚轮放大缩小，默认禁用
    map.enableContinuousZoom() //启用地图惯性拖拽，默认禁用
    panoramaService.getPanoramaByLocation(new BMap.Point(lng, lat), function(data) {
      console.log(data)
      var panoramaInfo = ''
      if (data == null) {
        alert('该位置无街景')
        return
      }
    })
  })
</script>
