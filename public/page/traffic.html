<!DOCTYPE html>
<html>
  <head>
    <title>Google Traffic</title>
    <!-- ditu.google.cn -->
    <!-- maps.google.com -->
    <!-- <script type="text/javascript" src="../js/core/TUI-core.js"></script> -->
    <!-- <script type="text/javascript" src="../lib/jquery/jquery.min.js"></script> -->
    <script
      type="text/javascript"
      src="https://maps.googleapis.com/maps/api/js?v=3.3.1&sensor=false&libraries=drawing&key=AIzaSyCpiiWsdoIB2tFSLI-PVUYp0FwvlAcD8Uk&callback=initMap"
      async
      defer
    ></script>
    <style type="text/css">
      #map {
        position: fixed !important;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
      }
    </style>
  </head>

  <body>
    <div id="map"></div>
    <script type="text/javascript">
      function getRequestParam(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return decodeURI(r[2])
        else return null
      }
      function initMap() {
        var lng = getRequestParam('lng')
        var lat = getRequestParam('lat')
        var zoom = getRequestParam('zoom')
        var myLatLng = { lat: parseInt(lat), lng: parseInt(lng) }
        // console.log(lng,lat)
        var map = new google.maps.Map(document.getElementById('map'), {
          zoom: parseInt(zoom),
          center: myLatLng
        })
        // var marker = new google.maps.Marker({
        //     position: myLatLng,
        //     map: map
        // });

        var trafficLayer = new google.maps.TrafficLayer()
        trafficLayer.setMap(map)
      }
      const faviconMap = {
        imperialgps: '../domain/imperialgps/favicon.ico',
        justrack: '../domain/justrack/favicon.ico',
        statgps: '../domain/statgps/favicon.ico',
        grupoFast: '../domain/grupoFast/favicon.ico',
        letztalkvideo: '../domain/letztalk/favicon.ico',
        pathfinderastreo: '../domain/path/favicon.ico',
        novagps: '../domain/nova/favicon.ico',
        hasnet: '../domain/hasnet/favicon.ico',
        rastrocar: '../domain//rastrocar/favicon.ico',
        tcbgps365ec: '../domain/tcbGps/favicon.ico',
        ecoflygps: '../domain/ecoflygps/favicon.ico',
        gtopcambodia: '../domain/gtop/favicon.ico',
        unequal: '../domain/unequal/favicon.ico',
        gpsfirst: '../domain/gpsfirst/favicon.ico',
        gocam: '../domain/gocam/favicon.ico',
        globaltrackpro: '../domain/global/favicon.ico',
        albtrack: '../domain/albtrack/favicon.ico',
        utrack: '../domain/utrack/favicon.ico',
        eagleeyegps: '../domain/eagle/favicon.ico',
        andestrack: '../domain/andestrack/favicon.ico',
        ontrackgps: '../domain/ontrackgps/favicon.ico',
        hrfsgps: '../domain/hrfsgps/favicon.ico',
        waypointgps: '../domain/waypoint/favicon.ico',
        trailx: '../domain/trailx/favicon.ico',
        sscarsystem: '../domain/sscarsystem/favicon.ico',
        meridianpro: '../domain/meridianpro/favicon.ico',
        kromotech: '../domain/kromotech/favicon.ico',
        geozapify: '../domain/geozapify/favicon.ico',
        radtrack: '../domain/radtrack/favicon.ico',
        savnav: '../domain/savnav/favicon.ico',
        spartantracker: '../domain/spartan/favicon.ico'
      }
      var domain = window.location.hostname

      var faviconUrl = faviconMap[Object.keys(faviconMap).find(key => domain.includes('key'))] || '../favicon.ico'

      let link = document.querySelector("link[rel*='icon']") || document.createElement('link')
      link.type = 'image/x-icon'
      link.rel = 'shortcut icon'
      link.href = faviconUrl
      document.getElementsByTagName('head')[0].appendChild(link)
    </script>
  </body>
</html>
