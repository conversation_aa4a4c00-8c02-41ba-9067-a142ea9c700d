<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
	<style type="text/css">
		body, html{width: 100%;height: 100%;margin:0;font-family:"微软雅黑";}
		#map {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1;
            width:100%; height:100%;}
		#result {width:100%;font-size:12px;}
    </style>
    <script type="module" src="../js/core/TUI-core.js"></script>
	<script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=7OrmYt0wctpOEcfFWG6Wc4Oh4HbV56GI&s=1"></script>
	<title>百度地图</title>
</head>
<body>
	<div id="map"></div>
</body>
</html>
<script type="text/javascript"> 
	var TUI = window.TUI
	window.onload=function(){
        var map = new BMap.Map('map');
        var lng=TUI.Core.getRequestParam('lng');
		var lat=TUI.Core.getRequestParam('lat');
		// lng=120.320032
		// lat=31.589666
        var point = new BMap.Point(lng,lat);
        var marker = new BMap.Marker(point);  // 创建标注
        map.addOverlay(marker);              // 将标注添加到地图中
        map.centerAndZoom(point, 16);
        map.enableScrollWheelZoom();   //启用滚轮放大缩小，默认禁用
        map.enableContinuousZoom();    //启用地图惯性拖拽，默认禁用
    }
</script>