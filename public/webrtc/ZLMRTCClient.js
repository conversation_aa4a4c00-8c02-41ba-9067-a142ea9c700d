/* eslint-disable */
var ZLMRTCClient=function(e){"use strict";let t={WEBRTC_NOT_SUPPORT:"WEBRTC_NOT_SUPPORT",WEBRTC_ICE_CANDIDATE_ERROR:"WEBRTC_ICE_CANDIDATE_ERROR",WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED:"WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED",WEBRTC_ON_REMOTE_STREAMS:"WEBRTC_ON_REMOTE_STREAMS",WEBRTC_ON_LOCAL_STREAM:"WEBRTC_ON_LOCAL_STREAM",WEBRTC_ON_CONNECTION_STATE_CHANGE:"WEBRTC_ON_CONNECTION_STATE_CHANGE",WEBRTC_ON_DATA_CHANNEL_OPEN:"WEBRTC_ON_DATA_CHANNEL_OPEN",WEBRTC_ON_DATA_CHANNEL_CLOSE:"WEBR<PERSON>_ON_DATA_CHANNEL_CLOSE",WE<PERSON><PERSON>_ON_DATA_CHANNEL_ERR:"WEBRTC_ON_DATA_CHANNEL_ERR",WEBRTC_ON_DATA_CHANNEL_MSG:"WEBRTC_ON_DATA_CHANNEL_MSG",CAPTURE_STREAM_FAILED:"CAPTURE_STREAM_FAILED"};function r(){return null!==window.navigator.userAgent.match("Firefox")}let n={MIC:"mic",SCREENCAST:"screen-cast",FILE:"file",MIXED:"mixed"},i={CAMERA:"camera",SCREENCAST:"screen-cast",FILE:"file",MIXED:"mixed"};class a{constructor(e,t){this.width=e,this.height=t}}let o=!0,s=!0;function c(e,t,r){let n=e.match(t);return n&&n.length>=r&&parseInt(n[r],10)}function d(e,t,r){if(!e.RTCPeerConnection)return;let n=e.RTCPeerConnection.prototype,i=n.addEventListener;n.addEventListener=function(e,n){if(e!==t)return i.apply(this,arguments);let a=e=>{let t=r(e);t&&(n.handleEvent?n.handleEvent(t):n(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(n,a),i.apply(this,[e,a])};let a=n.removeEventListener;n.removeEventListener=function(e,r){if(e!==t||!this._eventMap||!this._eventMap[t]||!this._eventMap[t].has(r))return a.apply(this,arguments);let n=this._eventMap[t].get(r);return this._eventMap[t].delete(r),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,a.apply(this,[e,n])},Object.defineProperty(n,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function p(e){return"boolean"!=typeof e?Error("Argument type: "+typeof e+". Please use a boolean."):(o=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function l(e){return"boolean"!=typeof e?Error("Argument type: "+typeof e+". Please use a boolean."):(s=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function u(){"object"==typeof window&&!o&&"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}function h(e,t){s&&console.warn(e+" is deprecated, please use "+t+" instead.")}function f(e){return"[object Object]"===Object.prototype.toString.call(e)}function m(e,t,r){let n=r?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;let a=[];return e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&a.push(e)}),a.forEach(t=>{e.forEach(r=>{r.type===n&&r.trackId===t.id&&function e(t,r,n){!(!r||n.has(r.id))&&(n.set(r.id,r),Object.keys(r).forEach(i=>{i.endsWith("Id")?e(t,t.get(r[i]),n):i.endsWith("Ids")&&r[i].forEach(r=>{e(t,t.get(r),n)})}))}(e,r,i)})}),i}let v=u;function g(e,t){let r=e&&e.navigator;if(!r.mediaDevices)return;let n=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;let t={};return Object.keys(e).forEach(r=>{if("require"===r||"advanced"===r||"mediaSource"===r)return;let n="object"==typeof e[r]?e[r]:{ideal:e[r]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);let i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];let a={};"number"==typeof n.ideal?(a[i("min",r)]=n.ideal,t.optional.push(a),(a={})[i("max",r)]=n.ideal,t.optional.push(a)):(a[i("",r)]=n.ideal,t.optional.push(a))}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",r)]=n.exact):["min","max"].forEach(e=>{void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,r)]=n[e])})}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(t.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){let a=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])};a((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),a(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=n(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});let s=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&!(r.mediaDevices.getSupportedConstraints&&r.mediaDevices.getSupportedConstraints().facingMode&&!s)){delete e.video.facingMode;let c;if("environment"===o.exact||"environment"===o.ideal?c=["back","rear"]:("user"===o.exact||"user"===o.ideal)&&(c=["front"]),c)return r.mediaDevices.enumerateDevices().then(t=>{let r=(t=t.filter(e=>"videoinput"===e.kind)).find(e=>c.some(t=>e.label.toLowerCase().includes(t)));return!r&&t.length&&c.includes("back")&&(r=t[t.length-1]),r&&(e.video.deviceId=o.exact?{exact:r.deviceId}:{ideal:r.deviceId}),e.video=n(e.video),v("chrome: "+JSON.stringify(e)),i(e)})}e.video=n(e.video)}return v("chrome: "+JSON.stringify(e)),i(e)},a=function(e){return t.version>=64?e:{name:({PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"})[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},o=function(e,t,n){i(e,e=>{r.webkitGetUserMedia(e,t,e=>{n&&n(a(e))})})};if(r.getUserMedia=o.bind(r),r.mediaDevices.getUserMedia){let s=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(e){return i(e,e=>s(e).then(t=>{if(e.audio&&!t.getAudioTracks().length||e.video&&!t.getVideoTracks().length)throw t.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return t},e=>Promise.reject(a(e))))}}}function C(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function y(e){if("object"!=typeof e||!e.RTCPeerConnection||"ontrack"in e.RTCPeerConnection.prototype)d(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e));else{Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});let t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function r(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===r.track.id):{track:r.track};let i=new Event("track");i.track=r.track,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)}),t.stream.getTracks().forEach(r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===r.id):{track:r};let i=new Event("track");i.track=r,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}}function T(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){let t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function e(){return this._senders=this._senders||[],this._senders.slice()};let r=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function e(n,i){let a=r.apply(this,arguments);return a||(a=t(this,n),this._senders.push(a)),a};let n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function e(t){n.apply(this,arguments);let r=this._senders.indexOf(t);-1!==r&&this._senders.splice(r,1)}}let i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function e(r){this._senders=this._senders||[],i.apply(this,[r]),r.getTracks().forEach(e=>{this._senders.push(t(this,e))})};let a=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function e(t){this._senders=this._senders||[],a.apply(this,[t]),t.getTracks().forEach(e=>{let t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){let o=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function e(){let t=o.apply(this,[]);return t.forEach(e=>e._pc=this),t},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function R(e){if(!e.RTCPeerConnection)return;let t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function e(){let[r,n,i]=arguments;if(arguments.length>0&&"function"==typeof r)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof r))return t.apply(this,[]);let a=function(e){let t={},r=e.result();return r.forEach(e=>{let r={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach(t=>{r[t]=e.stat(t)}),t[r.id]=r}),t},o=function(e){return new Map(Object.keys(e).map(t=>[t,e[t]]))};if(arguments.length>=2){let s=function(e){n(o(a(e)))};return t.apply(this,[s,r])}return new Promise((e,r)=>{t.apply(this,[function(t){e(o(a(t)))},r])}).then(n,i)}}function S(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){let t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function e(){let r=t.apply(this,[]);return r.forEach(e=>e._pc=this),r});let r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function e(){let t=r.apply(this,arguments);return t._pc=this,t}),e.RTCRtpSender.prototype.getStats=function e(){let t=this;return this._pc.getStats().then(e=>m(e,t.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){let n=e.RTCPeerConnection.prototype.getReceivers;n&&(e.RTCPeerConnection.prototype.getReceivers=function e(){let t=n.apply(this,[]);return t.forEach(e=>e._pc=this),t}),d(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function e(){let t=this;return this._pc.getStats().then(e=>m(e,t.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype))return;let i=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function t(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){let r=arguments[0],n,a,o;return(this.getSenders().forEach(e=>{e.track===r&&(n?o=!0:n=e)}),this.getReceivers().forEach(e=>(e.track===r&&(a?o=!0:a=e),e.track===r)),o||n&&a)?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):n?n.getStats():a?a.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return i.apply(this,arguments)}}function E(e){e.RTCPeerConnection.prototype.getLocalStreams=function e(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};let t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function e(r,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};let i=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(i)&&this._shimmedLocalStreams[n.id].push(i):this._shimmedLocalStreams[n.id]=[n,i],i};let r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function e(t){this._shimmedLocalStreams=this._shimmedLocalStreams||{},t.getTracks().forEach(e=>{let t=this.getSenders().find(t=>t.track===e);if(t)throw new DOMException("Track already exists.","InvalidAccessError")});let n=this.getSenders();r.apply(this,arguments);let i=this.getSenders().filter(e=>-1===n.indexOf(e));this._shimmedLocalStreams[t.id]=[t].concat(i)};let n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function e(t){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[t.id],n.apply(this,arguments)};let i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function e(t){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},t&&Object.keys(this._shimmedLocalStreams).forEach(e=>{let r=this._shimmedLocalStreams[e].indexOf(t);-1!==r&&this._shimmedLocalStreams[e].splice(r,1),1===this._shimmedLocalStreams[e].length&&delete this._shimmedLocalStreams[e]}),i.apply(this,arguments)}}function $(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return E(e);let r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function e(){let t=r.apply(this);return this._reverseStreams=this._reverseStreams||{},t.map(e=>this._reverseStreams[e.id])};let n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function t(r){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},r.getTracks().forEach(e=>{let t=this.getSenders().find(t=>t.track===e);if(t)throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[r.id]){let i=new e.MediaStream(r.getTracks());this._streams[r.id]=i,this._reverseStreams[i.id]=r,r=i}n.apply(this,[r])};let i=e.RTCPeerConnection.prototype.removeStream;function a(e,t){let r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{let n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(RegExp(i.id,"g"),n.id)}),new RTCSessionDescription({type:t.type,sdp:r})}e.RTCPeerConnection.prototype.removeStream=function e(t){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[t.id]||t]),delete this._reverseStreams[this._streams[t.id]?this._streams[t.id].id:t.id],delete this._streams[t.id]},e.RTCPeerConnection.prototype.addTrack=function t(r,n){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");let i=[].slice.call(arguments,1);if(1!==i.length||!i[0].getTracks().find(e=>e===r))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");let a=this.getSenders().find(e=>e.track===r);if(a)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};let o=this._streams[n.id];if(o)o.addTrack(r),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{let s=new e.MediaStream([r]);this._streams[n.id]=s,this._reverseStreams[s.id]=n,this.addStream(s)}return this.getSenders().find(e=>e.track===r)},["createOffer","createAnswer"].forEach(function(t){let r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){let e=arguments,t=arguments.length&&"function"==typeof arguments[0];return t?r.apply(this,[t=>{let r=a(this,t);e[0].apply(null,[r])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):r.apply(this,arguments).then(e=>a(this,e))}})[t]});let o=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function e(){var t,r;if(!arguments.length||!arguments[0].type)return o.apply(this,arguments);let n;return arguments[0]=(t=this,r=arguments[0],n=r.sdp,Object.keys(t._reverseStreams||[]).forEach(e=>{let r=t._reverseStreams[e],i=t._streams[r.id];n=n.replace(RegExp(r.id,"g"),i.id)}),new RTCSessionDescription({type:r.type,sdp:n})),o.apply(this,arguments)};let s=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){let e=s.get.apply(this);return""===e.type?e:a(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function e(t){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!t._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");let r=t._pc===this;if(!r)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let n;Object.keys(this._streams).forEach(e=>{let r=this._streams[e].getTracks().find(e=>t.track===e);r&&(n=this._streams[e])}),n&&(1===n.getTracks().length?this.removeStream(this._reverseStreams[n.id]):n.removeTrack(t.track),this.dispatchEvent(new Event("negotiationneeded")))}}function P(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){let r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}})[t]})}function b(e,t){d(e,"negotiationneeded",e=>{let r=e.target;if(!(t.version<72)&&(!r.getConfiguration||"plan-b"!==r.getConfiguration().sdpSemantics)||"stable"===r.signalingState)return e})}var w,k,D=Object.freeze({__proto__:null,shimMediaStream:C,shimOnTrack:y,shimGetSendersWithDtmf:T,shimGetStats:R,shimSenderReceiverGetStats:S,shimAddTrackRemoveTrackWithNative:E,shimAddTrackRemoveTrack:$,shimPeerConnection:P,fixNegotiationNeeded:b,shimGetUserMedia:g,shimGetDisplayMedia:function e(t,r){if((!t.navigator.mediaDevices||!("getDisplayMedia"in t.navigator.mediaDevices))&&t.navigator.mediaDevices){if("function"!=typeof r){console.error("shimGetDisplayMedia: getSourceId argument is not a function");return}t.navigator.mediaDevices.getDisplayMedia=function e(n){return r(n).then(e=>{let r=n.video&&n.video.width,i=n.video&&n.video.height,a=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:e,maxFrameRate:a||3}},r&&(n.video.mandatory.maxWidth=r),i&&(n.video.mandatory.maxHeight=i),t.navigator.mediaDevices.getUserMedia(n)})}}}}),A=(k={exports:{}},(w=function(e){var t={};t.generateIdentifier=function(){return Math.random().toString(36).substr(2,10)},t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map(function(e){return e.trim()})},t.splitSections=function(e){return e.split("\nm=").map(function(e,t){return(t>0?"m="+e:e).trim()+"\r\n"})},t.getDescription=function(e){var r=t.splitSections(e);return r&&r[0]},t.getMediaSections=function(e){var r=t.splitSections(e);return r.shift(),r},t.matchPrefix=function(e,r){return t.splitLines(e).filter(function(e){return 0===e.indexOf(r)})},t.parseCandidate=function(e){for(var t,r={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},n=8;n<t.length;n+=2)switch(t[n]){case"raddr":r.relatedAddress=t[n+1];break;case"rport":r.relatedPort=parseInt(t[n+1],10);break;case"tcptype":r.tcpType=t[n+1];break;case"ufrag":r.ufrag=t[n+1],r.usernameFragment=t[n+1];break;default:r[t[n]]=t[n+1]}return r},t.writeCandidate=function(e){var t=[];t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var r=e.type;return t.push("typ"),t.push(r),"host"!==r&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){var t=e.substr(9).split(" "),r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=3===t.length?parseInt(t[2],10):1,r.numChannels=r.channels,r},t.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},t.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){for(var t,r={},n=e.substr(e.indexOf(" ")+1).split(";"),i=0;i<n.length;i++)r[(t=n[i].trim().split("="))[0].trim()]=t[1];return r},t.writeFmtp=function(e){var t="",r=e.payloadType;if(void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var n=[];Object.keys(e.parameters).forEach(function(t){e.parameters[t]?n.push(t+"="+e.parameters[t]):n.push(t)}),t+="a=fmtp:"+r+" "+n.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){var t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(function(e){t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},t.parseSsrcMedia=function(e){var t=e.indexOf(" "),r={ssrc:parseInt(e.substr(7,t-7),10)},n=e.indexOf(":",t);return n>-1?(r.attribute=e.substr(t+1,n-t-1),r.value=e.substr(n+1)):r.attribute=e.substr(t+1),r},t.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(function(e){return parseInt(e,10)})}},t.getMid=function(e){var r=t.matchPrefix(e,"a=mid:")[0];if(r)return r.substr(6)},t.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1]}},t.getDtlsParameters=function(e,r){return{role:"auto",fingerprints:t.matchPrefix(e+r,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){var r="a=setup:"+t+"\r\n";return e.fingerprints.forEach(function(e){r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),r},t.parseCryptoLine=function(e){var t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;var t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,r){return t.matchPrefix(e+r,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,r){var n=t.matchPrefix(e+r,"a=ice-ufrag:")[0],i=t.matchPrefix(e+r,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substr(12),password:i.substr(10)}:null},t.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},t.parseRtpParameters=function(e){for(var r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=t.splitLines(e)[0].split(" "),i=3;i<n.length;i++){var a=n[i],o=t.matchPrefix(e,"a=rtpmap:"+a+" ")[0];if(o){var s=t.parseRtpMap(o),c=t.matchPrefix(e,"a=fmtp:"+a+" ");switch(s.parameters=c.length?t.parseFmtp(c[0]):{},s.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+a+" ").map(t.parseRtcpFb),r.codecs.push(s),s.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(s.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach(function(e){r.headerExtensions.push(t.parseExtmap(e))}),r},t.writeRtpDescription=function(e,r){var n="";n+="m="+e+" ",n+=r.codecs.length>0?"9":"0",n+=" UDP/TLS/RTP/SAVPF ",n+=r.codecs.map(function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType}).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",r.codecs.forEach(function(e){n+=t.writeRtpMap(e),n+=t.writeFmtp(e),n+=t.writeRtcpFb(e)});var i=0;return r.codecs.forEach(function(e){e.maxptime>i&&(i=e.maxptime)}),i>0&&(n+="a=maxptime:"+i+"\r\n"),n+="a=rtcp-mux\r\n",r.headerExtensions&&r.headerExtensions.forEach(function(e){n+=t.writeExtmap(e)}),n},t.parseRtpEncodingParameters=function(e){var r,n=[],i=t.parseRtpParameters(e),a=-1!==i.fecMechanisms.indexOf("RED"),o=-1!==i.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute}),c=s.length>0&&s[0].ssrc,d=t.matchPrefix(e,"a=ssrc-group:FID").map(function(e){return e.substr(17).split(" ").map(function(e){return parseInt(e,10)})});d.length>0&&d[0].length>1&&d[0][0]===c&&(r=d[0][1]),i.codecs.forEach(function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&r&&(t.rtx={ssrc:r}),n.push(t),a&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:o?"red+ulpfec":"red"},n.push(t))}}),0===n.length&&c&&n.push({ssrc:c});var p=t.matchPrefix(e,"b=");return p.length&&(p=0===p[0].indexOf("b=TIAS:")?parseInt(p[0].substr(7),10):0===p[0].indexOf("b=AS:")?950*parseInt(p[0].substr(5),10)-16e3:void 0,n.forEach(function(e){e.maxBitrate=p})),n},t.parseRtcpParameters=function(e){var r={},n=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute})[0];n&&(r.cname=n.value,r.ssrc=n.ssrc);var i=t.matchPrefix(e,"a=rtcp-rsize");r.reducedSize=i.length>0,r.compound=0===i.length;var a=t.matchPrefix(e,"a=rtcp-mux");return r.mux=a.length>0,r},t.parseMsid=function(e){var r,n=t.matchPrefix(e,"a=msid:");if(1===n.length)return{stream:(r=n[0].substr(7).split(" "))[0],track:r[1]};var i=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"msid"===e.attribute});if(i.length>0)return{stream:(r=i[0].value.split(" "))[0],track:r[1]}},t.parseSctpDescription=function(e){var r,n=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:");i.length>0&&(r=parseInt(i[0].substr(19),10)),isNaN(r)&&(r=65536);var a=t.matchPrefix(e,"a=sctp-port:");if(a.length>0)return{port:parseInt(a[0].substr(12),10),protocol:n.fmt,maxMessageSize:r};if(t.matchPrefix(e,"a=sctpmap:").length>0){var o=t.matchPrefix(e,"a=sctpmap:")[0].substr(10).split(" ");return{port:parseInt(o[0],10),protocol:o[1],maxMessageSize:r}}},t.writeSctpDescription=function(e,t){var r=[];return r="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&r.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),r.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,r,n){var i;return"v=0\r\no="+(n||"thisisadapterortc")+" "+(i=e||t.generateSessionId())+" "+(void 0!==r?r:2)+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.writeMediaSection=function(e,r,n,i){var a=t.writeRtpDescription(e.kind,r);if(a+=t.writeIceParameters(e.iceGatherer.getLocalParameters()),a+=t.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===n?"actpass":"active"),a+="a=mid:"+e.mid+"\r\n",e.direction?a+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?a+="a=sendrecv\r\n":e.rtpSender?a+="a=sendonly\r\n":e.rtpReceiver?a+="a=recvonly\r\n":a+="a=inactive\r\n",e.rtpSender){var o="msid:"+i.id+" "+e.rtpSender.track.id+"\r\n";a+="a="+o,a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+o,e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+o,a+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+t.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+t.localCName+"\r\n"),a},t.getDirection=function(e,r){for(var n=t.splitLines(e),i=0;i<n.length;i++)switch(n[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[i].substr(2)}return r?t.getDirection(r):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){var r=t.splitLines(e)[0].substr(2).split(" ");return{kind:r[0],port:parseInt(r[1],10),protocol:r[2],fmt:r.slice(3).join(" ")}},t.parseOLine=function(e){var r=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:r[0],sessionId:r[1],sessionVersion:parseInt(r[2],10),netType:r[3],addressType:r[4],address:r[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var r=t.splitLines(e),n=0;n<r.length;n++)if(r[n].length<2||"="!==r[n].charAt(1))return!1;return!0},e.exports=t})(k,k.exports),k.exports);function x(e,t,r,n,i){var a=A.writeRtpDescription(e.kind,t);if(a+=A.writeIceParameters(e.iceGatherer.getLocalParameters()),a+=A.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":i||"active"),a+="a=mid:"+e.mid+"\r\n",e.rtpSender&&e.rtpReceiver?a+="a=sendrecv\r\n":e.rtpSender?a+="a=sendonly\r\n":e.rtpReceiver?a+="a=recvonly\r\n":a+="a=inactive\r\n",e.rtpSender){var o=e.rtpSender._initialTrackId||e.rtpSender.track.id;e.rtpSender._initialTrackId=o;var s="msid:"+(n?n.id:"-")+" "+o+"\r\n";a+="a="+s,a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+s,e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+s,a+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+A.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+A.localCName+"\r\n"),a}function _(e,t){var r={codecs:[],headerExtensions:[],fecMechanisms:[]},n=function(e,t){e=parseInt(e,10);for(var r=0;r<t.length;r++)if(t[r].payloadType===e||t[r].preferredPayloadType===e)return t[r]},i=function(e,t,r,i){var a=n(e.parameters.apt,r),o=n(t.parameters.apt,i);return a&&o&&a.name.toLowerCase()===o.name.toLowerCase()};return e.codecs.forEach(function(n){for(var a=0;a<t.codecs.length;a++){var o=t.codecs[a];if(n.name.toLowerCase()===o.name.toLowerCase()&&n.clockRate===o.clockRate){if("rtx"===n.name.toLowerCase()&&n.parameters&&o.parameters.apt&&!i(n,o,e.codecs,t.codecs))continue;(o=JSON.parse(JSON.stringify(o))).numChannels=Math.min(n.numChannels,o.numChannels),r.codecs.push(o),o.rtcpFeedback=o.rtcpFeedback.filter(function(e){for(var t=0;t<n.rtcpFeedback.length;t++)if(n.rtcpFeedback[t].type===e.type&&n.rtcpFeedback[t].parameter===e.parameter)return!0;return!1});break}}}),e.headerExtensions.forEach(function(e){for(var n=0;n<t.headerExtensions.length;n++){var i=t.headerExtensions[n];if(e.uri===i.uri){r.headerExtensions.push(i);break}}}),r}function I(e,t,r){return -1!==({offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}})[t][e].indexOf(r)}function M(e,t){var r=e.getRemoteCandidates().find(function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type});return r||e.addRemoteCandidate(t),!r}function O(e,t){var r=Error(t);return r.name=e,r.code=({NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0})[e],r}var L=function(e,t){function r(t,r){r.addTrack(t),r.dispatchEvent(new e.MediaStreamTrackEvent("addtrack",{track:t}))}function n(t,r,n,i){var a=new Event("track");a.track=r,a.receiver=n,a.transceiver={receiver:n},a.streams=i,e.setTimeout(function(){t._dispatchEvent("track",a)})}var i=function(r){var n,i,a,o=this,s=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach(function(e){o[e]=s[e].bind(s)}),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this._localDescription=null,this._remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.connectionState="new",this.iceGatheringState="new",r=JSON.parse(JSON.stringify(r||{})),this.usingBundle="max-bundle"===r.bundlePolicy,"negotiate"===r.rtcpMuxPolicy)throw O("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(r.rtcpMuxPolicy||(r.rtcpMuxPolicy="require"),r.iceTransportPolicy){case"all":case"relay":break;default:r.iceTransportPolicy="all"}switch(r.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:r.bundlePolicy="balanced"}if(r.iceServers=(n=r.iceServers||[],i=t,a=!1,(n=JSON.parse(JSON.stringify(n))).filter(function(e){if(e&&(e.urls||e.url)){var t=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var r="string"==typeof t;return r&&(t=[t]),t=t.filter(function(e){return 0!==e.indexOf("turn:")||-1===e.indexOf("transport=udp")||-1!==e.indexOf("turn:[")||a?0===e.indexOf("stun:")&&i>=14393&&-1===e.indexOf("?transport=udp"):(a=!0,!0)}),delete e.url,e.urls=r?t[0]:t,!!t.length}})),this._iceGatherers=[],r.iceCandidatePoolSize)for(var c=r.iceCandidatePoolSize;c>0;c--)this._iceGatherers.push(new e.RTCIceGatherer({iceServers:r.iceServers,gatherPolicy:r.iceTransportPolicy}));else r.iceCandidatePoolSize=0;this._config=r,this.transceivers=[],this._sdpSessionId=A.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1};Object.defineProperty(i.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}}),Object.defineProperty(i.prototype,"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}}),i.prototype.onicecandidate=null,i.prototype.onaddstream=null,i.prototype.ontrack=null,i.prototype.onremovestream=null,i.prototype.onsignalingstatechange=null,i.prototype.oniceconnectionstatechange=null,i.prototype.onconnectionstatechange=null,i.prototype.onicegatheringstatechange=null,i.prototype.onnegotiationneeded=null,i.prototype.ondatachannel=null,i.prototype._dispatchEvent=function(e,t){!this._isClosed&&(this.dispatchEvent(t),"function"==typeof this["on"+e]&&this["on"+e](t))},i.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},i.prototype.getConfiguration=function(){return this._config},i.prototype.getLocalStreams=function(){return this.localStreams},i.prototype.getRemoteStreams=function(){return this.remoteStreams},i.prototype._createTransceiver=function(e,t){var r=this.transceivers.length>0,n={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};if(this.usingBundle&&r)n.iceTransport=this.transceivers[0].iceTransport,n.dtlsTransport=this.transceivers[0].dtlsTransport;else{var i=this._createIceAndDtlsTransports();n.iceTransport=i.iceTransport,n.dtlsTransport=i.dtlsTransport}return t||this.transceivers.push(n),n},i.prototype.addTrack=function(t,r){if(this._isClosed)throw O("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");if(this.transceivers.find(function(e){return e.track===t}))throw O("InvalidAccessError","Track already exists.");for(var n,i=0;i<this.transceivers.length;i++)this.transceivers[i].track||this.transceivers[i].kind!==t.kind||(n=this.transceivers[i]);return n||(n=this._createTransceiver(t.kind)),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(r)&&this.localStreams.push(r),n.track=t,n.stream=r,n.rtpSender=new e.RTCRtpSender(t,n.dtlsTransport),n.rtpSender},i.prototype.addStream=function(e){var r=this;if(t>=15025)e.getTracks().forEach(function(t){r.addTrack(t,e)});else{var n=e.clone();e.getTracks().forEach(function(e,t){var r=n.getTracks()[t];e.addEventListener("enabled",function(e){r.enabled=e.enabled})}),n.getTracks().forEach(function(e){r.addTrack(e,n)})}},i.prototype.removeTrack=function(t){if(this._isClosed)throw O("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof e.RTCRtpSender))throw TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var r=this.transceivers.find(function(e){return e.rtpSender===t});if(!r)throw O("InvalidAccessError","Sender was not created by this connection.");var n=r.stream;r.rtpSender.stop(),r.rtpSender=null,r.track=null,r.stream=null,-1===this.transceivers.map(function(e){return e.stream}).indexOf(n)&&this.localStreams.indexOf(n)>-1&&this.localStreams.splice(this.localStreams.indexOf(n),1),this._maybeFireNegotiationNeeded()},i.prototype.removeStream=function(e){var t=this;e.getTracks().forEach(function(e){var r=t.getSenders().find(function(t){return t.track===e});r&&t.removeTrack(r)})},i.prototype.getSenders=function(){return this.transceivers.filter(function(e){return!!e.rtpSender}).map(function(e){return e.rtpSender})},i.prototype.getReceivers=function(){return this.transceivers.filter(function(e){return!!e.rtpReceiver}).map(function(e){return e.rtpReceiver})},i.prototype._createIceGatherer=function(t,r){var n=this;if(r&&t>0)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var i=new e.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});return Object.defineProperty(i,"state",{value:"new",writable:!0}),this.transceivers[t].bufferedCandidateEvents=[],this.transceivers[t].bufferCandidates=function(e){var r=!e.candidate||0===Object.keys(e.candidate).length;i.state=r?"completed":"gathering",null!==n.transceivers[t].bufferedCandidateEvents&&n.transceivers[t].bufferedCandidateEvents.push(e)},i.addEventListener("localcandidate",this.transceivers[t].bufferCandidates),i},i.prototype._gather=function(t,r){var n=this,i=this.transceivers[r].iceGatherer;if(!i.onlocalcandidate){var a=this.transceivers[r].bufferedCandidateEvents;this.transceivers[r].bufferedCandidateEvents=null,i.removeEventListener("localcandidate",this.transceivers[r].bufferCandidates),i.onlocalcandidate=function(e){if(!n.usingBundle||!(r>0)){var a=new Event("icecandidate");a.candidate={sdpMid:t,sdpMLineIndex:r};var o=e.candidate,s=!o||0===Object.keys(o).length;if(s)("new"===i.state||"gathering"===i.state)&&(i.state="completed");else{"new"===i.state&&(i.state="gathering"),o.component=1,o.ufrag=i.getLocalParameters().usernameFragment;var c=A.writeCandidate(o);a.candidate=Object.assign(a.candidate,A.parseCandidate(c)),a.candidate.candidate=c,a.candidate.toJSON=function(){return{candidate:a.candidate.candidate,sdpMid:a.candidate.sdpMid,sdpMLineIndex:a.candidate.sdpMLineIndex,usernameFragment:a.candidate.usernameFragment}}}var d=A.getMediaSections(n._localDescription.sdp);s?d[a.candidate.sdpMLineIndex]+="a=end-of-candidates\r\n":d[a.candidate.sdpMLineIndex]+="a="+a.candidate.candidate+"\r\n",n._localDescription.sdp=A.getDescription(n._localDescription.sdp)+d.join("");var p=n.transceivers.every(function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state});"gathering"!==n.iceGatheringState&&(n.iceGatheringState="gathering",n._emitGatheringStateChange()),s||n._dispatchEvent("icecandidate",a),p&&(n._dispatchEvent("icecandidate",new Event("icecandidate")),n.iceGatheringState="complete",n._emitGatheringStateChange())}},e.setTimeout(function(){a.forEach(function(e){i.onlocalcandidate(e)})},0)}},i.prototype._createIceAndDtlsTransports=function(){var t=this,r=new e.RTCIceTransport(null);r.onicestatechange=function(){t._updateIceConnectionState(),t._updateConnectionState()};var n=new e.RTCDtlsTransport(r);return n.ondtlsstatechange=function(){t._updateConnectionState()},n.onerror=function(){Object.defineProperty(n,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:r,dtlsTransport:n}},i.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var r=this.transceivers[e].iceTransport;r&&(delete r.onicestatechange,delete this.transceivers[e].iceTransport);var n=this.transceivers[e].dtlsTransport;n&&(delete n.ondtlsstatechange,delete n.onerror,delete this.transceivers[e].dtlsTransport)},i.prototype._transceive=function(e,r,n){var i=_(e.localCapabilities,e.remoteCapabilities);r&&e.rtpSender&&(i.encodings=e.sendEncodingParameters,i.rtcp={cname:A.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(i.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(i)),n&&e.rtpReceiver&&i.codecs.length>0&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach(function(e){delete e.rtx}),e.recvEncodingParameters.length?i.encodings=e.recvEncodingParameters:i.encodings=[{}],i.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(i.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(i.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(i))},i.prototype.setLocalDescription=function(e){var t,r,n=this;if(-1===["offer","answer"].indexOf(e.type))return Promise.reject(O("TypeError",'Unsupported type "'+e.type+'"'));if(!I("setLocalDescription",e.type,n.signalingState)||n._isClosed)return Promise.reject(O("InvalidStateError","Can not set local "+e.type+" in state "+n.signalingState));if("offer"===e.type)r=(t=A.splitSections(e.sdp)).shift(),t.forEach(function(e,t){var r=A.parseRtpParameters(e);n.transceivers[t].localCapabilities=r}),n.transceivers.forEach(function(e,t){n._gather(e.mid,t)});else if("answer"===e.type){r=(t=A.splitSections(n._remoteDescription.sdp)).shift();var i=A.matchPrefix(r,"a=ice-lite").length>0;t.forEach(function(e,t){var a=n.transceivers[t],o=a.iceGatherer,s=a.iceTransport,c=a.dtlsTransport,d=a.localCapabilities,p=a.remoteCapabilities;if(!(A.isRejected(e)&&0===A.matchPrefix(e,"a=bundle-only").length)&&!a.rejected){var l=A.getIceParameters(e,r),u=A.getDtlsParameters(e,r);i&&(u.role="server"),n.usingBundle&&0!==t||(n._gather(a.mid,t),"new"===s.state&&s.start(o,l,i?"controlling":"controlled"),"new"===c.state&&c.start(u));var h=_(d,p);n._transceive(a,h.codecs.length>0,!1)}})}return n._localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?n._updateSignalingState("have-local-offer"):n._updateSignalingState("stable"),Promise.resolve()},i.prototype.setRemoteDescription=function(i){var a=this;if(-1===["offer","answer"].indexOf(i.type))return Promise.reject(O("TypeError",'Unsupported type "'+i.type+'"'));if(!I("setRemoteDescription",i.type,a.signalingState)||a._isClosed)return Promise.reject(O("InvalidStateError","Can not set remote "+i.type+" in state "+a.signalingState));var o={};a.remoteStreams.forEach(function(e){o[e.id]=e});var s=[],c=A.splitSections(i.sdp),d=c.shift(),p=A.matchPrefix(d,"a=ice-lite").length>0,l=A.matchPrefix(d,"a=group:BUNDLE ").length>0;a.usingBundle=l;var u=A.matchPrefix(d,"a=ice-options:")[0];return u?a.canTrickleIceCandidates=u.substr(14).split(" ").indexOf("trickle")>=0:a.canTrickleIceCandidates=!1,c.forEach(function(n,c){var u=A.splitLines(n),h=A.getKind(n),f=A.isRejected(n)&&0===A.matchPrefix(n,"a=bundle-only").length,m=u[0].substr(2).split(" ")[2],v=A.getDirection(n,d),g=A.parseMsid(n),C=A.getMid(n)||A.generateIdentifier();if(f||"application"===h&&("DTLS/SCTP"===m||"UDP/DTLS/SCTP"===m)){a.transceivers[c]={mid:C,kind:h,protocol:m,rejected:!0};return}!f&&a.transceivers[c]&&a.transceivers[c].rejected&&(a.transceivers[c]=a._createTransceiver(h,!0));var y=A.parseRtpParameters(n);f||(O=A.getIceParameters(n,d),(L=A.getDtlsParameters(n,d)).role="client"),D=A.parseRtpEncodingParameters(n);var T=A.parseRtcpParameters(n),R=A.matchPrefix(n,"a=end-of-candidates",d).length>0,S=A.matchPrefix(n,"a=candidate:").map(function(e){return A.parseCandidate(e)}).filter(function(e){return 1===e.component});if(("offer"===i.type||"answer"===i.type)&&!f&&l&&c>0&&a.transceivers[c]&&(a._disposeIceAndDtlsTransports(c),a.transceivers[c].iceGatherer=a.transceivers[0].iceGatherer,a.transceivers[c].iceTransport=a.transceivers[0].iceTransport,a.transceivers[c].dtlsTransport=a.transceivers[0].dtlsTransport,a.transceivers[c].rtpSender&&a.transceivers[c].rtpSender.setTransport(a.transceivers[0].dtlsTransport),a.transceivers[c].rtpReceiver&&a.transceivers[c].rtpReceiver.setTransport(a.transceivers[0].dtlsTransport)),"offer"!==i.type||f)"answer"===i.type&&!f&&($=(E=a.transceivers[c]).iceGatherer,P=E.iceTransport,b=E.dtlsTransport,w=E.rtpReceiver,k=E.sendEncodingParameters,x=E.localCapabilities,a.transceivers[c].recvEncodingParameters=D,a.transceivers[c].remoteCapabilities=y,a.transceivers[c].rtcpParameters=T,S.length&&"new"===P.state&&((p||R)&&(!l||0===c)?P.setRemoteCandidates(S):S.forEach(function(e){M(E.iceTransport,e)})),l&&0!==c||("new"===P.state&&P.start($,O,"controlling"),"new"===b.state&&b.start(L)),!_(E.localCapabilities,E.remoteCapabilities).codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&E.sendEncodingParameters[0].rtx&&delete E.sendEncodingParameters[0].rtx,a._transceive(E,"sendrecv"===v||"recvonly"===v,"sendrecv"===v||"sendonly"===v),w&&("sendrecv"===v||"sendonly"===v)?(I=w.track,g?(o[g.stream]||(o[g.stream]=new e.MediaStream),r(I,o[g.stream]),s.push([I,w,o[g.stream]])):(o.default||(o.default=new e.MediaStream),r(I,o.default),s.push([I,w,o.default]))):delete E.rtpReceiver);else{(E=a.transceivers[c]||a._createTransceiver(h)).mid=C,E.iceGatherer||(E.iceGatherer=a._createIceGatherer(c,l)),S.length&&"new"===E.iceTransport.state&&(R&&(!l||0===c)?E.iceTransport.setRemoteCandidates(S):S.forEach(function(e){M(E.iceTransport,e)})),x=e.RTCRtpReceiver.getCapabilities(h),t<15019&&(x.codecs=x.codecs.filter(function(e){return"rtx"!==e.name})),k=E.sendEncodingParameters||[{ssrc:(2*c+2)*1001}];var E,$,P,b,w,k,D,x,I,O,L,N,j=!1;"sendrecv"===v||"sendonly"===v?(j=!E.rtpReceiver,w=E.rtpReceiver||new e.RTCRtpReceiver(E.dtlsTransport,h),j&&(I=w.track,g&&"-"===g.stream||(g?(o[g.stream]||(o[g.stream]=new e.MediaStream,Object.defineProperty(o[g.stream],"id",{get:function(){return g.stream}})),Object.defineProperty(I,"id",{get:function(){return g.track}}),N=o[g.stream]):(o.default||(o.default=new e.MediaStream),N=o.default)),N&&(r(I,N),E.associatedRemoteMediaStreams.push(N)),s.push([I,w,N]))):E.rtpReceiver&&E.rtpReceiver.track&&(E.associatedRemoteMediaStreams.forEach(function(t){var r,n,i=t.getTracks().find(function(e){return e.id===E.rtpReceiver.track.id});i&&(r=i,(n=t).removeTrack(r),n.dispatchEvent(new e.MediaStreamTrackEvent("removetrack",{track:r})))}),E.associatedRemoteMediaStreams=[]),E.localCapabilities=x,E.remoteCapabilities=y,E.rtpReceiver=w,E.rtcpParameters=T,E.sendEncodingParameters=k,E.recvEncodingParameters=D,a._transceive(a.transceivers[c],!1,j)}}),void 0===a._dtlsRole&&(a._dtlsRole="offer"===i.type?"active":"passive"),a._remoteDescription={type:i.type,sdp:i.sdp},"offer"===i.type?a._updateSignalingState("have-remote-offer"):a._updateSignalingState("stable"),Object.keys(o).forEach(function(t){var r=o[t];if(r.getTracks().length){if(-1===a.remoteStreams.indexOf(r)){a.remoteStreams.push(r);var i=new Event("addstream");i.stream=r,e.setTimeout(function(){a._dispatchEvent("addstream",i)})}s.forEach(function(e){var t=e[0],i=e[1];r.id===e[2].id&&n(a,t,i,[r])})}}),s.forEach(function(e){!e[2]&&n(a,e[0],e[1],[])}),e.setTimeout(function(){a&&a.transceivers&&a.transceivers.forEach(function(e){e.iceTransport&&"new"===e.iceTransport.state&&e.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))})},4e3),Promise.resolve()},i.prototype.close=function(){this.transceivers.forEach(function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()}),this._isClosed=!0,this._updateSignalingState("closed")},i.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",t)},i.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout(function(){if(t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t._dispatchEvent("negotiationneeded",e)}},0))},i.prototype._updateIceConnectionState=function(){var e,t={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(e){e.iceTransport&&!e.rejected&&t[e.iceTransport.state]++}),e="new",t.failed>0?e="failed":t.checking>0?e="checking":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0?e="connected":t.completed>0&&(e="completed"),e!==this.iceConnectionState){this.iceConnectionState=e;var r=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",r)}},i.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(e){e.iceTransport&&e.dtlsTransport&&!e.rejected&&(t[e.iceTransport.state]++,t[e.dtlsTransport.state]++)}),t.connected+=t.completed,e="new",t.failed>0?e="failed":t.connecting>0?e="connecting":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0&&(e="connected"),e!==this.connectionState){this.connectionState=e;var r=new Event("connectionstatechange");this._dispatchEvent("connectionstatechange",r)}},i.prototype.createOffer=function(){var r=this;if(r._isClosed)return Promise.reject(O("InvalidStateError","Can not call createOffer after close"));var n=r.transceivers.filter(function(e){return"audio"===e.kind}).length,i=r.transceivers.filter(function(e){return"video"===e.kind}).length,a=arguments[0];if(a){if(a.mandatory||a.optional)throw TypeError("Legacy mandatory/optional constraints not supported.");void 0!==a.offerToReceiveAudio&&(n=!0===a.offerToReceiveAudio?1:!1===a.offerToReceiveAudio?0:a.offerToReceiveAudio),void 0!==a.offerToReceiveVideo&&(i=!0===a.offerToReceiveVideo?1:!1===a.offerToReceiveVideo?0:a.offerToReceiveVideo)}for(r.transceivers.forEach(function(e){"audio"===e.kind?--n<0&&(e.wantReceive=!1):"video"===e.kind&&--i<0&&(e.wantReceive=!1)});n>0||i>0;)n>0&&(r._createTransceiver("audio"),n--),i>0&&(r._createTransceiver("video"),i--);var o=A.writeSessionBoilerplate(r._sdpSessionId,r._sdpSessionVersion++);return r.transceivers.forEach(function(n,i){var a=n.track,o=n.kind,s=n.mid||A.generateIdentifier();n.mid=s,n.iceGatherer||(n.iceGatherer=r._createIceGatherer(i,r.usingBundle));var c=e.RTCRtpSender.getCapabilities(o);t<15019&&(c.codecs=c.codecs.filter(function(e){return"rtx"!==e.name})),c.codecs.forEach(function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1"),n.remoteCapabilities&&n.remoteCapabilities.codecs&&n.remoteCapabilities.codecs.forEach(function(t){e.name.toLowerCase()===t.name.toLowerCase()&&e.clockRate===t.clockRate&&(e.preferredPayloadType=t.payloadType)})}),c.headerExtensions.forEach(function(e){(n.remoteCapabilities&&n.remoteCapabilities.headerExtensions||[]).forEach(function(t){e.uri===t.uri&&(e.id=t.id)})});var d=n.sendEncodingParameters||[{ssrc:(2*i+1)*1001}];a&&t>=15019&&"video"===o&&!d[0].rtx&&(d[0].rtx={ssrc:d[0].ssrc+1}),n.wantReceive&&(n.rtpReceiver=new e.RTCRtpReceiver(n.dtlsTransport,o)),n.localCapabilities=c,n.sendEncodingParameters=d}),"max-compat"!==r._config.bundlePolicy&&(o+="a=group:BUNDLE "+r.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),o+="a=ice-options:trickle\r\n",r.transceivers.forEach(function(e,t){o+=x(e,e.localCapabilities,"offer",e.stream,r._dtlsRole),o+="a=rtcp-rsize\r\n",e.iceGatherer&&"new"!==r.iceGatheringState&&(0===t||!r.usingBundle)&&(e.iceGatherer.getLocalCandidates().forEach(function(e){e.component=1,o+="a="+A.writeCandidate(e)+"\r\n"}),"completed"===e.iceGatherer.state&&(o+="a=end-of-candidates\r\n"))}),Promise.resolve(new e.RTCSessionDescription({type:"offer",sdp:o}))},i.prototype.createAnswer=function(){var r=this;if(r._isClosed)return Promise.reject(O("InvalidStateError","Can not call createAnswer after close"));if(!("have-remote-offer"===r.signalingState||"have-local-pranswer"===r.signalingState))return Promise.reject(O("InvalidStateError","Can not call createAnswer in signalingState "+r.signalingState));var n=A.writeSessionBoilerplate(r._sdpSessionId,r._sdpSessionVersion++);r.usingBundle&&(n+="a=group:BUNDLE "+r.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),n+="a=ice-options:trickle\r\n";var i=A.getMediaSections(r._remoteDescription.sdp).length;return r.transceivers.forEach(function(e,a){if(!(a+1>i)){if(e.rejected){"application"===e.kind?"DTLS/SCTP"===e.protocol?n+="m=application 0 DTLS/SCTP 5000\r\n":n+="m=application 0 "+e.protocol+" webrtc-datachannel\r\n":"audio"===e.kind?n+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===e.kind&&(n+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),n+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+e.mid+"\r\n";return}e.stream&&("audio"===e.kind?o=e.stream.getAudioTracks()[0]:"video"===e.kind&&(o=e.stream.getVideoTracks()[0]),o&&t>=15019&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1}));var o,s=_(e.localCapabilities,e.remoteCapabilities);!s.codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,n+=x(e,s,"answer",e.stream,r._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(n+="a=rtcp-rsize\r\n")}}),Promise.resolve(new e.RTCSessionDescription({type:"answer",sdp:n}))},i.prototype.addIceCandidate=function(e){var t,r=this;return e&&!(void 0!==e.sdpMLineIndex||e.sdpMid)?Promise.reject(TypeError("sdpMLineIndex or sdpMid required")):new Promise(function(n,i){if(!r._remoteDescription)return i(O("InvalidStateError","Can not add ICE candidate without a remote description"));if(e&&""!==e.candidate){var a=e.sdpMLineIndex;if(e.sdpMid){for(var o=0;o<r.transceivers.length;o++)if(r.transceivers[o].mid===e.sdpMid){a=o;break}}var s=r.transceivers[a];if(!s)return i(O("OperationError","Can not add ICE candidate"));if(s.rejected)return n();var c=Object.keys(e.candidate).length>0?A.parseCandidate(e.candidate):{};if("tcp"===c.protocol&&(0===c.port||9===c.port)||c.component&&1!==c.component)return n();if((0===a||a>0&&s.iceTransport!==r.transceivers[0].iceTransport)&&!M(s.iceTransport,c))return i(O("OperationError","Can not add ICE candidate"));var d=e.candidate.trim();0===d.indexOf("a=")&&(d=d.substr(2)),t=A.getMediaSections(r._remoteDescription.sdp),t[a]+="a="+(c.type?d:"end-of-candidates")+"\r\n",r._remoteDescription.sdp=A.getDescription(r._remoteDescription.sdp)+t.join("")}else for(var p=0;p<r.transceivers.length&&(r.transceivers[p].rejected||(r.transceivers[p].iceTransport.addRemoteCandidate({}),t=A.getMediaSections(r._remoteDescription.sdp),t[p]+="a=end-of-candidates\r\n",r._remoteDescription.sdp=A.getDescription(r._remoteDescription.sdp)+t.join(""),!r.usingBundle));p++);n()})},i.prototype.getStats=function(t){if(t&&t instanceof e.MediaStreamTrack){var r=null;if(this.transceivers.forEach(function(e){e.rtpSender&&e.rtpSender.track===t?r=e.rtpSender:e.rtpReceiver&&e.rtpReceiver.track===t&&(r=e.rtpReceiver)}),!r)throw O("InvalidAccessError","Invalid selector.");return r.getStats()}var n=[];return this.transceivers.forEach(function(e){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(t){e[t]&&n.push(e[t].getStats())})}),Promise.all(n).then(function(e){var t=new Map;return e.forEach(function(e){e.forEach(function(e){t.set(e.id,e)})}),t})},["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach(function(t){var r=e[t];if(r&&r.prototype&&r.prototype.getStats){var n=r.prototype.getStats;r.prototype.getStats=function(){return n.apply(this).then(function(e){var t=new Map;return Object.keys(e).forEach(function(r){var n;e[r].type=({inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"})[(n=e[r]).type]||n.type,t.set(r,e[r])}),t})}}});var a=["createOffer","createAnswer"];return a.forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[0]||"function"==typeof e[1]?t.apply(this,[arguments[2]]).then(function(t){"function"==typeof e[0]&&e[0].apply(null,[t])},function(t){"function"==typeof e[1]&&e[1].apply(null,[t])}):t.apply(this,arguments)}}),(a=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]||"function"==typeof e[2]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)},function(t){"function"==typeof e[2]&&e[2].apply(null,[t])}):t.apply(this,arguments)}}),["getStats"].forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)}):t.apply(this,arguments)}}),i};function N(e){let t=e&&e.navigator,r=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return r(e).catch(e=>{var t;return Promise.reject({name:{PermissionDeniedError:"NotAllowedError"}[(t=e).name]||t.name,message:t.message,constraint:t.constraint,toString(){return this.name}})})}}function j(e){"getDisplayMedia"in e.navigator&&e.navigator.mediaDevices&&(!e.navigator.mediaDevices||!("getDisplayMedia"in e.navigator.mediaDevices))&&(e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator))}function F(e,t){if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function e(t){return t}),e.RTCSessionDescription||(e.RTCSessionDescription=function e(t){return t}),t.version<15025)){let r=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set(e){r.set.call(this,e);let t=new Event("enabled");t.enabled=e,this.dispatchEvent(t)}})}!e.RTCRtpSender||"dtmf"in e.RTCRtpSender.prototype||Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}}),e.RTCDtmfSender&&!e.RTCDTMFSender&&(e.RTCDTMFSender=e.RTCDtmfSender);let n=L(e,t.version);e.RTCPeerConnection=function e(r){if(r&&r.iceServers){var i,a;let o;r.iceServers=(i=r.iceServers,t.version,o=!1,(i=JSON.parse(JSON.stringify(i))).filter(e=>{if(e&&(e.urls||e.url)){let t=e.urls||e.url;e.url&&!e.urls&&h("RTCIceServer.url","RTCIceServer.urls");let r="string"==typeof t;return r&&(t=[t]),t=t.filter(e=>{if(0===e.indexOf("stun:"))return!1;let t=e.startsWith("turn")&&!e.startsWith("turn:[")&&e.includes("transport=udp");return t&&!o?(o=!0,!0):t&&!o}),delete e.url,e.urls=r?t[0]:t,!!t.length}})),u("ICE servers after filtering:",r.iceServers)}return new n(r)},e.RTCPeerConnection.prototype=n.prototype}function G(e){!e.RTCRtpSender||"replaceTrack"in e.RTCRtpSender.prototype||(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)}var U=Object.freeze({__proto__:null,shimPeerConnection:F,shimReplaceTrack:G,shimGetUserMedia:N,shimGetDisplayMedia:j});function B(e,t){let r=e&&e.navigator,n=e&&e.MediaStreamTrack;if(r.getUserMedia=function(e,t,n){h("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(e).then(t,n)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){let i=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},a=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(e){return"object"==typeof e&&"object"==typeof e.audio&&(i((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","mozAutoGainControl"),i(e.audio,"noiseSuppression","mozNoiseSuppression")),a(e)},n&&n.prototype.getSettings){let o=n.prototype.getSettings;n.prototype.getSettings=function(){let e=o.apply(this,arguments);return i(e,"mozAutoGainControl","autoGainControl"),i(e,"mozNoiseSuppression","noiseSuppression"),e}}if(n&&n.prototype.applyConstraints){let s=n.prototype.applyConstraints;n.prototype.applyConstraints=function(e){return"audio"===this.kind&&"object"==typeof e&&(i(e=JSON.parse(JSON.stringify(e)),"autoGainControl","mozAutoGainControl"),i(e,"noiseSuppression","mozNoiseSuppression")),s.apply(this,[e])}}}}function W(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function z(e,t){if("object"!=typeof e||!(e.RTCPeerConnection||e.mozRTCPeerConnection))return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){let r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}})[t]});let r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function e(){let[i,a,o]=arguments;return n.apply(this,[i||null]).then(e=>{if(t.version<53&&!a)try{e.forEach(e=>{e.type=r[e.type]||e.type})}catch(n){if("TypeError"!==n.name)throw n;e.forEach((t,n)=>{e.set(n,Object.assign({},t,{type:r[t.type]||t.type}))})}return e}).then(a,o)}}function V(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;let t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function e(){let r=t.apply(this,[]);return r.forEach(e=>e._pc=this),r});let r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function e(){let t=r.apply(this,arguments);return t._pc=this,t}),e.RTCRtpSender.prototype.getStats=function e(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function H(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;let t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function e(){let r=t.apply(this,[]);return r.forEach(e=>e._pc=this),r}),d(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function e(){return this._pc.getStats(this.track)}}function q(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function e(t){h("removeStream","removeTrack"),this.getSenders().forEach(e=>{e.track&&t.getTracks().includes(e.track)&&this.removeTrack(e)})})}function K(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function X(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function e(){this.setParametersPromises=[];let r=arguments[1],n=r&&"sendEncodings"in r;n&&r.sendEncodings.forEach(e=>{if("rid"in e&&!/^[a-z0-9]{0,16}$/i.test(e.rid))throw TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw RangeError("max_framerate must be >= 0.0")});let i=t.apply(this,arguments);if(n){let{sender:a}=i,o=a.getParameters();"encodings"in o&&(1!==o.encodings.length||0!==Object.keys(o.encodings[0]).length)||(o.encodings=r.sendEncodings,a.sendEncodings=r.sendEncodings,this.setParametersPromises.push(a.setParameters(o).then(()=>{delete a.sendEncodings}).catch(()=>{delete a.sendEncodings})))}return i})}function J(e){if(!("object"==typeof e&&e.RTCRtpSender))return;let t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function e(){let r=t.apply(this,arguments);return"encodings"in r||(r.encodings=[].concat(this.sendEncodings||[{}])),r})}function Q(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function e(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function Y(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function e(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}var Z=Object.freeze({__proto__:null,shimOnTrack:W,shimPeerConnection:z,shimSenderGetStats:V,shimReceiverGetStats:H,shimRemoveStream:q,shimRTCDataChannel:K,shimAddTransceiver:X,shimGetParameters:J,shimCreateOffer:Q,shimCreateAnswer:Y,shimGetUserMedia:B,shimGetDisplayMedia:function e(t,r){(!t.navigator.mediaDevices||!("getDisplayMedia"in t.navigator.mediaDevices))&&t.navigator.mediaDevices&&(t.navigator.mediaDevices.getDisplayMedia=function e(n){if(!(n&&n.video)){let i=new DOMException("getDisplayMedia without video constraints is undefined");return i.name="NotFoundError",i.code=8,Promise.reject(i)}return!0===n.video?n.video={mediaSource:r}:n.video.mediaSource=r,t.navigator.mediaDevices.getUserMedia(n)})}});function ee(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function e(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){let t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function e(r){this._localStreams||(this._localStreams=[]),this._localStreams.includes(r)||this._localStreams.push(r),r.getAudioTracks().forEach(e=>t.call(this,e,r)),r.getVideoTracks().forEach(e=>t.call(this,e,r))},e.RTCPeerConnection.prototype.addTrack=function e(r,...n){return n&&n.forEach(e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function e(t){this._localStreams||(this._localStreams=[]);let r=this._localStreams.indexOf(t);if(-1===r)return;this._localStreams.splice(r,1);let n=t.getTracks();this.getSenders().forEach(e=>{n.includes(e.track)&&this.removeTrack(e)})})}}function et(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function e(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach(e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);let t=new Event("addstream");t.stream=e,this.dispatchEvent(t)})})}});let t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function e(){let r=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach(e=>{if(r._remoteStreams||(r._remoteStreams=[]),r._remoteStreams.indexOf(e)>=0)return;r._remoteStreams.push(e);let t=new Event("addstream");t.stream=e,r.dispatchEvent(t)})}),t.apply(r,arguments)}}}function er(e){if("object"!=typeof e||!e.RTCPeerConnection)return;let t=e.RTCPeerConnection.prototype,r=t.createOffer,n=t.createAnswer,i=t.setLocalDescription,a=t.setRemoteDescription,o=t.addIceCandidate;t.createOffer=function e(t,n){let i=arguments.length>=2?arguments[2]:arguments[0],a=r.apply(this,[i]);return n?(a.then(t,n),Promise.resolve()):a},t.createAnswer=function e(t,r){let i=arguments.length>=2?arguments[2]:arguments[0],a=n.apply(this,[i]);return r?(a.then(t,r),Promise.resolve()):a};let s=function(e,t,r){let n=i.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n};t.setLocalDescription=s,s=function(e,t,r){let n=a.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.setRemoteDescription=s,s=function(e,t,r){let n=o.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.addIceCandidate=s}function en(e){let t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){let r=t.mediaDevices,n=r.getUserMedia.bind(r);t.mediaDevices.getUserMedia=e=>n(ei(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=(function e(r,n,i){t.mediaDevices.getUserMedia(r).then(n,i)}).bind(t))}function ei(e){return e&&void 0!==e.video?Object.assign({},e,{video:function e(t){return f(t)?Object.keys(t).reduce(function(r,n){let i=f(t[n]),a=i?e(t[n]):t[n],o=i&&!Object.keys(a).length;return void 0===a||o?r:Object.assign(r,{[n]:a})},{}):t}(e.video)}):e}function ea(e){if(!e.RTCPeerConnection)return;let t=e.RTCPeerConnection;e.RTCPeerConnection=function e(r,n){if(r&&r.iceServers){let i=[];for(let a=0;a<r.iceServers.length;a++){let o=r.iceServers[a];!o.hasOwnProperty("urls")&&o.hasOwnProperty("url")?(h("RTCIceServer.url","RTCIceServer.urls"),(o=JSON.parse(JSON.stringify(o))).urls=o.url,delete o.url,i.push(o)):i.push(r.iceServers[a])}r.iceServers=i}return new t(r,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function eo(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function es(e){let t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function e(r){if(r){void 0!==r.offerToReceiveAudio&&(r.offerToReceiveAudio=!!r.offerToReceiveAudio);let n=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind);!1===r.offerToReceiveAudio&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==r.offerToReceiveAudio||n||this.addTransceiver("audio"),void 0!==r.offerToReceiveVideo&&(r.offerToReceiveVideo=!!r.offerToReceiveVideo);let i=this.getTransceivers().find(e=>"video"===e.receiver.track.kind);!1===r.offerToReceiveVideo&&i?"sendrecv"===i.direction?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":"recvonly"===i.direction&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):!0!==r.offerToReceiveVideo||i||this.addTransceiver("video")}return t.apply(this,arguments)}}function ec(e){"object"==typeof e&&!e.AudioContext&&(e.AudioContext=e.webkitAudioContext)}var ed=Object.freeze({__proto__:null,shimLocalStreamsAPI:ee,shimRemoteStreamsAPI:et,shimCallbacksAPI:er,shimGetUserMedia:en,shimConstraints:ei,shimRTCIceServerUrls:ea,shimTrackEventTransceiver:eo,shimCreateOfferLegacy:es,shimAudioContext:ec});function ep(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;let t=e.RTCIceCandidate;e.RTCIceCandidate=function e(r){if("object"==typeof r&&r.candidate&&0===r.candidate.indexOf("a=")&&((r=JSON.parse(JSON.stringify(r))).candidate=r.candidate.substr(2)),r.candidate&&r.candidate.length){let n=new t(r),i=A.parseCandidate(r.candidate),a=Object.assign(n,i);return a.toJSON=function e(){return{candidate:a.candidate,sdpMid:a.sdpMid,sdpMLineIndex:a.sdpMLineIndex,usernameFragment:a.usernameFragment}},a}return new t(r)},e.RTCIceCandidate.prototype=t.prototype,d(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}function el(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});let r=function(e){if(!e||!e.sdp)return!1;let t=A.splitSections(e.sdp);return t.shift(),t.some(e=>{let t=A.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})},n=function(e){let t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return -1;let r=parseInt(t[1],10);return r!=r?-1:r},i=function(e){let r=65536;return"firefox"===t.browser&&(r=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),r},a=function(e,r){let n=65536;"firefox"===t.browser&&57===t.version&&(n=65535);let i=A.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?n=parseInt(i[0].substr(19),10):"firefox"===t.browser&&-1!==r&&(n=2147483637),n},o=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function e(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){let{sdpSemantics:s}=this.getConfiguration();"plan-b"===s&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(r(arguments[0])){let c=n(arguments[0]),d=i(c),p=a(arguments[0],c),l;l=0===d&&0===p?Number.POSITIVE_INFINITY:0===d||0===p?Math.max(d,p):Math.min(d,p);let u={};Object.defineProperty(u,"maxMessageSize",{get:()=>l}),this._sctp=u}return o.apply(this,arguments)}}function eu(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){let r=e.send;e.send=function n(){let i=arguments[0],a=i.length||i.size||i.byteLength;if("open"===e.readyState&&t.sctp&&a>t.sctp.maxMessageSize)throw TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return r.apply(e,arguments)}}let r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function e(){let n=r.apply(this,arguments);return t(n,this),n},d(e,"datachannel",e=>(t(e.channel,e.target),e))}function eh(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;let t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return({completed:"connected",checking:"connecting"})[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{let r=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{let t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;let r=new Event("connectionstatechange",e);t.dispatchEvent(r)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}})}function ef(e,t){if(!e.RTCPeerConnection||"chrome"===t.browser&&t.version>=71||"safari"===t.browser&&t.version>=605)return;let r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function t(n){if(n&&n.sdp&&-1!==n.sdp.indexOf("\na=extmap-allow-mixed")){let i=n.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n");e.RTCSessionDescription&&n instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:n.type,sdp:i}):n.sdp=i}return r.apply(this,arguments)}}function em(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;let r=e.RTCPeerConnection.prototype.addIceCandidate;r&&0!==r.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function e(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}var ev=Object.freeze({__proto__:null,shimRTCIceCandidate:ep,shimMaxMessageSize:el,shimSendThrowTypeError:eu,shimConnectionState:eh,removeExtmapAllowMixed:ef,shimAddIceCandidateNullOrEmpty:em});!function e({window:t}={},r={shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0}){let n=u,i=function e(t){let r={browser:null,version:null};if(void 0===t||!t.navigator)return r.browser="Not a browser.",r;let{navigator:n}=t;return n.mozGetUserMedia?(r.browser="firefox",r.version=c(n.userAgent,/Firefox\/(\d+)\./,1)):n.webkitGetUserMedia||!1===t.isSecureContext&&t.webkitRTCPeerConnection&&!t.RTCIceGatherer?(r.browser="chrome",r.version=c(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2)):n.mediaDevices&&n.userAgent.match(/Edge\/(\d+).(\d+)$/)?(r.browser="edge",r.version=c(n.userAgent,/Edge\/(\d+).(\d+)$/,2)):t.RTCPeerConnection&&n.userAgent.match(/AppleWebKit\/(\d+)\./)?(r.browser="safari",r.version=c(n.userAgent,/AppleWebKit\/(\d+)\./,1),r.supportsUnifiedPlan=t.RTCRtpTransceiver&&"currentDirection"in t.RTCRtpTransceiver.prototype):r.browser="Not a supported browser.",r}(t),a={browserDetails:i,commonShim:ev,extractVersion:c,disableLog:p,disableWarnings:l};switch(i.browser){case"chrome":if(!D||!P||!r.shimChrome){n("Chrome shim is not included in this adapter release.");break}if(null===i.version){n("Chrome shim can not determine version, not shimming.");break}n("adapter.js shimming chrome."),a.browserShim=D,em(t,i),g(t,i),C(t),P(t,i),y(t),$(t,i),T(t),R(t),S(t),b(t,i),ep(t),eh(t),el(t,i),eu(t),ef(t,i);break;case"firefox":if(!Z||!z||!r.shimFirefox){n("Firefox shim is not included in this adapter release.");break}n("adapter.js shimming firefox."),a.browserShim=Z,em(t,i),B(t,i),z(t,i),W(t),q(t),V(t),H(t),K(t),X(t),J(t),Q(t),Y(t),ep(t),eh(t),el(t,i),eu(t);break;case"edge":if(!U||!F||!r.shimEdge){n("MS edge shim is not included in this adapter release.");break}n("adapter.js shimming edge."),a.browserShim=U,N(t),j(t),F(t,i),G(t),el(t,i),eu(t);break;case"safari":if(!ed||!r.shimSafari){n("Safari shim is not included in this adapter release.");break}n("adapter.js shimming safari."),a.browserShim=ed,em(t,i),ea(t),es(t),er(t),ee(t),et(t),eo(t),en(t),ec(t),ep(t),el(t,i),eu(t),ef(t,i);break;default:n("Unsupported browser!")}return a}({window:"undefined"==typeof window?void 0:window});class eg{constructor(e){if(!Object.values(n).some(t=>t===e))throw TypeError("Invalid source.");this.source=e,this.deviceId=void 0}}class eC{constructor(e){if(!Object.values(i).some(t=>t===e))throw TypeError("Invalid source.");this.source=e,this.deviceId=void 0,this.resolution=void 0,this.frameRate=void 0}}class ey{constructor(e=!1,t=!1){this.audio=e,this.video=t}}function eT(e){return"object"==typeof e.video&&e.video.source===i.SCREENCAST}class eR{static createMediaStream(e){if("object"!=typeof e||!e.audio&&!e.video)return Promise.reject(TypeError("Invalid constrains"));if(!eT(e)&&"object"==typeof e.audio&&e.audio.source===n.SCREENCAST)return Promise.reject(TypeError("Cannot share screen without video."));if(eT(e)&&!(null!==window.navigator.userAgent.match("Chrome"))&&!r())return Promise.reject(TypeError("Screen sharing only supports Chrome and Firefox."));if(eT(e)&&"object"==typeof e.audio&&e.audio.source!==n.SCREENCAST)return Promise.reject(TypeError("Cannot capture video from screen cast while capture audio from other source."));if(!e.audio&&!e.video)return Promise.reject(TypeError("At least one of audio and video must be requested."));let t=Object.create({});return("object"==typeof e.audio&&e.audio.source===n.MIC?(t.audio=Object.create({}),null!==window.navigator.userAgent.match(/Edge\/(\d+).(\d+)$/)?t.audio.deviceId=e.audio.deviceId:t.audio.deviceId={exact:e.audio.deviceId}):e.audio.source===n.SCREENCAST?t.audio=!0:t.audio=e.audio,"object"==typeof e.video?(t.video=Object.create({}),"number"==typeof e.video.frameRate&&(t.video.frameRate=e.video.frameRate),e.video.resolution&&e.video.resolution.width&&e.video.resolution.height&&(e.video.source===i.SCREENCAST?(t.video.width=e.video.resolution.width,t.video.height=e.video.resolution.height):(t.video.width=Object.create({}),t.video.width.exact=e.video.resolution.width,t.video.height=Object.create({}),t.video.height.exact=e.video.resolution.height)),"string"==typeof e.video.deviceId&&(t.video.deviceId={exact:e.video.deviceId}),r()&&e.video.source===i.SCREENCAST&&(t.video.mediaSource="screen")):t.video=e.video,eT(e))?navigator.mediaDevices.getDisplayMedia(t):navigator.mediaDevices.getUserMedia(t)}}var eS=Object.freeze({__proto__:null,AudioTrackConstraints:eg,VideoTrackConstraints:eC,StreamConstraints:ey,MediaStreamFactory:eR,AudioSourceInfo:n,VideoSourceInfo:i,TrackKind:{AUDIO:"audio",VIDEO:"video",AUDIO_AND_VIDEO:"av"},Resolution:a});let eE,e$;function e8(e,...t){eE&&eE(e,...t)}function eP(e,...t){e$&&e$(e,...t)}class eb{constructor(e){this.listener={},this.type=""|e}on(e,t){return this.listener[e]||(this.listener[e]=[]),this.listener[e].push(t),!0}off(e,t){if(this.listener[e]){var r=this.listener[e].indexOf(t);return r>-1&&this.listener[e].splice(r,1),!0}return!1}offAll(){this.listener={}}dispatch(e,t){return!!this.listener[e]&&(this.listener[e].map(e=>{e.apply(null,[t])}),!0)}}var ew,ek=function e(t,r){return function e(){for(var n=Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];return t.apply(r,n)}},eD=Object.prototype.toString,eA=(ew=Object.create(null),function(e){var t=eD.call(e);return ew[t]||(ew[t]=t.slice(8,-1).toLowerCase())});function ex(e){return e=e.toLowerCase(),function t(r){return eA(r)===e}}function e_(e){return Array.isArray(e)}function eI(e){return void 0===e}var eM=ex("ArrayBuffer");function eO(e){return null!==e&&"object"==typeof e}function eL(e){if("object"!==eA(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}var eN=ex("Date"),ej=ex("File"),eF=ex("Blob"),eG=ex("FileList");function eU(e){return"[object Function]"===eD.call(e)}var eB=ex("URLSearchParams");function e0(e,t){if(null!=e){if("object"!=typeof e&&(e=[e]),e_(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}}var eW,ez=(eW="undefined"!=typeof Uint8Array&&Object.getPrototypeOf(Uint8Array),function(e){return eW&&e instanceof eW}),e3={isArray:e_,isArrayBuffer:eM,isBuffer:function e(t){return null!==t&&!eI(t)&&null!==t.constructor&&!eI(t.constructor)&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)},isFormData:function e(t){var r="[object FormData]";return t&&("function"==typeof FormData&&t instanceof FormData||eD.call(t)===r||eU(t.toString)&&t.toString()===r)},isArrayBufferView:function e(t){var r;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&eM(t.buffer)},isString:function e(t){return"string"==typeof t},isNumber:function e(t){return"number"==typeof t},isObject:eO,isPlainObject:eL,isUndefined:eI,isDate:eN,isFile:ej,isBlob:eF,isFunction:eU,isStream:function e(t){return eO(t)&&eU(t.pipe)},isURLSearchParams:eB,isStandardBrowserEnv:function e(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:e0,merge:function e(){var t={};function r(r,n){eL(t[n])&&eL(r)?t[n]=e(t[n],r):eL(r)?t[n]=e({},r):e_(r)?t[n]=r.slice():t[n]=r}for(var n=0,i=arguments.length;n<i;n++)e0(arguments[n],r);return t},extend:function e(t,r,n){return e0(r,function e(r,i){n&&"function"==typeof r?t[i]=ek(r,n):t[i]=r}),t},trim:function e(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")},stripBOM:function e(t){return 65279===t.charCodeAt(0)&&(t=t.slice(1)),t},inherits:function e(t,r,n,i){t.prototype=Object.create(r.prototype,i),t.prototype.constructor=t,n&&Object.assign(t.prototype,n)},toFlatObject:function e(t,r,n){var i,a,o,s={};r=r||{};do{for(a=(i=Object.getOwnPropertyNames(t)).length;a-- >0;)s[o=i[a]]||(r[o]=t[o],s[o]=!0);t=Object.getPrototypeOf(t)}while(t&&(!n||n(t,r))&&t!==Object.prototype);return r},kindOf:eA,kindOfTest:ex,endsWith:function e(t,r,n){t=String(t),(void 0===n||n>t.length)&&(n=t.length),n-=r.length;var i=t.indexOf(r,n);return -1!==i&&i===n},toArray:function e(t){if(!t)return null;var r=t.length;if(eI(r))return null;for(var n=Array(r);r-- >0;)n[r]=t[r];return n},isTypedArray:ez,isFileList:eG};function eV(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var e4=function e(t,r,n){if(!r)return t;if(n)i=n(r);else if(e3.isURLSearchParams(r))i=r.toString();else{var i,a=[];e3.forEach(r,function e(t,r){null!=t&&(e3.isArray(t)?r+="[]":t=[t],e3.forEach(t,function e(t){e3.isDate(t)?t=t.toISOString():e3.isObject(t)&&(t=JSON.stringify(t)),a.push(eV(r)+"="+eV(t))}))}),i=a.join("&")}if(i){var o=t.indexOf("#");-1!==o&&(t=t.slice(0,o)),t+=(-1===t.indexOf("?")?"?":"&")+i}return t};function e2(){this.handlers=[]}e2.prototype.use=function e(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},e2.prototype.eject=function e(t){this.handlers[t]&&(this.handlers[t]=null)},e2.prototype.forEach=function e(t){e3.forEach(this.handlers,function e(r){null!==r&&t(r)})};var e1=e2,eH=function e(t,r){e3.forEach(t,function e(n,i){i!==r&&i.toUpperCase()===r.toUpperCase()&&(t[r]=n,delete t[i])})};function e5(e,t,r,n,i){Error.call(this),this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i)}e3.inherits(e5,Error,{toJSON:function e(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var e6=e5.prototype,eq={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED"].forEach(function(e){eq[e]={value:e}}),Object.defineProperties(e5,eq),Object.defineProperty(e6,"isAxiosError",{value:!0}),e5.from=function(e,t,r,n,i,a){var o=Object.create(e6);return e3.toFlatObject(e,o,function e(t){return t!==Error.prototype}),e5.call(o,e.message,t,r,n,i),o.name=e.name,a&&Object.assign(o,a),o};var e7=e5,eK={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},eX=function e(t,r){r=r||new FormData;var n=[];function i(e){return null===e?"":e3.isDate(e)?e.toISOString():e3.isArrayBuffer(e)||e3.isTypedArray(e)?"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}return!function e(t,a){if(e3.isPlainObject(t)||e3.isArray(t)){if(-1!==n.indexOf(t))throw Error("Circular reference detected in "+a);n.push(t),e3.forEach(t,function t(n,o){if(!e3.isUndefined(n)){var s,c=a?a+"."+o:o;if(n&&!a&&"object"==typeof n){if(e3.endsWith(o,"{}"))n=JSON.stringify(n);else if(e3.endsWith(o,"[]")&&(s=e3.toArray(n))){s.forEach(function(e){e3.isUndefined(e)||r.append(c,i(e))});return}}e(n,c)}}),n.pop()}else r.append(a,i(t))}(t),r},eJ=function e(t,r,n){var i=n.config.validateStatus;!n.status||!i||i(n.status)?t(n):r(new e7("Request failed with status code "+n.status,[e7.ERR_BAD_REQUEST,e7.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))},e9=e3.isStandardBrowserEnv()?{write:function e(t,r,n,i,a,o){var s=[];s.push(t+"="+encodeURIComponent(r)),e3.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),e3.isString(i)&&s.push("path="+i),e3.isString(a)&&s.push("domain="+a),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read:function e(t){var r=document.cookie.match(RegExp("(^|;\\s*)("+t+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function e(t){this.write(t,"",Date.now()-864e5)}}:{write:function e(){},read:function e(){return null},remove:function e(){}},eQ=function e(t,r){var n,i,a;return t&&(n=r,!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n))?(i=t,(a=r)?i.replace(/\/+$/,"")+"/"+a.replace(/^\/+/,""):i):r},eY=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],eZ=function e(t){var r,n,i,a={};return t&&e3.forEach(t.split("\n"),function e(t){i=t.indexOf(":"),r=e3.trim(t.substr(0,i)).toLowerCase(),n=e3.trim(t.substr(i+1)),r&&!(a[r]&&eY.indexOf(r)>=0)&&("set-cookie"===r?a[r]=(a[r]?a[r]:[]).concat([n]):a[r]=a[r]?a[r]+", "+n:n)}),a},te=e3.isStandardBrowserEnv()?function e(){var t,r=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function i(e){var t=e;return r&&(n.setAttribute("href",t),t=n.href),n.setAttribute("href",t),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return t=i(window.location.href),function e(r){var n=e3.isString(r)?i(r):r;return n.protocol===t.protocol&&n.host===t.host}}():function e(){return!0};function tt(e){e7.call(this,null==e?"canceled":e,e7.ERR_CANCELED),this.name="CanceledError"}e3.inherits(tt,e7,{__CANCEL__:!0});var tr=tt,tn=function e(t){var r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return r&&r[1]||""},ti=function e(t){return new Promise(function e(r,n){var i,a=t.data,o=t.headers,s=t.responseType;function c(){t.cancelToken&&t.cancelToken.unsubscribe(i),t.signal&&t.signal.removeEventListener("abort",i)}e3.isFormData(a)&&e3.isStandardBrowserEnv()&&delete o["Content-Type"];var d=new XMLHttpRequest;if(t.auth){var p=t.auth.username||"",l=t.auth.password?unescape(encodeURIComponent(t.auth.password)):"";o.Authorization="Basic "+btoa(p+":"+l)}var u=eQ(t.baseURL,t.url);function h(){if(d){var e="getAllResponseHeaders"in d?eZ(d.getAllResponseHeaders()):null,i={data:s&&"text"!==s&&"json"!==s?d.response:d.responseText,status:d.status,statusText:d.statusText,headers:e,config:t,request:d};eJ(function e(t){r(t),c()},function e(t){n(t),c()},i),d=null}}if(d.open(t.method.toUpperCase(),e4(u,t.params,t.paramsSerializer),!0),d.timeout=t.timeout,"onloadend"in d?d.onloadend=h:d.onreadystatechange=function e(){d&&4===d.readyState&&(0!==d.status||d.responseURL&&0===d.responseURL.indexOf("file:"))&&setTimeout(h)},d.onabort=function e(){d&&(n(new e7("Request aborted",e7.ECONNABORTED,t,d)),d=null)},d.onerror=function e(){n(new e7("Network Error",e7.ERR_NETWORK,t,d,d)),d=null},d.ontimeout=function e(){var r=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded",i=t.transitional||eK;t.timeoutErrorMessage&&(r=t.timeoutErrorMessage),n(new e7(r,i.clarifyTimeoutError?e7.ETIMEDOUT:e7.ECONNABORTED,t,d)),d=null},e3.isStandardBrowserEnv()){var f=(t.withCredentials||te(u))&&t.xsrfCookieName?e9.read(t.xsrfCookieName):void 0;f&&(o[t.xsrfHeaderName]=f)}"setRequestHeader"in d&&e3.forEach(o,function e(t,r){void 0===a&&"content-type"===r.toLowerCase()?delete o[r]:d.setRequestHeader(r,t)}),e3.isUndefined(t.withCredentials)||(d.withCredentials=!!t.withCredentials),s&&"json"!==s&&(d.responseType=t.responseType),"function"==typeof t.onDownloadProgress&&d.addEventListener("progress",t.onDownloadProgress),"function"==typeof t.onUploadProgress&&d.upload&&d.upload.addEventListener("progress",t.onUploadProgress),(t.cancelToken||t.signal)&&(i=function(e){d&&(n(!e||e&&e.type?new tr:e),d.abort(),d=null)},t.cancelToken&&t.cancelToken.subscribe(i),t.signal&&(t.signal.aborted?i():t.signal.addEventListener("abort",i))),a||(a=null);var m=tn(u);if(m&&-1===["http","https","file"].indexOf(m)){n(new e7("Unsupported protocol "+m+":",e7.ERR_BAD_REQUEST,t));return}d.send(a)})},ta={"Content-Type":"application/x-www-form-urlencoded"};function to(e,t){!e3.isUndefined(e)&&e3.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var ts,tc={transitional:eK,adapter:("undefined"!=typeof XMLHttpRequest?ts=ti:"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process)&&(ts=ti),ts),transformRequest:[function e(t,r){if(eH(r,"Accept"),eH(r,"Content-Type"),e3.isFormData(t)||e3.isArrayBuffer(t)||e3.isBuffer(t)||e3.isStream(t)||e3.isFile(t)||e3.isBlob(t))return t;if(e3.isArrayBufferView(t))return t.buffer;if(e3.isURLSearchParams(t))return to(r,"application/x-www-form-urlencoded;charset=utf-8"),t.toString();var n,i=e3.isObject(t),a=r&&r["Content-Type"];if((n=e3.isFileList(t))||i&&"multipart/form-data"===a){var o=this.env&&this.env.FormData;return eX(n?{"files[]":t}:t,o&&new o)}return i||"application/json"===a?(to(r,"application/json"),function e(t,r,n){if(e3.isString(t))try{return(0,JSON.parse)(t),e3.trim(t)}catch(i){if("SyntaxError"!==i.name)throw i}return(0,JSON.stringify)(t)}(t)):t}],transformResponse:[function e(t){var r=this.transitional||tc.transitional,n=r&&r.silentJSONParsing,i=r&&r.forcedJSONParsing,a=!n&&"json"===this.responseType;if(a||i&&e3.isString(t)&&t.length)try{return JSON.parse(t)}catch(o){if(a){if("SyntaxError"===o.name)throw e7.from(o,e7.ERR_BAD_RESPONSE,this,null,this.response);throw o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:null},validateStatus:function e(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};e3.forEach(["delete","get","head"],function e(t){tc.headers[t]={}}),e3.forEach(["post","put","patch"],function e(t){tc.headers[t]=e3.merge(ta)});var td=tc,tp=function e(t,r,n){var i=this||td;return e3.forEach(n,function e(n){t=n.call(i,t,r)}),t},tl=function e(t){return!!(t&&t.__CANCEL__)};function tu(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new tr}var th=function e(t){return tu(t),t.headers=t.headers||{},t.data=tp.call(t,t.data,t.headers,t.transformRequest),t.headers=e3.merge(t.headers.common||{},t.headers[t.method]||{},t.headers),e3.forEach(["delete","get","head","post","put","patch","common"],function e(r){delete t.headers[r]}),(t.adapter||td.adapter)(t).then(function e(r){return tu(t),r.data=tp.call(t,r.data,r.headers,t.transformResponse),r},function e(r){return!tl(r)&&(tu(t),r&&r.response&&(r.response.data=tp.call(t,r.response.data,r.response.headers,t.transformResponse))),Promise.reject(r)})},tf=function e(t,r){r=r||{};var n={};function i(e,t){return e3.isPlainObject(e)&&e3.isPlainObject(t)?e3.merge(e,t):e3.isPlainObject(t)?e3.merge({},t):e3.isArray(t)?t.slice():t}function a(e){return e3.isUndefined(r[e])?e3.isUndefined(t[e])?void 0:i(void 0,t[e]):i(t[e],r[e])}function o(e){if(!e3.isUndefined(r[e]))return i(void 0,r[e])}function s(e){return e3.isUndefined(r[e])?e3.isUndefined(t[e])?void 0:i(void 0,t[e]):i(void 0,r[e])}function c(e){return e in r?i(t[e],r[e]):e in t?i(void 0,t[e]):void 0}var d={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:c};return e3.forEach(Object.keys(t).concat(Object.keys(r)),function e(t){var r=d[t]||a,i=r(t);e3.isUndefined(i)&&r!==c||(n[t]=i)}),n},tm={version:"0.27.2"},tv=tm.version,tg={};["object","boolean","number","function","string","symbol"].forEach(function(e,t){tg[e]=function r(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});var tC={};tg.transitional=function e(t,r,n){function i(e,t){return"[Axios v"+tv+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(e,n,a){if(!1===t)throw new e7(i(n," has been removed"+(r?" in "+r:"")),e7.ERR_DEPRECATED);return r&&!tC[n]&&(tC[n]=!0,console.warn(i(n," has been deprecated since v"+r+" and will be removed in the near future"))),!t||t(e,n,a)}};var ty={assertOptions:function e(t,r,n){if("object"!=typeof t)throw new e7("options must be an object",e7.ERR_BAD_OPTION_VALUE);for(var i=Object.keys(t),a=i.length;a-- >0;){var o=i[a],s=r[o];if(s){var c=t[o],d=void 0===c||s(c,o,t);if(!0!==d)throw new e7("option "+o+" must be "+d,e7.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new e7("Unknown option "+o,e7.ERR_BAD_OPTION)}},validators:tg},tT=ty.validators;function tR(e){this.defaults=e,this.interceptors={request:new e1,response:new e1}}tR.prototype.request=function e(t,r){"string"==typeof t?(r=r||{}).url=t:r=t||{},(r=tf(this.defaults,r)).method?r.method=r.method.toLowerCase():this.defaults.method?r.method=this.defaults.method.toLowerCase():r.method="get";var n,i=r.transitional;void 0!==i&&ty.assertOptions(i,{silentJSONParsing:tT.transitional(tT.boolean),forcedJSONParsing:tT.transitional(tT.boolean),clarifyTimeoutError:tT.transitional(tT.boolean)},!1);var a=[],o=!0;this.interceptors.request.forEach(function e(t){("function"!=typeof t.runWhen||!1!==t.runWhen(r))&&(o=o&&t.synchronous,a.unshift(t.fulfilled,t.rejected))});var s=[];if(this.interceptors.response.forEach(function e(t){s.push(t.fulfilled,t.rejected)}),!o){var c=[th,void 0];for(Array.prototype.unshift.apply(c,a),c=c.concat(s),n=Promise.resolve(r);c.length;)n=n.then(c.shift(),c.shift());return n}for(var d=r;a.length;){var p=a.shift(),l=a.shift();try{d=p(d)}catch(u){l(u);break}}try{n=th(d)}catch(h){return Promise.reject(h)}for(;s.length;)n=n.then(s.shift(),s.shift());return n},tR.prototype.getUri=function e(t){t=tf(this.defaults,t);var r=eQ(t.baseURL,t.url);return e4(r,t.params,t.paramsSerializer)},e3.forEach(["delete","get","head","options"],function e(t){tR.prototype[t]=function(e,r){return this.request(tf(r||{},{method:t,url:e,data:(r||{}).data}))}}),e3.forEach(["post","put","patch"],function e(t){function r(e){return function r(n,i,a){return this.request(tf(a||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:n,data:i}))}}tR.prototype[t]=r(),tR.prototype[t+"Form"]=r(!0)});var tS=tR;function tE(e){if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function e(r){t=r});var t,r=this;this.promise.then(function(e){if(r._listeners){var t,n=r._listeners.length;for(t=0;t<n;t++)r._listeners[t](e);r._listeners=null}}),this.promise.then=function(e){var t,n=new Promise(function(e){r.subscribe(e),t=e}).then(e);return n.cancel=function e(){r.unsubscribe(t)},n},e(function e(n){!r.reason&&(r.reason=new tr(n),t(r.reason))})}tE.prototype.throwIfRequested=function e(){if(this.reason)throw this.reason},tE.prototype.subscribe=function e(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]},tE.prototype.unsubscribe=function e(t){if(this._listeners){var r=this._listeners.indexOf(t);-1!==r&&this._listeners.splice(r,1)}},tE.source=function e(){var t;return{token:new tE(function e(r){t=r}),cancel:t}};var t$=function e(t){return function e(r){return t.apply(null,r)}},t8=function e(t){return e3.isObject(t)&&!0===t.isAxiosError},tP=function e(t){var r=new tS(t),n=ek(tS.prototype.request,r);return e3.extend(n,tS.prototype,r),e3.extend(n,r),n.create=function r(n){return e(tf(t,n))},n}(td);tP.Axios=tS,tP.CanceledError=tr,tP.CancelToken=tE,tP.isCancel=tl,tP.VERSION=tm.version,tP.toFormData=eX,tP.AxiosError=e7,tP.Cancel=tP.CanceledError,tP.all=function e(t){return Promise.all(t)},tP.spread=t$,tP.isAxiosError=t8;var tb=tP;tb.default=tP;var tw=tb;let tk=[{label:"4K(UHD)",width:3840,height:2160},{label:"1080p(FHD)",width:1920,height:1080},{label:"UXGA",width:1600,height:1200,ratio:"4:3"},{label:"720p(HD)",width:1280,height:720},{label:"SVGA",width:800,height:600},{label:"VGA",width:640,height:480},{label:"360p(nHD)",width:640,height:360},{label:"CIF",width:352,height:288},{label:"QVGA",width:320,height:240},{label:"QCIF",width:176,height:144},{label:"QQVGA",width:160,height:120}];function tD(){return tk}console.log("build date:","Mon Jul 04 2022 19:50:55 GMT+0800 (China Standard Time)"),console.log("version:","1.0.1");let tA=class e extends eb{constructor(e){super("RTCPusherPlayer"),this.TAG="[RTCPusherPlayer]",this.options=Object.assign({},{element:"",debug:!1,zlmsdpUrl:"",simulcast:!1,useCamera:!0,audioEnable:!0,videoEnable:!0,recvOnly:!1,resolution:{w:0,h:0},usedatachannel:!1},e),this.options.debug&&(eE=console.log,e$=console.error),this.e={onicecandidate:this._onIceCandidate.bind(this),ontrack:this._onTrack.bind(this),onicecandidateerror:this._onIceCandidateError.bind(this),onconnectionstatechange:this._onconnectionstatechange.bind(this),ondatachannelopen:this._onDataChannelOpen.bind(this),ondatachannelmsg:this._onDataChannelMsg.bind(this),ondatachannelerr:this._onDataChannelErr.bind(this),ondatachannelclose:this._onDataChannelClose.bind(this)},this._remoteStream=null,this._localStream=null,this.pc=new RTCPeerConnection(null),this.pc.onicecandidate=this.e.onicecandidate,this.pc.onicecandidateerror=this.e.onicecandidateerror,this.pc.ontrack=this.e.ontrack,this.pc.onconnectionstatechange=this.e.onconnectionstatechange,this.datachannel=null,this.options.usedatachannel&&(this.datachannel=this.pc.createDataChannel("chat"),this.datachannel.onclose=this.e.ondatachannelclose,this.datachannel.onerror=this.e.ondatachannelerr,this.datachannel.onmessage=this.e.ondatachannelmsg,this.datachannel.onopen=this.e.ondatachannelopen),!this.options.recvOnly&&(this.options.audioEnable||this.options.videoEnable)?this.start():this.receive()}receive(){this.options.videoEnable&&this.pc.addTransceiver("video",{direction:"recvonly",sendEncodings:[]}),this.options.audioEnable&&this.pc.addTransceiver("audio",{direction:"recvonly",sendEncodings:[]}),this.pc.createOffer().then(e=>{e8(this.TAG,"offer:",e.sdp),this.pc.setLocalDescription(e).then(()=>{tw({method:"post",url:this.options.zlmsdpUrl,responseType:"json",data:e.sdp,headers:{"Content-Type":"text/plain;charset=utf-8"}}).then(e=>{let r=e.data;if(0!=r.code){this.dispatch(t.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,r);return}let n={};n.sdp=r.sdp,n.type="answer",e8(this.TAG,"answer:",r.sdp),this.pc.setRemoteDescription(n).then(()=>{e8(this.TAG,"set remote sucess")}).catch(e=>{eP(this.TAG,e)})})})}).catch(e=>{eP(this.TAG,e)})}start(){let e=!1,r=!1;this.options.useCamera?(this.options.videoEnable&&(e=new eC(i.CAMERA)),this.options.audioEnable&&(r=new eg(n.MIC))):this.options.videoEnable?(e=new eC(i.SCREENCAST),this.options.audioEnable&&(r=new eg(n.SCREENCAST))):this.options.audioEnable?r=new eg(n.MIC):eP(this.TAG,"error paramter"),0!=this.options.resolution.w&&0!=this.options.resolution.h&&"object"==typeof e&&(e.resolution=new a(this.options.resolution.w,this.options.resolution.h)),eR.createMediaStream(new ey(r,e)).then(e=>{this._localStream=e,this.dispatch(t.WEBRTC_ON_LOCAL_STREAM,e);let r={direction:"sendrecv",sendEncodings:[]},n={direction:"sendrecv",sendEncodings:[]};this.options.simulcast&&e.getVideoTracks().length>0&&(n.sendEncodings=[{rid:"h",active:!0,maxBitrate:1e6},{rid:"m",active:!0,maxBitrate:5e5,scaleResolutionDownBy:2},{rid:"l",active:!0,maxBitrate:2e5,scaleResolutionDownBy:4}]),this.options.audioEnable&&(e.getAudioTracks().length>0?this.pc.addTransceiver(e.getAudioTracks()[0],r):(r.direction="recvonly",this.pc.addTransceiver("audio",r))),this.options.videoEnable&&(e.getVideoTracks().length>0?this.pc.addTransceiver(e.getVideoTracks()[0],n):(n.direction="recvonly",this.pc.addTransceiver("video",n))),this.pc.createOffer().then(e=>{e8(this.TAG,"offer:",e.sdp),this.pc.setLocalDescription(e).then(()=>{tw({method:"post",url:this.options.zlmsdpUrl,responseType:"json",data:e.sdp,headers:{"Content-Type":"text/plain;charset=utf-8"}}).then(e=>{let r=e.data;if(0!=r.code){this.dispatch(t.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,r);return}let n={};n.sdp=r.sdp,n.type="answer",e8(this.TAG,"answer:",r.sdp),this.pc.setRemoteDescription(n).then(()=>{e8(this.TAG,"set remote sucess")}).catch(e=>{eP(this.TAG,e)})})})}).catch(e=>{eP(this.TAG,e)})}).catch(e=>{this.dispatch(t.CAPTURE_STREAM_FAILED)})}_onIceCandidate(e){e.candidate&&e8("Remote ICE candidate: \n "+e.candidate.candidate)}_onTrack(e){this.options.element&&e.streams&&e.streams.length>0?(this.options.element.srcObject=e.streams[0],this._remoteStream=e.streams[0],this.dispatch(t.WEBRTC_ON_REMOTE_STREAMS,e)):eP("element pararm is failed")}_onIceCandidateError(e){this.dispatch(t.WEBRTC_ICE_CANDIDATE_ERROR,e)}_onconnectionstatechange(e){this.dispatch(t.WEBRTC_ON_CONNECTION_STATE_CHANGE,this.pc.connectionState)}_onDataChannelOpen(e){e8(this.TAG,"ondatachannel open:",e),this.dispatch(t.WEBRTC_ON_DATA_CHANNEL_OPEN,e)}_onDataChannelMsg(e){e8(this.TAG,"ondatachannel msg:",e),this.dispatch(t.WEBRTC_ON_DATA_CHANNEL_MSG,e)}_onDataChannelErr(e){e8(this.TAG,"ondatachannel err:",e),this.dispatch(t.WEBRTC_ON_DATA_CHANNEL_ERR,e)}_onDataChannelClose(e){e8(this.TAG,"ondatachannel close:",e),this.dispatch(t.WEBRTC_ON_DATA_CHANNEL_CLOSE,e)}sendMsg(e){null!=this.datachannel?this.datachannel.send(e):eP(this.TAG,"data channel is null")}closeDataChannel(){this.datachannel&&(this.datachannel.close(),this.datachannel=null)}close(){this.closeDataChannel(),this.pc&&(this.pc.close(),this.pc=null),this.options&&(this.options=null),this._localStream&&this._localStream.getTracks().forEach((e,t)=>{e.stop()}),this._remoteStream&&this._remoteStream.getTracks().forEach((e,t)=>{e.stop()})}get remoteStream(){return this._remoteStream}get localStream(){return this._localStream}},tx=function e(){return new Promise(function(e,t){let r=[],n=0,o=0;for(let s=0;s<tk.length;++s){let c=new eC(i.CAMERA);c.resolution=new a(tk[s].width,tk[s].height),eR.createMediaStream(new ey(!1,c)).then(t=>{r.push(tk[s]),++n+o==tk.length&&e(r)}).catch(t=>{n+ ++o==tk.length&&e(r)})}})},t_=function e(t,r){return new Promise(function(e,n){let o=new eC(i.CAMERA);o.resolution=new a(t,r),eR.createMediaStream(new ey(!1,o)).then(t=>{e()}).catch(e=>{n(e)})})};return e.Endpoint=tA,e.Events=t,e.GetAllScanResolution=tD,e.GetSupportCameraResolutions=tx,e.Media=eS,e.isSupportResolution=t_,Object.defineProperty(e,"__esModule",{value:!0}),e}({});