<!doctype html><html lang="en"><head><meta charset="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=Edge"/><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no"><meta name="format-detection" content="telephone=no,email=no"/><title>亲友守护服务</title><script defer="defer" src="/static/static/js/lib-polyfill.js"></script><script defer="defer" src="/static/static/js/lib-axios.js"></script><script defer="defer" src="/static/static/js/784.js"></script><script defer="defer" src="/static/static/js/friend-guard/friend-guard.js"></script><link href="/static/static/css/784.4d8a338c.css" rel="stylesheet"><link href="/static/static/css/friend-guard/friend-guard.cc788111.css" rel="stylesheet"></head><body><div id="app"></div><script type="text/x-template" id="x-template"><div class="page-friend-guard">
      <div class="page-friend-guard-top">
        <img class="friend-guard-title" :src="titleImg" alt="">
        <!-- <button @click="sendMessage">发送消息</button> -->
        <div class="friend-guard-expire">
          <span>距离活动结束:</span>
          <span class="expire-label-digital">{{days}}</span>
          <span>天</span>
          <span class="expire-label-digital">{{hours<10?'0'+hours:hours}}</span>
          <span>时</span>
          <span class="expire-label-digital">{{mins<10?'0'+mins:mins}}</span>
          <span>分</span>
          <!-- {{seconds}} -->
        </div>
        <a href="../friend-guard-rules/index.html" class="friend-guard-activity-btn">活动规则<span class="friend-guard-activity-btn-angle"></span></a>
      </div>
      <div class="page-friend-guard-content">
        <div class="detail-container">
          <div class="detail-container-title">
            <img class="icon-line-bias" :src="iconLineBias" alt="">
            <span>亲友守护服务详情</span>
            <img class="icon-line-bias" :src="iconLineBiasRight" alt="">
          </div>
          <div class="detail-list">
            <div class="detail-list-item" v-for="item in lists" :key="item.id">
              <div class="detail-list-img-container">
                <img class="detail-list-item-icon" :src="item.icon" alt="icon">
                <img v-if="item.tips" class="detail-list-item-tip" :src="item.tips" alt="tips">
              </div>
              <span class="detail-list-item-title">{{item.title}}</span>
              <span class="detail-list-item-desc">{{item.desc}}</span>
            </div>
          </div>
          <button class="friend-guard-bottom-btn"  @click="sendMessage">立即守护亲友</button>
        </div>
      </div>
    </div></script></body></html>