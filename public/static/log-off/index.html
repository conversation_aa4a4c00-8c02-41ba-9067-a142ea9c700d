<!doctype html><html lang="en"><head><meta charset="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=Edge"/><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no"/><meta name="format-detection" content="telephone=no,email=no"/><title>立即定位平台</title><script defer="defer" src="/static/static/js/lib-polyfill.js"></script><script defer="defer" src="/static/static/js/lib-axios.js"></script><script defer="defer" src="/static/static/js/784.js"></script><script defer="defer" src="/static/static/js/983.js"></script><script defer="defer" src="/static/static/js/869.js"></script><script defer="defer" src="/static/static/js/log-off/log-off.js"></script><link href="/static/static/css/784.4d8a338c.css" rel="stylesheet"><link href="/static/static/css/983.dc15c55a.css" rel="stylesheet"><link href="/static/static/css/869.c1c38de2.css" rel="stylesheet"><link href="/static/static/css/log-off/log-off.035e4fac.css" rel="stylesheet"></head><body><div id="app"></div><script type="text/x-template" id="x-template"><div class="c-container" v-loading="loading">
        <div class="c-head" :style="{ backgroundImage: `url(${headerBg})` }">
          <el-dropdown class="c-head-select" v-model="language">
          <span class="el-dropdown-link">
            {{ languages[language] }}<i class="el-icon-caret-bottom"></i>
          </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="key in Object.keys(languages)" :key="key" :value="key" @click.native="handleMenuClick(key)">{{ languages[key] }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="c-tip">
          <div class="c-tip-left">
            <svg width="24px" height="24px" viewBox="0 0 24 24">
              <g id="账号注销" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g transform="translate(-520.000000, -226.000000)" id="Feedback/Alert/Large/Warning">
                  <g transform="translate(496.000000, 210.000000)">
                    <g id="编组" transform="translate(24.000000, 16.000000)">
                      <g id="通用图标/info" fill="#FFFFFF" opacity="0">
                        <rect id="Rectangle" x="0" y="0" width="24" height="24"></rect>
                      </g>
                      <circle id="Mask" stroke="#FFA235" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" cx="12" cy="12" r="10.9090909"></circle>
                      <line x1="11.9954545" y1="5.45454545" x2="11.9954545" y2="15.4545455" id="Path" stroke="#FFA235" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></line>
                      <circle id="Oval" stroke="#FFA120" fill="#FFA235" cx="12" cy="17.9545455" r="1"></circle>
                    </g>
                  </g>
                </g>
              </g>
            </svg>
          </div>
          <div class="c-tip-right">
            <div class="c-tip-right__title">{{ $t('tipTitle') }}</div>
            <div class="c-tip-right__message">{{ $t('tipContent') }}</div>
          </div>
        </div>
        <div class="c-step">
          <div class="c-step-item">
            <div class="c-step-item__circle" :class="[isHasUserInfo ? 'c-step-item__circle--active': '']">
              <template v-if="!isHasUserInfo">1</template>
              <template v-else>
                <i style="color: #006AFF; font-weight: 600;" class="el-icon-check"></i>
              </template>
            </div>
            <div class="c-step-item__title">{{ $t('cancellationApply') }}</div>
          </div>
          <div class="c-step-item">
            <div class="c-step-item__circle c-step-item__circle__dashed" :class="[!isHasUserInfo ? 'c-step-item__circle--no-active' : '']">
              2
            </div>
            <div class="c-step-item__title c-step-item__title--active">{{ $t('contactTheService') }}</div>
          </div>
          <div class="c-step-item">
            <div class="c-step-item__circle c-step-item__circle--no-active">3</div>
            <div class="c-step-item__title c-step-item__title--no-active">{{ $t('accountCancellation') }}</div>
          </div>
        </div>
        <div class="c-login">
          <template v-if="!Object.keys(userInfo).length">
            <div class="c-login-title">{{ $t('accountCancellation') }}</div>
            <div class="c-login-form">
              <el-input style="margin-bottom: 24px" v-model="form.name">
                <template #prefix>
                  <span v-if="isCn">{{ $t('user') }}:</span>
                  <span class="swd-icon" v-else>
                    <svg width="15px" height="15px" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <g id="账号注销" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="账号注销-其他语言" transform="translate(-738.000000, -568.000000)" fill="#9C9C9C" fill-rule="nonzero">
                                <g id="编组备份-2" transform="translate(738.000000, 568.000000)">
                                    <path d="M14.3250868,12.03125 C13.9496528,11.1436632 13.4136285,10.3493924 12.7300347,9.******** C12.046441,8.******** 11.25,8.******** 10.3624132,8.07074653 C10.141059,7.97743056 9.91536458,7.89496528 9.68967014,7.82335069 C10.8723958,7.0703125 11.6601563,5.74652778 11.6601563,4.24479167 C11.6601563,1.90755208 9.75911458,0.00434027778 7.41970486,0.00434027778 C5.08246528,0.00434027778 3.17925347,1.90538194 3.17925347,4.24479167 C3.17925347,5.76171875 3.98003472,7.09418403 5.18012153,7.84288194 C4.97178819,7.91015625 4.76779514,7.98611111 4.56597222,8.07074653 C3.67838542,8.******** 2.88411458,8.******** 2.20052083,9.******** C1.51692708,10.3493924 0.980902778,11.1458333 0.60546875,12.03125 C0.217013889,12.9492188 0.01953125,13.9236111 0.01953125,14.9305556 L1.01779514,14.9305556 C1.01779514,11.3758681 3.91059028,8.48307292 7.46527778,8.48307292 C11.0199653,8.48307292 13.9127604,11.3758681 13.9127604,14.9305556 L14.9110243,14.9305556 C14.9110243,13.9236111 14.7135417,12.9492188 14.3250868,12.03125 Z M4.1796875,4.24262153 C4.1796875,2.45442708 5.63368056,1.00043403 7.421875,1.00043403 C9.21006944,1.00043403 10.6640625,2.45442708 10.6640625,4.24262153 C10.6640625,6.03081597 9.21006944,7.48480903 7.421875,7.48480903 C5.63368056,7.48263889 4.1796875,6.02864583 4.1796875,4.24262153 Z" id="形状"></path>
                                </g>
                            </g>
                        </g>
                    </svg>
                  </span>
                </template>
              </el-input>
              <el-input type="password" v-model="form.password">
                <template #prefix>
                  <span v-if="isCn">{{ $t('password') }}：</span>
                  <span class="swd-icon" v-else>
                    <svg width="15px" height="17px" viewBox="0 0 15 17" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <g id="账号注销" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="账号注销-其他语言" transform="translate(-738.000000, -641.000000)" fill="#9C9C9C" fill-rule="nonzero">
                                <g id="编组备份-2" transform="translate(738.000000, 641.000000)">
                                    <path d="M9.053292,9.52527681 C9.053292,8.68185624 8.37143576,8 7.52801519,8 L7.46503248,8 C6.64078056,8 6,8.70102489 6,9.52527681 C6,9.55539897 6,9.58552114 6.00273838,9.6156433 C6.00273838,9.63481195 6.00547676,9.6539806 6.00821514,9.67314925 L6.00821514,9.68684114 C6.05750595,10.1907028 6.33408217,10.6041979 6.76126922,10.8670823 L6.76126922,13.3836521 C6.76126922,13.7971472 7.11178166,14.1476597 7.52527681,14.1476597 C7.93877196,14.1476597 8.28928441,13.7971472 8.28928441,13.3836521 L8.28928441,10.8670823 C8.71920983,10.6069363 8.99578605,10.1934412 9.04233849,9.68684114 L9.04233849,9.67314925 C9.04507686,9.6539806 9.04507686,9.63481195 9.04781524,9.6156433 C9.053292,9.58552114 9.053292,9.55539897 9.053292,9.52527681 Z" id="路径"></path>
                                    <path d="M12.4155971,5.93521205 L12.22726,5.93521205 L12.22726,4.39570313 C12.2419085,1.88571429 10.1785714,0 7.45186942,0 C4.77329799,0 2.72460938,1.88571429 2.72460938,4.39570312 L2.72460938,5.93521205 L2.58231027,5.93521205 C1.15094866,5.93521205 0,7.00145089 0,8.25 L0,14.1852121 C0,15.4871652 1.1655971,16.5 2.5844029,16.5 L12.4155971,16.5 C13.8490513,16.5 15,15.4319196 15,14.1852121 L15,8.25 C14.9686105,6.9609375 13.8030134,5.93521205 12.4155971,5.93521205 Z M3.48214286,4.36808036 L3.53027344,4.36808036 C3.53027344,2.20429688 5.18554687,0.679520089 7.45396205,0.679520089 C9.77050781,0.679520089 11.4236886,2.24665179 11.4236886,4.36808036 L11.4236886,5.90758929 L3.53236607,5.90758929 L3.53236607,4.37176339 L3.48214286,4.37176339 L3.48214286,4.36808036 Z M14.2131696,14.1852121 C14.2131696,15.0857143 13.4095982,15.8352121 12.4323382,15.8352121 L2.5844029,15.8352121 C1.60714286,15.8352121 0.803571429,15.0857143 0.803571429,14.1852121 L0.803571429,8.25 C0.803571429,7.34949777 1.60714286,6.6 2.5844029,6.6 L12.4323382,6.6 C13.4095982,6.6 14.2131696,7.34949777 14.2131696,8.25 L14.2131696,14.1852121 L14.2131696,14.1852121 Z" id="形状"></path>
                                </g>
                            </g>
                        </g>
                    </svg>
                  </span>
                </template>
              </el-input>
              <div class="c-login-button" @click="login">{{ $t('applyCancellation') }}</div>
            </div>
          </template>
          <template v-else>
            <div class="c-login-title">{{ $t('contactTheService') }}</div>
            <div class="c-login-data">
              <div class="c-login-data__item">
                <span>{{ $t('service') }}：</span>
                <span>{{ userInfo.name }}</span>
              </div>
              <div class="c-login-data__item">
                <span>{{ $t('contacts') }}：</span>
                <span>{{ userInfo.linkMan }}</span>
              </div>
              <div class="c-login-data__item">
                <span>{{ $t('phone') }}：</span>
                <span>{{ userInfo.linkPhone }}</span>
              </div>
              <div class="c-login-data__item">
                <span>{{ $t('email') }}：</span>
                <span>{{ userInfo.email }}</span>
              </div>
              <div class="c-login-data__item">
                <span>{{ $t('address') }}：</span>
                <span>{{ userInfo.address }}</span>
              </div>
              <div class="c-login-data__last">{{ $t('connectService') }}</div>
            </div>
          </template>
        </div>
        <div class="c-footer">
          <div class="c-name">{{title}}</div>
          Copyright©2018-2025 {{ $t('addressNumber') }} | {{$t('privacyPolicy')}} | {{$t('serviceTerms')}} | {{$t('openInterface')}}
        </div>
      </div></script></body></html>