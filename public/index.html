<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- <meta name="viewport" content="width=device-width,initial-scale=1.0" /> -->
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="Cache" content="no-cache" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />

    <!-- 根据域名动态设置title和meta标签 -->
    <script>
      ;(function() {
        var url = window.location.href
        var config = null

        // 域名配置：gpsnow.net使用中文，其他所有域名使用英文
        if (url.indexOf('gpsnow.net') > -1) {
          // 中文配置（仅限gpsnow.net）
          config = {
            title: '立即定位|全球领先动态物联网|位置服务SaaS软件|车队管理平台|GPS跟踪系统|全球卫星定位系统',
            description: '立即定位提供全球领先的动态物联网位置服务SaaS软件平台，专业的车队管理系统、GPS跟踪系统和全球卫星定位解决方案。',
            keywords: '车队管理,GPS跟踪,卫星定位,物联网,位置服务,SaaS软件,车辆监控'
          }
        } else {
          // 英文配置（whatsgps.com和其他所有域名的默认配置）
          config = {
            title: 'WhatsGPS|GPS Tracking System |Fleet Management Sofware Platform|Global Leading Dynamic IoT System| Location Services SaaS Software',
            description:
              'WhatsGPS provides global leading GPS tracking system and fleet management software platform with dynamic IoT location services SaaS solution.',
            keywords: 'GPS tracking,fleet management,IoT system,location services,SaaS software,vehicle monitoring'
          }
        }

        if (!url.includes('gpsnow.net') && !url.includes('whatsgps')) {
          config.title = ''
        }

        if (config) {
          // 设置title
          document.title = config.title

          // 设置description
          var metaDesc = document.querySelector('meta[name="description"]') || document.createElement('meta')
          metaDesc.name = 'description'
          metaDesc.content = config.description
          if (!document.querySelector('meta[name="description"]')) {
            document.head.appendChild(metaDesc)
          }

          // 设置keywords
          var metaKeywords = document.querySelector('meta[name="keywords"]') || document.createElement('meta')
          metaKeywords.name = 'keywords'
          metaKeywords.content = config.keywords
          if (!document.querySelector('meta[name="keywords"]')) {
            document.head.appendChild(metaKeywords)
          }
        }
      })()
    </script>
    <style>
      .first-loading-wrp {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        min-height: 420px;
        height: 100vh;
      }
      .first-loading-wrp > h1 {
        font-size: 128px;
      }
      .first-loading-wrp .loading-wrp {
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .dot {
        animation: antRotate 1.2s infinite linear;
        transform: rotate(45deg);
        position: relative;
        display: inline-block;
        font-size: 32px;
        width: 32px;
        height: 32px;
        box-sizing: border-box;
      }
      .dot i {
        width: 14px;
        height: 14px;
        position: absolute;
        display: block;
        background-color: #1890ff;
        border-radius: 100%;
        transform: scale(0.75);
        transform-origin: 50% 50%;
        opacity: 0.3;
        animation: antSpinMove 1s infinite linear alternate;
      }
      .dot i:nth-child(1) {
        top: 0;
        left: 0;
      }
      .dot i:nth-child(2) {
        top: 0;
        right: 0;
        -webkit-animation-delay: 0.4s;
        animation-delay: 0.4s;
      }
      .dot i:nth-child(3) {
        right: 0;
        bottom: 0;
        -webkit-animation-delay: 0.8s;
        animation-delay: 0.8s;
      }
      .dot i:nth-child(4) {
        bottom: 0;
        left: 0;
        -webkit-animation-delay: 1.2s;
        animation-delay: 1.2s;
      }
      @keyframes antRotate {
        to {
          -webkit-transform: rotate(405deg);
          transform: rotate(405deg);
        }
      }
      @-webkit-keyframes antRotate {
        to {
          -webkit-transform: rotate(405deg);
          transform: rotate(405deg);
        }
      }
      @keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }
      @-webkit-keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }
    </style>
  </head>

  <body>
    <noscript>
      <strong>We're sorry doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <div class="first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
        <div style="display: flex; justify-content: center; align-items: center;">Loading...</div>
      </div>
    </div>
  </body>
</html>
