# 立即定位 SaaS 前端平台

## 项目简介

立即定位 SaaS 是一个基于 Vue 2.7 的 SaaS 前端 Web 应用，为用户提供实时定位、设备管理等功能的综合平台。本项目使用 Vue CLI 构建，集成了多种现代前端技术和工具，提供高效、易用的用户界面。

## 技术栈

- **核心框架**：Vue 2.7 (支持 Composition API)
- **路由管理**：Vue Router 3.x
- **状态管理**：Vuex 3.x & Pinia
- **UI 组件库**：Element UI
- **CSS 预处理器**：SCSS
- **HTTP 客户端**：Axios
- **国际化**：Vue-i18n
- **图表库**：ECharts
- **地图组件**：Maptalks、Mapbox GL
- **工具库**：Dayjs、js-md5、qs 等
- **构建工具**：Vue CLI

## 项目结构

```
src/
├── api/v2/        # 后端API接口定义，按功能模块划分
├── assets/        # 静态资源（图片、字体等）
├── base-ui/       # 自定义UI组件
├── components/    # 可复用组件，按功能和类型分类
├── directive/     # 自定义指令
├── enums/         # 枚举值定义
├── filter/        # 过滤器
├── hooks/         # 可复用的组合式函数
├── i18n/          # 国际化配置
├── icons/         # SVG图标
├── layout/        # 布局组件
├── mixins/        # 混入
├── plugins/       # 插件配置
├── router/        # 路由配置，使用模块化管理
├── store/         # 状态管理（Vuex和Pinia）
├── styles/        # 全局样式文件
├── utils/         # 工具函数库
├── views/         # 页面视图组件
├── App.vue        # 根组件
├── main.js        # 应用入口文件
├── permission.js  # 权限控制配置
└── settings.js    # 应用全局设置
```

## 安装与运行

### 环境要求

- Node.js 14.16.0
- npm 6.x 或更高版本

### 安装依赖

```bash
npm install
```

### 本地开发

```bash
# 开发环境运行
npm run dev
```

### 构建部署

```bash
# 生产环境构建
npm run build

# 测试环境构建
npm run build-test

# 预发布环境构建
npm run build-pre
```

## 国际化

项目支持多语言切换功能，使用 vue-i18n 实现。可以通过以下命令管理翻译内容：

```bash
# 导出翻译到Excel
npm run export_excel

# 从Excel导入翻译
npm run import_excel

# 执行翻译任务
npm run translate
```

## 代码规范与提交规范

项目使用 ESLint 和 Prettier 进行代码规范检查和格式化：
安装 ESLint 插件，并配置编辑器 settings.json 。

```json
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  }
```

Git 提交规范：

```
[type]:[【影响的功能模块】][description]

例如：feat:【设备详情】新增订单状态字段
```

类型(type)包括：

- feat: 新增功能
- fix: 修复 bug
- docs: 文档注释
- style: 代码格式(不影响代码运行的变动)
- refactor: 重构、优化
- perf: 性能优化
- test: 增加测试
- chore: 构建过程或辅助工具的变动
- revert: 回退
- build: 打包

## 开发规范

详细的开发规范请参考项目中的规范文档：

- 项目结构规范
- 路由规范
- API 接口规范
- 组件开发规范
- 工具函数规范
- 状态管理规范
- 国际化规范
- 样式规范
- Git 规范
