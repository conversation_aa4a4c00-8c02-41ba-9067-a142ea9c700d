# 🌐 Vue.js 国际化自动处理脚本

![Vue.js](https://img.shields.io/badge/Vue.js-2.7-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white)
![Node.js](https://img.shields.io/badge/Node.js-Required-339933?style=for-the-badge&logo=node.js&logoColor=white)
![License](https://img.shields.io/badge/License-MIT-blue?style=for-the-badge)

**🚀 自动化 Vue 项目中文国际化的强大工具**

_一键扫描、智能替换、完美生成_

---

## 🎯 快速开始

### 📦 安装依赖

运行脚本前，请确保项目已安装以下开发依赖：

```bash
npm install --save-dev pinyin crypto
```

### ⚡ 使用方法

#### 💡 支持多种路径输入方式

✅ **绝对路径**：`D:\workStation\ljdw2-saas-front-web\src\views\Translate`  
✅ **相对路径**：`views/Translate`（相对于 src 目录）

✅ **单个文件**：`views/User/index.vue`（相对于 src 目录）

#### 🔧 使用 npm 命令（推荐）

```bash
# 基础用法：处理指定目录
npm run replace "views/Translate"           # 相对路径（相对于src目录）
npm run replace "components/Dialog"         # 相对路径
npm run replace "D:\path\to\your\directory" # 绝对路径

# 处理单个文件
npm run replace "views/User/index.vue"

# 🌟 使用命名空间：npm run replace "路径" "模块名"
npm run replace "views/Report" "report"     # 生成嵌套结构 { "report": { "key": "value" } }
npm run replace "views/User" "user"         # 调用方式: $t('user.keyName')
```

**💡 命名空间说明：**

- **不使用命名空间**：生成平铺结构 `{ "key": "value" }`，调用 `$t('key')`
- **使用命名空间**：生成嵌套结构 `{ "module": { "key": "value" } }`，调用 `$t('module.key')`
- **适用场景**：大型项目建议使用命名空间避免 key 冲突，小项目或通用组件可使用平铺模式

**💡 文件中的文字符写法技巧**

```diff
示例：
- ❌ 容易提取错误：label="'用户名'+'：'"
+ ✅ 正常提取：label="用户名：" → :label="$t('yonghuming')"  // 属性值整体写一起

- ❌ 不提取：<span>{{ value ===1 ? '上架' : '下架' }}</span>
- ❌ 不提取：<span>成功{{ count }}条</span>
+ ✅ 正常提取：<span>新增</span>  // 元素内容带有变量时，由于是整体替换整个元素的内容，处理变(例如三元表达，与或表达式)比较麻烦。加上这种情况相对少见，统一排除不处理避免提前出乱码，手动提取

- ❌ 容易提取错误：const fileName = `${dayjs().format('YYYY-MM-DD')}_条码统计.xlsx`
+ ✅ 正常提取：const fileName = `${dayjs().format('YYYY-MM-DD')}_`+'条码统计'+'.xlsx'
```

## ⚙️ 脚本配置

💡 **多项目适配**：如需在不同项目中使用，请修改脚本顶部的 `EXISTING_TRANSLATIONS_PATH` 常量，指向项目的翻译文件路径。

### ⚠️ 重要提示

> 1. **🔐 脚本会自动备份整个处理目录，翻译完成后记得手动删除备份文件**
> 2. **📍 生成的翻译文件位于：`scripts/zh-CN.json`**
> 3. <span style="color: red; font-weight: bold;">⚠️ 重要：运行脚本后，务必手动检查生成的代码和翻译文件，不要过度信任脚本的自动替换，特殊情况可能出现错误！一次性不要处理太多文件和目录方便选择回滚</span>

> 📌 **使用注意事项**
>
> 4. **🔒 备份重要性**：脚本会自动备份，但建议在重要项目上先在测试目录试用
> 5. **🔄 重复运行**：已处理的文件再次运行脚本不会产生新的翻译条目
> 6. **🔍 手动检查**：建议处理后手动检查生成的代码和翻译文件
> 7. **🌍 内容过滤**：脚本会跳过纯英文、数字等非中文内容
> 8. **📝 翻译调整**：生成的翻译文件需要根据实际需求调整翻译内容
> 9. **🔄 版本控制**：建议在版本控制环境下运行，便于回滚变更
> 10. **🔍 翻译文件依赖**：脚本会自动读取 `src/i18n/langs/cn.json`，确保该文件存在且格式正确
> 11. **🎯 Key 匹配精度**：优先使用现有翻译的精确匹配，建议保持翻译文件的规范性

---

## ✨ 脚本功能

🎨 自动扫描 Vue 项目中的中文内容，将其替换为国际化函数调用，并生成对应的翻译文件。

### 🎯 新增功能特点

✅ **智能翻译查找**

- 自动读取项目现有翻译文件 `src/i18n/langs/cn.json`
- 优先使用现有翻译 key，避免重复生成
- 保持项目翻译一致性，减少维护成本

✅ **安全的备份机制**

- 处理前自动备份整个目录
- 时间戳命名避免备份目录冲突
- 递归备份保持目录结构

✅ **智能 key 生成**

- 去除标点符号生成干净的拼音 key
- 自动处理长文本并添加 MD5 hash 避免翻译 key 冲突
- 支持带变量的模板字符串

✅ **完整的文件处理**

- 递归处理目录下的所有`.vue`和`.js`文件
- 支持 Vue Options API 和 Composition API
- 自动检测并添加必要的 i18n 导入

✅ **智能注释排除**

- 自动跳过 HTML 注释、JavaScript 注释中的中文内容
- 支持单行注释 `//`、多行注释 `/* */` 和 HTML 注释 `<!-- -->`
- 确保代码注释不被误转换为国际化调用

✅ **模块化命名空间**

- 支持可选的模块名参数，按功能模块组织翻译结构
- 双重输出模式：嵌套结构和平铺结构
- 避免不同模块间的翻译 key 冲突

## 📁 支持的文件类型

| 文件类型 | 处理内容          | 状态        |
| -------- | ----------------- | ----------- |
| `.vue`   | Template + Script | ✅ 完全支持 |
| `.js`    | JavaScript 代码   | ✅ 完全支持 |

---

## 🔄 处理示例

### 📝 原始代码

```vue
<template>
  <div>
    <!-- 这是一个注释，不会被转换 -->
    <h1>用户管理</h1>
    <el-form-item label="用户名：" prop="username">
      <el-input placeholder="请输入用户名"></el-input>
    </el-form-item>
    <el-button>保存</el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 这也是注释，不会被处理
      message: '欢迎使用系统！'
    }
  },
  methods: {
    handleSave() {
      /* 多行注释中的中文
         也不会被转换 */
      this.$message.success('保存成功')
    }
  }
}
</script>
```

### ✨ 处理后代码

```vue
<template>
  <div>
    <!-- 这是一个注释，不会被转换 -->
    <h1>{{ $t('yonghuguanli') }}</h1>
    <el-form-item :label="$t('yonghuming')" prop="username">
      <el-input :placeholder="$t('qingshuru_2a1b3c')"></el-input>
    </el-form-item>
    <el-button>{{ $t('baocun') }}</el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 这也是注释，不会被处理
      message: this.$t('huanyingshiyong_4d5e6f')
    }
  },
  methods: {
    handleSave() {
      /* 多行注释中的中文
         也不会被转换 */
      this.$message.success(this.$t('baocunchenggong'))
    }
  }
}
</script>
```

### 📄 生成的翻译文件

**平铺模式（不使用命名空间）：**

```json
{
  "yonghuguanli": "用户管理",
  "yonghuming": "用户名：",
  "qingshuru_2a1b3c": "请输入用户名",
  "baocun": "保存",
  "huanyingshiyong_4d5e6f": "欢迎使用系统！",
  "baocunchenggong": "保存成功"
}
```

**命名空间模式（使用模块名 "user"）：**

```json
{
  "user": {
    "yonghuguanli": "用户管理",
    "yonghuming": "用户名：",
    "qingshuru_2a1b3c": "请输入用户名",
    "baocun": "保存",
    "huanyingshiyong_4d5e6f": "欢迎使用系统！",
    "baocunchenggong": "保存成功"
  }
}
```

### 🔍 智能翻译查找示例

**📋 场景**：处理包含现有翻译的代码

```vue
<!-- 原始代码 -->
<template>
  <el-dialog cancel-text="取消">
    <h1>这是全新的标题</h1>
    <p>你好</p>
  </el-dialog>
</template>
```

**🧠 脚本处理逻辑**：

1. **📖 加载现有翻译**：读取 `cn.json` 文件，包含 2981 条翻译
2. **🔍 查找匹配**：
   - `"取消"` → 在现有翻译中找到 → 使用 `lg.cancel`
   - `"这是全新的标题"` → 未找到匹配 → 生成新 key `zheshiquanxin_abc123`
   - `"你好"` → 在现有翻译中找到 → 使用现有 key

**✨ 处理后代码**：

```vue
<!-- 处理后代码 -->
<template>
  <el-dialog :cancel-text="$t('lg.cancel')">
    <!-- 使用现有key -->
    <h1>{{ $t('zheshiquanxin_abc123') }}</h1>
    <!-- 生成新key -->
    <p>{{ $t('existingKey') }}</p>
    <!-- 使用现有key -->
  </el-dialog>
</template>
```

**📊 生成的新翻译文件**（仅包含新内容）：

```json
{
  "zheshiquanxin_abc123": "这是全新的标题"
}
```

**💡 优势**：

- ✅ 复用现有翻译，避免重复
- ✅ 保持项目翻译 key 的一致性
- ✅ 减少翻译文件的冗余

## 📂 输出文件

脚本会在`scripts/`目录下生成统一的翻译文件：

- **输出文件名**：`zh-CN.json`
- **文件路径**：`scripts/zh-CN.json`

所有处理的中文内容都会合并到同一个翻译文件中，便于统一管理。

---

## 🔄 处理内容类型

### 🎨 Template 部分（仅.vue 文件）

| 处理类型       | 原始代码                         | 转换后                                      |
| -------------- | -------------------------------- | ------------------------------------------- |
| **标签间文本** | `<span>你好</span>`              | `<span>{{ $t('nihao') }}</span>`            |
| **静态属性**   | `label="用户名："`               | `:label="$t('yonghuming')"`                 |
| **动态属性**   | `:title="'早上好'"`              | `:title="$t('zaoshanghao')"`                |
| **包含变量**   | `<span>成功{{ count }}个</span>` | `<span>{{ $t('chenggong', count) }}</span>` |
| **HTML 注释**  | `<!-- 这是注释 -->`              | 保持不变，不会被处理                        |

### 💻 Script 部分（.vue 和.js 文件）

| 处理类型         | 原始代码            | 转换后                                                      |
| ---------------- | ------------------- | ----------------------------------------------------------- |
| **单引号字符串** | `'操作成功'`        | `this.$t('caozuochenggong')` 或 `i18n.t('caozuochenggong')` |
| **模板字符串**   | `` `欢迎${name}` `` | `this.$t('huanying', name)` 或 `i18n.t('huanying', name)`   |
| **单行注释**     | `// 这是注释`       | 保持不变，不会被处理                                        |
| **多行注释**     | `/* 这是注释 */`    | 保持不变, 不会被处理                                        |

### 🚫 跳过处理的内容

- **HTML 注释**：`<!-- 任何中文内容 -->`
- **JavaScript 单行注释**：`// 任何中文内容`
- **JavaScript 多行注释**：`/* 任何中文内容 */`
- **已国际化内容**：包含 `$t()` 或 `i18n.t()` 的字符串

---

## 🧠 API 类型智能检测

### 🎯 Vue 文件检测

```
┌─────────────────┐
│   📄 Vue文件     │
└─────────┬───────┘
          │
          ▼
    ┌─────────────┐    ✅ 是    ┌──────────────────┐    🎯 使用
    │包含setup()? │ ──────────▶ │ Composition API  │ ──────────▶ i18n.t()
    └─────┬───────┘             └──────────────────┘
          │ ❌ 否
          ▼
  ┌──────────────────┐   ✅ 是    ┌──────────────────┐
  │包含import vue?   │ ──────────▶ │ Composition API  │ ──┐
  └─────┬────────────┘             └──────────────────┘   │ 🎯 使用
        │ ❌ 否                                           ├──▶ i18n.t()
        ▼                                                 │
  ┌──────────────────┐             ┌──────────────────┐   │
  │   Options API    │    🎯 使用   │   this.$t()      │ ──┘
  └──────────────────┘ ──────────▶ └──────────────────┘
```

**🔍 检测规则总结：**

| 🔎 检测条件            | 🎨 API 类型    | 🛠️ 使用方式 | 📦 自动处理 |
| ---------------------- | -------------- | ----------- | ----------- |
| 包含 `setup()`         | 🔥 Composition | `i18n.t()`  | ✅ 自动导入 |
| 包含 `import from vue` | 🔥 Composition | `i18n.t()`  | ✅ 自动导入 |
| 其他情况               | ⚡ Options     | `this.$t()` | ❌ 无需导入 |

### 📄 JS 文件检测

| 文件类型          | 检测条件                           | 使用方式    | 示例           |
| ----------------- | ---------------------------------- | ----------- | -------------- |
| **🔧 Mixin 文件** | 文件名包含`mixin`                  | `this.$t()` | `userMixin.js` |
| **⚙️ 配置文件**   | 包含`export const`且无`this.`      | `i18n.t()`  | `config.js`    |
| **🎨 组件文件**   | 包含`export default {}`和 Vue 选项 | `this.$t()` | `component.js` |
| **📦 其他文件**   | 默认情况                           | `i18n.t()`  | `utils.js`     |

---

## 🔑 Key 生成规则

### 🧠 智能翻译查找

脚本会在生成新 key 之前，先从项目现有的翻译文件中查找匹配的翻译：

| 🔍 查找流程         | 📋 说明                                | 🎯 示例                                              |
| ------------------- | -------------------------------------- | ---------------------------------------------------- |
| **1️⃣ 加载现有翻译** | 自动读取 `src/i18n/langs/cn.json` 文件 | 📄 加载 2981 条现有翻译                              |
| **2️⃣ 精确匹配**     | 优先使用现有翻译的 key                 | `"取消"` → 使用现有 key `lg.cancel`                  |
| **3️⃣ 生成新 key**   | 找不到匹配时才生成新 key               | `"这是全新内容"` → 生成新 key `zheshiquanxin_abc123` |

**💡 处理效果对比：**

```diff
- 改进前：所有中文都生成新key，可能产生重复翻译
+ 改进后：优先复用现有翻译，避免重复，保持一致性

示例：
- ❌ 改进前：cancel-text="取消" → :cancel-text="$t('quxiao')"
+ ✅ 改进后：cancel-text="取消" → :cancel-text="$t('lg.cancel')"  // 使用现有key
```

### 📏 Key 生成算法

当找不到现有翻译时，按以下规则生成新 key：

| 文本类型        | 规则                             | 示例                                                |
| --------------- | -------------------------------- | --------------------------------------------------- |
| **🟢 短文本**   | ≤4 个中文字符，直接转拼音        | `你好` → `nihao`                                    |
| **🟡 长文本**   | >4 个中文字符，前 4 个+6 位 hash | `你好世界朋友们` → `nihaoze_a1b2c3`                 |
| **🔵 变量文本** | 包含变量，前 2 个+6 位 hash      | `你好${name}` → `ni_d4e5f6`                         |
| **🔧 特殊符号** | 自动过滤特殊符号后生成拼音       | `温度(℃)` → `wendu`<br>`超高温:{0}` → `chaogaowen0` |

### 🔑 Key 生成的特殊处理

- ✅ **去除空格和标点**：生成纯小写字母的 key
- ✅ **过滤特殊符号**：自动过滤 `℃`、`°`、`%`、`=`、`/`、`{}`、`[]` 等特殊符号
- ✅ **长度限制**：拼音部分不超过 20 个字符
- ✅ **防冲突**：使用 MD5 hash 确保唯一性

---

## 🎪 内容处理规则

| 功能                | 说明                            | 示例                            |
| ------------------- | ------------------------------- | ------------------------------- |
| **🚫 跳过已国际化** | 已包含`$t()`的内容不重复处理    | `$t('hello')` → 保持不变        |
| **💬 智能注释排除** | 自动跳过注释中的中文内容        | `<!-- 注释 -->` → 保持不变      |
| **🔢 变量占位符**   | 使用`{0}`, `{1}`, `{2}`格式     | `你好{0}世界{1}`                |
| **📥 自动导入**     | Composition API 自动添加 import | `import { i18n } from '@/i18n'` |
| **🎭 嵌套处理**     | 正确处理嵌套 template 标签      | 支持多层嵌套                    |

---

## 🛡️ 错误处理

脚本提供友好的错误提示和解决建议：

| 错误类型 | 说明         | 解决方案             |
| -------- | ------------ | -------------------- |
| ❌       | 目录不存在   | 检查路径是否正确     |
| ❌       | 路径不是目录 | 确认输入的是目录路径 |
| ❌       | 权限不足     | 检查文件/目录权限    |
| ❌       | 文件处理失败 | 查看详细错误信息     |

---

**🎉 享受自动化国际化的便利！**

_Made with ❤️ for Vue.js developers_
