const fs = require('fs').promises
const path = require('path')
const { pinyin } = require('pinyin')
const crypto = require('crypto')

// ==================== 配置常量 ====================
// 🔧 现有翻译文件路径配置
// 💡 在不同项目中使用时，请修改此路径指向项目的翻译文件
const EXISTING_TRANSLATIONS_PATH = '../../src/i18n/langs/cn.json'

// 🎯 其他配置项
const OUTPUT_FILENAME = 'zh-CN.json' // 输出翻译文件名
const SUPPORTED_EXTENSIONS = ['.vue', '.js'] // 支持的文件类型
// ================================================

// 获取命令行参数
function getTargetPath() {
  const args = process.argv.slice(2)

  if (args.length === 0) {
    console.log('❌ 错误：请指定要处理的目录或文件路径')
    console.log('📖 使用方法：')
    console.log('  目录处理 (绝对路径): node scripts/replaceI18nKey.js "D:\\workStation\\ljdw2-saas-front-web\\src\\views\\Translate" [模块名]')
    console.log('  目录处理 (相对路径): node scripts/replaceI18nKey.js "views/Translate" [模块名]')
    console.log('  单文件处理 (绝对路径): node scripts/replaceI18nKey.js "D:\\workStation\\ljdw2-saas-front-web\\src\\views\\User\\index.vue" [模块名]')
    console.log('  单文件处理 (相对路径): node scripts/replaceI18nKey.js "views/User/index.vue" [模块名]')
    console.log('  或者使用 npm 命令：')
    console.log('  npm run replace "路径" [模块名]  (脚本会自动判断是绝对路径还是相对路径)')
    console.log('')
    console.log('💡 模块名说明（可选参数）：')
    console.log('  - 用于组织翻译key的命名空间，避免key冲突')
    console.log('  - 提供模块名时：生成嵌套结构 { "report": { "key": "value" } }')
    console.log('  - 不提供模块名时：生成平铺结构 { "key": "value" }')
    console.log('  - i18n调用方式：$t("report.key") 或 $t("key")')
    console.log('')
    console.log('📝 示例：')
    console.log('  # 目录处理（使用命名空间）')
    console.log('  node scripts/replaceI18nKey.js "views/Report" "report"')
    console.log('  # 目录处理（不使用命名空间）')
    console.log('  node scripts/replaceI18nKey.js "views/Common"')
    console.log('  # 单文件处理（使用命名空间）')
    console.log('  node scripts/replaceI18nKey.js "views/User/index.vue" "user"')
    console.log('  # 单文件处理（不使用命名空间）')
    console.log('  node scripts/replaceI18nKey.js "components/Dialog/UserDialog.vue"')
    process.exit(1)
  }

  const inputPath = args[0]
  const moduleName = args[1] || '' // 第二个参数可选，默认为空字符串
  let targetPath
  let outputFileName
  let isFile = false

  // 验证模块名格式（只在提供了模块名时验证）
  if (moduleName && !/^[a-zA-Z][a-zA-Z0-9_]*$/.test(moduleName)) {
    console.log('❌ 错误：模块名格式无效')
    console.log('💡 模块名必须以字母开头，只能包含字母、数字和下划线')
    console.log('✅ 有效示例: report, userManagement, device_list')
    console.log('❌ 无效示例: 123abc, report-list, 报表模块')
    process.exit(1)
  }

  // 判断是否为绝对路径
  if (path.isAbsolute(inputPath)) {
    // 绝对路径
    targetPath = inputPath
    console.log('📂 使用绝对路径:', targetPath)
  } else {
    // 相对路径，相对于src目录
    targetPath = path.resolve(__dirname, '../../src', inputPath)
    console.log('📂 使用相对路径 (相对于src):', inputPath)
    console.log('📂 解析后的绝对路径:', targetPath)
  }

  // 统一使用配置的输出文件名
  outputFileName = OUTPUT_FILENAME

  // 验证路径是否存在并判断是文件还是目录
  try {
    const stat = require('fs').statSync(targetPath)
    if (stat.isFile()) {
      // 是文件，检查文件扩展名是否支持
      const ext = path.extname(targetPath)
      if (!SUPPORTED_EXTENSIONS.includes(ext)) {
        console.log('❌ 错误：不支持的文件类型:', ext)
        console.log('💡 支持的文件类型:', SUPPORTED_EXTENSIONS.join(', '))
        process.exit(1)
      }
      isFile = true
      console.log('✅ 目标文件验证成功')
    } else if (stat.isDirectory()) {
      isFile = false
      console.log('✅ 目标目录验证成功')
    } else {
      console.log('❌ 错误：指定的路径既不是文件也不是目录:', targetPath)
      process.exit(1)
    }
  } catch (error) {
    console.log('❌ 错误：指定的路径不存在:', targetPath)
    console.log('💡 请检查路径是否正确')
    process.exit(1)
  }

  console.log('📄 输出文件名:', outputFileName)

  if (moduleName) {
    console.log('🏷️  翻译模块名:', moduleName)
    console.log('🔧 使用命名空间模式')
  } else {
    console.log('🔧 使用平铺模式（不使用命名空间）')
  }

  return {
    searchPath: targetPath,
    outputFileName: outputFileName,
    moduleName: moduleName,
    isFile: isFile
  }
}

// 获取路径配置
const pathConfig = getTargetPath()

// 配置项
const config = {
  // 要搜索的路径（可能是目录或文件）
  searchPath: pathConfig.searchPath,
  // 是否为单个文件
  isFile: pathConfig.isFile,
  // 要处理的文件类型
  fileExtensions: SUPPORTED_EXTENSIONS,
  outputPath: path.resolve(__dirname, `./${pathConfig.outputFileName}`),
  // 翻译模块名
  moduleName: pathConfig.moduleName
}

// 创建反向映射表（中文内容 -> key）
function createReverseMapping(obj, reverseTranslations, prefix = '') {
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key
    if (typeof value === 'string') {
      // 如果是字符串值，添加到反向映射中
      reverseTranslations[value] = fullKey
    } else if (typeof value === 'object' && value !== null) {
      // 如果是嵌套对象，递归处理
      createReverseMapping(value, reverseTranslations, fullKey)
    }
  }
}

// 读取现有的翻译文件
async function loadExistingTranslations() {
  const existingTranslationsPath = path.resolve(__dirname, EXISTING_TRANSLATIONS_PATH)
  let existingTranslations = {}
  let reverseTranslations = {}

  try {
    const content = await fs.readFile(existingTranslationsPath, 'utf8')
    existingTranslations = JSON.parse(content)

    createReverseMapping(existingTranslations, reverseTranslations)
    console.log('✅ 成功加载现有翻译文件:', existingTranslationsPath)
    console.log('📊 包含翻译条目:', Object.keys(reverseTranslations).length, '条')
  } catch (error) {
    console.log('⚠️  无法读取现有翻译文件:', existingTranslationsPath)
    console.log('   将创建新的翻译文件')
    if (error.code === 'ENOENT') {
      console.log('💡 提示：请确认翻译文件路径是否正确')
    }
  }

  return { existingTranslations, reverseTranslations }
}

// 查找现有翻译key
function findExistingKey(text, reverseTranslations) {
  // 先尝试精确匹配
  if (reverseTranslations[text]) {
    return reverseTranslations[text]
  }

  // 尝试匹配去除前后空格的文本
  const trimmedText = text.trim()
  if (reverseTranslations[trimmedText]) {
    return reverseTranslations[trimmedText]
  }

  return null
}

// 生成短hash值
function generateShortHash(text) {
  return crypto
    .createHash('md5')
    .update(text)
    .digest('hex')
    .substring(0, 6)
}

// 递归复制目录
async function copyDirectory(src, dest) {
  try {
    // 创建目标目录
    await fs.mkdir(dest, { recursive: true })

    // 读取源目录
    const entries = await fs.readdir(src, { withFileTypes: true })

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name)
      const destPath = path.join(dest, entry.name)

      if (entry.isDirectory()) {
        // 递归复制子目录
        await copyDirectory(srcPath, destPath)
      } else {
        // 复制文件
        await fs.copyFile(srcPath, destPath)
      }
    }
  } catch (error) {
    console.error('复制目录出错:', error)
    throw error
  }
}

// 创建目录备份
async function createDirectoryBackup(dirPath) {
  try {
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, '-')
      .slice(0, 19)
    const backupPath = `${dirPath}_backup_${timestamp}`

    console.log('开始备份目录...')
    console.log('源目录:', dirPath)
    console.log('备份目录:', backupPath)

    await copyDirectory(dirPath, backupPath)
    console.log('目录备份完成!')
    return backupPath
  } catch (error) {
    console.error('创建目录备份失败:', error)
    throw error
  }
}

// 生成拼音key
function generatePinyinKey(text, isTemplate = false) {
  // 清理文本，去除多余的空格、换行和标点符号
  const cleanText = text
    .trim()
    .replace(/[\s\n\r]+/g, ' ')
    .replace(/[：:，,。.！!？?；;、""''（）()【】[\]《》<>℃°%=/{}]/g, '') // 去除常见标点符号和特殊符号

  // 根据类型决定处理的字符数
  let processText
  let needsHash = false

  if (isTemplate) {
    // 模板字符串只取前2个字符，并添加hash避免冲突
    processText = cleanText.slice(0, 2)
    needsHash = true
  } else {
    // 普通文本，如果超过4个中文字符，只取前4个字符并添加hash
    const chineseChars = cleanText.match(/[\u4e00-\u9fa5]/g) || []
    if (chineseChars.length > 4) {
      // 提取前4个中文字符
      let chineseCount = 0
      let result = ''
      for (let char of cleanText) {
        if (/[\u4e00-\u9fa5]/.test(char)) {
          chineseCount++
          if (chineseCount <= 4) {
            result += char
          } else {
            break
          }
        } else {
          // 保留非中文字符（如标点符号）
          if (chineseCount < 4) {
            result += char
          }
        }
      }
      processText = result
      needsHash = true
    } else {
      processText = cleanText
      needsHash = false
    }
  }

  // 将中文转换为拼音,并去掉声调
  const pinyinResult = pinyin(processText, {
    style: 'normal', // 不带声调
    heteronym: false // 禁用多音字模式
  })

  // 将拼音数组转换为全小写的key，并去除空格
  let pinyinKey = pinyinResult
    .map(item => {
      const word = item[0] // 由于禁用了多音字模式，所以只取第一个读音
      return word.toLowerCase()
    })
    .join('')
    .replace(/\s+/g, '') // 去除所有空格

  // 限制拼音部分长度不超过20个字母
  if (pinyinKey.length > 20) {
    pinyinKey = pinyinKey.substring(0, 20)
    needsHash = true // 截断后需要添加hash避免冲突
  }

  // 如果需要hash值来避免冲突，则添加hash后缀
  if (needsHash) {
    const hash = generateShortHash(cleanText)
    pinyinKey = `${pinyinKey}_${hash}`
  }

  return pinyinKey
}

// 清理模板字符串中的文本
function cleanTemplateText(text) {
  return text.replace(/[\s\n\r]+/g, ' ').trim()
}

// 获取HTML注释的位置范围
function getHtmlCommentRanges(content) {
  const ranges = []
  const regex = /<!--[\s\S]*?-->/g
  let match
  while ((match = regex.exec(content)) !== null) {
    ranges.push({
      start: match.index,
      end: match.index + match[0].length
    })
  }
  return ranges
}

// 获取JavaScript注释的位置范围
function getJsCommentRanges(content) {
  const ranges = []

  // 单行注释 //
  const singleLineRegex = /\/\/.*$/gm
  let match
  while ((match = singleLineRegex.exec(content)) !== null) {
    ranges.push({
      start: match.index,
      end: match.index + match[0].length
    })
  }

  // 多行注释 /* */ 和 /** */
  const multiLineRegex = /\/\*[\s\S]*?\*\//g
  while ((match = multiLineRegex.exec(content)) !== null) {
    ranges.push({
      start: match.index,
      end: match.index + match[0].length
    })
  }

  return ranges
}

// 检查位置是否在注释范围内
function isInCommentRange(position, commentRanges) {
  return commentRanges.some(range => position >= range.start && position < range.end)
}

// 从Vue文件中提取script内容
function extractScriptContent(content) {
  const scriptMatch = content.match(/<script\b[^>]*>([\s\S]*?)<\/script>/m)
  return scriptMatch
    ? {
        fullMatch: scriptMatch[0],
        content: scriptMatch[1],
        tag: scriptMatch[0].match(/<script[^>]*>/)[0]
      }
    : null
}

// 检查字符串是否包含中文
function containsChinese(str) {
  return /[\u4e00-\u9fa5]/.test(str)
}

// 替换引号中的内容并收集翻译
function replaceQuotedContent(scriptContent, apiType = 'options', reverseTranslations = {}, moduleName = '') {
  // 匹配单引号中的内容，排除转义的单引号
  const singleQuoteRegex = /'([^'\\]|\\[^])*'/g
  // 匹配模板字符串中的内容
  const templateRegex = /`([^`\\]|\\[^])*`/g

  let replacements = []
  let translations = {}

  // 获取JavaScript注释范围
  const commentRanges = getJsCommentRanges(scriptContent)

  // 处理模板字符串中的中文
  let newContent = scriptContent.replace(templateRegex, (match, ...args) => {
    const offset = args[args.length - 2] // 获取匹配位置

    // 检查是否在注释范围内
    if (isInCommentRange(offset, commentRanges)) {
      return match
    }

    if (!containsChinese(match)) return match

    // 去掉前后的反引号
    const templateText = match.slice(1, -1)

    // 清理并保存整个模板字符串的内容
    const cleanText = cleanTemplateText(templateText)
    if (containsChinese(cleanText)) {
      // 检查是否包含变量 ${variable}
      const variableRegex = /\$\{([^}]+)\}/g
      const variableMatches = [...cleanText.matchAll(variableRegex)]

      if (variableMatches.length > 0) {
        // 包含变量的模板字符串，需要特殊处理
        // 提取所有变量表达式
        const variables = variableMatches.map(match => match[1].trim())

        // 将变量语法 ${var} 替换为 i18n 占位符 {0}, {1}, {2}
        let translationText = cleanText
        const uniqueVariables = [...new Set(variables)] // 去重

        // 按照出现顺序为每个唯一变量分配索引
        let translationTextWithIndexes = cleanText
        uniqueVariables.forEach((variable, index) => {
          // 将所有该变量的出现都替换为对应的索引占位符
          const regex = new RegExp(`\\$\\{\\s*${variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*\\}`, 'g')
          translationTextWithIndexes = translationTextWithIndexes.replace(regex, `{${index}}`)
        })

        // 先查找现有翻译
        let key = findExistingKey(translationTextWithIndexes, reverseTranslations)
        let isExistingKey = !!key
        if (key) {
          console.log('🔍 找到现有翻译key:', key)
        } else {
          key = generatePinyinKey(translationTextWithIndexes, true)
          // 记录翻译（保存带索引占位符的版本）
          translations[key] = translationTextWithIndexes
          console.log('🆕 生成新翻译key:', key)
        }

        // 生成对应的i18n调用
        const fullKey = !isExistingKey && moduleName ? `${moduleName}.${key}` : key
        const i18nCall = apiType === 'composition' ? `i18n.t('${fullKey}')` : `this.$t('${fullKey}')`

        // 生成调用，使用多个参数传递
        let parameterList = ''
        if (uniqueVariables.length > 0) {
          parameterList = ', ' + '[' + uniqueVariables.join(', ') + ']'
        }

        const replacement = apiType === 'composition' ? `i18n.t('${fullKey}'${parameterList})` : `this.$t('${fullKey}'${parameterList})`

        replacements.push({
          original: match,
          replacement,
          key,
          variables: uniqueVariables
        })
        return replacement
      } else {
        // 不包含变量的普通模板字符串
        let key = findExistingKey(cleanText, reverseTranslations)
        let isExistingKey = !!key
        if (key) {
          console.log('🔍 找到现有翻译key:', key)
        } else {
          key = generatePinyinKey(cleanText)
          // 记录翻译
          translations[key] = cleanText
          console.log('🆕 生成新翻译key:', key)
        }

        // 只对新生成的key添加模块前缀
        const fullKey = !isExistingKey && moduleName ? `${moduleName}.${key}` : key
        const replacement = apiType === 'composition' ? `i18n.t('${fullKey}')` : `this.$t('${fullKey}')`

        replacements.push({
          original: match,
          replacement,
          key
        })
        return replacement
      }
    }

    return match
  })

  // 处理单引号字符串中的中文
  newContent = newContent.replace(singleQuoteRegex, (match, ...args) => {
    const offset = args[args.length - 2] // 获取匹配位置

    // 检查是否在注释范围内
    if (isInCommentRange(offset, commentRanges)) {
      return match
    }

    // 如果内容包含中文，则替换为i18n.t('key')并记录
    if (containsChinese(match)) {
      // 去掉前后的单引号
      const chineseText = match.slice(1, -1)
      const cleanText = cleanTemplateText(chineseText)

      let key = findExistingKey(cleanText, reverseTranslations)
      let isExistingKey = !!key
      if (key) {
        console.log('🔍 找到现有翻译key:', key)
      } else {
        key = generatePinyinKey(cleanText)
        // 记录翻译
        translations[key] = cleanText
        console.log('🆕 生成新翻译key:', key)
      }

      // 只对新生成的key添加模块前缀
      const fullKey = !isExistingKey && moduleName ? `${moduleName}.${key}` : key
      const replacement = apiType === 'composition' ? `i18n.t('${fullKey}')` : `this.$t('${fullKey}')`

      replacements.push({
        original: match,
        replacement,
        key
      })
      return replacement
    }
    return match
  })

  if (replacements.length > 0) {
    console.log(`\n✅ Script替换完成: 共替换 ${replacements.length} 处内容`)
  }

  return {
    newContent,
    translations
  }
}

// 检查是否有i18n.t()调用
function hasI18nTCall(content) {
  return /i18n\.t\s*\(/.test(content)
}

// 检查是否已经导入i18n
function hasI18nImport(content) {
  return content.includes('import { i18n }') || content.includes('import i18n') || content.includes('const i18n =')
}

// 添加i18n导入语句
function addI18nImport(content) {
  // 如果已经有import语句，在最后一个import后添加
  const lastImportIndex = content.lastIndexOf('import')
  if (lastImportIndex !== -1) {
    const endOfLastImport = content.indexOf('\n', lastImportIndex) + 1
    return content.slice(0, endOfLastImport) + "import { i18n } from '@/i18n'\n" + content.slice(endOfLastImport)
  }
  // 如果没有import语句，在文件开头添加
  return "import { i18n } from '@/i18n'\n" + content
}

// 从Vue文件中提取template内容
function extractTemplateContent(content) {
  // 找到第一个<template>标签的位置
  const startTagMatch = content.match(/<template(?:\s[^>]*)?>/i)
  if (!startTagMatch) {
    return null
  }

  const startTagIndex = startTagMatch.index
  const startTag = startTagMatch[0]

  // 从<template>标签结束位置开始查找
  let index = startTagIndex + startTag.length
  let templateCount = 1 // 当前template标签嵌套层级

  // 逐字符查找，直到找到匹配的</template>
  while (index < content.length && templateCount > 0) {
    // 查找下一个template相关标签
    const remainingContent = content.slice(index)
    const nextTemplateMatch = remainingContent.match(/<\/?template(?:\s[^>]*)?>/i)

    if (!nextTemplateMatch) {
      return null
    }

    const tagIndex = index + nextTemplateMatch.index
    const tag = nextTemplateMatch[0]

    if (tag.startsWith('</')) {
      // 这是结束标签
      templateCount--
    } else {
      // 这是开始标签
      templateCount++
    }

    // 移动到当前标签之后
    index = tagIndex + tag.length

    // 如果找到了匹配的结束标签
    if (templateCount === 0) {
      const endTagIndex = tagIndex
      const templateContent = content.slice(startTagIndex + startTag.length, endTagIndex)

      return {
        fullMatch: content.slice(startTagIndex, index),
        content: templateContent,
        tag: startTag
      }
    }
  }

  return null
}

// 替换模板中的中文内容
function replaceTemplateContent(templateContent, reverseTranslations = {}, moduleName = '') {
  // 匹配>和<之间的内容，但不包括空白字符
  const textContentRegex = />([^><]+)</g
  // 匹配HTML属性中的内容
  // 1. 静态属性: attribute="content" 或 attribute='content'
  // 2. 动态属性: :attribute="'content'" 或 v-bind:attribute="'content'"
  const staticAttributeRegex = /([a-zA-Z][\w-]*)=(["'])([^"']*?)\2/g
  const dynamicAttributeRegex = /:([a-zA-Z][\w-]*)=("[^"]*"|'[^']*')/g

  let replacements = []
  let translations = {}

  // 获取HTML注释范围
  const commentRanges = getHtmlCommentRanges(templateContent)

  // 首先处理标签间的文本内容
  let newContent = templateContent.replace(textContentRegex, (match, text, offset) => {
    // 检查是否在注释范围内
    if (isInCommentRange(offset, commentRanges)) {
      return match
    }

    // 如果文本内容包含 {{ }} 表达式，直接跳过不处理
    if (/\{\{[\s\S]*?\}\}/.test(text.trim())) {
      console.log('⏭️  跳过包含Vue表达式的文本:', text.trim())
      return match
    }

    // 如果内容包含中文，则处理
    if (containsChinese(text.trim())) {
      const cleanText = cleanTemplateText(text)

      // 不包含变量的普通中文文本
      let key = findExistingKey(cleanText, reverseTranslations)
      let isExistingKey = !!key
      if (key) {
        console.log('🔍 找到现有翻译key:', key)
      } else {
        key = generatePinyinKey(cleanText)
        // 记录翻译
        translations[key] = cleanText
        console.log('🆕 生成新翻译key:', key)
      }

      // 只对新生成的key添加模块前缀
      const fullKey = !isExistingKey && moduleName ? `${moduleName}.${key}` : key
      const replacement = `>{{ $t('${fullKey}') }}<`

      replacements.push({
        original: match,
        replacement,
        type: 'text',
        key,
        originalText: cleanText
      })
      return replacement
    }
    return match
  })

  // 处理动态绑定属性中的内容 :label="'早上好'"
  newContent = newContent.replace(dynamicAttributeRegex, (match, attrName, attrValue, offset) => {
    // 检查是否在注释范围内
    if (isInCommentRange(offset, commentRanges)) {
      return match
    }

    // 检查属性值是否是字符串格式 'content' 或 "content"
    const stringContentMatch = attrValue.match(/^(['"])(.*?)\1$/)
    if (stringContentMatch) {
      const stringContent = stringContentMatch[2]

      // 如果已经包含$t国际化函数，跳过处理
      if (stringContent.includes('$t(')) {
        return match
      }

      // 如果字符串内容包含中文，则替换
      if (containsChinese(stringContent)) {
        // 检查是否有额外的引号包围（如 "'早上好'" 中的单引号），如果有则去掉
        let actualContent = stringContent
        const extraQuoteMatch = stringContent.match(/^(['"])(.*?)\1$/)
        if (extraQuoteMatch) {
          actualContent = extraQuoteMatch[2]
        }

        const cleanText = cleanTemplateText(actualContent)
        let key = findExistingKey(cleanText, reverseTranslations)
        let isExistingKey = !!key
        if (key) {
          console.log('🔍 找到现有翻译key:', key)
        } else {
          key = generatePinyinKey(cleanText)
          // 记录翻译
          translations[key] = cleanText
          console.log('🆕 生成新翻译key:', key)
        }

        // 动态属性使用 $t('key') 的格式，只对新生成的key添加模块前缀
        const fullKey = !isExistingKey && moduleName ? `${moduleName}.${key}` : key
        const replacement = `:${attrName}="$t('${fullKey}')"`
        replacements.push({
          original: match,
          replacement,
          type: 'dynamic-attribute',
          attrName,
          key,
          originalText: cleanText
        })
        return replacement
      }
    }
    return match
  })

  // 处理静态HTML属性中的内容 - 需要排除已被动态属性处理的部分
  newContent = newContent.replace(staticAttributeRegex, (match, attrName, quote, attrValue, offset) => {
    // 检查是否在注释范围内
    if (isInCommentRange(offset, commentRanges)) {
      return match
    }

    // 跳过以冒号开头的动态属性，因为它们已经被动态属性正则处理过了
    const beforeAttr = newContent.substr(0, newContent.indexOf(match))
    if (beforeAttr.endsWith(':') || beforeAttr.endsWith('v-bind:')) {
      return match
    }

    // 如果属性值包含中文，则替换为$t('key')并转换为动态属性
    if (containsChinese(attrValue)) {
      const cleanText = cleanTemplateText(attrValue)
      let key = findExistingKey(cleanText, reverseTranslations)
      let isExistingKey = !!key
      if (key) {
        console.log('🔍 找到现有翻译key:', key)
      } else {
        key = generatePinyinKey(cleanText)
        // 记录翻译
        translations[key] = cleanText
        console.log('🆕 生成新翻译key:', key)
      }

      // 静态属性转换为动态属性：label="中文" => :label="$t('key')"，只对新生成的key添加模块前缀
      const fullKey = !isExistingKey && moduleName ? `${moduleName}.${key}` : key
      const replacement = `:${attrName}="$t('${fullKey}')"`
      replacements.push({
        original: match,
        replacement,
        type: 'static-attribute',
        attrName,
        key,
        originalText: cleanText
      })
      return replacement
    }
    return match
  })

  if (replacements.length > 0) {
    console.log(`\n✅ Template替换完成: 共替换 ${replacements.length} 处内容`)
  }

  return {
    newContent,
    translations
  }
}

// 检测Vue文件使用的API类型
function detectVueApiType(scriptContent) {
  // 检查是否使用Composition API
  if (
    scriptContent.includes('setup()') ||
    scriptContent.includes('setup (') ||
    scriptContent.includes('<script setup>') ||
    scriptContent.includes('import { ') ||
    scriptContent.includes('import {') ||
    scriptContent.match(/from\s+['"]vue['"]/)
  ) {
    return 'composition'
  }

  // 默认为Options API
  return 'options'
}

// 检测.js文件应该使用的API类型
function detectJsFileApiType(filePath, content) {
  const fileName = path.basename(filePath, '.js')

  // 如果是mixin文件，使用Options API（this.$t）
  if (fileName.toLowerCase().includes('mixin')) {
    return 'options'
  }

  // 检查文件内容是否是Vue组件风格的对象
  // 如果包含典型的Vue选项API结构，使用Options API
  if (
    content.includes('export default {') &&
    (content.includes('data()') ||
      content.includes('methods:') ||
      content.includes('computed:') ||
      content.includes('mounted()') ||
      content.includes('created()'))
  ) {
    return 'options'
  }

  // 检查是否是配置文件（包含常量导出但没有this上下文）
  // 配置文件通常只有export const，不应该使用this.$t
  if (content.includes('export const') && !content.includes('this.')) {
    return 'composition'
  }

  // 如果文件内容包含this.（说明是在Vue组件上下文中），使用Options API
  if (content.includes('this.')) {
    return 'options'
  }

  // 默认使用Composition API风格（i18n.t + import）
  // 适用于普通的工具函数、配置文件等
  return 'composition'
}

// 处理单个文件
async function processFile(filePath, allTranslations, reverseTranslations = {}) {
  try {
    // 读取文件内容
    const content = await fs.readFile(filePath, 'utf8')
    console.log('\n开始处理文件:', filePath)

    // 如果是.vue文件，处理template和script内容
    const ext = path.extname(filePath)
    if (ext === '.vue') {
      let newContent = content

      // 处理template内容
      console.log('开始处理template部分')
      const templateData = extractTemplateContent(content)
      if (templateData) {
        const { newContent: newTemplateContent, translations: templateTranslations } = replaceTemplateContent(
          templateData.content,
          reverseTranslations,
          config.moduleName
        )
        newContent = content.replace(templateData.fullMatch, `${templateData.tag}${newTemplateContent}</template>`)

        // 合并template翻译
        Object.assign(allTranslations, templateTranslations)
      }

      // 处理script内容
      console.log('开始处理script部分')
      const scriptData = extractScriptContent(newContent)
      if (scriptData) {
        const apiType = detectVueApiType(scriptData.content)
        console.log(`检测到API类型: ${apiType}`)

        const { newContent: newScriptContent, translations } = replaceQuotedContent(scriptData.content, apiType, reverseTranslations, config.moduleName)

        // 检查并添加i18n导入 (Composition API且有i18n.t()调用时需要)
        let finalContent = newScriptContent
        if (apiType === 'composition' && (Object.keys(translations).length > 0 || hasI18nTCall(newScriptContent)) && !hasI18nImport(newScriptContent)) {
          finalContent = addI18nImport(newScriptContent)
          console.log('✅ 已添加i18n导入语句')
        }

        // 替换原文件中的script内容
        newContent = newContent.replace(scriptData.fullMatch, `${scriptData.tag}${finalContent}</script>`)

        // 合并翻译
        Object.assign(allTranslations, translations)
      }

      // 写入新内容
      await fs.writeFile(filePath, newContent)
      console.log('文件处理完成')
      console.log('==========================================')
    } else if (ext === '.js') {
      // 处理.js文件
      console.log('开始处理.js文件内容')

      const apiType = detectJsFileApiType(filePath, content)
      console.log(`检测到API类型: ${apiType}`)

      const { newContent: newScriptContent, translations } = replaceQuotedContent(content, apiType, reverseTranslations, config.moduleName)

      // 检查并添加i18n导入 (只在Composition API且有翻译内容时需要)
      let finalContent = newScriptContent
      if (apiType === 'composition' && (Object.keys(translations).length > 0 || hasI18nTCall(newScriptContent)) && !hasI18nImport(newScriptContent)) {
        finalContent = addI18nImport(newScriptContent)
        console.log('✅ 已添加i18n导入语句')
      }

      // 合并翻译
      Object.assign(allTranslations, translations)

      // 写入新内容
      await fs.writeFile(filePath, finalContent)
      console.log('文件处理完成')
      console.log('==========================================')
    }
  } catch (error) {
    console.error('处理文件出错:', error)
  }
}

// 递归读取目录下的所有文件
async function readFilesRecursively(dirPath, allTranslations, reverseTranslations) {
  try {
    const files = await fs.readdir(dirPath)

    for (const file of files) {
      const fullPath = path.join(dirPath, file)
      const stat = await fs.stat(fullPath)

      if (stat.isDirectory()) {
        // 如果是目录，递归处理
        console.log('进入子目录:', fullPath)
        await readFilesRecursively(fullPath, allTranslations, reverseTranslations)
      } else if (stat.isFile()) {
        // 如果是文件，检查扩展名并处理
        const ext = path.extname(file)
        if (config.fileExtensions.includes(ext)) {
          await processFile(fullPath, allTranslations, reverseTranslations)
        }
      }
    }
  } catch (error) {
    console.error('读取目录出错:', dirPath, error)
  }
}

// 创建单文件备份
async function createFileBackup(filePath) {
  try {
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, '-')
      .slice(0, 19)

    const dir = path.dirname(filePath)
    const fileName = path.basename(filePath)
    const ext = path.extname(fileName)
    const nameWithoutExt = path.basename(fileName, ext)

    const backupPath = path.join(dir, `${nameWithoutExt}_backup_${timestamp}${ext}`)

    console.log('开始备份文件...')
    console.log('源文件:', filePath)
    console.log('备份文件:', backupPath)

    await fs.copyFile(filePath, backupPath)
    console.log('文件备份完成!')
    return backupPath
  } catch (error) {
    console.error('创建文件备份失败:', error)
    throw error
  }
}

// 读取目录下的所有文件
async function readFiles() {
  try {
    console.log('\n' + '='.repeat(60))
    console.log('🚀 开始执行国际化处理脚本')
    console.log('='.repeat(60))
    console.log('📂 目标路径:', config.searchPath)
    console.log('🔧 处理模式:', config.isFile ? '单文件模式' : '目录递归模式')
    console.log('📄 输出文件:', config.outputPath)
    if (config.moduleName) {
      console.log('🏷️  翻译模块:', config.moduleName)
    } else {
      console.log('🔧 模式: 平铺结构（无命名空间）')
    }
    if (!config.isFile) {
      console.log('🔧 支持文件类型:', config.fileExtensions.join(', '))
    }
    console.log('='.repeat(60))

    const allTranslations = {}
    const { reverseTranslations } = await loadExistingTranslations()

    if (config.isFile) {
      // 单文件处理模式
      await createFileBackup(config.searchPath)
      console.log('\n开始处理单个文件:', config.searchPath)
      await processFile(config.searchPath, allTranslations, reverseTranslations)
    } else {
      // 目录处理模式
      await createDirectoryBackup(config.searchPath)
      console.log('\n开始递归处理目录:', config.searchPath)
      await readFilesRecursively(config.searchPath, allTranslations, reverseTranslations)
    }

    // 生成翻译文件结构（支持可选的命名空间）
    const finalTranslations = {}
    if (config.moduleName && Object.keys(allTranslations).length > 0) {
      // 使用命名空间模式
      finalTranslations[config.moduleName] = allTranslations
    } else {
      // 平铺模式
      Object.assign(finalTranslations, allTranslations)
    }

    const jsonContent = JSON.stringify(finalTranslations, null, 2)
    await fs.writeFile(config.outputPath, jsonContent, 'utf8')

    console.log('\n' + '='.repeat(60))
    console.log('✅ 处理完成！')
    console.log('='.repeat(60))
    console.log('📄 翻译文件已生成:', config.outputPath)
    console.log('📊 包含翻译条目:', Object.keys(allTranslations).length, '条')
    console.log('🔧 处理模式:', config.isFile ? '单文件模式' : '目录递归模式')
    if (config.moduleName) {
      console.log('🏷️  翻译模块:', config.moduleName)
    } else {
      console.log('🔧 结构模式: 平铺结构')
    }
    console.log('💾 原始文件已备份')
    console.log('='.repeat(60))

    if (Object.keys(allTranslations).length > 0) {
      console.log('\n📝 生成的翻译结构预览:')
      console.log(`{`)

      if (config.moduleName) {
        // 命名空间模式预览
        console.log(`  "${config.moduleName}": {`)
        const sampleKeys = Object.keys(allTranslations).slice(0, 3)
        sampleKeys.forEach(key => {
          console.log(`    "${key}": "${allTranslations[key]}"${key === sampleKeys[sampleKeys.length - 1] ? '' : ','}`)
        })
        if (Object.keys(allTranslations).length > 3) {
          console.log(`    // ... 还有 ${Object.keys(allTranslations).length - 3} 条翻译`)
        }
        console.log(`  }`)
      } else {
        // 平铺模式预览
        const sampleKeys = Object.keys(allTranslations).slice(0, 3)
        sampleKeys.forEach(key => {
          console.log(`  "${key}": "${allTranslations[key]}"${key === sampleKeys[sampleKeys.length - 1] ? '' : ','}`)
        })
        if (Object.keys(allTranslations).length > 3) {
          console.log(`  // ... 还有 ${Object.keys(allTranslations).length - 3} 条翻译`)
        }
      }

      console.log(`}`)

      // 显示使用方式
      console.log('\n💡 使用方式:')
      if (config.moduleName) {
        console.log(`  Template: {{ $t('${config.moduleName}.keyName') }}`)
        console.log(`  Script: this.$t('${config.moduleName}.keyName')`)
      } else {
        console.log(`  Template: {{ $t('keyName') }}`)
        console.log(`  Script: this.$t('keyName')`)
      }
    } else {
      console.log('\n💡 没有找到需要替换的中文内容')
      if (config.isFile) {
        console.log('   目标文件可能不包含中文文本，或者所有中文都已经被替换为翻译key')
      } else {
        console.log('   目标目录中的文件可能不包含中文文本，或者所有中文都已经被替换为翻译key')
      }
    }
  } catch (error) {
    console.error('\n❌ 处理出错:', error)
    process.exit(1)
  }
}

// 运行脚本
readFiles()
