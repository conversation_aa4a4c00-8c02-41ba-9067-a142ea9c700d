const fs = require('fs')
const path = require('path')
const XLSX = require('xlsx')

// 需要导出的文件所在目录
let fileDirectoryPath = '../../src/i18n/langs'

let allData = []

// 函数用于递归遍历文件夹并导入所有 JSON 文件
function requireAllJSONFiles(directory) {
  //自动读取文件夹下面的所有文件
  // const files = fs.readdirSync(directory)
  //自定义设置遍历顺序
  const files = [
    './cn.json',
    './en.json',
    './ar.json',
    './bg.json',
    './de.json',
    './es.json',
    './fa.json',
    './fr.json',
    './id.json',
    './it.json',
    './pt.json',
    './ru.json',
    './tw.json',
    './vi.json',
    './he.json',
    './nl.json',
    './tr.json',
    './pl.json',
    './ro.json',
    './sq.json',
    './ka.json',
    './lo.json',
    './mn.json',
    './am.json'
  ]
  files.forEach((file, index) => {
    const filePath = path.join(directory, file)
    // 检查文件是否是 JSON 文件
    if (fs.statSync(filePath).isFile() && path.extname(file) === '.json') {
      const jsonData = require(filePath)
      let languageKey = 'language' + index //生成excel里这门语言这列的表头标题
      filterData(jsonData, null, languageKey)
      // 最终生成格式，数组里包含相同key的对象，然后给xlsx转换成excel
      // [
      //   {
      //     "key":'sdfsfsdf_dfdsgfggdsf',
      //     "value1中文":'你好',
      //     "value2英文":'hello',
      //     "value3西班牙语":'hello1',
      //   },
      //   {
      //     "key":'sdfsfsdf_dfdsgfggdsf12',
      //     "value1中文":'你好12',
      //     "value2英文":'hello12',
      //     "value3西班牙语":'hello1',
      //   },
      // ]
    } else if (fs.statSync(filePath).isDirectory()) {
      // 如果是子文件夹，则递归调用此函数
      requireAllJSONFiles(filePath)
    }
  })
}
function filterData(jsonData, preKey, languageKey) {
  for (let key in jsonData) {
    if (typeof jsonData[key] === 'string') {
      let finalKey = preKey ? preKey + '.' + key : key
      let keyIndex = allData.findIndex(item => item.key === finalKey)
      if (keyIndex === -1) {
        allData.push({ key: finalKey, [languageKey]: jsonData[key] })
      } else {
        allData[keyIndex][languageKey] = jsonData[key]
      }
    } else {
      //对象需要递归遍历
      let nextKey = preKey ? preKey + '.' + key : key
      filterData(jsonData[key], nextKey, languageKey)
    }
  }
}

let directoryPath = path.join(__dirname, fileDirectoryPath)

requireAllJSONFiles(directoryPath)

// 示例 JSON 数据
// const exampleJson = [
//   { name: 'John', age: 30 },
//   { name: 'Jane', age: 25 },
//   { name: 'liming', age: 40 },
// ];

// 创建一个工作簿
const workbook = XLSX.utils.book_new()

// 创建一个工作表
const worksheet = XLSX.utils.json_to_sheet(allData)

// 将工作表添加到工作簿
XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet 1')

// 将工作簿保存为 Excel 文件
let outputPath = path.join(__dirname, './' + 'PC翻译.xlsx')
XLSX.writeFile(workbook, outputPath)

console.log('Excel 文件已生成')
