const fs = require('fs')
const path = require('path')
const xlsx = require('xlsx')

//导入的目录
const directoryPath = '../../src/i18n/langs/'

// 1. 读取Excel文件，文件名对应当前目录下的翻译excel文件名
let workbook = xlsx.readFile(path.join(__dirname, 'PC翻译.xlsx'))

// 2. 选择要处理的工作表（Sheet），通常为第一个工作表
let sheetName = workbook.SheetNames[0]
let sheet = workbook.Sheets[sheetName]

// 3. 将Excel文件转换为数据
let importExcelData = xlsx.utils.sheet_to_json(sheet)
// excel转成json的数据格式示例如下(是个数组)：
// [
//   { key: 'lg.importRecord', language0: '导入记录', language1: 'Import Record'... },
//   { key: 'lg.saleRecord', language0: '销售记录', language1: 'Sales Record'... },
//   { key: 'lg.transRecord', language0: '转移记录', language1: 'Transfer Record'... },
//   { key: 'lg.importBatch', language0: '导入批次', language1: 'Import batch'... },
//   { key: 'KFCuQkp2b0zldaAxmGwgB', language0: '邮箱未注册', language1: 'Email is not registered'... },
//    ...
// ]
// console.log(importData)

// 以中文翻译文件为模板Json生成其他语言翻译文件
const templateData = require(path.join(__dirname, '../../src/i18n/langs/cn.json'))

//需要生成的翻译文件枚举
const importDataMaps = [
  // { title: 'language0', fileName: 'cn.json' },
  { title: 'language1', fileName: 'en.json' },
  { title: 'language2', fileName: 'ar.json' },
  { title: 'language3', fileName: 'bg.json' },
  { title: 'language4', fileName: 'de.json' },
  { title: 'language5', fileName: 'es.json' },
  { title: 'language6', fileName: 'fa.json' },
  { title: 'language7', fileName: 'fr.json' },
  { title: 'language8', fileName: 'id.json' },
  { title: 'language9', fileName: 'it.json' },
  { title: 'language10', fileName: 'pt.json' },
  { title: 'language11', fileName: 'ru.json' },
  { title: 'language12', fileName: 'tw.json' },
  { title: 'language13', fileName: 'vi.json' },
  { title: 'language14', fileName: 'he.json' },
  { title: 'language15', fileName: 'nl.json' },
  { title: 'language16', fileName: 'tr.json' },
  { title: 'language17', fileName: 'pl.json' },
  { title: 'language18', fileName: 'ro.json' },
  { title: 'language19', fileName: 'sq.json' },
  { title: 'language20', fileName: 'ka.json' },
  { title: 'language21', fileName: 'lo.json' },
  { title: 'language22', fileName: 'mn.json' },
  { title: 'language23', fileName: 'am.json' }
]

// 4. 遍历枚举生成对应JSON数据文件
exportAlltoJson(importDataMaps)
console.log('Excel文件已成功转换为JSON文件')

function exportAlltoJson(importDataMaps) {
  //需要导出的那一列语言的表头标题(对应导出的excel里的列表头值language0，language1，language2...)
  let generateLangKey = ''
  //导出的一列语言的json数据
  let exportJson = {}
  for (let i = 0; i < importDataMaps.length; i++) {
    generateLangKey = importDataMaps[i].title
    exportJson = generateData(importExcelData, templateData, {}, null, generateLangKey)
    // 4. 将JSON数据写入文件
    fs.writeFileSync(path.join(__dirname, directoryPath + importDataMaps[i].fileName), JSON.stringify(exportJson, null, 2))
  }
}

/**
 * @importData {Array} excel转成json的数据
 * @templateJson {Object} 当前json对象，初调方法时是中文翻译文件的json对象templateData
 * @generateJson {Object} 生成的新json对象，初调方法传{}
 * @preKey {String | null } 嵌套对象的key拼凑'.'，
 * 例如：
 * {
 *   lg:{
 *    limites:{
 *        order:'1222'
 *      }
 *    }
 * }
 * 拼凑后的key是lg.limits.order  这个是导出的excel的时候拼凑生成的，导入时也需要拼凑来去excel里找对应的key列的值
 * @generateLangKey {Object} 初始json对象
 * @returns {Object}
 *
 */

function generateData(importData, templateJson, generateJson, preKey, generateLangKey) {
  for (let key in templateJson) {
    if (typeof templateJson[key] === 'string') {
      let finalKey = preKey ? preKey + '.' + key : key
      for (let i = 0; i < importData.length; i++) {
        if (finalKey === importData[i].key) {
          generateJson[key] = importData[i][generateLangKey]
        }
      }
    } else {
      //对象需要递归遍历
      let nextKey = preKey ? preKey + '.' + key : key
      generateJson[key] = generateData(importData, templateJson[key], {}, nextKey, generateLangKey)
    }
  }
  return generateJson
}
