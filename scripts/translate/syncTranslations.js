const fs = require('fs')
const path = require('path')
const axios = require('axios')

// DeepSeek API配置
const DEEPSEEK_API_KEY = '***********************************'
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions'

// 语言代码映射
// const LANG_CODE_MAP = {
//   bg: '孟加拉语'
// }

const LANG_CODE_MAP = {
  en: 'English',
  ar: 'Arabic',
  bg: '孟加拉语',
  de: 'German',
  es: 'Spanish',
  fa: 'Persian',
  fr: 'French',
  he: 'Hebrew',
  id: 'Indonesian',
  it: 'Italian',
  ka: 'Georgian',
  lo: 'Lao',
  mn: 'Mongolian',
  nl: 'Dutch',
  pl: '波兰语',
  pt: '葡萄牙语',
  ro: '罗马尼亚语',
  ru: '俄语',
  sq: '阿尔巴尼亚语',
  tr: '土耳其语',
  tw: 'Traditional Chinese',
  vi: 'Vietnamese',
  am: 'አማርኛ'
}

// 语言文件目录
const LANGS_DIR = path.resolve(__dirname, '../../src/i18n/langs')

// 读取中文翻译文件作为基准
const cnTranslations = JSON.parse(fs.readFileSync(path.join(LANGS_DIR, 'cn.json'), 'utf8'))

// 获取对象中指定路径的值
function getValueByPath(obj, path) {
  const parts = path.split('.')
  let current = obj

  for (const part of parts) {
    if (!current || current[part] === undefined) return undefined
    current = current[part]
  }

  return current
}

// 提取需要翻译的文本
function extractTranslatableTexts(sourceObj, targetObj, prefix = '', result = new Map()) {
  for (const key in sourceObj) {
    const currentPath = prefix ? `${prefix}.${key}` : key

    if (typeof sourceObj[key] === 'object' && !Array.isArray(sourceObj[key])) {
      extractTranslatableTexts(sourceObj[key], targetObj, currentPath, result)
    } else {
      // 跳过特殊的key，如language
      if (key === 'language') continue

      // 获取目标对象中的值
      const targetValue = getValueByPath(targetObj, currentPath)

      // 如果目标值不存在，则需要翻译
      if (targetValue === undefined) {
        result.set(currentPath, sourceObj[key])
      }
    }
  }
  return result
}

// 将Map按大小分批
function splitMapIntoBatches(map, batchSize) {
  const batches = []
  let currentBatch = new Map()
  let count = 0

  for (const [key, value] of map) {
    currentBatch.set(key, value)
    count++

    if (count === batchSize) {
      batches.push(currentBatch)
      currentBatch = new Map()
      count = 0
    }
  }

  if (currentBatch.size > 0) {
    batches.push(currentBatch)
  }

  return batches
}

// 根据路径设置对象的值
function setValueByPath(obj, path, value) {
  const parts = path.split('.')
  let current = obj

  for (let i = 0; i < parts.length - 1; i++) {
    if (!current[parts[i]]) {
      current[parts[i]] = {}
    }
    current = current[parts[i]]
  }

  current[parts[parts.length - 1]] = value
}

// 同步所有语言文件
async function syncTranslations() {
  // 读取所有语言文件
  const files = fs.readdirSync(LANGS_DIR)

  for (const file of files) {
    if (file === 'cn.json') continue // 跳过中文文件

    const langCode = file.replace('.json', '')
    if (!LANG_CODE_MAP[langCode]) {
      console.log(`⚠️ Skipping ${file}: No language code mapping found`)
      continue
    }

    const filePath = path.join(LANGS_DIR, file)
    console.log(`\nProcessing ${file}...`)

    try {
      // 读取现有翻译
      let currentTranslations = {}
      try {
        currentTranslations = JSON.parse(fs.readFileSync(filePath, 'utf8'))
      } catch (e) {
        console.log(`Creating new file for ${file}`)
      }

      // 提取需要翻译的文本（只提取缺失的翻译）
      const textsToTranslate = extractTranslatableTexts(cnTranslations, currentTranslations)

      if (textsToTranslate.size === 0) {
        console.log(`✅ No new translations needed for ${file}`)
        continue
      }

      console.log(`Found ${textsToTranslate.size} missing translations in ${file}`)
      console.log('Missing keys:', Array.from(textsToTranslate.keys()))

      // 将文本分批，每批最多10条
      const batches = splitMapIntoBatches(textsToTranslate, 20)
      console.log(`Split ${textsToTranslate.size} texts into ${batches.length} batches`)

      // 用于跟踪总翻译数量
      let totalTranslated = 0

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i]
        console.log(`Processing batch ${i + 1}/${batches.length} (${batch.size} texts)...`)

        try {
          // 将所有文本组合成一个字符串，用特殊分隔符分隔
          const textToTranslate = Array.from(batch.values()).join('\n---SPLIT---\n')
          const prompt = `请将以下中文文本翻译成${LANG_CODE_MAP[langCode]}，保持每段文本的格式，并用"---SPLIT---"分隔,注意：你的回复中不允许包含其他内容，只返回翻译结果和分隔符，需要翻译的内容如下：\n\n${textToTranslate}`

          const response = await axios.post(
            DEEPSEEK_API_URL,
            {
              model: 'deepseek-chat',
              messages: [
                {
                  role: 'user',
                  content: prompt
                }
              ],
              temperature: 0.3,
              max_tokens: 2000
            },
            {
              headers: {
                Authorization: `Bearer ${DEEPSEEK_API_KEY}`,
                'Content-Type': 'application/json'
              }
            }
          )

          // 提取翻译结果并分割
          const translatedText = response.data.choices[0].message.content.trim()
          const translatedParts = translatedText.split('---SPLIT---').map(part => part.trim())

          // 将翻译结果应用到对象中并立即写入文件
          const paths = Array.from(batch.keys())
          paths.forEach((path, index) => {
            let translation = translatedParts[index] || batch.get(path)

            // 处理特殊语言的编码问题
            if (langCode === 'am') {
              // 对阿姆哈拉语进行特殊处理
              translation = translation
                .normalize('NFC') // 使用NFC标准化，这对阿姆哈拉语更合适
                .replace(/[^\S\r\n]/g, ' ') // 统一空白字符
                .replace(/\ufffd/g, '') // 移除替换字符
                .trim()
            }

            setValueByPath(currentTranslations, path, translation)
          })

          // 使用Buffer处理JSON字符串，确保正确的UTF-8编码
          const jsonContent = JSON.stringify(currentTranslations, null, 2)
          const buffer = Buffer.from(jsonContent, 'utf8')
          fs.writeFileSync(filePath, buffer)

          totalTranslated += batch.size
          console.log(`✅ Batch ${i + 1} completed. Progress: ${totalTranslated}/${textsToTranslate.size} translations`)

          // 添加延迟以避免API限制
          if (i < batches.length - 1) {
            console.log('Waiting 2 seconds before next batch...')
            await new Promise(resolve => setTimeout(resolve, 2000))
          }
        } catch (error) {
          console.error(`❌ Batch translation error (batch ${i + 1}): ${error.message}`)

          // 翻译失败时使用原文
          for (const [path, text] of batch) {
            setValueByPath(currentTranslations, path, text)
          }

          // 即使失败也写入文件，保存已翻译的内容
          fs.writeFileSync(filePath, JSON.stringify(currentTranslations, null, 2), 'utf8')

          totalTranslated += batch.size
          console.log(`⚠️ Batch ${i + 1} saved with original text. Progress: ${totalTranslated}/${textsToTranslate.size} translations`)

          // 如果是API限制相关错误，增加等待时间
          if (error.response?.status === 429 || error.response?.status === 402) {
            console.log('API rate limit hit, waiting 5 seconds...')
            await new Promise(resolve => setTimeout(resolve, 5000))
          }
        }
      }

      console.log(`✅ Successfully completed ${file} with ${totalTranslated} translations`)
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error)
    }
  }

  console.log('\nTranslation sync completed! 🎉')
}

// 执行同步
syncTranslations().catch(console.error)
