<template>
  <div class="right-box" ref="fullScreenTarget">
    <right-box-bar
      ref="rightBoxBar"
      :currentCar="currentCar"
      :hasCheckList="Object.values(mediaBoxes)"
      :currentSplitType="currentSplitType"
      @startPlay="startPlay"
      @setVideoPlay="setVideoPlay"
      :isFullScreen="isFullScreen"
      @handleFullScreen="() => handleFullScreen('.right-box')"
      @changeMapWindowVisible="changeMapWindowVisible"
      @changeScreen="handleChangeScreen"
      @clearAllBoxes="clearAllBoxes"
    />
    <div class="no-camera-box moretop">
      <div :class="`grid-${currentSplitType}`" class="draggable-container">
        <div
          class="no-camera-item"
          v-for="(item, index) in screenList"
          :key="index"
          :class="{ 'active-cell': selectedItem === index }"
          @click="selectedItem = index"
        >
          <div class="no-camera-text-box">
            <svg-icon icon-class="no-camera" className="no-camera"></svg-icon>
            <div class="no-camera-text">{{ item.text }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="video-box-container">
      <div :class="`grid-${currentSplitType}`" class="video-draggable-container">
        <div class="video-camera-item" v-for="(box, index) in visibleBoxes" :key="index" @contextmenu.prevent="showContextMenu($event, box, index)">
          <div class="video-cell" @click.stop="selectCell(box, index)">
            <template v-if="box">
              <!-- V5设备是H265格式webrtc不支持只能使用jessibuc播放器 -->
              <MultiScreenJessibucaLive
                :activeVideo="selectedCell == index"
                v-if="isV5device(box.car.machineType) || isTestUser || isFlv(box.channel)"
                @leftBoxFold="$emit('leftBoxFold')"
                :deviceInfo="box.car"
                :channelInfo="box.channel"
                :playerId="box.id"
                :hasCheckList="box.channel"
              />
              <!-- V6-V7设备是H264格式使用webrtc播放性能更好 -->
              <MulitScreenWebrtcLive
                :activeVideo="selectedCell == index"
                v-else
                @leftBoxFold="$emit('leftBoxFold')"
                :currentSplitType="currentSplitType"
                :hasCheckList="box.channel"
                :deviceInfo="box.car"
                :playerId="box.id"
                :channelInfo="box.channel"
                :isAccV7Pro="isAccV7Pro"
              />
            </template>
            <template v-else>
              <div v-if="!box" :class="{ 'active-empty': selectedCell == index }" class="empty-state"></div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- S 地图窗口 -->
    <device-position
      ref="devicePosition"
      class="position-box"
      v-if="mapWindowVisible"
      :targetCarId="currentCar.carId"
      @close="changeMapWindowVisible"
    ></device-position>
    <!-- E 地图窗口 -->
    <!-- 右键菜单 -->
    <DeviceCxtMenu
      ref="deviceContextMenu"
      class="device-context-menu"
      v-show="contextMenu.visible"
      :position="{ top: contextMenu.y, left: contextMenu.x }"
      :contextMenu="videoMenu"
      @startVideo="handleStartVideo(contextMenu.selectedBox, contextMenu.selectedBox ? contextMenu.selectedBox.channel : '')"
      @stopVideo="setVideoPlay(null, contextMenu.selectedBox ? contextMenu.selectedBox.id : '')"
      @clearVideo="clearVideo(contextMenu.selectedBox)"
    />
  </div>
</template>
<script setup>
import { onMounted, onUnmounted, ref, watch, computed } from 'vue'
import store from '@/store'
import { getControlRes, _videoWakeUp, startPull, _getDeviceTraffic } from '@/api'
import RightBoxBar from './RightBoxBar.vue'
import MultiScreenJessibucaLive from '@/components/Video/MultiScreenJessibucaLive.vue'
import MulitScreenWebrtcLive from '@/components/Video/MulitScreenWebrtcLive.vue'
import DeviceCxtMenu from '@/components/Tree/Device/DeviceCxtMenu.vue'
import DevicePosition from './DevicePosition.vue'
import deepClone from 'lodash/cloneDeep'
import { useFullScreen } from '@/hooks/useFullScreen'
import { CommandStatusMap } from '@/components/Device/v2/Command/components/comp/config'
/** 流量告警 */
import { getComponentVnode } from '@/constant/utils'
import FlowTipModel from '@/views/VideoMonitor/components/FlowTipModel.vue'
import { TRAFFIC_CONSUME_ENUM } from '@/views/Finance/VideoBandwidth/helper'
import { BASE_MESSAGE } from '@/base-ui/message'
import { MessageBox } from 'element-ui'

import { i18n } from '@/i18n'
const { handleFullScreen, isFullScreen } = useFullScreen()

const props = defineProps({
  isLeftBoxFold: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['leftBoxFold', 'updateHighlight'])
const videoMenu = ref([
  {
    name: i18n.t('TE2fBaGlpdBY0lKWjOTKq'),
    value: 2
  },
  {
    name: i18n.t('q9r2Om1U7SO896v3LSVI0'),
    value: 3
  },
  {
    name: i18n.t('3JCIEcBT9HpfXFK0Fu_TN'),
    value: 4
  }
])
const isTestUser = computed(() => store.state.user.isTestUser)
const mediaBoxes = ref({})
const visibleBoxes = computed({
  get: () => {
    // 将对象形式转换为数组形式
    const maxLength = currentSplitType.value
    const result = {}
    for (let i = 0; i < maxLength; i++) {
      result[i] = mediaBoxes.value[i] || null
    }
    return result
  },
  set: value => {
    mediaBoxes.value = { ...value }
  }
})

const isAccV7Pro = ref(false) // 是否为Acc关闭的V7-Pro设备
const currentSplitType = ref(4) // 当前分屏数
const screenList = ref([]) // 分屏数对应的数组
const selectedItem = ref(0)

const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  selectedBox: null,
  selectedIndex: null
})
const isV5device = machineType => [200, 221, 393].includes(machineType)
const isFlv = channel => channel.webUrl && channel.webUrl.includes('wss')

const mapWindowVisible = ref(false) // 地图信息右下角小窗口显隐控制
const changeCar = (car, channelNumber) => {
  const channelsToAdd = channelNumber ? [channelNumber] : car.channels
  addNum = channelsToAdd.length
  channelsToAdd.forEach(ch => {
    handleAddChannel(car, ch.channelNumber || ch)
  })
  mapWindowVisible.value = false
}
// const changeCar = (car, channelNumber) => {
//   // 检查是否已经有相同设备的通道在播放
//   const existingDeviceChannels = Object.values(mediaBoxes.value)
//     .filter(box => box && box.car.imei === car.imei)
//     .map(box => box.channel.channelNumber)

//   // 确定要添加的通道
//   let channelsToAdd = channelNumber ? [channelNumber] : car.channels.map(ch => ch.channelNumber || ch)

//   // 如果已经有相同设备的通道在播放，需要排除这些通道
//   if (existingDeviceChannels.length > 0 && !channelNumber) {
//     // 如果是播放整个设备，需要过滤掉已经在播放的通道
//     channelsToAdd = channelsToAdd.filter(ch => {
//       // 如果是对象，获取channelNumber
//       const chNum = typeof ch === 'object' ? ch.channelNumber : ch
//       return !existingDeviceChannels.includes(chNum)
//     })
//   }

//   // 如果过滤后没有需要添加的通道，直接返回
//   if (channelsToAdd.length === 0) {
//     return
//   }

//   addNum = channelsToAdd.length
//   addIndex = 0
//   startAddIndex = 0

//   // 从选中的单元格开始添加通道
//   channelsToAdd.forEach(ch => {
//     handleAddChannel(car, typeof ch === 'object' ? ch.channelNumber : ch)
//   })

//   mapWindowVisible.value = false
// }

/**
* 已经添加到视频中未清除的通道不会被重复添加
* 选中高亮逻辑 添加通道时，保持在空屏高亮直至最后一个通道
* //选中空屏-添加 高亮到空的通道
//选中通道-替换 高亮到当前通道
// case1:选空盒子(非第一个索引)

*/
const selectedCell = ref(null) // 视频盒子当前选中分屏索引

const handleAddChannel = (car, channelNumber) => {
  const targetChannel = car.channels.find(ch => ch.channelNumber == channelNumber)
  const newBoxId = `${car.imei}-${channelNumber}`

  // 检查是否已存在相同的盒子
  const existingBoxIndex = Object.entries(mediaBoxes.value).find(([_, box]) => box && box.id === newBoxId)?.[0]
  console.log(existingBoxIndex, 'existingBoxIndex')

  if (existingBoxIndex) {
    // 如果通道已存在，更新addIndex并直接播放
    addIndex += 1
    return startPlay(mediaBoxes.value[existingBoxIndex], mediaBoxes.value[existingBoxIndex].channel)
  }

  const newBox = {
    car: deepClone(car),
    channel: targetChannel,
    id: newBoxId
  }

  // 根据当前选中的单元格和车辆，决定是替换还是添加盒子
  if (selectedCell.value != null && deviceSelected.value) {
    handleReplaceBox(newBox)
    startAddIndex++
    if (startAddIndex === addNum || startAddIndex + addIndex === addNum) {
      startAddIndex = 0
      addIndex = 0
    }
  } else {
    handleAppendBox(newBox)
  }

  // 自动调整布局
  autoAdjustSplit()
}

//插入分屏
//选中状态在当前播放通道的下一个空屏幕 直至最后一个
const handleAppendBox = newBox => {
  const currentIndex = selectedCell.value === null ? null : Number(selectedCell.value)
  // 寻找所有空位
  const emptySlots = Array.from({ length: MAX_TOTAL }, (_, i) => i).filter(i => !(i in mediaBoxes.value))
  // 如果没有空位，提示用户并返回
  if (emptySlots.length === 0) {
    BASE_MESSAGE.closeAll()
    BASE_MESSAGE.warning(i18n.t('8_hhY_1Tx0o0w7Y1wdCRb'))
    selectedCell.value = null
    return
  }
  // 如果当前选中了一个索引
  if (currentIndex !== null) {
    // 如果当前索引是空的，直接在这里添加
    if (!(currentIndex in mediaBoxes.value)) {
      mediaBoxes.value = {
        ...mediaBoxes.value,
        [currentIndex]: newBox
      }
      selectedCell.value = findNextEmptySlot(currentIndex)
    }
    // 否则，找到下一个空位添加
    else {
      const nextEmptySlot = emptySlots[0]
      mediaBoxes.value = {
        ...mediaBoxes.value,
        [nextEmptySlot]: newBox
      }
      selectedCell.value = findNextEmptySlot(nextEmptySlot)
    }
  }
  // 如果没有选中索引，添加到第一个空位
  else {
    const firstEmptySlot = emptySlots[0]
    mediaBoxes.value = {
      ...mediaBoxes.value,
      [firstEmptySlot]: newBox
    }
    selectedCell.value = findNextEmptySlot(firstEmptySlot)
  }

  // 开始播放新的媒体盒子
  startPlay(newBox, newBox.channel)
}

//找到下一个空位
const findNextEmptySlot = currentIndex => {
  for (let i = currentIndex + 1; i < MAX_TOTAL; i++) {
    if (!(i in mediaBoxes.value)) {
      return i
    }
  }
  // 如果后面没有空位，返回null
  return null
}

// const handleReplaceBox = newBox => {
//   let insertIndex = parseInt(selectedCell.value) + startAddIndex

//   if (insertIndex >= MAX_TOTAL) {
//     return
//   }
//   mediaBoxes.value = {
//     ...mediaBoxes.value,
//     [insertIndex]: newBox
//   }

//   selectCell(newBox, selectedCell.value)
//   startPlay(newBox, newBox.channel, true)
// }
const handleReplaceBox = newBox => {
  let insertIndex = parseInt(selectedCell.value) + startAddIndex

  if (insertIndex >= MAX_TOTAL) {
    return
  }

  // 检查该位置是否已经有同一设备的通道
  const existingBoxAtIndex = mediaBoxes.value[insertIndex]
  if (existingBoxAtIndex && existingBoxAtIndex.car.imei === newBox.car.imei && existingBoxAtIndex.id === newBox.id) {
    // 如果是完全相同的通道，可以提示用户或者直接播放
    return startPlay(existingBoxAtIndex, existingBoxAtIndex.channel)
  }

  // 否则直接替换该位置的通道
  mediaBoxes.value = {
    ...mediaBoxes.value,
    [insertIndex]: newBox
  }

  // 开始播放但不改变选中框位置
  startPlay(newBox, newBox.channel, true)
}
const handleChangeScreen = num => {
  const oldBoxes = { ...mediaBoxes.value }
  const newMediaBoxes = {}
  // 停止被移除通道的监控
  Object.entries(oldBoxes).forEach(([index, box]) => {
    if (parseInt(index) >= num && box && box.channel.videoStatus) {
      setVideoPlay(null, box.id)
    }
    if (parseInt(index) < num) {
      newMediaBoxes[index] = box
    }
  })
  currentSplitType.value = num
  mediaBoxes.value = newMediaBoxes
  //选中状态是被减少的分屏索引 选中框将手动置为选中第一分屏
  if (selectedCell.value >= num) {
    selectedCell.value = 0 // 选中第一个分屏
    selectCell(oldBoxes[0], selectedCell.value)
  } else if (selectedCell.value === null) {
    selectCell()
  }
}
const autoAdjustSplit = () => {
  const filledCount = Object.keys(mediaBoxes.value).length
  if (filledCount >= MAX_TOTAL) {
    BASE_MESSAGE.closeAll()
    BASE_MESSAGE.warning(i18n.t('8_hhY_1Tx0o0w7Y1wdCRb'))
    // this.selectedCell = null
    return
  }
  // 当前分屏剩余容量检查
  const remaining = currentSplitType.value - filledCount
  if (remaining > 0) return
  // 扩容
  const nextSplit = getNextSplit(currentSplitType.value)
  if (nextSplit) {
    const newMediaBoxes = {}
    Object.entries(mediaBoxes.value).forEach(([index, box]) => {
      if (parseInt(index) < nextSplit) {
        newMediaBoxes[index] = box
      }
    })
    currentSplitType.value = nextSplit
    mediaBoxes.value = newMediaBoxes
  }
}
const cameraList = ref({}) // 可选摄像头
//选中某一个盒子
const selectCell = (box, index) => {
  selectedCell.value = index === null ? null : Number(index)
  //记录当前选中的通道列表
  if (box) {
    currentCar.value = { ...box.car, ...box.channel }
    cameraList.value = mediaBoxes.value
  } else {
    currentCar.value = null
    cameraList.value = []
    mapWindowVisible.value = false
  }
  hideContextMenu()
}
//清空某一通道
const clearVideo = box => {
  if (!box) return BASE_MESSAGE.warning(i18n.t('hJ_bvQO9HYG8ge-S_IDO-'))
  // 找到要清除的索引
  const indexToClear = Object.entries(mediaBoxes.value).find(([_, b]) => b && b.id === box.id)?.[0]
  if (!indexToClear) return

  const newMediaBoxes = { ...mediaBoxes.value }
  delete newMediaBoxes[Number(indexToClear)]
  mediaBoxes.value = newMediaBoxes
  if (Object.keys(mediaBoxes.value).length === 0) {
    currentCar.value = null
    selectedCell.value = null
  } else {
    const nextIndex = Number(indexToClear)
    selectCell(mediaBoxes.value[nextIndex], nextIndex)
  }
}
const hideContextMenu = () => {
  contextMenu.value.visible = false
}
const handleStartVideo = async (box, channel) => {
  if (!box) return
  const { data } = await _getDeviceTraffic({ carIds: [box.car.carId] })

  // 流量充足
  if (!data.length || data[0].dataStatus === TRAFFIC_CONSUME_ENUM.NORMAL) {
    return startPlay(box, channel)
  }

  // 流量告警
  const config = {
    props: {
      info: {
        machineName: box.car.machineName,
        dataStatus: data[0].dataStatus,
        usedDataSize: data[0].usedDataSize,
        totalDataSize: data[0].totalDataSize
      }
    }
  }
  MessageBox.confirm(getComponentVnode(FlowTipModel, config), {
    title: i18n.t('PromptMessage'),
    customClass: 'c-flow-tip',
    confirmButtonText: data[0].dataStatus === TRAFFIC_CONSUME_ENUM.TRAFFIC_WARN ? i18n.t('continuePlaying') : i18n.t('lg.confirm'),
    cancelButtonText: i18n.t('CancelPlayback'),
    showCancelButton: data[0].dataStatus !== TRAFFIC_CONSUME_ENUM.TRAFFIC_EXHAUSTED
  }).then(() => {
    startPlay(box, channel)
  })
}
const showContextMenu = (event, box, index) => {
  event.preventDefault()
  contextMenu.value.visible = true
  contextMenu.value.x = event.pageX
  contextMenu.value.y = event.pageY
  contextMenu.value.selectedBox = box
  contextMenu.value.selectedIndex = index
}
const currentCar = ref({})
const showCurrentCar = car => {
  currentCar.value = car
}
const clearAllBoxes = () => {
  emits('updateHighlight', [])
  mediaBoxes.value = {}
  currentCar.value = null
  selectedCell.value = null
  BASE_MESSAGE.closeAll()
  BASE_MESSAGE({
    message: i18n.t('Mwu8g570bWTiEqGKSZBKM', []),
    type: 'warning',
    duration: '1000'
  })
}
const getNextSplit = current => {
  // const splitLevels = [1, 4, 6, 9, 16]
  const splitLevels = [1, 4, 6, 9]
  const index = splitLevels.indexOf(current) // 获取索引
  return index >= 0 && index < splitLevels.length - 1 ? splitLevels[index + 1] : null
}
const isPlayingList = ref([]) // 播放中的摄像头
const setVideoPlay = (box, channel) => {
  /**
   * 是否指定设备or通道停止播放
   * 不指定的情况默认取全部盒子
   */
  if (!box && !channel) {
    // 使用 Object.values 替代 filter
    const isPlayingList = Object.values(mediaBoxes.value).filter(item => item !== null && item.channel.videoStatus)

    // 有播放中的摄像头，全部结束播放
    if (isPlayingList.length) {
      BASE_MESSAGE.closeAll()
      BASE_MESSAGE({
        message: i18n.t('Mwu8g570bWTiEqGKSZBKM', []),
        type: 'warning',
        duration: '1000'
      })
      const newMediaBoxes = {}
      Object.entries(mediaBoxes.value).forEach(([key, item]) => {
        if (item !== null) {
          newMediaBoxes[key] = {
            ...item,
            channel: {
              ...item.channel,
              videoStatus: 0,
              webUrl: ''
            }
          }
        }
      })
      mediaBoxes.value = newMediaBoxes
      return true
    }
    if (!box || channel) {
      BASE_MESSAGE({
        message: i18n.t('hJ_bvQO9HYG8ge-S_IDO-'),
        type: 'warning'
      })
      return
    }
    return false
  } else {
    if (channel) {
      const targetItem = Object.values(mediaBoxes.value).find(item => item != null && item.id === channel)
      if (targetItem && targetItem.channel.videoStatus) {
        if (targetItem.car.machineType === 265) {
          handleStopV7ProCommand(targetItem.car.machineName)
        }
        BASE_MESSAGE({
          message: i18n.t('Mwu8g570bWTiEqGKSZBKM', [targetItem.car.machineName]),
          type: 'warning',
          duration: '1000'
        })
        const newMediaBoxes = { ...mediaBoxes.value }
        const boxIndex = Object.entries(mediaBoxes.value).find(([_, item]) => item && item.id === channel)?.[0]

        if (boxIndex) {
          newMediaBoxes[boxIndex] = {
            ...targetItem,
            channel: {
              ...targetItem.channel,
              videoStatus: 0,
              webUrl: ''
            }
          }
        }

        mediaBoxes.value = newMediaBoxes
        return true
      }
    }
    if (box) {
      if (box.machineType === 265) {
        handleStopV7ProCommand(box.machineName)
      }

      // 使用 Object.values 和 filter 查找目标盒子
      const targetBoxes = Object.values(mediaBoxes.value).filter(item => item && item.car.carId == box.carId)

      if (targetBoxes.length > 0) {
        targetBoxes.forEach(targetBox => {
          if (targetBox.channel.videoStatus) {
            targetBox.channel.videoStatus = 0
            targetBox.channel.webUrl = ''
          }
        })
        BASE_MESSAGE({
          message: i18n.t('Mwu8g570bWTiEqGKSZBKM', [box.machineName]),
          type: 'warning',
          duration: '1000'
        })
      }
      box.channels.forEach(channel => {
        channel.videoStatus = 0
        channel.webUrl = ''
      })
      return true
    }
  }
}

const startPlay = async (box, channel, isReplace = false) => {
  // 没有选中摄像头
  if (!Object.values(mediaBoxes.value).length) {
    BASE_MESSAGE.closeAll()
    BASE_MESSAGE({
      message: i18n.t('hJ_bvQO9HYG8ge-S_IDO-'),
      type: 'warning'
    })
    return
  }

  if (!box && !channel) {
    BASE_MESSAGE({
      message: i18n.t('hJ_bvQO9HYG8ge-S_IDO-'),
      type: 'warning'
    })
    return
  }

  // 判断当前添加的通道or设备是 V7-pro
  if (box.car.machineType === 265 && box.car.carStatus.accStatus !== 1) {
    await handleV7WakeUpCommand(box, channel)
  } else {
    await playStream(box, channel, isReplace)
  }
}
const getStreamLoading = ref(false) // 获取播放流地址loading状态，true时再次点击开始监控按钮，拦截
const playStream = async (box, channel, isReplace) => {
  if (channel.videoStatus === 3 && !isReplace) {
    return
  }

  channel.videoStatus = 1 // 每个播放器窗口开始 loading
  getStreamLoading.value = true // 防止重复点击【开始播放按钮】
  try {
    const result = await getStreamData(box.car, channel.channelNumber)
    if (result.channelNumber === box.channel.channelNumber) {
      if (result.data) {
        // 正常返回播放流地址
        box.channel = {
          ...box.channel,
          videoStatus: 3,
          webUrl: isV5device(box.car.machineType) ? result.data.webUrl : result.data.webrtcUrl
        }
      } else if (result.code === '-113') {
        // 通道占用
        box.channel.videoStatus = 0
        BASE_MESSAGE.closeAll()
        BASE_MESSAGE({
          message: i18n.t('IKa5v1S4C9m3JqKSNCGDm'),
          type: 'warning'
        })
      } else {
        // 其他错误
        box.channel.videoStatus = 2
      }
    }

    getStreamLoading.value = false
  } catch (error) {
    getStreamLoading.value = false
  }
}

// 获取流播放接口
const getStreamData = async (car, channelNumber) => {
  let params = {
    channelNumber: channelNumber,
    deviceId: car.imei,
    latitude: car.carStatus.latc,
    longitude: car.carStatus.lonc,
    platform: 0,
    steamType: 1,
    tag: 0,
    transMode: 1
  }
  try {
    const { data, code } = await startPull(params)
    return { channelNumber, data, code }
  } catch (e) {
    return { channelNumber, data: null, code: null }
  }
}
const changeMapWindowVisible = () => {
  mapWindowVisible.value = !mapWindowVisible.value
}
/*
  用于记录v7pro下发唤醒指令 定时器
  结构 { `machineName`: { times: 0, channel: [Object] } }
 */
let v7ProTimes = {}

let addNum = 0
let addIndex = 0 // 用于记录当前添加的通道数量
let startAddIndex = 0
const MAX_TOTAL = 9 // 最大分屏数
const rightBoxBar = ref(null)

// V7-pro 唤醒指令下发
const handleV7WakeUpCommand = async (box, channel) => {
  const setVideoLoading = value => {
    if (channel.videoStatus === 3 || channel.videoStatus === 1) return
    box.channel.videoStatus = value // 直接赋值
  }
  setVideoLoading(1)
  const machineName = box.car.machineName

  // 如果已经存在唤醒指令，检查是否重复添加
  if (v7ProTimes[machineName]) {
    const isExis = v7ProTimes[machineName].channels.some(
      ([existingBox, existingChannel]) => existingBox.id === box.id && existingChannel.channelNumber === channel.channelNumber
    )
    if (!isExis) {
      v7ProTimes[machineName].channels.push([box, channel]) // 保持结构一致
    }
    return
  }

  // 初始化唤醒指令
  v7ProTimes[machineName] = {
    times: undefined,
    channels: [[box, channel]]
  }

  // 下发唤醒指令
  const { data } = await _videoWakeUp({ carId: box.car.carId })
  let num = 0

  v7ProTimes[machineName].times = setInterval(async () => {
    const result = await getControlRes({ serNO: data.serNO, controlType: 0 })
    num++

    if ([CommandStatusMap.ReplySuccess].includes(result.data.resCode)) {
      clearInterval(v7ProTimes[machineName]?.times)
      if (v7ProTimes[machineName]) {
        v7ProTimes[machineName].channels.forEach(([box, channel]) => {
          playStream(box, channel, true)
          setVideoLoading(0, box)
        })
        // 清理已完成的唤醒指令
        delete v7ProTimes[machineName]
      }
    }

    if (num === 10) {
      num = null
      setVideoLoading(0)
      clearInterval(v7ProTimes[machineName].times)
      rightBoxBar.value.isV7ProStart = false
      BASE_MESSAGE.error(i18n.t('唤醒失败'))
    }
  }, 1000)
}

// 停掉唤醒指令查询
const handleStopV7ProCommand = name => {
  if (v7ProTimes[name]) {
    clearInterval(v7ProTimes[name].times)
  }
  v7ProTimes[name] = null
}
/** 检验当前所有设备是否流量充足 */
const handleCheckDevices = async () => {
  const carIds = Object.values(mediaBoxes.value)
    .filter(box => box !== null)
    .reduce((pre, cur) => {
      if (cur && !pre.includes(cur.car.carId)) {
        pre.push(cur.car.carId)
      }
      return pre
    }, [])

  const [isPass, message, isExhausted, dataStatus, notPlayCarId] = await rightBoxBar.value.getDeviceTraffic(carIds)

  /** 恢复播放 - 只播放流量正常的设备 */
  const startAllDevices = () => {
    Object.values(mediaBoxes.value)
      .filter(box => box !== null)
      .forEach(box => {
        // 只播放流量正常的设备
        if (!notPlayCarId.includes(box.car.carId)) {
          startPlay(box, box.channel)
        }
      })
  }

  if (isPass) return startAllDevices()

  MessageBox.confirm(getComponentVnode(FlowTipModel, { props: { CustomContent: message, dataStatus } }), {
    title: i18n.t('PromptMessage'),
    customClass: 'c-flow-tip',
    confirmButtonText: isExhausted ? i18n.t('lg.confirm') : i18n.t('continuePlaying'),
    cancelButtonText: i18n.t('CancelPlayback')
  }).then(() => {
    // 无论是否有设备流量耗尽，都播放流量正常的设备
    startAllDevices()
  })
}
const playingState = ref([]) // 保存隐藏前的播放状态
let timer = null
let isStop = false
const handleVisibilityChange = () => {
  if (document.visibilityState === 'hidden') {
    timer = setTimeout(() => {
      const anyPlaying = Object.values(mediaBoxes.value).some(box => {
        return box && box.channel && box.channel.videoStatus === 3
      })
      isStop = anyPlaying

      Object.values(mediaBoxes.value).forEach(box => {
        if (box && box.channel.videoStatus) {
          setVideoPlay(null, box.id)
        }
      })
    }, 10000)
    return
  }
  timer && clearTimeout(timer)
  if (isStop) {
    handleCheckDevices()
    isStop = false
  }
}
onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange)
  document.addEventListener('click', hideContextMenu)
})
onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  document.removeEventListener('click', hideContextMenu)
})
const noCameraText = computed(() => {
  return i18n.t('asdf2353WEEEW34463E05')
})
watch(
  currentSplitType,
  newVal => {
    screenList.value = Array.from({ length: newVal }, (_, i) => ({
      text: noCameraText.value,
      id: i + 1
    }))
  },
  { immediate: true }
)
watch(
  () => mediaBoxes.value,
  newVal => {
    const highlightList = Object.values(newVal)
      .filter(box => box)
      .map(box => `${box.car.carId}-${box.channel.channelNumber}`)
    emits('updateHighlight', highlightList)
  },
  { deep: true }
)
const deviceSelected = ref(false)
watch(
  currentCar,
  newVal => {
    if (newVal) deviceSelected.value = true
    else deviceSelected.value = false
  },
  { deep: true }
)
defineExpose({
  changeCar,
  setVideoPlay,
  mediaBoxes,
  showCurrentCar
})
</script>

<style lang="scss" scoped>
.right-box {
  z-index: 1;
  flex: 1;
  height: calc(100vh - 92px);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: $containerRadius;
  .left-box-fold-container {
    position: absolute;
    left: 0;
    top: 27px;
    padding: 3px 8px 3px 5px;
    border: 1px solid $border;
    border-left: none;
    border-radius: 0 $inputRadius $inputRadius 0;
    background-color: #fff;
    cursor: pointer;
    z-index: 9999;
    flex-shrink: 0;
    box-sizing: border-box;
    img {
      width: 18px;
      height: 18px;
      transform: rotate(180deg);
    }
  }
  .video-box-container {
    width: 100%;
    height: calc(100% - 58px);
    margin-top: 10px;
    border-radius: $containerRadius;
    overflow: hidden;
    display: grid;
    .video-camera-item {
      position: relative;
      &.device-context-menu {
        position: absolute;
      }
    }
    .video-draggable-container {
      // 不同分屏模式下的栅格布局
      &.grid-1 {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr;
      }
      &.grid-4 {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
      }
      &.grid-6 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr; // 两列
        .video-camera-item:first-child {
          grid-column: 1 / span 2; // 第一列占据两列
          grid-row: 1 / span 2; // 第一行占据两行
        }
      }
      &.grid-9 {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(3, 1fr);
      }
      &.grid-16 {
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(4, 1fr);
      }

      .video-cell {
        position: absolute;
        width: 100%;
        height: 100%;
      }
    }
  }
  .no-camera-box {
    display: flex;
    display: grid;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    border-radius: 0 6px 6px 0;
    overflow: hidden;
    &.moretop {
      margin-top: 12px;
      top: 106px;
      height: calc(100% - 118px);
      border-radius: $containerRadius;
    }
    .bg-img {
      width: 100%;
      height: 100%;
      position: absolute;
    }
    .no-camera-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      &::before {
        content: '';
        position: absolute;
        display: block;
        width: 100%; // 默认大小
        height: 100%;
        background-image: url(@/assets/img/video/video_videoPlayerEmptyHolder.png); // 图片路径
        background-size: cover;
        background-repeat: no-repeat;
      }
    }
    .no-camera-text-box {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      .no-camera {
        // width: 110px;
        // height: 124px;
        height: 30%;
        width: 30%;
        top: 20%;
        left: 35%;
        position: absolute;
      }
      .no-camera-text {
        margin-top: 70px;
        border-bottom: 288px;
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        color: #b1d0fc;
        line-height: 22px;
      }
    }
    .el-icon-loading {
      font-size: 64px;
      color: white;
    }
  }

  .visibility-visible {
    visibility: visible;
    z-index: 3;
  }
  .visibility-hidden {
    visibility: hidden;
  }
  .call-panel {
    .connecting {
      font-size: 12px;
      color: #cbd4dc;
      padding-top: 100px;
      text-align: center;
    }
  }
}
.draggable-container {
  display: grid;
  width: 100%;
  height: 100%;
  &.grid-1 {
    grid-template-columns: repeat(1, 1fr);
  }
  &.grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  &.grid-6 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr; // 两列

    .no-camera-item:first-child {
      grid-column: 1 / span 2; // 第一列占据两列
      grid-row: 1 / span 2; // 第一行占据两行
    }
    &.grid-9 {
      grid-template-columns: repeat(3, 1fr);
    }
    &.grid-16 {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}
.active-cell {
  // border: 2px solid #63e764;
}
.active-empty {
  // border: 2px solid #63e764;
  // height: calc(100% - 4px);
  &::before {
    z-index: 100;
    content: '';
    position: absolute;
    height: calc(100% - 4px);
    width: calc(100% - 4px);
    top: 0;
    left: 0;
    border: 2px solid #63e764;
  }
}
</style>
