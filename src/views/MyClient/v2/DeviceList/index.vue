<!-- file 我的客户 -->
<template>
  <div class="c-my-client">
    <PcArrowFold class="c-my-client__icon" :class="{ rotate: isFold }" @click="isFold = !isFold" />
    <!-- 客户树 -->
    <template>
      <ClientPanel
        :warpperHeight="'144px'"
        :moreOperations="moreOperations"
        :showContextMenu="true"
        :countType="'device'"
        is-show-more-option
        @nodeClick="selectUser"
        @rightClick="rightSelectEnt"
        @moreOperateClick="moreOperateClick"
        :class="{ isfold: isFold }"
        :showClientExpire="true"
        :activeTab="activeTab"
      />
    </template>
    <!-- 信息栏 -->
    <template>
      <div class="c-my-client__container">
        <UserAssets class="c-my-client__info" :target-user="targetUser" />
        <SearchEquipment class="c-my-client__equipment" :target-user="targetUser" @changeActiveName="changeActiveName" />
      </div>
    </template>

    <!-- 新增|编辑客户 -->
    <template>
      <OperClient
        :title="dialogRecord.title"
        ref="operClientRef"
        :controlType="dialogRecord.controlType"
        :clientForm="clientForm"
        v-model="operateEntVisible"
      />
    </template>
    <!-- 客户详情[所属客户|详情|转移|下级客户] -->
    <template>
      <Client ref="ClientRef" :active="activeName" v-model="clientDetailVisible" :selectedRow="selectedRow" />
    </template>
    <!-- 删除客户 -->
    <template>
      <DelClient v-model="deleteEntVisible" :entId="entIdByRow" />
    </template>
    <!-- 修改密码 -->
    <template>
      <ChangePassword :info="id" :name="name" :modifiedType="'Client'" :dialogVisible.sync="showChangePassword" />
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import UserAssets from './UserAssets'
import SearchEquipment from './SearchEquipment'
import ClientPanel from '@/components/Tree/v2/ClientPanel'
import OperClient from '@/components/Client/OperClient'
import Client from '@/components/Client/Common/Client.vue'
import DelClient from '@/components/Client/DelClient'
import ChangePassword from '@/components/Client/ChangePassword.vue'
import { menuActions } from './config'
import { getProcessedOperations, clientMoreOperation } from '@/hooks/useTreeOperation.js'
import { getEnterprise } from '@/api'
import { EVENT_BUS, EVENT_NAME } from '@/utils/event-bus'
import { PcArrowFold } from '@/assets/icon'

const targetUser = ref({})

const selectUser = userInfo => {
  targetUser.value = userInfo
}
// rightclick
const selectTargetEnt = ref({})
const rightSelectEnt = entInfo => {
  selectTargetEnt.value = entInfo
}
//更多操作
const moreOperateClick = (eventName, userInfo) => {
  menuActions(eventName, userInfo)
}
const moreOperations = computed(() => {
  return getProcessedOperations(clientMoreOperation)
})
//新建编辑客户
const operateEntVisible = ref(false)
const dialogRecord = ref({})
const clientForm = ref()
const operClientRef = ref(null)
const handleOperator = async info => {
  let { type, id, data } = info
  if (type.controlType === 'Update') {
    const { data } = await getEnterprise({ entId: id })
    clientForm.value = { ...data, password: '********' }
  } else if (type.controlType === 'Add') {
    clientForm.value = {}
    operClientRef.value?.setEntId(data)
  }

  dialogRecord.value = type
  operateEntVisible.value = true
}

// 客户详情弹窗
const selectedRow = ref({})
const activeName = ref('')
const clientDetailVisible = ref(false)
const handleClient = data => {
  const { active, info } = data
  selectedRow.value = info
  clientDetailVisible.value = true
  activeName.value = active
}
//删除客户弹窗
const deleteEntVisible = ref(false)
const entIdByRow = ref('')
const handleDelClient = id => {
  deleteEntVisible.value = true
  // entIdByRow.value = selectTargetEnt.value.id
  entIdByRow.value = id
}
// 修改客户弹窗
const id = ref('')
const name = ref('')
const showChangePassword = ref(false)
const handleResetPwd = info => {
  id.value = info.id
  name.value = info.name
  showChangePassword.value = true // 显示修改密码的对话框
}
const activeTab = ref('all')
const changeActiveName = name => {
  activeTab.value = name
}
onMounted(() => {
  EVENT_BUS.$on(EVENT_NAME.CLIENT.EDIT, handleOperator)
  EVENT_BUS.$on(EVENT_NAME.CLIENT.ADD, handleOperator)
  EVENT_BUS.$on(EVENT_NAME.CLIENT.DETAIL, handleClient)
  EVENT_BUS.$on(EVENT_NAME.CLIENT.TRANSFER, handleClient)
  EVENT_BUS.$on(EVENT_NAME.CLIENT.SUB, handleClient)
  EVENT_BUS.$on(EVENT_NAME.CLIENT.DELETE, handleDelClient)
  EVENT_BUS.$on(EVENT_NAME.CLIENT.RESETPWD, handleResetPwd)
})

onBeforeUnmount(() => {
  EVENT_BUS.$off(EVENT_NAME.CLIENT.EDIT, handleOperator)
  EVENT_BUS.$off(EVENT_NAME.CLIENT.ADD, handleOperator)
  EVENT_BUS.$off(EVENT_NAME.CLIENT.DETAIL, handleClient)
  EVENT_BUS.$off(EVENT_NAME.CLIENT.TRANSFER, handleClient)
  EVENT_BUS.$off(EVENT_NAME.CLIENT.SUB, handleClient)
  EVENT_BUS.$off(EVENT_NAME.CLIENT.DELETE, handleDelClient)
  EVENT_BUS.$off(EVENT_NAME.CLIENT.RESETPWD, handleResetPwd)
})

const isFold = ref(false)
</script>

<style lang="scss">
.c-my-client {
  height: 100%;
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  position: relative;
  .c-my-client__icon {
    position: absolute;
    left: 310px;
    top: 80px;
    cursor: pointer;
    z-index: 2;
    transition: all 0.2s ease;
    &.rotate {
      left: 20px;
      transform: rotate(180deg);
    }
  }
}

.c-my-client__tree {
  width: 320px;
  border-radius: $containerRadius;
  background-color: #ffffff;
  height: 100%;
  overflow: hidden;
  margin-right: 10px;
}
.c-my-client__container {
  width: calc(100% - 330px);
  display: flex;
  flex: 1;
  flex-direction: column;
}
.c-my-client__info {
  flex-shrink: 0;
  box-sizing: border-box;
  min-height: 116px;
  border-radius: $containerRadius;
  background-color: #ffffff;
}
.c-my-client__equipment {
  flex: 1;
  position: relative;
  box-sizing: border-box;
  padding: 20px;
  margin-top: 10px;
  border-radius: $containerRadius;
  background-color: #ffffff;
  overflow: hidden;
}
</style>
