<!-- 设备查询 -->
<template>
  <div class="c-search-equipment">
    <el-form class="c-search-equipment__form mb-1" :inline="true">
      <div class="c-search-equipment__form__left">
        <el-form-item>
          <base-button
            v-has="'DeviceList:device:sale'"
            type="default"
            icon="sale"
            change-icon
            :content="$t('lg.limits.Bulk_sales')"
            @click="handleMenuEvent(SEARCH_MORE_MENU_CONFIG.BATCH_SALE.key, null, targetUser)"
          >
            {{ $t('lg.limits.Bulk_sales') }}
          </base-button>
          <base-button
            v-has="'DeviceList:device:renew'"
            type="default"
            icon="money"
            change-icon
            :content="$t('lg.limits.Batch_renewal')"
            @click="handleMenuEvent(SEARCH_MORE_MENU_CONFIG.BATCH_RENEW.key, selectedDevice)"
          >
            {{ $t('lg.limits.Batch_renewal') }}
          </base-button>
          <base-button
            v-has="'DeviceList:device:import'"
            type="default"
            icon="import"
            change-icon
            :content="$t('lg.limits.Batch_Import')"
            @click="handleMenuEvent(SEARCH_MORE_MENU_CONFIG.BATCH_IMPORT.key, null, targetUser)"
          >
            {{ $t('lg.limits.Batch_Import') }}
          </base-button>
          <base-button
            v-has="'DeviceList:device:upgrade'"
            type="default"
            icon="upGradation"
            change-icon
            :content="$t('lg.limits.car_manager_upgrade')"
            @click="handleMenuEvent(SEARCH_MORE_MENU_CONFIG.BATCH_UPGRADE.key, selectedDevice)"
          >
            {{ $t('lg.limits.car_manager_upgrade') }}
          </base-button>
        </el-form-item>
        <el-form-item>
          <el-popover popper-class="c-search-equipment__popper" placement="bottom" trigger="hover">
            <div class="c-search-equipment__menu">
              <div
                v-for="[key, item] in Object.entries(SEARCH_MORE_MENU)"
                :key="key"
                class="c-search-equipment__menu__item"
                v-has="item.permission"
                @click="handleMenuEvent(item.key, selectedDevice, targetUser)"
              >
                {{ item.label }}
              </div>
            </div>
            <template #reference>
              <span class="ljdw-color-blue cursor-point">{{ $t('IikV2EDWehRNB_DuPyoAi') }}</span>
            </template>
          </el-popover>
        </el-form-item>
      </div>
      <div class="c-search-equipment__form__right">
        <el-form-item>
          <el-checkbox v-model="formData.subFlag" @change="search">{{ $t('Z8MQX4KbfoflrmZEy5Vj6') }}</el-checkbox>
        </el-form-item>
        <el-form-item label="">
          <el-input :placeholder="$t('bzVAxsJEtQtawIDU5RBt1')" @keydown.native.enter="handleSearch" v-model="formData.queryStrList[0]">
            <template #prepend>
              <el-select v-model="formData.queryType" :placeholder="$t('lg.pleaseChoose')">
                <el-option :label="$t('lg.machineName')" value="1"></el-option>
                <el-option label="IMEI" value="2"></el-option>
                <el-option :label="$t('AmK-1t3nyDtFLUWuuGc7m')" value="3"></el-option>
                <el-option label="SimNo" value="4"></el-option>
                <el-option label="ICCID" value="5"></el-option>
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="c-search-equipment__form__center">
          <base-button type="primary" @click="handleSearch">
            <div class="flex center">
              <PcButtonSearch class="c-search-equipment__search" size="14" />
              <span>{{ $t('lg.query') }}</span>
            </div>
          </base-button>
          <el-button v-has="'DeviceList:search:batch'" @click="handleMenuEvent('batchSearch')">{{ $t('3nuuynf0P_SrOx0JqRKPW') }}</el-button>
        </el-form-item>
        <el-form-item class="c-search-equipment__filter">
          <AdvanceDeviceSearch @reset="handleSearchReset" @confirm="handleFilterSearch" />
        </el-form-item>
      </div>
    </el-form>
    <DataTable
      v-loading="isLoading"
      ref="equipmentRef"
      class="c-search-common__table"
      :data-config="dataConfig"
      :table-config="{ isNeedNo: true, height: '100%', isNeedCheckBox: true, export: true, showSum: true, isShowEmptyImage: true }"
      :data-list="dataList"
      :pagination-config="paginationConfig"
      :selectedDevice="selectedDevice"
      @paginationChange="paginationChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      @filter-change="handleFilterChange"
      @export="handleExport"
      @select="handleSelectChange"
      @select-all="handleSelection"
    >
      <template #imei="{value, record}">
        <div class="c-search-equipment__copy">
          <span class="ljdw-color-blue cursor-point" @click="openDetail(record)">{{ value }}</span>
          <i class="el-icon-document-copy cursor-point ljdw-color-blue ml-1" @click="copy(value, $event)"></i>
        </div>
      </template>
      <!-- machineTypeAscription: 1 宠物 2 车辆 -->

      <template #carNo="{ record, value }">
        {{ record.machineTypeAscription === 2 && isCarFlag ? value : '-' }}
      </template>
      <template #petCarNo="{ record,value }">
        {{ record.machineTypeAscription === 1 && isPetFlag ? value : '-' }}
      </template>

      <template #state="{record}">
        <div class="center flex">
          <component
            :is="statusCompMap[record.stateValue].comp"
            :class="statusCompMap[record.stateValue].class"
            size="17px"
            style="margin-right:4px"
          ></component>
          <span :class="statusCompMap[record.stateValue].class">{{ statusCompMap[record.stateValue].name }}</span>
        </div>
        <!-- <PcOffline size="16" :class="{ deviceStatus: value }" /> -->
      </template>
      <template #versionType="{value}">{{ value === 1 ? i18n.t('d4Ehx0fyIKAOO_AwNZQFe') : i18n.t('GHgMufpRw6-B7ZpF_CMob') }}</template>
      <template #mileage="{value}">{{ MeterToKmMi(value) }}</template>
      <template #overSpeedingThreshold="{value}">{{ MeterToKmMi(value * 1000) || '-' }}</template>
      <template #customExpireTime="{value}">
        <span>{{ handleDateError(value) }}</span>
      </template>
      <template #joinTime="{value}">
        <span>{{ handleDateError(value) }}</span>
      </template>
      <template #saleTime="{value}">
        <span>{{ handleDateError(value) }}</span>
      </template>
      <template #serviceTime="{value}">
        <span>{{ handleDateError(value) }}</span>
      </template>
      <template #activeTime="{value}">
        <span>{{ handleDateError(value) }}</span>
      </template>
      <template #carType="{record}">
        <img
          :src="showIcon(record.carType)"
          :class="{ 'pet-icon': record.carType === 33 || record.carType === 34, 'pet-defalut-icon': record.carType !== 33 && record.carType !== 34 }"
          alt=""
        />
      </template>
      <template #lastPosition="{record,index}">
        <span v-if="record.lastPosition">{{ record.lastPosition }}</span>
        <span v-else-if="!record.lat || !record.lon">-</span>
        <el-button v-else type="text" @click="showAddress(record, index)">{{ $t('vBpDexpv7D_KNy_aaxmZx') }}</el-button>
      </template>
      <template #operator="{record}">
        <div class="c-search-common__table__operator">
          <span class="mr-1" v-if="record.versionType === 0" @click="handleUpgrade">{{ $t('3umrb4aXbfmzceFs9R4Lp') }}</span>
          <span class="mr-1" @click="openDetail(record)">{{ $t('d2rfxOdgIK8sx-aXn_UjT') }}</span>
          <el-dropdown>
            <span>{{ $t('IikV2EDWehRNB_DuPyoAi') }}</span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="[key, value] in Object.entries(getDropdownMenu(record, selection))" :key="key" @click.native="value">{{
                key
              }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </template>
      <template #operatorHeader="{label}">
        <span @click="openCustomColumn" style="font-size: 14px;">
          {{ label }}
          <PcFilterate class="icon-filter" />
        </span>
      </template>
      <template #remark="{record}">{{ getCurrentDeviceRemark(record) }}</template>
      <template #voltage="{value}"> {{ value ? `${value / 1000}V` : '-' }} </template>
      <template #groupName="{record}">
        <span v-if="!carGroupMap[record.entId]">Loading...</span>
        <el-select v-else v-model="record.carGroupId" :disabled="!checkPermission('DeviceList:device:group')" @change="handleChangeGroup(record)">
          <el-option v-for="item in carGroupMap[record.entId]" :key="item.carGroupId" :label="item.name" :value="item.carGroupId"></el-option>
        </el-select>
      </template>
    </DataTable>

    <!-- 弹层 -->
    <template>
      <!-- 批量查询 -->
      <BatchSearch ref="batchSearchRef" @batch-search="handleBatchSearch" />
      <!-- 批量销售 -->
      <BatchSaleIntegrate ref="batchSaleIntegrateRef" v-model="batchOperation.batchSaleVisible" @update="handleSearch" />
      <!-- 批量导入 -->
      <BatchImportIntegrate ref="batchImportIntegrateRef" v-model="batchOperation.batchImportVisible" @update="handleSearch" />
      <!-- 批量续订 -->
      <BatchRenewIntegrate v-model="batchOperation.batchRenewVisible" @update="handleSearch" />
      <!-- 批量升级 -->
      <BatchUpgradeIntegrate v-model="batchOperation.batchUpgradeVisible" @update="handleSearch" />
      <!-- 批量转移 -->
      <BatchTransferIntegrate ref="batchTransferIntegrateRef" v-model="batchOperation.batchTransferVisible" @update="handleSearch" />
      <!-- 批量分配 -->
      <BatchDistribute ref="batchDistributeRef" v-model="batchOperation.batchDistributeVisible" @update="handleSearch" />
      <!-- 批量报警设置 -->
      <BatchAlarmSetting ref="batchAlarmSettingRef" v-model="batchOperation.batchAlarmVisible" @update="handleSearch" />
      <!-- 批量指令 -->
      <BatchInstruction ref="batchInstructionRef" v-model="batchOperation.batchInstructionVisible" @update="handleSearch" />
      <!-- 修改信息 -->
      <BatchEdit ref="batchEditRef" v-model="batchOperation.batchEditVisible" @update="handleSearch" />
      <!-- 修改型号 -->
      <BatchEditModel ref="batchEditModelRef" v-model="batchOperation.batchEditModelVisible" @update="handleSearch" />
      <!-- 分组 -->
      <BatchGroup v-model="batchOperation.batchGroupingVisible" />
      <!-- 批量设置定时任务 -->
      <BatchTask ref="batchTaskRef" v-model="batchOperation.batchSetTaskVisible" />
      <!-- 批量修改用户到期 -->
      <BatchUpdateMaturity v-model="batchOperation.batchUpdateMaturityVisible" @update="handleSearch" />

      <!-- 设备详情 -->
      <EquipmentDetail
        v-model="equipmentVisible"
        :user-id="targetUser.id"
        :equipment-info="equipmentInfo"
        @renew="handleRenewRecord"
        @upgrade="handleUpdateRecord"
        @search="search"
      />
      <!-- 密码重置 -->
      <CopyDialog ref="copyDialogRef" :msg="$t('2gK1OvSK1N490nqe6BmML')" :pass-word="restPasswordRecord.password" :user-name="restPasswordRecord.imei" />
    </template>
    <!-- 自定义列弹窗 -->
    <CustomColumn @change="changeColumns" :customColumnsIndex="columnsIndex" :customColumns="columns" :visible.sync="showCustomColumn" />
  </div>
</template>

<script setup>
import { onMounted, set, ref, provide, watch, computed, onUnmounted, nextTick } from 'vue'
import store from '@/store'
import router from '@/router'
import dayjs from 'dayjs'
import { BASE_MESSAGE } from '@/base-ui/message'
import { SEARCH_MORE_MENU, DEALER_TABLE_CONFIG, All_COLUNMS, SEARCH_MORE_MENU_CONFIG, DEVICE_STATUS } from './config'
import { exportExcelResolveAddress } from '@/utils/statistics.js'
import { DETAIL_CAR_TYPE } from './components/EquipmentDetail/components/config'
import { PcButtonSearch, PcFilter, PcFilterate, PcOffline, PcOnline } from '@/assets/icon'
import { loadAddress } from '@/utils/address.js'
import { MeterToKmMi } from '@/utils/convert'
import { useDataTable } from '@/hooks/useCommon'
import { useSearchEquipment } from './useSearchEquipment'
import { getDeviceList, getUserPreference, getExportDeviceList, getUserGroup, setGroup } from '@/api'
import { EVENT_BUS, EVENT_NAME } from '@/utils/event-bus'
import { checkPermission } from '@/utils/auth'
import handleCopy from '@/utils/clipboard'

import DataTable from '@/components/Common/DataTable'
import AdvanceDeviceSearch from '@/components/Device/AdvanceDeviceSearch'
import BatchSearch from '@/components/Device/DeviceBatchSearch'
import CopyDialog from '@/components/Bussiness/CopyDialog'
import BatchSaleIntegrate from './components/BatchSaleIntegrate'
import BatchImportIntegrate from './components/BatchImportIntegrate'
import BatchRenewIntegrate from './components/BatchRenewIntegrate'
import BatchUpgradeIntegrate from './components/BatchUpgradeIntegrate'
import BatchTransferIntegrate from './components/BatchTransferIntegrate'
import BatchAlarmSetting from './components/BatchAlarmSetting'
import BatchInstruction from './components/BatchInstructions'
import BatchDistribute from './components/BatchDistribute'
import BatchEdit from './components/BatchEdit'
import BatchEditModel from './components/BatchEditModel'
import BatchGroup from './components/BatchGroup'
import BatchTask from './components/BatchTask'
import BatchUpdateMaturity from './components/BatchUpdateMaturity'
import EquipmentDetail from './components/EquipmentDetail'
import CustomColumn from '@/components/Device/CustomColumn.vue'
import { i18n } from '@/i18n'
const props = defineProps({
  targetUser: {
    type: Object,
    default: () => ({})
  }
})

const initGovernmentFormData = {
  queryStrList: [],
  startTime: '',
  endTime: '',
  id: store.state.user.userinfo.entId,
  queryType: '1',
  subFlag: false,
  expirType: undefined,
  expirTimeType: undefined
}
const statusCompMap = {
  [DEVICE_STATUS.ONLINE]: { comp: PcOnline, class: 'online', name: i18n.t('flK64mYHgrj-k7-D2XupZ') },
  [DEVICE_STATUS.OFFLINE]: { comp: PcOffline, class: 'offline', name: i18n.t('dtFn22xfEx789uFKvaG_n') },
  [DEVICE_STATUS.NOT_USED]: { comp: PcOffline, class: 'inactive', name: i18n.t('M_dYy2C4ldXbU0GcSh8XQ') },
  [DEVICE_STATUS.EXPIRED]: { comp: PcOffline, class: 'expire', name: i18n.t('Jet1u7xx2NupyskORg_e-') }
}
const equipmentRef = ref(null)
const carGroupMap = ref({})
const { formData, dataList, selection, paginationConfig, isLoading, search, paginationChange, handleSelectionChange, getSearchParams } = useDataTable(
  getDeviceList,
  initGovernmentFormData,
  {
    paramsCallBack: params => {
      params.subFlag = params.subFlag ? 1 : 0
    },
    resultCallBack: async params => {
      nextTick(() => {
        selectedDevice.value.forEach(record => {
          equipmentRef.value.toggleRowSelection(
            params.value.find(item => item.carId === record.carId),
            true
          )
        })
      })
      setTimeout(() => {
        equipmentRef.value.updateTableBodyHeight()
      })
      // 查询分组
      const entIds = [...new Set(dataList.value.map(item => item.entId))]
      const { data } = await getUserGroup({ entIds })
      entIds.forEach(id => {
        if (!carGroupMap.value[id]) {
          set(carGroupMap.value, id, data.filter(item => Number(item.entId) === Number(id)) || [])
          carGroupMap.value[id].unshift(data[0])
        }
      })
      dataList.value.forEach(item => {
        set(item, 'carGroupList', carGroupMap.value[item.entId])
      })
    }
  }
)
const {
  batchSearchRef,
  batchSaleIntegrateRef,
  batchImportIntegrateRef,
  batchTransferIntegrateRef,
  batchDistributeRef,
  batchAlarmSettingRef,
  batchInstructionRef,
  batchEditModelRef,
  batchTaskRef,
  batchOperation,
  restPasswordRecord,
  copyDialogRef,
  handleMenuEvent,
  getDropdownMenu,
  handleUpgrade
} = useSearchEquipment()

const getCurrentDeviceRemark = record => {
  const userInfo = store.state.user.userinfo
  const remark = userInfo.entType === 0 && userInfo.userType === 1 ? record.agentRemark : record.remark
  return remark || '-'
}

const { versionType } = router.currentRoute.params
const versionTypeMap = {
  basic: 1,
  standard: 2,
  expired: 3
}
if (versionType) {
  formData.value.versionStatus = versionTypeMap[versionType]
  //查询版本过滤时，需要包含下级
  formData.value.subFlag = true
}
const clearSelection = (isSearch = false) => {
  isSearch && search()
  selectedId.value = []
  selectedDevice.value = []
  equipmentRef.value.clearSelection()
}
watch(
  () => props.targetUser,
  ({ id }) => {
    formData.value.id = id
    searchColumn()
    search()
    //versionStatus参数是点击版本小图标带参跳转到当前页面过滤查询。只在页面加载生效一次，防止后续其他的查询会附带这个参数导致过滤结果不符合
    formData.value.versionStatus = undefined
    // 只有在没有路由参数的情况下才重置 subFlag
    if (!router.currentRoute.query.subFlag) {
      formData.value.subFlag = false
    }
  }
)
const getCurrentInfo = computed(() => store.state.user.userinfo)
//固定列
const columnsIndex = ref('1,2,7')
const searchColumn = async () => {
  const { ret, data } = await getUserPreference({ perfenceId: 1, fkId: getCurrentInfo.value.userId })
  if (ret) {
    if (data) {
      const currentColumns = columnsIndex.value.split(',')
      const newColumns = [...data.perfenceString.split(','), ...currentColumns]
      columnsIndex.value = Array.from(new Set(newColumns)).join(',')
    } else {
      // 如果没有数据，设置为默认列
      columnsIndex.value = '1,2,3,6,7,8,9,10,12,13,18,22'
    }
  }
}
//  选中的列
const selectedColumns = computed(() => {
  return columnsIndex.value
    .split(',')
    .map(index => All_COLUNMS[index])
    .filter(Boolean)
})

// 获取过滤后的列配置
const filteredColumns = computed(() => {
  return DEALER_TABLE_CONFIG().filter(column => {
    const isSelected = selectedColumns.value.includes(String(column.key)) || column.alwaysShow
    if (!column.machineTypeAscription) {
      return isSelected
    }
    const typeVisibilityMap = {
      1: isPetFlag.value,
      2: isCarFlag.value
    }
    return isSelected && typeVisibilityMap[column.machineTypeAscription]
  })
})

// 处理导出字段名 各元素取值参考返参参数名。此外petName(宠物名称)、lastPosition(最后位置)为新增列名，当前返参不返回
// 备注字段也需要做特殊处理:经销商、管理员角色调用 agentRemark 字段，政企、普通管理员、单设备调用 remark 字段，其余字段直接使用 key 对应字段名
const fieldNames = computed(() => {
  const result = []
  filteredColumns.value.forEach(column => {
    if (column.key !== 'operator') {
      // 如果是宠物类型且是petCarNo字段，用petName替换
      if (column.key === 'petCarNo' && column.machineTypeAscription === 1) {
        result.push('petName')
      } else if (column.key === 'remark') {
        result.push(getCurrentInfo.value.entType === 0 && getCurrentInfo.value.userType === 1 ? 'agentRemark' : 'remark')
      } else {
        result.push(column.key)
      }
    }
  })
  return result
})

const dataConfig = computed(() => {
  return filteredColumns.value
})

const isPetFlag = ref(false)
const isCarFlag = ref(false)
watch(columnsIndex, newColumnsIndex => {
  const Columns = newColumnsIndex.split(',').map(Number)
  // 检查特定列的键
  isCarFlag.value = Columns.includes(5) // 5 代表车辆
  isPetFlag.value = Columns.includes(14) // 14 代表宠物
})
const changeColumns = list => {
  columnsIndex.value = list
}
const columns = ref([])

// 图标显示
const showIcon = type => {
  const carTypeConfig = DETAIL_CAR_TYPE.find(item => item.label === String(type))
  return carTypeConfig ? carTypeConfig.icon : ''
}
// 查看地址
const showAddress = async row => {
  let address = await loadAddress({
    lon: row.lon,
    lat: row.lat,
    lonC: row.lonc,
    latC: row.latc,
    businessType: 19,
    carId: row.carId
  })
  set(row, 'lastPosition', address || i18n.t('deviceList.wufajiexi_826280'))
}
const handleBatchSearch = record => {
  const firstParam = formData.value.queryStrList[0]
  formData.value = { ...formData.value, ...record }
  search()
  getSearchParams().queryStrList = [firstParam]
}

// 更多筛选
const sortType = {
  activeTime: 1,
  serviceTime: 2,
  fuelConsumption: 3,
  mileage: 4,
  customExpireTime: 5,
  joinTime: 6,
  saleTime: 7
}
const handleSortChange = record => {
  const { prop, order } = record
  if (!order) {
    formData.value.asc = false
    delete formData.value.orderBy
  } else if (order === 'ascending') {
    formData.value.asc = true
    formData.value.orderBy = sortType[prop]
  } else if (order === 'descending') {
    formData.value.asc = false
    formData.value.orderBy = sortType[prop]
  }
  search()
}

const handleFilterChange = record => {
  formData.value.state = record[Object.keys(record).at(-1)].at(-1)
  search()
}

// 打开详情
const equipmentVisible = ref(false)
const equipmentInfo = ref({})
const openDetail = record => {
  equipmentVisible.value = true
  equipmentInfo.value = record
}

// 更多查询
const handleFilterSearch = async params => {
  formData.value = { ...formData.value, ...params }
  await search(formData.value)
}
//重置查询
const handleSearchReset = () => {
  formData.value = { ...formData.value, ...initGovernmentFormData }
  formData.value.id = props.targetUser.id
  formData.value.subFlag = false
  formData.value.queryStrList[0] = ''
  formData.value.startTime = ''
  formData.value.endTime = ''
  search()
}

// 文件下载
const exportLoading = ref(false)
const handleExport = async event => {
  if (exportLoading.value) return
  const { total } = paginationConfig.value
  if (total < 1) return BASE_MESSAGE.error(i18n.t('lg.noData'))
  if (total > 10000) return BASE_MESSAGE.error(i18n.t('deviceList.dancidaochu_796931'))
  exportLoading.value = true
  try {
    let arrayBuffData = await getExportDeviceList({ ...getSearchParams(), fieldNames: fieldNames.value })
    exportExcelResolveAddress(arrayBuffData, dayjs().format('YYYY-MM-DD') + '_' + i18n.t('lg.limits.Device_List') + '.xlsx')
  } catch (e) {
    exportLoading.value = false
  } finally {
    exportLoading.value = false
    event?.done?.()
  }
}

const handleDateError = value => {
  const time = dayjs(value).format('YYYY-MM-DD')
  return time === 'Invalid Date' ? value : time
}

const handleRenewRecord = record => {
  EVENT_BUS.$emit(EVENT_NAME.DEVICE.CLEAR)
  selectedDevice.value = record ? [record] : []
  batchOperation.batchRenewVisible = true
  equipmentVisible.value = false
}
const handleUpdateRecord = record => {
  EVENT_BUS.$emit(EVENT_NAME.DEVICE.CLEAR)
  selectedDevice.value = record ? [record] : []
  batchOperation.batchUpgradeVisible = true
  equipmentVisible.value = false
}
const selectedId = ref([])
const selectedDevice = ref([])
//表格勾选
const handleSelectChange = (e, row) => {
  if (selectedId.value.includes(row.carId)) {
    selectedDevice.value = selectedDevice.value.filter(item => item.carId !== row.carId)
    selectedId.value = selectedId.value.filter(item => item !== row.carId)
  } else {
    selectedId.value.push(row.carId)
    selectedDevice.value.push(row)
  }
}

//全选
const handleSelection = selections => {
  selections = selections.filter(item => item)
  const ids = !selections.length ? dataList.value.map(item => item.carId) : selections.map(item => item.carId)
  if (!selections.length) {
    selectedId.value = selectedId.value.filter(item => !ids.includes(item))
    selectedDevice.value = selectedDevice.value.filter(item => !ids.includes(item.carId))
  } else {
    ids.forEach((id, index) => {
      if (!selectedId.value.includes(id)) {
        selectedId.value.push(id)
      }
      if (!selectedDevice.value.some(item => item.carId === id)) {
        selectedDevice.value.push(selections[index])
      }
    })
  }
}

// 修改状态进行查询
const handleStateSearch = ({ state, subFlag, expireType, expireTimeType } = {}) => {
  if (state !== undefined) {
    formData.value.state = state
  }
  if (subFlag !== undefined) {
    formData.value.subFlag = Boolean(subFlag)
  }
  // 处理过期查询参数
  if (expireType !== undefined) {
    formData.value.expirType = expireType
  }
  if (expireTimeType !== undefined) {
    formData.value.expirTimeType = expireTimeType
  }
  search()
}
provide('allselections', selectedDevice)

const handleMenu = event => {
  const { eventName, data } = event
  handleMenuEvent(eventName, null, data)
}

// 切换分组
const handleChangeGroup = async record => {
  try {
    const { ret } = await setGroup({
      imeis: record.imei,
      carGroupId: record.carGroupId
    })
    if (ret) return BASE_MESSAGE.success(i18n.t('rVAoxxyYFdp_tC9Dnyb6M'))
  } catch (e) {
    BASE_MESSAGE.error(i18n.t('jCngDisNEtHDk2AbrnGT0'))
  }
}
const emit = defineEmits(['changeActiveName'])
onMounted(() => {
  EVENT_BUS.$on(EVENT_NAME.DEVICE.SEARCH, handleStateSearch)
  EVENT_BUS.$on(EVENT_NAME.DEVICE.RENEW, handleRenewRecord)
  EVENT_BUS.$on(EVENT_NAME.DEVICE.UPGRADE, handleUpdateRecord)
  EVENT_BUS.$on(EVENT_NAME.DEVICE.SALE, handleMenu)
  EVENT_BUS.$on(EVENT_NAME.DEVICE.IMPORT, handleMenu)
  EVENT_BUS.$on(EVENT_NAME.DEVICE.CLEAR, clearSelection)

  // 处理路由参数
  const { query } = router.currentRoute
  if (query.expiredType !== undefined && query.expireTimeType !== undefined) {
    emit('changeActiveName', 'expire')
  }
})
onUnmounted(() => {
  EVENT_BUS.$off(EVENT_NAME.DEVICE.SEARCH, handleStateSearch)
  EVENT_BUS.$off(EVENT_NAME.DEVICE.RENEW, handleRenewRecord)
  EVENT_BUS.$off(EVENT_NAME.DEVICE.UPGRADE, handleUpdateRecord)
  EVENT_BUS.$off(EVENT_NAME.DEVICE.SALE, handleMenu)
  EVENT_BUS.$off(EVENT_NAME.DEVICE.IMPORT, handleMenu)
  EVENT_BUS.$off(EVENT_NAME.DEVICE.CLEAR, clearSelection)
})

//展开自定义列弹窗
const showCustomColumn = ref(false)
const openCustomColumn = () => {
  showCustomColumn.value = true
}

// 查询
const handleSearch = () => {
  selection.value = []
  selectedId.value = []
  selectedDevice.value = []
  formData.value.state = undefined
  search()
}

// 复制
const copy = (text, e) => {
  handleCopy(
    text,
    e,
    () => {
      BASE_MESSAGE.success(i18n.t('nEXvu2BRLQvSN2q4zNh16'))
    },
    () => {}
  )
}

// 处理宠物名称字段，machineTypeAscription为1时赋值petCarNo
watch(dataList, newList => {
  newList.forEach(item => {
    if (item.machineTypeAscription === 1) {
      item.petCarNo = item.carNo
    }
  })
})
</script>

<style lang="scss">
.c-search-equipment {
  @include column();
  .el-input-group__prepend .el-input {
    width: 100px;
  }
}
.c-search-equipment__search {
  padding-right: 5px;
}
.c-search-equipment__popper {
  padding: 8px 0;
  border-radius: $inputRadius;
}
.c-search-equipment__form {
  position: relative;
  display: flex;
  justify-content: space-between;
  .el-form-item {
    margin-bottom: 0;
  }
}
// .c-search-equipment__filter .el-form-item__content {
//   position: static;
// }
.c-search-equipment__filter__icon {
  // @include center();
  // position: absolute;
  // z-index: 1;
  // top: 16px;
  // right: 0;
  // transform: translateY(-50%);
  // cursor: pointer;
  color: $primary;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.c-search-equipment__menu__item {
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  padding: 10px;
  cursor: pointer;
  box-sizing: border-box;
  &:hover {
    color: $primary;
    background-color: $border;
  }
}
.c-search-common__table__operator {
  span {
    cursor: pointer;
    color: $primary;
  }
}
.c-search-equipment__form__right {
  @include end();
  flex-wrap: wrap;
  // padding-right: 50px;
}
.c-search-equipment__form__center .el-form-item__content {
  @include center();
}
.icon-filter {
  margin-left: 27px;
}
.pet-icon {
  width: 18px;
}
.pet-defalut-icon {
  width: 28px;
}
.c-search-equipment__copy {
  white-space: nowrap;
  .el-icon-document-copy {
    display: none;
  }
  &:hover .el-icon-document-copy {
    display: inline-block;
  }
}
.online {
  color: #42b041;
}
.offline {
  color: #8c8c8c;
}
.inactive {
  color: #d8d8d8;
}
.expire {
  color: #ff6648;
  & + span {
    color: #ff6648;
  }
}
</style>
