<!-- file: 经销商布局 卡片顺序：客户卡片、设备卡片、服务卡片、快捷功能、状态统计、增长量TOP10、设备统计、客户统计-->
<template>
  <div class="c-dealer-home">
    <div class="c-dealer-home__flex">
      <Client v-has="'Dashboard:overview:user'" class="c-dealer-home__item" />
      <DealerEquipment v-has="'Dashboard:overview:device'" class="c-dealer-home__item" />
      <DealerServe v-has="'Dashboard:overview:service'" class="c-dealer-home__item" />
    </div>

    <!-- 快捷入口 -->
    <ShortcutFunction v-has="'Dashboard:device:shortcut'" class="c-dealer-home__card" />
    <!-- 状态统计 -->
    <DeviceStatus v-has="'Dashboard:overview:statistics'" ref="deviceStatusRef" class="c-dealer-home__card" :style="statusStatisticGrid" />

    <!-- 型号统计 -->
    <TypeCount v-has="'Dashboard:deviceType:statistics'" class="c-dealer-home__card" />

    <!-- 设备统计 -->
    <DeviceStatistic
      v-has="'Dashboard:overview:analyze'"
      ref="deviceStatisticRef"
      class="c-dealer-home__card c-dealer-home__device__statistic"
      :style="statusStatisticGrid"
    />
    <!-- 客户统计 -->
    <ClientCount
      v-has="'Dashboard:statistics:analyze'"
      ref="clientCountRef"
      class="c-dealer-home__card c-dealer-home__device__statistic"
      :style="clientIncrementGrid"
    />

    <!-- 增长量 -->
    <CacheIncrementTopTen v-has="'Dashboard:overview:tentop'" class="c-dealer-home__card" :style="clientIncrementGrid" />
    <!-- 设备地域 -->
    <CacheDeviceRegion v-has="'Dashboard:overview:area'" class="c-dealer-home__container c-dealer-home__region" ref="deviceRegionRef" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useElementResize } from '@/hooks/useElementResize'
import Client from './component/Client'
import DealerEquipment from './component/DealerEquipment'
import DealerServe from './component/DealerServe'
import DeviceStatistic from './component/DeviceStatistic'
import DeviceStatus from '@/views/Dashboard/components/DeviceStatus'
import ClientCount from '@/views/Dashboard/components/ClientCount'
import IncrementTopTen from '@/views/Dashboard/components/IncrementTopTen'
import DeviceRegion from '@/views/Dashboard/components/DeviceRegion'
import ShortcutFunction from '@/views/Dashboard/components/ShortcutFunction'
import TypeCount from '@/views/Dashboard/components/TypeCount.vue'
import { checkPermission } from '@/utils/auth'
import { withCache } from '@/utils/withCache'

const CacheDeviceRegion = withCache({
  cacheKey: () => `DeviceRegion__Cache`
})(DeviceRegion)

const CacheIncrementTopTen = withCache({
  cacheKey: () => `IncrementTopTen__Cache`
})(IncrementTopTen)

const deviceStatusRef = ref(null)
const clientCountRef = ref(null)
const deviceStatisticRef = ref(null)
const deviceRegionRef = ref(null)

const handleChangeSize = () => {
  deviceStatusRef.value?.resize()
  clientCountRef.value?.resize()
  deviceRegionRef.value?.resize()
  deviceStatisticRef.value?.handleEchartResize()
}
useElementResize('.sidebar-logo-container', handleChangeSize)

const statusStatisticGrid = computed(() => {
  return checkPermission('Dashboard:overview:statistics') && checkPermission('Dashboard:overview:analyze') ? {} : { 'grid-column': '1 / 4' }
})

const clientIncrementGrid = computed(() => {
  return checkPermission('Dashboard:statistics:analyze') && checkPermission('Dashboard:overview:tentop') ? {} : { 'grid-column': '1 / 4' }
})

onMounted(() => {
  window.addEventListener('resize', handleChangeSize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleChangeSize)
})
</script>

<style lang="scss">
.c-dealer-home__item {
  box-sizing: border-box;
  padding: 28px 33px 10px;
  height: 218px;
  flex: 1;
  min-width: 350px;
  background-color: #ffffff;
  border-radius: $containerRadius;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.05);
  background-image: url('~@/assets/img/home/<USER>');
  background-position: bottom center;
  background-size: 100% 100%;
  &:nth-child(2) {
    background-image: url('~@/assets/img/home/<USER>');
    margin: 0 20px;
  }
  &:nth-child(3) {
    background-image: url('~@/assets/img/home/<USER>');
  }
}

.c-dealer-home__device__statistic {
  padding-left: 0;
  flex: 1;
}
.c-dealer-home__clientCount {
  // grid-column: 2 / 4;
  flex: 1;
  background-color: #fff;
  overflow: hidden;
}
.c-dealer-home__flex {
  grid-column: 1 / 4;
  display: flex;
}
.c-dealer-home__gap {
  gap: 20px;
}
</style>
