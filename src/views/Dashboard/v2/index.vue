<!-- file: 首页 -->
<template>
  <div class="c-home">
    <!-- 政企布局 -->
    <template v-if="isGovernment">
      <GovernmentHome class="c-home-contain" />
    </template>

    <!-- 经销商布局 -->
    <template v-else>
      <DealerHome class="c-home-contain" />
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import store from '@/store'
import DealerHome from './DealerHome'
import GovernmentHome from './GovernmentHome'
import { SAAS_ROLE } from '@/constant/common'

const isGovernment = computed(() => store.getters['entType'] === SAAS_ROLE.GOVERNMENT)
</script>

<style lang="scss">
.c-home {
  background-color: #f4f7fe;
  padding: 16px;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}
.c-home-contain {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 16px;
  gap: 16px;
  width: auto;
  min-width: 1050px;
}
.c-dealer-home__container,
.c-government-home__container {
  height: 400px;
  min-width: 350px;
  background: #fff;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  padding: 20px 30px 10px;
  border-radius: $containerRadius;
}
//快捷功能调整卡片高度
.c-dealer-home__card,
.c-government-home__card {
  height: 410px;
  min-width: 350px;
  background: #fff;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  padding: 20px 30px 10px;
  border-radius: $containerRadius;
  display: flex;
  flex-direction: column;
}
.c-dealer-home__region,
.c-government-home__region {
  grid-column: 1 / 4;
  height: 500px;
  overflow: hidden;
}
.active-text {
  color: $primary !important;
}
</style>
