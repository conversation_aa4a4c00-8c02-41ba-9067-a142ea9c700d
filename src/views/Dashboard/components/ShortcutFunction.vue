<template>
  <div class="c-device-shortcut">
    <el-tabs v-model="activeTab">
      <el-tab-pane v-for="tab in availableTabs" :key="tab.name" :label="tab.label" :name="tab.name" lazy />
    </el-tabs>
    <div class="c-device-shortcut__container">
      <component class="c-device-shortcut__content" :is="activeComponent" ref="currentComponent" @changeBtn="val => (isLoading = val)" />
    </div>
    <div class="c-device-shortcut__actions">
      <el-button class="c-batch-import__btn" @click="handleRefresh">{{ $t('QHewpO-a1QEPaAwvF6Xzr') }}</el-button>
      <el-button class="c-batch-import__btn" type="primary" :disabled="isLoading" @click="handleConfirm">
        <i v-if="isLoading" class="el-icon-loading" />
        <span>{{ okText }}</span>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useLoading } from '@/hooks/useCommon'
import InputDevice from '../components/shortcut/InputDevice.vue'
import SaleDevice from '../components/shortcut/SaleDevice.vue'
import RenewDevice from '../components/shortcut/RenewDevice.vue'
import UpdateDevice from '../components/shortcut/UpdateDevice.vue'
import ResetDevice from '../components/shortcut/ResetDevice.vue'
import { checkPermission } from '@/utils/auth'
const { isLoading } = useLoading()
import { i18n } from '@/i18n'

// 统一管理标签页配置
const TAB_CONFIG = {
  importTab: {
    label: i18n.t('lg.limits.Import'),
    permission: 'Dashboard:device:import',
    component: InputDevice,
    buttonText: i18n.t('lg.limits.Import')
  },
  saleTab: {
    label: i18n.t('lg.logDict.sale'),
    permission: 'Dashboard:device:sale',
    component: SaleDevice,
    buttonText: i18n.t('lg.logDict.sale')
  },
  renewTab: {
    label: i18n.t('lg.limits.Renew'),
    permission: 'Dashboard:device:renew',
    component: RenewDevice,
    buttonText: i18n.t('lg.limits.Renew')
  },
  updateTab: {
    label: i18n.t('3umrb4aXbfmzceFs9R4Lp'),
    permission: 'Dashboard:service:upgrade',
    component: UpdateDevice,
    buttonText: i18n.t('3umrb4aXbfmzceFs9R4Lp')
  },
  resetTab: {
    label: i18n.t('lg.reset'),
    permission: 'Dashboard:device:reset',
    component: ResetDevice,
    buttonText: i18n.t('lg.reset')
  }
}

const availableTabs = computed(() =>
  Object.entries(TAB_CONFIG)
    .filter(([_, config]) => checkPermission(config.permission))
    .map(([name, config]) => ({ name, label: config.label }))
)

// 动态设置默认选中的第一个有权限的标签页
const activeTab = ref('')
onMounted(() => {
  if (availableTabs.value.length > 0) {
    activeTab.value = availableTabs.value[0].name
  }
})

const activeComponent = computed(() => (activeTab.value ? TAB_CONFIG[activeTab.value].component : null))

// 计算按钮文本
const okText = computed(() => (activeTab.value ? TAB_CONFIG[activeTab.value].buttonText : ''))

const currentComponent = ref(null)

// 统一处理确认操作
const handleConfirm = () => {
  if (currentComponent.value?.handleOk) {
    currentComponent.value.handleOk()
  }
}

// 统一处理刷新操作
const handleRefresh = () => {
  if (currentComponent.value?.handleClose) {
    currentComponent.value.handleClose()
  }
}
</script>
<style lang="scss">
.c-textarea {
  .el-textarea {
    .el-textarea__inner {
      font-family: 'PingFang SC, PingFang SC';
    }
  }
}
</style>
<style lang="scss" scoped>
.c-device-shortcut__container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 0;
  margin-top: 3px;
  .c-device-shortcut__content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
.c-device-shortcut__actions {
  text-align: right;
  .c-batch-import__btn {
    height: 40px;
    min-width: 92px;
    .loadingBtn {
      cursor: not-allowed;
    }
  }
}
</style>
