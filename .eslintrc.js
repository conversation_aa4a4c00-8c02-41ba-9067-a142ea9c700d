module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: ['plugin:vue/essential', 'eslint:recommended', '@vue/prettier'],
  parserOptions: {
    parser: 'babel-eslint'
  },
  rules: {
    'no-console': 0,
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-unused-vars': [
      0,
      {
        // 允许声明未使用变量
        vars: 'local',
        // 参数不检查
        args: 'none'
      }
    ],
    'prettier/prettier': [
      'warn',
      {
        singleQuote: true,
        semi: false,
        printWidth: 160
      }
    ],
    'vue/no-side-effects-in-computed-properties': 0,
    'vue/multi-word-component-names': 0,
    'vue/no-mutating-props': 0,
    'no-dupe-else-if': 0,
    'no-import-assign': 0,
    'no-prototype-builtins': 0
  }
}
